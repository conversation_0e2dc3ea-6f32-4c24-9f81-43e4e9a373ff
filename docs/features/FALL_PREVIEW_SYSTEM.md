# 方块下落预览系统

## 功能概述

为方块消除后的下落逻辑添加了视觉预览效果，让玩家能够预先看到方块消除后的最终布局，提升游戏的视觉反馈和策略规划体验。

## 适用场景

1. **部分方块消除后**：上方方块的自然重力下落
2. **整行消除后**：上方方块的垂直平移下落
3. **道具效果后**：火球、闪电链、激流等道具消除方块后的下落

## 视觉效果特性

### 预览外观
- **半透明投影**：在方块最终落地位置显示半透明的"投影预览"
- **一致视觉风格**：与当前游戏中正在下落的活动方块底部投影保持一致
- **透明度设置**：默认40%透明度，确保能够区分正常方块和预览投影
- **完整外观**：显示方块的完整外观（颜色、特效等），但以半透明形式呈现

### 动画效果
- **淡出动画**：当实际下落动画开始时，预览逐渐淡出（15帧持续时间）
- **无冲突设计**：确保预览不会与现有的活动方块投影系统产生冲突

## 技术实现

### 核心方法

#### 1. 预览计算
```javascript
calculateFallPreview(validColumns, blocksToCheck, removedPositions)
```
- 计算每个方块的最终落地位置
- 创建网格临时副本进行模拟
- 返回预览数据数组

#### 2. 预览显示
```javascript
showFallPreview(validColumns, blocksToCheck, removedPositions)
```
- 清除之前的预览
- 计算新的预览位置
- 只显示会发生位置变化的方块

#### 3. 预览渲染
```javascript
renderFallPreview(ctx)
```
- 在渲染循环中绘制半透明预览
- 支持淡出动画效果
- 使用与正常方块相同的渲染逻辑

### 集成点

#### 网格系统集成
- **构造函数**：添加预览系统相关属性
- **applyGravity方法**：在执行重力前显示预览
- **render方法**：在绘制固定方块后、动画前渲染预览
- **updateAnimations方法**：更新预览淡出动画

#### 游戏控制器集成
- 自动集成到现有的 `applyGravity` 调用中
- 无需修改游戏控制器代码
- 支持所有消除场景（三消、行消、道具效果）

## 配置选项

### 可调节参数
```javascript
// 预览功能开关
this.previewEnabled = true;

// 预览透明度 (0-1)
this.previewAlpha = 0.4;

// 淡出动画持续时间（帧数）
this.previewFadeOutDuration = 15;
```

### 控制方法
```javascript
// 启用/禁用预览功能
grid.setPreviewEnabled(enabled);

// 设置预览透明度
grid.setPreviewAlpha(alpha);

// 检查是否有活跃预览
grid.hasActivePreview();

// 手动清除预览
grid.clearFallPreview();
```

## 性能优化

### 高效计算
- **临时矩阵**：使用浅拷贝创建网格副本进行计算
- **增量更新**：只计算会发生位置变化的方块
- **智能过滤**：预览数据只包含实际需要移动的方块

### 渲染优化
- **透明度检查**：只渲染透明度大于0的预览
- **早期退出**：没有预览时直接返回
- **复用渲染**：使用现有方块渲染逻辑，避免重复代码

## 用户体验提升

### 策略规划
- **预见结果**：玩家可以预先看到消除后的布局
- **决策辅助**：帮助玩家做出更好的策略决策
- **减少意外**：避免因不可预见的下落结果导致的挫败感

### 视觉反馈
- **即时反馈**：消除操作的结果立即可见
- **平滑过渡**：从预览到实际动画的平滑过渡
- **清晰区分**：预览与实际方块的明确视觉区分

## 兼容性

### 现有系统兼容
- **无破坏性**：不影响现有游戏逻辑
- **可选功能**：可以随时启用或禁用
- **向后兼容**：不影响现有的下落动画系统

### 扩展性
- **模块化设计**：预览系统独立封装
- **易于扩展**：可以轻松添加新的预览效果
- **配置灵活**：支持多种自定义配置

## 调试支持

### 日志输出
- 预览计算过程的详细日志
- 预览方块数量统计
- 淡出动画状态跟踪

### 开发工具
- 预览状态检查方法
- 手动控制预览显示/隐藏
- 透明度实时调节

## 总结

方块下落预览系统为游戏提供了重要的视觉反馈改进，让玩家能够更好地理解和预测游戏状态变化。该系统设计精良，性能优化，与现有系统完美集成，显著提升了游戏的用户体验和策略深度。
