# 🎮 关卡配置系统优化

## 📋 概述

实现了全新的关卡配置系统，支持阶段性递增、灵活的游戏参数配置和无限关卡生成。

## 🆕 新增功能

### 1. 阶段性配置系统

```javascript
// 每10关为一个阶段，自动递增难度
export const STAGE_CONFIG = {
  stage1: {  // 1-10关：新手期
    colorCount: 3,              // 颜色数量限制
    effectProbability: 0.05,    // 特殊效果概率
    speedFactor: 0.8,           // 下落速度因子
    allowedEffects: ['frozen'], // 允许的效果类型
    stageTheme: 'beginner'      // 阶段主题
  },
  stage2: {  // 11-20关：入门期
    colorCount: 4,
    effectProbability: 0.1,
    speedFactor: 1.0,
    allowedEffects: ['frozen', 'mine'],
    stageTheme: 'basic'
  }
  // ... 更多阶段
};
```

### 2. 动态关卡生成

```javascript
// 自动生成关卡配置
function generateLevelConfig(levelId, customConfig = {}) {
  const stageConfig = getStageConfig(levelId);
  const baseScore = 1000;
  
  return {
    id: levelId,
    name: `第${levelId}关`,
    colorCount: stageConfig.colorCount,
    effectProbability: stageConfig.effectProbability,
    speedFactor: stageConfig.speedFactor,
    targetScore: Math.floor(baseScore * levelId * stageConfig.targetMultiplier),
    // ... 更多配置
  };
}
```

### 3. 特殊关卡类型

```javascript
export const LEVEL_TYPES = {
  CLASSIC: 'classic',           // 经典模式
  TARGET_SCORE: 'target_score', // 目标分数
  TIME_ATTACK: 'time_attack',   // 时间挑战
  SURVIVAL: 'survival',         // 生存模式
  NO_ITEMS: 'no_items',        // 禁用道具
  CHAIN_COMBO: 'chain_combo'    // 连击挑战
};
```

### 4. Boss关卡和特殊挑战

```javascript
export const SPECIAL_LEVELS = {
  boss: {
    10: {  // 第10关Boss
      name: '第一章 Boss',
      type: LEVEL_TYPES.SURVIVAL,
      timeLimit: 180,
      effectProbability: 0.3,
      initialBlocks: [/* 复杂障碍 */],
      rewards: { coins: 1000, gems: 5 }
    }
  },
  challenge: {
    15: {  // 第15关特殊挑战
      name: '无道具挑战',
      type: LEVEL_TYPES.NO_ITEMS,
      itemsAllowed: false,
      specialRules: ['no_items']
    }
  }
};
```

## 🔧 核心配置参数

### 游戏参数
- **colorCount**: 可用颜色数量（3-7种）
- **effectProbability**: 特殊效果出现概率（0.0-1.0）
- **speedFactor**: 下落速度倍数（0.5-3.0）
- **allowedEffects**: 允许的方块效果列表
- **itemsAllowed**: 是否允许使用道具
- **timeLimit**: 时间限制（秒，0为无限制）

### 目标配置
- **targetScore**: 目标分数
- **starThresholds**: 三星评分阈值
- **specialRules**: 特殊规则数组

### 奖励配置
```javascript
rewards: {
  completion: {
    coins: 100,      // 完成奖励金币
    gems: 2,         // 完成奖励宝石
    fireball: 3,     // 火球道具奖励
    lightning: 2,    // 闪电道具奖励
    waterflow: 1     // 激流道具奖励
  },
  stars: {
    1: { coins: 50 },                    // 1星奖励
    2: { coins: 75, gems: 1 },          // 2星奖励
    3: { coins: 100, gems: 2 }          // 3星奖励
  }
}
```

## 📈 阶段递增规律

| 阶段 | 关卡范围 | 颜色数 | 效果概率 | 速度倍数 | 允许效果 |
|------|----------|--------|----------|----------|----------|
| 1    | 1-10     | 3      | 5%       | 0.8x     | 冰冻     |
| 2    | 11-20    | 4      | 10%      | 1.0x     | 冰冻+地雷 |
| 3    | 21-30    | 4      | 15%      | 1.2x     | 冰冻+地雷 |
| 4    | 31-40    | 5      | 20%      | 1.4x     | 冰冻+地雷 |
| 5    | 41-50    | 5      | 25%      | 1.6x     | 冰冻+地雷 |
| 6+   | 51+      | 6      | 30%      | 1.8x     | 冰冻+地雷 |

## 🎯 实现细节

### 1. Tetromino类修改
```javascript
_createBlocks(options = {}) {
  const { 
    colorCount = 7,  // 新增：颜色数量限制
    allowedEffects = ['frozen', 'mine']  // 新增：允许的效果
  } = options;
  
  // 根据颜色数量限制获取可用颜色
  const availableColorKeys = allColorKeys.slice(0, colorCount);
  
  // 只从允许的效果中选择
  const availableEffects = allowedEffects.filter(effectName => {
    return BLOCK_EFFECTS[effectName.toUpperCase()] !== undefined;
  });
}
```

### 2. 游戏控制器修改
```javascript
// 支持速度因子
const speedFactor = this.options.speedFactor || 1.0;
const fallSpeed = Math.max(1, Math.floor(baseFallSpeed / speedFactor));

// 传递关卡配置到Tetromino
this.currentTetromino = new Tetromino(randomShape, {
  colorDistribution: TETROMINO_COLOR_DISTRIBUTION,
  effectProbability: this.options.effectProbability,
  colorCount: this.options.colorCount,
  allowedEffects: this.options.allowedEffects
});
```

### 3. Main类加载关卡
```javascript
this.gameController = new GameController({
  level: levelId,
  colorCount: levelConfig.colorCount,
  effectProbability: levelConfig.effectProbability,
  speedFactor: levelConfig.speedFactor,
  allowedEffects: levelConfig.allowedEffects,
  levelType: levelConfig.type,
  itemsAllowed: levelConfig.itemsAllowed,
  maxItems: levelConfig.maxItems
});
```

## 🚀 新增API方法

```javascript
// 获取关卡阶段信息
const stageInfo = getStageInfo(15);
// {
//   stageNumber: 2,
//   stageName: "第2章",
//   levelInStage: 5,
//   isBossLevel: false,
//   isSpecialLevel: true
// }

// 获取下一阶段预览
const nextStage = getNextStagePreview(8);
// {
//   level: 11,
//   improvements: {
//     colorIncrease: true,
//     speedIncrease: true,
//     newEffects: ['mine']
//   }
// }

// 动态生成更多关卡
const levels = generateMoreLevels(100, 10);  // 生成100-109关
```

## 🎮 用户体验改进

1. **渐进式难度**：每10关递增一次难度参数
2. **视觉反馈**：关卡选择界面显示阶段信息
3. **成就感**：Boss关卡和特殊挑战增加游戏深度
4. **平衡性**：各参数按合理比例递增
5. **无限内容**：动态生成支持无限关卡

## 📝 使用示例

```javascript
// 获取关卡配置
const level15Config = getLevelConfig(15);
console.log(level15Config);
// {
//   id: 15,
//   name: "无道具挑战",
//   type: "no_items",
//   colorCount: 4,
//   effectProbability: 0.1,
//   speedFactor: 1.0,
//   itemsAllowed: false,
//   targetScore: 18000,
//   rewards: { ... }
// }

// 检查是否为特殊关卡
const stageInfo = getStageInfo(10);
if (stageInfo.isBossLevel) {
  console.log("这是Boss关卡！");
}
```

## 🔍 调试信息

新增的控制台日志：
- `生成方块，可用颜色数量: X, 颜色列表: [...]`
- `方块添加效果: frozen/mine`
- 关卡配置加载信息

这套优化系统提供了高度可配置的关卡体验，支持渐进式难度递增和丰富的游戏变化。 