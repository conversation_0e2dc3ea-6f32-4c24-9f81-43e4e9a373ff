# 游戏设计文档目录

## 📁 文档结构

### 🎮 游戏设计
- [`level-design/`](./level-design/) - 关卡设计相关文档
  - [`LEVEL_DESIGN_OVERVIEW.md`](./level-design/LEVEL_DESIGN_OVERVIEW.md) - 关卡设计总览
  - [`INITIAL_BLOCKS_DESIGN.md`](./level-design/INITIAL_BLOCKS_DESIGN.md) - 初始方块布局设计
  - [`NEW_LEVEL_DESIGN_SUMMARY.md`](./level-design/NEW_LEVEL_DESIGN_SUMMARY.md) - 新关卡设计总结
  - [`LEVEL_DATA_TABLE.md`](./level-design/LEVEL_DATA_TABLE.md) - 关卡数据表格

### 🎨 界面设计
- [`ui-design/`](./ui-design/) - 用户界面设计文档
  - [`STAGE_BASED_LEVEL_SELECTION.md`](./ui-design/STAGE_BASED_LEVEL_SELECTION.md) - 分阶段关卡选择界面

### 🎓 教学系统
- [`tutorial/`](./tutorial/) - 教学和引导系统文档
  - [`PROGRESSIVE_ELEMENT_INTRODUCTION.md`](./tutorial/PROGRESSIVE_ELEMENT_INTRODUCTION.md) - 渐进式元素引入系统

### 🔧 开发工具
- [`development/`](./development/) - 开发相关文档
  - [`DEBUG_MODE_GUIDE.md`](./development/DEBUG_MODE_GUIDE.md) - 调试模式使用指南

## 📖 快速导航

### 新手开发者
1. 先阅读 [关卡设计总览](./level-design/LEVEL_DESIGN_OVERVIEW.md)
2. 了解 [初始方块布局设计](./level-design/INITIAL_BLOCKS_DESIGN.md)
3. 查看 [调试模式指南](./development/DEBUG_MODE_GUIDE.md)

### 关卡设计师
1. [关卡设计总览](./level-design/LEVEL_DESIGN_OVERVIEW.md) - 整体设计理念
2. [关卡数据表格](./level-design/LEVEL_DATA_TABLE.md) - 具体数值参考
3. [新关卡设计总结](./level-design/NEW_LEVEL_DESIGN_SUMMARY.md) - 最新设计方案

### UI设计师
1. [分阶段关卡选择界面](./ui-design/STAGE_BASED_LEVEL_SELECTION.md) - 界面设计规范

### 教学设计师
1. [渐进式元素引入系统](./tutorial/PROGRESSIVE_ELEMENT_INTRODUCTION.md) - 教学流程设计

## 🔄 文档更新记录

### 最新更新
- **2024-01**: 创建文档目录结构
- **2024-01**: 整理关卡设计文档
- **2024-01**: 添加渐进式教学系统文档

### 版本历史
- **v1.0**: 初始文档结构
- **v1.1**: 添加九关布局模板系统
- **v1.2**: 完善分阶段界面设计
- **v1.3**: 新增渐进式元素引入系统

## 📝 文档贡献指南

### 文档命名规范
- 使用大写字母和下划线：`DOCUMENT_NAME.md`
- 文件夹使用小写字母和连字符：`folder-name/`
- 保持名称简洁且描述性强

### 文档结构规范
1. **标题**: 使用 `#` 开头
2. **目录**: 复杂文档需要目录导航
3. **代码块**: 使用 ````javascript` 标记代码
4. **表格**: 用于数据展示
5. **图标**: 使用 emoji 增强可读性

### 更新流程
1. 修改相关文档
2. 更新 `README.md` 中的更新记录
3. 检查文档链接是否正确
4. 确保格式统一

## 🎯 设计理念

本游戏遵循以下核心设计理念：

### 易学难精
- **新手友好**: 渐进式学习曲线
- **深度挑战**: 为高级玩家提供复杂策略
- **持续成长**: 明确的技能提升路径

### 文化融合
- **中文命名**: 武侠风格的阶段命名
- **本土化**: 符合中文用户习惯的设计
- **情感共鸣**: 富有激励性的描述文案

### 技术先进
- **模块化**: 清晰的代码结构
- **可扩展**: 支持无限内容扩展
- **高性能**: 优化的渲染和逻辑处理

## 🤝 团队协作

### 角色分工
- **游戏设计师**: 负责关卡设计和平衡性
- **UI设计师**: 负责界面设计和用户体验
- **程序员**: 负责技术实现和性能优化
- **教学设计师**: 负责新手引导和教学流程

### 沟通渠道
- **文档**: 通过 Markdown 文档记录设计决策
- **代码注释**: 在代码中详细说明实现逻辑
- **版本控制**: 使用 Git 跟踪变更历史

## 📞 联系方式

如有任何问题或建议，请通过以下方式联系：
- 创建 Issue 讨论设计问题
- 提交 Pull Request 贡献文档
- 在代码中添加详细注释

---

*最后更新: 2024年1月*
