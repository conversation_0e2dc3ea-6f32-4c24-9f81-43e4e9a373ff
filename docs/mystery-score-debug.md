# 神秘2分调试追踪

## 🎯 **问题描述**

用户第一次消除时，分数从2开始而不是0：
```
📊 连击分数: 20 × 1.2x = 24 (2 → 26)
```

这个初始的2分来源不明，需要追踪。

## 🔍 **调试日志追踪**

现在已经在关键位置添加了详细的分数追踪日志：

### **GameController创建过程**
```
🎮 [timestamp] 创建游戏控制器前 - global=X
🎮 [timestamp] GameController构造函数完成 - score=0
🎮 [timestamp] 游戏控制器创建后 - controller=0, global=X
```

### **游戏开始过程**
```
🎮 [timestamp] 开始游戏 - 重置前分数: controller=X, global=Y
🔄 [timestamp] 数据重置后 - global=0
🎯 [timestamp] 游戏控制器启动后 - controller=0, global=0
🔗 [timestamp] 分数同步后 - controller=0, global=0
🎮 [timestamp] GameController.start() 开始 - score=X
🎮 [timestamp] GameController.start() 重置分数后 - score=0
🎮 [timestamp] GameController.start() 完成 - score=0
```

### **第一次消除时**
```
📊 连击分数: 20 × 1.2x = 24 (? → 26)
```

## 🧪 **测试步骤**

1. **开始新游戏**
2. **观察控制台日志**
3. **记录所有分数变化的时间戳**
4. **进行第一次消除**
5. **分析分数从何时开始不是0**

## 🔍 **可能的分数来源**

### **已排除的来源**
- ✅ GameController构造函数：设置为0
- ✅ GameController.start()：重置为0
- ✅ GameGlobal.databus.reset()：重置为0

### **待检查的来源**
- ❓ **levelManager.setupInitialBlocks()** - 可能触发某些事件
- ❓ **垃圾生成器启动** - 可能有初始分数
- ❓ **连击系统初始化** - 可能有默认分数
- ❓ **事件监听器** - 可能在初始化时触发
- ❓ **道具管理器** - 可能有初始奖励
- ❓ **关卡配置** - 可能有起始分数设置

### **隐藏的分数修改**
- ❓ **异步事件** - 在start()后异步触发的分数修改
- ❓ **UI更新** - UI组件可能修改分数
- ❓ **定时器** - 某个定时器可能在后台增加分数

## 🔧 **调试策略**

### **1. 分数监控器**
```javascript
// 在控制台执行，监控分数变化
let lastControllerScore = 0;
let lastGlobalScore = 0;

const scoreMonitor = setInterval(() => {
  const controllerScore = main.gameController ? main.gameController.score : 0;
  const globalScore = GameGlobal.databus.score;
  
  if (controllerScore !== lastControllerScore) {
    console.log(`🔍 Controller分数变化: ${lastControllerScore} → ${controllerScore} (+${controllerScore - lastControllerScore})`);
    console.trace('分数变化调用栈');
    lastControllerScore = controllerScore;
  }
  
  if (globalScore !== lastGlobalScore) {
    console.log(`🔍 Global分数变化: ${lastGlobalScore} → ${globalScore} (+${globalScore - lastGlobalScore})`);
    lastGlobalScore = globalScore;
  }
}, 100);

// 停止监控
// clearInterval(scoreMonitor);
```

### **2. 分数设置拦截**
```javascript
// 拦截分数设置，查看调用栈
if (main.gameController) {
  let originalScore = main.gameController.score;
  Object.defineProperty(main.gameController, 'score', {
    get() {
      return originalScore;
    },
    set(value) {
      if (value !== originalScore) {
        console.log(`🔍 分数被设置: ${originalScore} → ${value}`);
        console.trace('分数设置调用栈');
        originalScore = value;
      }
    }
  });
}
```

### **3. 事件监听追踪**
```javascript
// 监听所有可能影响分数的事件
if (main.gameController) {
  const originalEmit = main.gameController.emit;
  main.gameController.emit = function(event, ...args) {
    if (event.includes('score') || event.includes('match') || event.includes('combo')) {
      console.log(`🔍 事件触发: ${event}`, args);
    }
    return originalEmit.call(this, event, ...args);
  };
}
```

## 🎯 **预期发现**

通过详细的日志追踪，我们应该能够发现：

1. **分数何时从0变成2**
2. **是哪个函数或事件导致的**
3. **调用栈信息**
4. **是否是异步操作**

## 📋 **调试检查清单**

### **游戏启动时**
- [ ] GameController构造函数完成时分数是否为0
- [ ] start()方法调用前分数是否为0
- [ ] start()方法调用后分数是否为0
- [ ] 所有初始化完成后分数是否为0

### **第一次消除前**
- [ ] 触摸事件处理前分数是否为0
- [ ] 方块锁定前分数是否为0
- [ ] 匹配检查前分数是否为0
- [ ] 消除计算前分数是否为0

### **分数变化时机**
- [ ] 记录第一次分数变化的确切时间
- [ ] 记录分数变化的调用栈
- [ ] 确认是否有多个分数修改源

## 🚨 **如果找到2分来源**

一旦找到2分的来源，需要：

1. **确认是否是合理的分数** - 比如初始奖励
2. **检查是否应该被重置** - 在start()方法中
3. **修复分数计算逻辑** - 确保一致性
4. **更新日志显示** - 正确显示分数变化

通过这些详细的调试日志，我们应该能够精确定位这个神秘的2分来源！
