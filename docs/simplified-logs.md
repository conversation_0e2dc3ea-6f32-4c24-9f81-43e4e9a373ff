# 简化的连击系统日志

## 🎯 **日志优化**

移除了疯狂刷屏的调试日志，现在只显示关键信息：

### **连击日志**
- **前3次连击**：每次都显示
- **每5次连击**：显示里程碑（5, 10, 15, 20...）
- **阶段升级**：显示阶段变化

**示例**：
```
🎯 连击: 1, 能量: 3/500, 倍数: 1.2x
🎯 连击: 2, 能量: 6/500, 倍数: 1.2x
🎯 连击: 3, 能量: 9/500, 倍数: 1.2x
🎯 连击阶段升级! 4连击 → 中级连击 (1.8x倍数)
🎯 连击: 5, 能量: 15/500, 倍数: 1.8x
🎯 连击: 10, 能量: 30/500, 倍数: 1.8x
```

### **分数日志**
- **只在有倍数效果时显示**（倍数 > 1.0x）
- **简化格式**：基础分数 × 倍数 = 最终分数

**示例**：
```
📊 连击分数: 30 × 1.2x = 36 (总分: 156)
🔗 自动连锁消除! 连击: 5, 分数: 60 × 1.8x = 108
📏 满行消除! 行数: 1, 连击: 7, 分数: 200 × 1.8x = 360
```

### **道具日志**
- **简化格式**：道具类型 + 消除方块数 + 连击数

**示例**：
```
🔥 道具连击! fireball: 消除5个方块, 连击: 3
🔥 道具连击! lightning: 消除8个方块, 连击: 4
```

## 🧪 **测试验证**

现在您可以清楚地看到：

### **连击倍数效果**
1. **第1次三消**：
   ```
   🎯 连击: 1, 能量: 3/500, 倍数: 1.2x
   📊 连击分数: 30 × 1.2x = 36 (总分: 36)
   ```

2. **第4次连击（阶段升级）**：
   ```
   🎯 连击阶段升级! 4连击 → 中级连击 (1.8x倍数)
   📊 连击分数: 30 × 1.8x = 54 (总分: XXX)
   ```

3. **第7次连击**：
   ```
   📊 连击分数: 30 × 1.8x = 54 (总分: XXX)
   ```

### **自动连锁消除**
```
🔗 自动连锁消除! 连击: 5, 分数: 60 × 1.8x = 108
```

### **道具 + 连锁反应**
```
🔥 道具连击! fireball: 消除5个方块, 连击: 3
🔗 自动连锁消除! 连击: 4, 分数: 60 × 1.8x = 108
🔗 自动连锁消除! 连击: 5, 分数: 60 × 1.8x = 108
```

## 🎯 **关键信息一目了然**

现在控制台不会被无用的调试信息淹没，您可以清楚地看到：

- ✅ **连击倍数是否生效** - 分数计算显示倍数
- ✅ **能量是否正确积累** - 连击日志显示能量变化
- ✅ **阶段升级时机** - 阶段升级有专门提示
- ✅ **自动连锁是否计入连击** - 连锁消除显示连击数增加
- ✅ **道具是否计入连击** - 道具使用显示连击数增加

## 🔍 **如果需要更详细的调试**

如果需要临时查看更详细的信息，可以在控制台执行：

```javascript
// 查看连击系统完整状态
console.log('连击系统状态:', main.gameController.comboSystem.getStatus());

// 手动触发连击测试
main.gameController.comboSystem.addCombo(3, []);

// 查看当前倍数
console.log('当前倍数:', main.gameController.comboSystem.getComboMultiplier());
```

现在的日志系统既保留了关键信息，又不会造成信息过载！
