# 方块图片渲染系统

## 🎯 实现目标

为蓝绿红色的方块添加图片渲染支持，使用指定的图片资源来替代纯色渲染，提升游戏的视觉效果。

## 🖼️ 图片资源配置

### 支持的方块颜色
- **红色方块**: `images/blocks/tileRed.png`
- **蓝色方块**: `images/blocks/tileBlue.png`
- **绿色方块**: `images/blocks/tileGreen.png`
- **黄色方块**: `images/blocks/tileYellow.png`
- **橙色方块**: `images/blocks/tileOrange.png`
- **灰色方块**: `images/blocks/tileGray.png`
- **黑色方块**: `images/blocks/tileBlack.png`

### 回退机制
- **所有颜色**: 都有对应的图片资源
- **图片加载失败**: 自动回退到颜色渲染
- **图片未完成加载**: 使用颜色渲染直到图片加载完成
- **环境不支持**: 在不支持图片的环境中使用颜色渲染

## 🔧 技术实现

### 图片资源映射
```javascript
// 方块图片资源映射
const BLOCK_IMAGES = {
  [BLOCK_COLORS.RED]: 'images/blocks/tileRed.png',
  [BLOCK_COLORS.BLUE]: 'images/blocks/tileBlue.png',
  [BLOCK_COLORS.GREEN]: 'images/blocks/tileGreen.png'
};

// 图片加载缓存
const imageCache = new Map();
```

### 图片预加载系统
```javascript
function loadBlockImage(color) {
  const imagePath = BLOCK_IMAGES[color];
  if (!imagePath) {
    return Promise.resolve(null);
  }
  
  // 检查缓存
  if (imageCache.has(color)) {
    return Promise.resolve(imageCache.get(color));
  }
  
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      imageCache.set(color, img);
      console.log(`方块图片加载成功: ${imagePath}`);
      resolve(img);
    };
    img.onerror = (error) => {
      console.warn(`方块图片加载失败: ${imagePath}`, error);
      resolve(null); // 返回null而不是reject，支持回退
    };
    img.src = imagePath;
  });
}

export function preloadBlockImages() {
  const loadPromises = Object.keys(BLOCK_IMAGES).map(color => loadBlockImage(color));
  return Promise.all(loadPromises).then(() => {
    console.log('所有方块图片预加载完成');
  });
}
```

### 渲染系统重构

#### 主渲染方法
```javascript
render(ctx, x, y, size, isFalling = false) {
  ctx.save();
  
  // 应用消除动画效果
  if (this.isDestroying) {
    const progress = this.destroyAnimationFrame / 10;
    const scale = 1 - progress;
    ctx.translate(x + size / 2, y + size / 2);
    ctx.scale(scale, scale);
    ctx.translate(-(x + size / 2), -(y + size / 2));
    ctx.globalAlpha = 1 - progress;
  }
  
  // 尝试使用图片渲染
  const blockImage = imageCache.get(this.color);
  if (blockImage && blockImage.complete) {
    this._renderWithImage(ctx, x, y, size, blockImage, isFalling);
  } else {
    // 回退到颜色渲染
    this._renderWithColor(ctx, x, y, size, isFalling);
  }
  
  // 渲染特效
  if (this.effect !== BLOCK_EFFECTS.NONE) {
    this._renderEffect(ctx, x, y, size);
  }
  
  ctx.restore();
}
```

#### 图片渲染方法
```javascript
_renderWithImage(ctx, x, y, size, image, isFalling) {
  // 冰冻方块特殊处理
  if (this.effect === BLOCK_EFFECTS.FROZEN) {
    ctx.globalAlpha = 0.7; // 降低透明度
    ctx.filter = 'hue-rotate(200deg) saturate(0.6) brightness(0.8)'; // 蓝色滤镜
  }
  
  // 绘制图片
  ctx.drawImage(image, x, y, size, size);
  
  // 重置滤镜
  if (this.effect === BLOCK_EFFECTS.FROZEN) {
    ctx.filter = 'none';
    ctx.globalAlpha = 1;
  }
  
  // 下落中的方块发光效果
  if (isFalling) {
    const glowGradient = ctx.createRadialGradient(
      x + size/2, y + size/2, 0,
      x + size/2, y + size/2, size * 0.8
    );
    glowGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
    glowGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    
    ctx.fillStyle = glowGradient;
    ctx.fillRect(x, y, size, size);
  }
  
  // 添加高光效果
  const highlightGradient = ctx.createLinearGradient(x, y, x + size, y + size);
  highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.2)');
  highlightGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)');
  highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
  
  ctx.fillStyle = highlightGradient;
  ctx.fillRect(x, y, size, size);
}
```

#### 颜色渲染方法（回退）
```javascript
_renderWithColor(ctx, x, y, size, isFalling) {
  // 获取颜色信息
  const colorInfo = COLOR_MAP[this.color];
  
  // 冰冻方块颜色调整
  let fillStyle = colorInfo.fill;
  let strokeStyle = colorInfo.stroke;
  
  if (this.effect === BLOCK_EFFECTS.FROZEN) {
    fillStyle = this._adjustColorForFrozen(colorInfo.fill);
    strokeStyle = this._adjustColorForFrozen(colorInfo.stroke);
  }
  
  // 绘制圆角方块
  ctx.fillStyle = fillStyle;
  ctx.strokeStyle = strokeStyle;
  ctx.lineWidth = isFalling ? 3 : 2;
  
  // ... 圆角路径绘制
  // ... 渐变和高光效果
}
```

### 游戏集成

#### 主游戏初始化
```javascript
// 在 main.js 构造函数中
constructor() {
  this.initDebugMode();
  
  // 预加载方块图片（异步，不阻塞初始化）
  this.initBlockImages();
  
  // 初始化其他管理器...
}

async initBlockImages() {
  try {
    console.log('开始预加载方块图片...');
    await preloadBlockImages();
    console.log('方块图片预加载完成');
  } catch (error) {
    console.warn('方块图片预加载失败，将使用颜色渲染', error);
  }
}
```

## 🎨 视觉效果特性

### 冰冻效果处理
- **滤镜应用**: 使用CSS滤镜实现冰冻效果
  - `hue-rotate(200deg)`: 色相旋转，增加蓝色调
  - `saturate(0.6)`: 降低饱和度
  - `brightness(0.8)`: 降低亮度
- **透明度**: 降低到0.7，营造被冰封的感觉
- **兼容性**: 与现有冰冻特效叠加显示

### 下落动画增强
- **发光效果**: 径向渐变白色光晕
- **视觉反馈**: 突出下落中的方块
- **性能优化**: 只在下落时应用额外效果

### 高光效果
- **线性渐变**: 从左上到右下的白色高光
- **自然质感**: 模拟光照效果
- **适度透明**: 不遮盖图片细节

## 📊 性能考虑

### 缓存机制
- **图片缓存**: 使用Map缓存已加载的图片
- **避免重复加载**: 检查缓存避免重复请求
- **内存管理**: 图片对象复用

### 异步加载
- **非阻塞**: 图片加载不阻塞游戏初始化
- **渐进增强**: 图片加载完成后自动切换到图片渲染
- **错误处理**: 加载失败时优雅回退

### 渲染优化
- **条件渲染**: 只有支持的颜色才尝试图片渲染
- **状态检查**: 检查图片完成状态避免渲染错误
- **上下文管理**: 正确的save/restore避免状态污染

## 🔧 扩展性设计

### 新颜色支持
```javascript
// 添加新颜色只需在映射中添加条目
const BLOCK_IMAGES = {
  [BLOCK_COLORS.RED]: 'images/blocks/tileRed.png',
  [BLOCK_COLORS.BLUE]: 'images/blocks/tileBlue.png',
  [BLOCK_COLORS.GREEN]: 'images/blocks/tileGreen.png',
  [BLOCK_COLORS.YELLOW]: 'images/blocks/tileYellow.png', // 新增
  [BLOCK_COLORS.PURPLE]: 'images/blocks/tilePurple.png'  // 新增
};
```

### 动态图片切换
- **主题系统**: 可以根据主题切换不同的图片集
- **季节变化**: 支持节日或季节性的图片替换
- **用户自定义**: 允许玩家选择喜欢的方块样式

### 特效图片
- **特殊状态**: 为不同特效状态提供专门的图片
- **动画帧**: 支持多帧动画图片
- **粒子效果**: 结合图片和粒子系统

## 🎮 用户体验

### 视觉提升
- **质感增强**: 图片提供更丰富的视觉细节
- **风格统一**: 保持游戏整体美术风格
- **识别性**: 更容易区分不同颜色的方块

### 兼容性保证
- **回退机制**: 确保在任何情况下都能正常显示
- **渐进增强**: 图片加载完成前使用颜色渲染
- **错误恢复**: 加载失败时自动切换到备用方案

这个图片渲染系统为游戏提供了更丰富的视觉体验，同时保持了良好的性能和兼容性。通过智能的回退机制，确保游戏在任何情况下都能正常运行。
