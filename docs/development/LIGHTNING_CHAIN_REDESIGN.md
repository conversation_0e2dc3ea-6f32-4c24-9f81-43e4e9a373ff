# 闪电链道具重新设计

## 🎯 设计目标

根据您的反馈，重新设计闪电链道具的效果机制，让它能够智能地寻找并连接同色方块，即使距离较远也能连接，连接数量由道具等级决定。

## 🔄 设计变更

### 原有设计问题
- **局限性**: 只能连接相邻或近距离的同色方块
- **随机性**: 路径选择过于随机，缺乏策略性
- **效率低**: 无法充分利用分散的同色方块

### 新设计优势
- **全局搜索**: 能找到整个游戏区域内的所有同色方块
- **智能连接**: 优先连接距离近的方块，但也能连接远距离方块
- **策略性强**: 玩家可以预期闪电链的效果范围
- **视觉效果**: 闪电路径更加自然和美观

## 🔧 技术实现

### 核心算法流程

#### 1. 全局同色方块搜索
```javascript
_findAllSameColorBlocks(targetColor) {
  const sameColorBlocks = [];
  
  // 遍历整个游戏区域
  for (let row = 0; row < this.grid.rows; row++) {
    for (let col = 0; col < this.grid.cols; col++) {
      const block = this.grid.getBlock(row, col);
      if (block) {
        // 考虑冰冻方块的原始颜色
        const blockRealColor = block.effect === BLOCK_EFFECTS.FROZEN ? 
          block.originalColor : block.color;
        
        if (blockRealColor === targetColor) {
          sameColorBlocks.push({ row, col, block });
        }
      }
    }
  }
  
  return sameColorBlocks;
}
```

#### 2. 智能路径构建
```javascript
_buildSmartLightningPath(startRow, startCol, allSameColorBlocks, maxChainCount) {
  // 按距离排序所有同色方块
  const sortedBlocks = allSameColorBlocks
    .filter(block => !(block.row === startRow && block.col === startCol))
    .map(block => ({
      ...block,
      distance: this._calculateDistance(startRow, startCol, block.row, block.col)
    }))
    .sort((a, b) => a.distance - b.distance);
  
  // 贪心算法选择目标方块
  const targetBlocks = [{ row: startRow, col: startCol }];
  const maxTargets = Math.min(maxChainCount, sortedBlocks.length + 1);
  
  for (let i = 0; i < sortedBlocks.length && targetBlocks.length < maxTargets; i++) {
    const block = sortedBlocks[i];
    
    // 添加随机性，前3个方块优先选择
    const randomFactor = Math.random();
    const shouldInclude = randomFactor > 0.2 || i < 3;
    
    if (shouldInclude) {
      targetBlocks.push(block);
    }
  }
  
  // 构建连接路径
  const chainPath = this._buildConnectionPath(targetBlocks);
  
  return { chainPath, connectedBlocks: targetBlocks };
}
```

#### 3. 最近邻路径连接
```javascript
_buildConnectionPath(targetBlocks) {
  const chainPath = [];
  let currentBlock = targetBlocks[0];
  chainPath.push({ row: currentBlock.row, col: currentBlock.col });
  
  const remainingBlocks = [...targetBlocks.slice(1)];
  
  // 使用最近邻算法连接剩余方块
  while (remainingBlocks.length > 0) {
    // 找到距离当前方块最近的下一个方块
    let nearestIndex = 0;
    let nearestDistance = this._calculateDistance(
      currentBlock.row, currentBlock.col,
      remainingBlocks[0].row, remainingBlocks[0].col
    );
    
    for (let i = 1; i < remainingBlocks.length; i++) {
      const distance = this._calculateDistance(
        currentBlock.row, currentBlock.col,
        remainingBlocks[i].row, remainingBlocks[i].col
      );
      
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestIndex = i;
      }
    }
    
    // 连接到最近的方块
    const nextBlock = remainingBlocks[nearestIndex];
    const pathSegment = this._generatePathSegment(
      currentBlock.row, currentBlock.col,
      nextBlock.row, nextBlock.col
    );
    
    chainPath.push(...pathSegment.slice(1));
    currentBlock = nextBlock;
    remainingBlocks.splice(nearestIndex, 1);
  }
  
  return chainPath;
}
```

#### 4. 自然路径段生成
```javascript
_generatePathSegment(startRow, startCol, endRow, endCol) {
  const path = [];
  path.push({ row: startRow, col: startCol });
  
  if (startRow === endRow && startCol === endCol) {
    return path;
  }
  
  const deltaRow = endRow - startRow;
  const deltaCol = endCol - startCol;
  const steps = Math.max(Math.abs(deltaRow), Math.abs(deltaCol));
  
  if (steps > 1) {
    // 创建曲线路径而不是直线
    for (let i = 1; i < steps; i++) {
      const progress = i / steps;
      
      // 线性插值
      const interpolatedRow = startRow + deltaRow * progress;
      const interpolatedCol = startCol + deltaCol * progress;
      
      // 添加随机偏移创建自然的闪电效果
      const randomOffset = 0.3;
      const offsetRow = interpolatedRow + (Math.random() - 0.5) * randomOffset;
      const offsetCol = interpolatedCol + (Math.random() - 0.5) * randomOffset;
      
      path.push({ 
        row: Math.round(offsetRow), 
        col: Math.round(offsetCol) 
      });
    }
  }
  
  path.push({ row: endRow, col: endCol });
  return path;
}
```

## 📊 道具等级效果

### 连锁数量配置
```javascript
// 在 item-config.js 中
chainCount: [3, 5, 8] // 1级3个，2级5个，3级8个方块
```

### 等级差异
- **1级**: 连接3个同色方块，适合清理小范围区域
- **2级**: 连接5个同色方块，中等范围清理
- **3级**: 连接8个同色方块，大范围清理，高级策略

## 🎮 游戏体验提升

### 策略性增强
1. **预期性**: 玩家可以预见闪电链会连接哪些方块
2. **规划性**: 可以根据方块分布制定使用策略
3. **效率性**: 能够更有效地清理分散的同色方块

### 视觉效果改进
1. **自然路径**: 闪电不再是直线，而是自然的曲线
2. **智能连接**: 路径看起来更合理和美观
3. **距离感**: 能够展现远距离连接的震撼效果

### 平衡性考虑
1. **随机因子**: 保留20%的随机性，避免过于可预测
2. **距离优先**: 优先连接近距离方块，符合物理直觉
3. **等级递进**: 连锁数量随等级合理增长

## 🔍 算法复杂度

### 时间复杂度
- **搜索阶段**: O(n) - 遍历所有方块
- **排序阶段**: O(k log k) - k为同色方块数量
- **路径构建**: O(m²) - m为选中的目标方块数量

### 空间复杂度
- **存储**: O(k) - 存储同色方块信息
- **路径**: O(p) - p为路径点数量

### 性能优化
- 早期终止：找到足够方块后停止搜索
- 缓存优化：可以缓存颜色分布信息
- 路径简化：移除不必要的中间点

## 🎯 设计优势总结

### 1. **智能化**
- 全局搜索同色方块
- 智能选择连接目标
- 优化的路径规划

### 2. **策略性**
- 可预期的效果范围
- 基于距离的优先级
- 等级差异明显

### 3. **视觉效果**
- 自然的闪电路径
- 远距离连接震撼
- 平滑的动画效果

### 4. **平衡性**
- 保留适度随机性
- 等级递进合理
- 符合游戏平衡

这个新的闪电链设计完美实现了您的需求：能够智能地寻找并连接同色方块，即使距离较远也能连接，连接数量由道具等级决定，同时保持了良好的视觉效果和游戏平衡性。
