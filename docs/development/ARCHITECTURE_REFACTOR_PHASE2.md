# 架构重构第二阶段：主循环分离

## 🎯 第二阶段目标

将臃肿的 `main.js` (2139行) 拆分为多个单一职责的模块，实现清晰的游戏循环管理和场景切换机制。

## ✅ 第二阶段完成工作

### 🏗️ 核心系统创建

#### 1. 游戏引擎 (`js/core/game-engine.js`)
```javascript
// 纯逻辑的游戏循环管理，平台无关
class GameEngine {
  - 游戏状态管理 (running, paused, stopped)
  - 主循环控制 (update/render分离)
  - 事件驱动架构 (engine:started, engine:paused等)
  - 模块化接口 (setGameModules)
  - 适配器集成 (使用平台适配器)
}
```

#### 2. 场景管理器 (`js/core/scene-manager.js`)
```javascript
// 负责游戏场景切换和状态管理
class SceneManager {
  - 场景类型定义 (SCENE_TYPES)
  - 场景栈支持 (pushScene/popScene)
  - 场景切换动画
  - 场景数据管理
  - 事件驱动通知
}
```

#### 3. 输入管理器 (`js/core/input-manager.js`)
```javascript
// 统一输入事件处理和分发
class InputManager {
  - 场景特定输入处理
  - 全局输入处理
  - 输入防抖机制
  - 事件缓冲系统
  - 统计和调试功能
}
```

#### 4. 平台入口 (`js/platforms/wechat-game.js`)
```javascript
// 微信小游戏平台特定实现
class WeChatGame {
  - 使用新的核心系统
  - 保持与原代码兼容
  - 逐步迁移原有功能
  - 场景驱动的游戏流程
}
```

### 📊 架构对比

#### 🔴 重构前 (`main.js`)
```
main.js (2139行)
├── 游戏循环逻辑           ❌ 混合在一起
├── 场景切换               ❌ 硬编码状态
├── 输入处理               ❌ 直接处理
├── 渲染逻辑               ❌ 与逻辑耦合
├── 事件处理               ❌ 回调地狱
└── 平台相关代码           ❌ 硬编码微信API
```

#### 🟢 重构后 (新架构)
```
js/
├── core/                  ✅ 纯逻辑，平台无关
│   ├── game-engine.js     ✅ 游戏循环管理
│   ├── scene-manager.js   ✅ 场景切换
│   └── input-manager.js   ✅ 输入事件分发
├── platforms/             ✅ 平台特定实现
│   └── wechat-game.js     ✅ 微信小游戏入口
├── adapters/              ✅ 平台抽象层
└── new-main.js           ✅ 新的启动入口
```

### 🎮 新的游戏流程

#### 1. 启动流程
```javascript
1. new-main.js 启动
2. 创建 WeChatGame 实例
3. 初始化适配器 (render, input, audio)
4. 创建核心系统 (engine, scene, input)
5. 初始化游戏模块 (保持兼容)
6. 启动游戏引擎
7. 切换到关卡选择场景
```

#### 2. 游戏循环
```javascript
GameEngine.mainLoop:
1. 计算 deltaTime
2. 如果未暂停 → _update(deltaTime)
3. _render() (包括暂停界面)
4. 请求下一帧
```

#### 3. 场景切换
```javascript
SceneManager.switchTo:
1. 发出 scene:leave 事件
2. 播放切换动画
3. 更新当前场景
4. 发出 scene:enter 事件
5. 发出 scene:changed 事件
```

#### 4. 输入处理
```javascript
InputManager:
1. 适配器 → 原始输入事件
2. 执行全局输入处理器
3. 执行当前场景特定处理器
4. 发出 input:processed 事件
```

## 🔧 技术特性

### ✨ 核心优势

#### 1. **事件驱动架构**
- 所有系统通过事件通信
- 松耦合，易于扩展
- 支持多个监听器

#### 2. **场景管理**
- 清晰的场景生命周期
- 支持场景栈（嵌套场景）
- 场景数据管理
- 切换动画支持

#### 3. **输入系统**
- 场景特定输入处理
- 全局输入处理
- 防抖机制
- 统计和调试

#### 4. **平台抽象**
- 使用适配器模式
- 核心逻辑平台无关
- 渐进式迁移

### 📈 代码质量提升

| 指标 | 重构前 | 重构后 | 提升 |
|------|--------|--------|------|
| 单个文件最大行数 | 2139行 | <400行 | 5.3倍 ⬇️ |
| 职责分离 | 1个万能类 | 4个专用类 | 明确分工 ✅ |
| 平台耦合 | 硬编码wx | 适配器抽象 | 解耦 ✅ |
| 可测试性 | 困难 | 容易 | 大幅提升 ✅ |
| 扩展性 | 修改核心 | 添加模块 | 安全扩展 ✅ |

### 🚀 新功能特性

#### 1. **调试和监控**
```javascript
// 全局调试函数
game.getGameState()     // 获取游戏状态
restartGame()          // 重启游戏
getGameInfo()          // 获取游戏信息

// 引擎状态监控
{
  isRunning: true,
  isPaused: false,
  frameCount: 1234,
  deltaTime: 16.67,
  fps: 60
}
```

#### 2. **场景系统**
```javascript
// 场景切换
sceneManager.switchTo(SCENE_TYPES.GAME_PLAYING, levelData)
sceneManager.pushScene(SCENE_TYPES.SETTINGS)
sceneManager.popScene()
sceneManager.goBack()

// 场景事件监听
sceneManager.on('scene:enter', handleSceneEnter)
sceneManager.on('scene:leave', handleSceneLeave)
```

#### 3. **输入系统**
```javascript
// 注册输入处理器
inputManager.registerSceneHandler('game_playing', 'move_left', handleMoveLeft)
inputManager.registerGlobalHandler('pause', handlePause)

// 输入统计
inputManager.getStats()
inputManager.getCurrentSceneHandlers()
```

## 🔄 兼容性策略

### 📁 文件共存
- `main.js` - 保留原文件
- `new-main.js` - 新架构入口
- 可以通过修改 `game.js` 切换使用

### 🔧 API兼容
- 保持所有原有游戏模块不变
- 渐进式迁移，不破坏现有功能
- 全局变量和函数保持一致

### 🎯 迁移策略
```javascript
// 阶段1: 并行运行
game.js → import './main.js'      // 使用原架构
game.js → import './new-main.js'  // 使用新架构

// 阶段2: 逐步迁移功能
WeChatGame._loadLevel()           // 先用原逻辑
WeChatGame.loadLevel()           // 迁移到新逻辑

// 阶段3: 替换原文件
new-main.js → main.js            // 完全替换
```

## 📝 使用指南

### 🚀 启动新架构
```javascript
// 修改 game.js
import './js/new-main.js';  // 使用新架构
// import './js/main.js';   // 注释掉旧架构
```

### 🎮 添加新场景
```javascript
// 1. 在 SCENE_TYPES 中添加场景类型
export const SCENE_TYPES = {
  // ... 现有场景
  NEW_SCENE: 'new_scene'
};

// 2. 在 WeChatGame 中添加场景处理
_onSceneEnter(sceneType, data) {
  switch (sceneType) {
    case SCENE_TYPES.NEW_SCENE:
      this._enterNewScene(data);
      break;
  }
}
```

### 🎯 添加输入处理
```javascript
// 场景特定输入
inputManager.registerSceneHandler(
  SCENE_TYPES.GAME_PLAYING,
  INPUT_EVENTS.MOVE_LEFT,
  () => gameController.moveLeft()
);

// 全局输入
inputManager.registerGlobalHandler(
  INPUT_EVENTS.PAUSE,
  () => sceneManager.pushScene(SCENE_TYPES.GAME_PAUSED)
);
```

## 🎯 下一阶段计划

### 🔧 第三阶段：GameController重构
1. **拆分GameController** (3210行)
   - 分离游戏状态管理
   - 分离分数系统
   - 分离匹配检测
   - 分离物理引擎

2. **创建专用模块**
   ```
   js/logic/
   ├── game-state.js       # 游戏状态管理
   ├── score-manager.js    # 分数系统
   ├── match-engine.js     # 匹配检测
   ├── physics-engine.js   # 物理系统
   └── effect-manager.js   # 特效处理
   ```

3. **渲染层分离**
   - 将渲染逻辑从 GameController 中分离
   - 创建专用的渲染器
   - 实现数据驱动渲染

### ⏱️ 时间规划
- **第3周**: 拆分GameController核心逻辑
- **第4周**: 创建专用管理器类
- **第5周**: 渲染层分离
- **第6周**: 测试和优化

## 🏆 重构收益评估

### 短期收益 (已实现)
- ✅ 代码结构更清晰
- ✅ 平台解耦完成  
- ✅ 调试功能增强
- ✅ 场景管理规范化

### 中期目标 (进行中)
- 🔄 GameController重构
- 🔄 Web平台支持准备
- 🔄 单元测试框架
- 🔄 性能监控系统

### 长期愿景
- 🎯 多平台发布
- 🎯 技术栈升级
- 🎯 团队协作效率
- 🎯 项目扩展性

---

**状态**: 第二阶段完成 ✅  
**下一步**: 开始第三阶段 - GameController重构

**测试命令**:
```javascript
// 在微信开发者工具控制台中
game.getGameState()
sceneManager.getState()
inputManager.getStats()
``` 