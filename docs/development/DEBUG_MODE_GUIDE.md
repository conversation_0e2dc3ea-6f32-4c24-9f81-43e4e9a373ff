# 调试模式使用指南

## 概述

游戏现在支持调试模式，可以将所有关卡的目标分数调整为原来的1/10，方便测试关卡完成功能。

## 功能特性

### 🎯 分数调整
- **调整倍数**: 所有关卡的目标分数变为原来的1/10
- **自动应用**: 包括动态生成的关卡和自定义配置的关卡
- **星级同步**: 星级阈值也会相应调整，保持游戏平衡

### 🔧 道具调试
- **无限使用**: 所有道具拥有无限使用次数，显示为∞次
- **无冷却时间**: 道具使用后立即可以再次使用
- **正常效果**: 道具的实际效果保持不变，仅跳过限制检查

### 🔧 控制方式

#### 1. 控制台命令
在微信开发者工具的控制台中输入以下命令：

```javascript
// 切换调试模式（开启/关闭）
toggleDebugMode()

// 手动设置调试模式
setDebugMode(true)   // 开启调试模式
setDebugMode(false)  // 关闭调试模式

// 查看当前调试模式状态
isDebugMode()
```

#### 2. 代码中设置
在 `js/main.js` 的 `initDebugMode()` 方法中：

```javascript
initDebugMode() {
  // 取消注释以下行来默认开启调试模式
  // setDebugMode(true);
  
  // ... 其他代码
}
```

### 📊 效果示例

#### 正常模式
- 第1关: 目标分数 500，星级要求 [300, 400, 500]
- 第2关: 目标分数 800，星级要求 [480, 640, 800]
- 第3关: 目标分数 1200，星级要求 [720, 960, 1200]

#### 调试模式 (1/10)
- 第1关: 目标分数 50，星级要求 [30, 40, 50]
- 第2关: 目标分数 80，星级要求 [48, 64, 80]
- 第3关: 目标分数 120，星级要求 [72, 96, 120]

### 🎮 界面提示

当调试模式开启时，游戏界面左上角会显示：
- 🐛 调试模式标识
- "分数要求: 原来的1/10" 提示
- 控制台切换提示

## 配置选项

### 基础配置
在 `js/config/game-config.js` 中可以调整：

```javascript
debug: {
  enabled: false,              // 是否开启调试模式
  scoreMultiplier: 0.1,        // 分数调整倍数 (1/10)
  fastLevelComplete: true,     // 快速关卡完成
  showDebugInfo: true,         // 显示调试信息
  unlockAllLevels: true,       // 解锁所有关卡 ✅
  infiniteItems: true,         // 无限道具 ✅
  noCooldown: true,            // 无冷却时间 ✅
  skipTutorial: false,         // 跳过教程 (预留)
  logLevel: 'info'             // 调试日志级别
}
```

### 扩展功能
已实现的调试功能：
- `unlockAllLevels`: 解锁所有关卡 ✅
- `infiniteItems`: 无限道具使用 ✅
- `noCooldown`: 道具无冷却时间 ✅
- `skipTutorial`: 跳过教程关卡 (预留)
- `logLevel`: 控制调试日志详细程度 (预留)

## 使用场景

### 🧪 测试关卡完成
1. 开启调试模式: `setDebugMode(true)`
2. 进入任意关卡
3. 轻松达到目标分数（只需原来的1/10）
4. 测试关卡完成界面和下一关功能

### 🔍 验证得分系统
1. 开启调试模式
2. 测试各种消除方式的得分：
   - 同色消除
   - 整行消除
   - 道具消除
   - 地雷爆炸
3. 验证分数累计是否正确

### 🎯 快速关卡测试
1. 开启调试模式
2. 快速通过多个关卡
3. 测试关卡解锁机制
4. 验证星级评定系统

## 注意事项

### ⚠️ 重要提醒
- 调试模式仅用于开发和测试
- 发布版本前请确保调试模式已关闭
- 调试模式下的游戏体验与正常模式不同

### 🔄 状态持久化
- 调试模式状态不会自动保存
- 每次重启游戏需要重新设置
- 可以在代码中设置默认状态

### 📝 日志输出
调试模式会在控制台输出详细信息：
```
调试模式: 开启
分数调试: 500 → 50 (×0.1)
分数调试: 800 → 80 (×0.1)
```

## 故障排除

### 问题1: 控制台命令无效
**解决方案**: 确保游戏已完全加载，函数已暴露到全局

### 问题2: 分数没有调整
**解决方案**: 检查关卡配置是否正确导入了调试模式函数

### 问题3: 界面没有显示调试信息
**解决方案**: 确认 `showDebugInfo` 配置为 `true`

## 开发者备注

调试模式的实现涉及以下文件：
- `js/config/game-config.js`: 配置管理
- `js/level/level-config.js`: 关卡分数调整
- `js/main.js`: 初始化和全局暴露
- `js/runtime/gameinfo.js`: 界面显示

这个系统设计为可扩展的，未来可以轻松添加更多调试功能。
