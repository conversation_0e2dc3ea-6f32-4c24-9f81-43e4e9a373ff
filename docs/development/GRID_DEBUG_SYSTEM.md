# 网格状态可视化调试系统

## 🎯 系统概述

为了精确调试地震术和其他道具的效果，我们实现了一套完整的网格状态可视化调试系统。该系统可以：

- 📊 实时显示网格中所有方块的详细状态
- 🔄 对比操作前后的状态变化
- 🌍 专门追踪地震术的完整执行流程
- 📋 快速检查当前棋盘状况
- 🔍 检测悬空方块和异常状态

## 🛠️ 核心功能

### 1. 基础状态查看 - `debugGridState()`

```javascript
// 查看当前网格的详细状态
debugGridState('我的调试标签', true);
```

**输出内容：**
- 📊 网格摘要（总方块数、空位数、填充率）
- 🧱 每个方块的详细信息（位置、类型、颜色、特效状态）
- 📏 行分析（每行的方块数量和分布）
- 📐 列分析（每列的高度和方块分布）
- 📋 可视化网格（字符形式的棋盘图）
- 🎬 当前动画状态

**示例输出：**
```
🔍 ===== 我的调试标签 ===== [2024-01-15T10:30:45.123Z]
📊 网格摘要: {totalBlocks: 45, totalEmpty: 155, fillPercentage: "22.5%"}

📋 可视化网格:
    0  1  2  3  4  5  6  7  8  9
   -- -- -- -- -- -- -- -- -- --
 0| ·  ·  ·  ·  ·  ·  ·  ·  ·  · 
 1| ·  ·  ·  ·  ·  ·  ·  ·  ·  · 
...
18| R  B  G  ·  ·  ·  R  G  B  R 
19| B  R  G  B  R  G  B  R  G  B 

🧱 方块详情: [详细的方块信息数组]
```

### 2. 状态对比 - `compareGridStates()`

```javascript
// 记录操作前状态
const beforeState = debugGridState('操作前', true);

// 执行操作...
// ...

// 记录操作后状态
const afterState = debugGridState('操作后', true);

// 对比状态变化
compareGridStates(beforeState, afterState, '我的操作');
```

**输出内容：**
- 📊 统计变化（方块数量、空位数量变化）
- 💨 消失的方块列表
- ✨ 新出现的方块列表
- 🔄 移动的方块轨迹

### 3. 地震术专用调试 - `debugEarthquake()`

```javascript
// 专门调试地震术效果
debugEarthquake(1); // 参数是地震术等级
```

**执行流程：**
1. 记录地震术触发前的完整状态
2. 触发地震术
3. 追踪重力应用过程
4. 记录下落动画完成后的状态
5. 输出完整的状态对比报告

### 4. 快速状态检查 - `quickGridCheck()`

```javascript
// 快速检查当前状态，不输出详细信息
quickGridCheck();
```

**输出内容：**
- 📊 基本统计信息
- ⚠️ 悬空方块警告
- 🎬 动画状态概览

## 🎮 使用方法

### 在游戏中使用

1. **打开浏览器开发者工具**（F12）
2. **进入游戏并开始关卡**
3. **在控制台中调用调试函数**

### 常用调试场景

#### 场景1：检查地震术是否正常工作
```javascript
// 方法1：使用专用调试功能
debugEarthquake(1);

// 方法2：手动跟踪
const before = debugGridState('地震前', true);
// 手动触发地震术（通过界面点击）
setTimeout(() => {
  const after = debugGridState('地震后', true);
  compareGridStates(before, after, '地震术效果');
}, 1000);
```

#### 场景2：检查是否有悬空方块
```javascript
// 快速检查
quickGridCheck();

// 详细检查
const state = debugGridState('悬空检查', true);
const floatingBlocks = state.blocks.filter(block => block.isFloating);
console.log('悬空方块:', floatingBlocks);
```

#### 场景3：监控游戏过程
```javascript
// 启用自动状态记录（每5秒记录一次）
debugMode.setAutoStateLogging(5000);

// 停止自动记录
debugMode.stopAutoStateLogging();
```

#### 场景4：分析特定操作的影响
```javascript
// 记录操作前状态
const beforeMove = debugGridState('移动前', false);

// 执行操作（如移动方块、使用道具等）
// ...

// 记录操作后状态并对比
const afterMove = debugGridState('移动后', false);
compareGridStates(beforeMove, afterMove, '方块移动');
```

## 📋 输出信息说明

### 网格可视化符号
- `·` - 空位
- `X` - 普通方块（或显示类型/颜色首字母）
- `💥` - 正在消除的方块
- `❄️` - 冰冻方块
- `💣` - 地雷方块

### 方块状态字段
```javascript
{
  position: { row: 18, col: 3 },      // 方块位置
  type: "normal",                      // 方块类型
  color: "red",                        // 方块颜色
  effect: "none",                      // 特殊效果
  isDestroying: false,                 // 是否正在消除
  isFrozen: false,                     // 是否冰冻
  destroyProgress: 0,                  // 消除进度
  id: "block_18_3",                    // 方块ID
  screenPos: { x: 120, y: 540 },      // 屏幕坐标
  isFloating: false,                   // 是否悬空
  canFallDistance: 0                   // 可下降距离
}
```

## 🔧 高级功能

### 调试模式设置
```javascript
// 启用详细日志
debugMode.enableVerboseLogging();

// 设置自动状态记录
debugMode.setAutoStateLogging(3000); // 每3秒记录一次

// 禁用详细日志
debugMode.disableVerboseLogging();

// 停止自动记录
debugMode.stopAutoStateLogging();
```

### 批量状态分析
```javascript
// 连续记录多个状态点
const states = [];
states.push(debugGridState('状态1', false));
// 执行操作...
states.push(debugGridState('状态2', false));
// 执行操作...
states.push(debugGridState('状态3', false));

// 批量对比
for (let i = 1; i < states.length; i++) {
  compareGridStates(states[i-1], states[i], `状态${i} -> 状态${i+1}`);
}
```

## 🐛 故障排除

### 常见问题

1. **调试函数不存在**
   - 确保游戏已完全加载
   - 检查控制台是否有初始化错误

2. **状态信息不准确**
   - 确保在游戏稳定状态下调用
   - 避免在动画进行中记录状态

3. **对比结果混乱**
   - 确保两个状态记录的时间间隔合适
   - 检查是否有其他操作干扰

### 调试技巧

1. **使用明确的标签**
   ```javascript
   debugGridState('地震术-等级1-第3次测试', true);
   ```

2. **记录关键时机**
   - 道具触发前
   - 重力应用后
   - 动画完成后
   - 消除检查后

3. **关注关键指标**
   - 方块总数变化
   - 悬空方块数量
   - 动画状态

## 📝 最佳实践

1. **调试地震术问题的标准流程：**
   ```javascript
   // 1. 检查初始状态
   quickGridCheck();
   
   // 2. 使用专用调试功能
   debugEarthquake(1);
   
   // 3. 检查最终状态
   setTimeout(() => {
     quickGridCheck();
   }, 2000);
   ```

2. **长期监控游戏状态：**
   ```javascript
   // 启用自动监控
   debugMode.setAutoStateLogging(10000);
   
   // 游戏结束后停止
   // debugMode.stopAutoStateLogging();
   ```

3. **性能考虑：**
   - 详细模式(`detailed: true`)会输出大量信息，仅在需要时使用
   - 自动记录功能会影响性能，测试完成后及时关闭
   - 避免在快速操作过程中频繁调用调试函数

这套调试系统将帮助您精确分析地震术和其他游戏功能的行为，快速定位问题并验证修复效果。 