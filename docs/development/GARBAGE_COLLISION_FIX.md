# 垃圾行生成与活动方块冲突问题解决方案

## 问题描述

在垃圾行生成时，整个方块堆都会向上推动，此时往往容易和正在下落中的活动方块位置冲突而撞上，导致游戏体验不佳。

## 问题原因

1. 垃圾生成器的 `pushExistingBlocksUp()` 方法只处理网格中已放置的方块
2. 没有考虑正在下落中的 `currentTetromino` 活动方块
3. 垃圾行向上推移时可能与活动方块占据相同位置

## 解决方案

### 1. 垃圾生成器改进

在 `js/game/garbage-generator.js` 中：
- 在 `generateGarbageRows()` 方法返回结果中添加 `needsActiveTetrominoAdjustment` 标记
- 标记需要游戏控制器处理活动方块位置调整

### 2. 游戏控制器改进

在 `js/game/controller.js` 中：
- 新增 `_adjustActiveTetrominoAfterGarbage()` 方法
- 在 `_handleGarbageEvent()` 中检测并调用位置调整

### 3. 位置调整策略

**策略优先级：**

1. **向上移动策略**：
   - 将活动方块向上移动垃圾行数量的行数
   - 检查新位置是否有效

2. **水平偏移策略**：
   - 如果向上移动无效，尝试水平偏移：[0, -1, 1, -2, 2]
   - 结合向上移动进行位置调整

3. **强制锁定策略**：
   - 如果无法调整，检查原始位置是否仍有效
   - 原始位置有效则立即锁定当前方块
   - 原始位置无效则游戏结束

### 4. 用户体验优化

- **重置锁定计时器**：调整后给玩家反应时间
- **事件通知**：发出 `tetromino:adjusted` 事件用于UI反馈
- **控制台日志**：详细记录调整过程便于调试

## 实现细节

### 核心调整逻辑

```javascript
_adjustActiveTetrominoAfterGarbage(rowCount) {
  if (!this.currentTetromino) return;
  
  const originalPosition = { ...this.currentTetromino.position };
  
  // 尝试向上移动
  this.currentTetromino.position.row -= rowCount;
  
  if (!this.currentTetromino.isValidPosition(this.grid)) {
    // 尝试水平偏移策略
    const horizontalOffsets = [0, -1, 1, -2, 2];
    let adjustmentSuccess = false;
    
    for (const offset of horizontalOffsets) {
      this.currentTetromino.position.col = originalPosition.col + offset;
      this.currentTetromino.position.row = originalPosition.row - rowCount;
      
      if (this.currentTetromino.isValidPosition(this.grid)) {
        adjustmentSuccess = true;
        break;
      }
    }
    
    if (!adjustmentSuccess) {
      // 强制锁定或游戏结束
      this.currentTetromino.position = originalPosition;
      if (this.currentTetromino.isValidPosition(this.grid)) {
        this.lockTimer = 30; // 立即锁定
      } else {
        this.gameOver();
      }
    }
  }
  
  this.lockTimer = 0; // 重置锁定计时器
}
```

## 测试验证

### 测试场景

1. **正常情况**：活动方块在高位置，垃圾行生成后向上移动无冲突
2. **位置冲突**：活动方块在中低位置，垃圾行推移后发生位置重叠
3. **边界情况**：活动方块靠近边界，需要水平偏移调整
4. **极限情况**：无法调整时的强制锁定或游戏结束

### 验证步骤

1. 在游戏进行时等待垃圾行生成预警
2. 将活动方块移动到可能冲突的位置
3. 观察垃圾行生成时活动方块的位置调整
4. 验证调整后的游戏继续流畅进行

## 优势

1. **自动处理**：无需玩家干预，自动解决位置冲突
2. **智能调整**：多种策略确保最佳的位置调整
3. **用户友好**：调整后重置计时器给玩家反应时间
4. **稳定性**：极端情况下的安全处理避免游戏崩溃

## 后续优化

1. **视觉反馈**：考虑添加活动方块位置调整的动画效果
2. **预警机制**：在垃圾行即将生成时给予更明显的视觉提示
3. **难度平衡**：根据游戏难度调整位置调整的宽松程度
4. **统计分析**：记录位置调整频率用于游戏平衡优化

## 相关文件

- `js/game/garbage-generator.js` - 垃圾生成器
- `js/game/controller.js` - 游戏控制器
- `js/ui/garbage-warning.js` - 垃圾预警UI

## 更新日期

2024年12月19日 - 初版实现完成 