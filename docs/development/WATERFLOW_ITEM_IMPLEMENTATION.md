# 激流道具功能实装

## 🌊 道具概述

激流道具是一个强力的清场道具，能够直接消除底部的若干行方块，类似于"满行消除"的效果，但不需要行满。

## ⚡ 道具效果

### 基本功能
- **效果**：直接消除底部的若干行方块
- **范围**：根据道具等级决定消除的行数
- **触发条件**：底部有方块即可使用
- **下落效果**：消除后上方方块会正确下落填补空隙

### 等级效果
| 等级 | 消除行数 | 动画效果 | 冷却时间 |
|------|----------|----------|----------|
| 1级  | 1行      | 3波浪    | 7秒      |
| 2级  | 2行      | 4波浪    | 7秒      |
| 3级  | 3行      | 5波浪    | 7秒      |

## 🎮 使用配置

### 道具参数
```javascript
// 道具等级配置
itemLevels: {
  [ITEM_TYPES.WATERFLOW]: 1  // 初始等级1
}

// 使用次数配置
itemUses: {
  [ITEM_TYPES.WATERFLOW]: 15  // 初始15次
}

// 冷却时间配置
maxCooldowns: {
  [ITEM_TYPES.WATERFLOW]: 420  // 7秒冷却
}

// 效果配置
itemEffects: {
  [ITEM_TYPES.WATERFLOW]: {
    rowCount: [1, 2, 3]  // 等级1-3的消除行数
  }
}
```

### 关卡初始配置
```javascript
// 在main.js中的关卡初始化
this.itemManager.resetItemUses(ITEM_TYPES.WATERFLOW, 5);  // 每关卡5次使用机会
```

## 🔧 技术实现

### 核心方法

#### 1. `_handleWaterflow(row, col, level)`
- 激流道具的入口处理方法
- 调用实际的激流逻辑
- 处理冷却和使用次数管理

#### 2. `_useWaterflow(level)`
- 激流道具的核心逻辑
- 检查底部是否有方块
- 确定要消除的行数
- 创建动画效果
- 触发方块清除

#### 3. `_clearBottomRows(targetRows)`
- 清除指定的底部行
- 启动方块消除动画
- 标记受影响的方块

#### 4. `_processWaterflowEffect(clearedRows)`
- 处理激流效果后的下落
- 移除被消除的方块
- 触发多行下落逻辑
- 恢复游戏状态

### 实现流程

```mermaid
graph TD
    A[使用激流道具] --> B[检查底部是否有方块]
    B --> C{有方块?}
    C -->|否| D[使用失败]
    C -->|是| E[确定消除行数]
    E --> F[创建激流动画]
    F --> G[清除底部行方块]
    G --> H[启动消除动画]
    H --> I[延迟200ms]
    I --> J[移除方块]
    J --> K[触发多行下落]
    K --> L[恢复游戏状态]
    L --> M[应用冷却]
```

### 特殊处理逻辑

1. **独立的处理流程**：
   - 激流道具不使用通用的道具处理流程
   - 有自己的冷却和使用次数管理
   - 直接在处理方法中应用效果

2. **与多行消除的集成**：
   ```javascript
   // 使用游戏控制器的多行消除下落逻辑
   const gameController = this._getDependency('getGameController');
   if (gameController && typeof gameController._handleMultipleRowClear === 'function') {
     const hasFallen = gameController._handleMultipleRowClear(clearedRows);
   }
   ```

3. **动画效果**：
   - 水波动画随等级增强
   - 波浪数量：3 + level
   - 动画持续400ms
   - 支持粒子效果（2级以上）

## 🎨 视觉效果

### 动画特效
- **水波效果**：从左到右的波浪动画
- **颜色渐变**：蓝色系渐变，等级越高颜色越深
- **粒子效果**：2级以上道具有额外的粒子特效
- **光晕边缘**：波浪边缘的光晕效果

### 音效
- **音效类型**：'shoot'（射击音效）
- **播放时机**：激流动画开始时

## 🎯 使用策略

### 最佳使用时机
1. **底部堆积严重**：当底部有大量方块堆积时
2. **即将游戏结束**：当方块快要到顶时的紧急清场
3. **连击准备**：清除底部为上方方块创造下落空间

### 等级选择
- **1级**：适合轻微清理底部
- **2级**：适合中等程度的清场
- **3级**：适合紧急情况的大范围清场

## 📝 代码修改记录

### 修改文件
1. **`js/item/item-manager.js`**
   - 新增 `_handleWaterflow()` 方法
   - 修改 `_useWaterflow()` 方法实现
   - 新增 `_clearBottomRows()` 方法
   - 新增 `_processWaterflowEffect()` 方法
   - 修改道具处理映射
   - 添加激流道具特殊处理逻辑

2. **`js/main.js`**
   - 增加激流道具初始使用次数到5次

### 配置调整
- 初始使用次数：10次 → 15次
- 关卡使用次数：1次 → 5次
- 保持7秒冷却时间不变

## 🧪 测试场景

### 基础功能测试
1. **单行消除**：1级激流消除底部1行
2. **多行消除**：2级/3级激流消除底部2-3行
3. **空底部测试**：底部无方块时使用失败
4. **下落测试**：消除后上方方块正确下落

### 边界情况测试
1. **网格边界**：消除行数不超过网格总行数
2. **冷却测试**：冷却期间无法使用
3. **使用次数**：用完后无法继续使用
4. **动画冲突**：与其他道具动画的兼容性

## 🎉 实装完成

激流道具现在已经完全实装，具备以下特性：

- ✅ 直接消除底部若干行方块
- ✅ 等级系统（1-3级，消除1-3行）
- ✅ 完整的动画效果（水波、粒子、光晕）
- ✅ 正确的方块下落逻辑
- ✅ 独立的冷却和使用次数管理
- ✅ 与现有游戏系统的完美集成
- ✅ 音效支持
- ✅ 错误处理和边界检查

激流道具为玩家提供了强力的清场能力，是应对紧急情况的重要工具！🌊

---

*实装完成时间: 2024年12月*  
*实装类型: 新道具功能开发* 