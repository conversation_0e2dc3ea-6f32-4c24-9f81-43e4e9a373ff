# 参数配置建议指南

## 🎯 核心参数配置

### 特殊效果平衡参数

#### 效果出现概率 (effectProbability)
```javascript
// 建议值范围：0.02 - 0.25
阶段1: 0.02  // 新手期，极少特效
阶段2: 0.05  // 基础期，少量特效
阶段3: 0.08  // 进阶期，适量特效
阶段4: 0.12  // 技能期，中等特效
阶段5: 0.15  // 挑战期，较多特效
阶段6: 0.20  // 专家期，大量特效
阶段7: 0.25  // 大师期，最多特效

// 调整建议：
// - 新手玩家：降低0.01-0.02
// - 困难玩家：降低0.02-0.03
// - 专家玩家：增加0.02-0.05
```

#### 正负面效果比例 (effectBalance)
```javascript
// 推荐配置
阶段1: { positive: 0.8, negative: 0.2 }   // 新手友好
阶段2: { positive: 0.7, negative: 0.3 }   // 逐步增加挑战
阶段3: { positive: 0.6, negative: 0.4 }   // 平衡过渡
阶段4: { positive: 0.55, negative: 0.45 } // 轻微挑战
阶段5: { positive: 0.5, negative: 0.5 }   // 完全平衡
阶段6: { positive: 0.45, negative: 0.55 } // 增加难度
阶段7: { positive: 0.4, negative: 0.6 }   // 高难度挑战

// 动态调整规则：
// 连续失败0次: 无调整
// 连续失败1次: positive +0.1, negative -0.1
// 连续失败2次: positive +0.2, negative -0.2
// 连续失败3次: positive +0.4, negative -0.4
// 连续失败4次: positive +0.6, negative -0.4
// 连续失败5次: positive +1.0, negative -0.8
```

#### 每个俄罗斯方块的最大特效数 (maxEffectsPerTetromino)
```javascript
阶段1-3: 1个特效  // 简单期
阶段4:   1个特效  // 技能期
阶段5-6: 2个特效  // 挑战期
阶段7:   3个特效  // 大师期

// 注意：多特效会显著增加复杂度，需要谨慎调整
```

### 道具系统参数

#### 道具初始数量
```javascript
// 推荐初始库存
火球术: 5个   // 基础道具，数量较多
闪电链: 3个   // 中级道具，数量中等
激流:   2个   // 高级道具，数量较少

// 新手期奖励倍数
itemBonusMultiplier: 2.0  // 新手期道具奖励翻倍
```

#### 道具冷却时间 (帧数，60帧=1秒)
```javascript
// 基础冷却时间
火球术: 180帧 (3秒)   // 常用道具，冷却较短
闪电链: 300帧 (5秒)   // 中级道具，中等冷却
激流:   420帧 (7秒)   // 高级道具，冷却较长

// 玩家类型调整倍数
困难玩家: 0.7倍 (冷却减少30%)
休闲玩家: 0.85倍
核心玩家: 1.0倍 (标准)
硬核玩家: 1.15倍
专家玩家: 1.3倍 (冷却增加30%)
```

#### 道具升级成本
```javascript
// 技能点成本 (累进式)
火球术升级: [10, 20, 35, 50]
闪电链升级: [15, 25, 40, 60]
激流升级:   [20, 35, 50, 75]

// 金币成本 (指数增长)
火球术升级: [500, 1000, 2000, 3500]
闪电链升级: [800, 1500, 2500, 4000]
激流升级:   [1200, 2000, 3000, 5000]
```

### 难度曲线参数

#### 颜色数量 (colorCount)
```javascript
// 建议范围：3-6种颜色
阶段1-2: 3种颜色  // 新手和基础期
阶段3-4: 4种颜色  // 进阶和技能期
阶段5-6: 5种颜色  // 挑战和专家期
阶段7:   6种颜色  // 大师期

// 调整原则：
// - 颜色越多，匹配难度越高
// - 新手期保持3种颜色至少30关
// - 每次增加不超过1种颜色
```

#### 速度因子 (speedFactor)
```javascript
// 建议范围：0.6-1.8
阶段1: 0.6   // 新手期，很慢
阶段2: 0.75  // 基础期，较慢
阶段3: 0.9   // 进阶期，接近标准
阶段4: 1.0   // 技能期，标准速度
阶段5: 1.15  // 挑战期，较快
阶段6: 1.3   // 专家期，快速
阶段7: 1.5   // 大师期，很快

// 微调建议：
// - 困难玩家：-0.1到-0.2
// - 专家玩家：+0.1到+0.3
```

#### 目标分数倍数 (targetMultiplier)
```javascript
// 建议范围：0.8-2.0
阶段1: 0.8   // 新手期，降低要求
阶段2: 1.0   // 基础期，标准要求
阶段3: 1.1   // 进阶期，略微提高
阶段4: 1.25  // 技能期，中等要求
阶段5: 1.4   // 挑战期，较高要求
阶段6: 1.6   // 专家期，高要求
阶段7: 1.8   // 大师期，很高要求

// 基础分数计算：baseScore * levelId * targetMultiplier
// 建议baseScore = 100-200
```

### 辅助功能参数

#### 辅助等级配置
```javascript
// 各等级功能开启情况
high: {
  hints: true,           // 提示开启
  pauseOnDanger: true,   // 危险暂停
  ghostPiece: true,      // 幽灵方块
  highlightMatches: true,// 匹配高亮
  hintCooldown: 180      // 3秒提示间隔
}

medium: {
  hints: true,
  pauseOnDanger: false,
  ghostPiece: true,
  highlightMatches: true,
  hintCooldown: 300      // 5秒提示间隔
}

low: {
  hints: true,
  pauseOnDanger: false,
  ghostPiece: false,
  highlightMatches: false,
  hintCooldown: 600      // 10秒提示间隔
}

minimal: {
  hints: false,
  pauseOnDanger: false,
  ghostPiece: false,
  highlightMatches: false
}

none: {
  // 所有辅助功能关闭
}
```

#### 危险检测参数
```javascript
dangerThreshold: 5      // 顶部5行算危险区域
autoPauseDelay: 1800    // 30秒无操作自动暂停
extendedLockDelay: 60   // 1秒扩展锁定延迟
maxUndoSteps: 3         // 最多3次撤销
```

### 经济系统参数

#### 初始资源
```javascript
playerData: {
  level: 1,
  experience: 0,
  coins: 1000,          // 初始金币
  gems: 10,             // 初始宝石
  totalPlayTime: 0,
  gamesPlayed: 0,
  highestScore: 0,
  totalScore: 0
}
```

#### 奖励配置
```javascript
// 关卡完成奖励
completion: {
  coins: levelId * 50,                    // 金币奖励
  fireball: Math.floor(levelId / 5) + 1,  // 火球术奖励
  lightning: Math.floor(levelId / 7) + 1, // 闪电链奖励
  waterflow: Math.floor(levelId / 10) + 1 // 激流奖励
}

// 星级奖励
stars: {
  1: { coins: levelId * 20 },
  2: { coins: levelId * 30, gems: 1 },
  3: { coins: levelId * 50, gems: 2 }
}

// 升级奖励
levelUp: {
  coins: newLevel * 100,
  gems: Math.floor(newLevel / 5) + 1,
  skillPoints: newLevel * 2,
  fireball: 3
}
```

#### 广告奖励配置
```javascript
adRewards: {
  fireball: {
    amount: 5,
    cooldown: 1800    // 30分钟冷却
  },
  lightning: {
    amount: 3,
    cooldown: 2400    // 40分钟冷却
  },
  waterflow: {
    amount: 2,
    cooldown: 3600    // 60分钟冷却
  }
}
```

### 平衡检测参数

#### 玩家类型识别阈值
```javascript
// 困难玩家识别
struggling: {
  completionRate: < 0.3,     // 完成率低于30%
  retryFrequency: > 3,       // 重试次数超过3次
  progressionSpeed: < 0.5    // 进度速度低于0.5
}

// 专家玩家识别
expert: {
  completionRate: > 0.8,     // 完成率超过80%
  retryFrequency: < 1,       // 重试次数少于1次
  progressionSpeed: > 1.5,   // 进度速度超过1.5
  itemUsageRate: < 0.3       // 道具使用率低于30%
}
```

#### 挑战性保持阈值
```javascript
challengeThresholds: {
  minDifficulty: 0.3,    // 最低难度保持30%
  maxAssistance: 0.7,    // 最大辅助不超过70%
  balancePoint: 0.5      // 理想平衡点50%
}
```

## 🔧 调试和测试建议

### A/B测试参数
```javascript
// 可以进行A/B测试的关键参数
testParameters: [
  'effectProbability',      // 特效概率
  'effectBalance',          // 正负面比例
  'itemBonusMultiplier',   // 道具奖励倍数
  'targetMultiplier',      // 目标分数倍数
  'speedFactor',           // 速度因子
  'assistanceLevel'        // 辅助等级
]
```

### 监控指标
```javascript
// 需要监控的关键指标
monitoringMetrics: [
  'levelCompletionRate',   // 关卡完成率
  'averageRetryCount',     // 平均重试次数
  'itemUsageFrequency',    // 道具使用频率
  'sessionDuration',       // 游戏时长
  'playerRetention',       // 玩家留存
  'progressionSpeed'       // 进度速度
]
```

### 调整建议
1. **渐进式调整**：每次只调整一个参数
2. **数据驱动**：基于实际游戏数据进行调整
3. **玩家反馈**：结合玩家反馈进行优化
4. **版本控制**：记录每次参数调整的版本
5. **回滚机制**：准备参数回滚方案

通过合理配置这些参数，可以实现游戏的精确平衡，为不同类型的玩家提供最佳的游戏体验。
