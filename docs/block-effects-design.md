# 方块特效系统完整设计方案

## 📋 当前实现状态

### ✅ 已实现特效
1. **冰冻❄️** - 需要两次匹配才能消除
2. **地雷💣** - 爆炸消除3×3区域

### 🔄 待实现特效
3. **护盾🛡️** - 免疫一次道具攻击
4. **磁铁🧲** - 吸引相同颜色方块
5. **彩虹🌈** - 万能匹配颜色
6. **病毒🦠** - 传播到相邻方块
7. **水晶💎** - 提供额外分数奖励
8. **锚点⚓** - 阻止方块下落

## 🎯 详细特效设计方案

### 3. 护盾🛡️ (Shield) - 负面效果

**触发条件**: 道具攻击护盾方块时
**效果机制**:
- 第一次道具攻击：消耗护盾，方块变为普通方块
- 第二次道具攻击：正常消除方块
- 普通匹配消除：直接消除（绕过护盾）

**视觉效果**:
- 方块周围显示蓝色护盾光环
- 受到道具攻击时护盾破碎动画
- 护盾破碎后显示裂纹效果

**实现要点**:
```javascript
// 在道具攻击时检查护盾
if (block.effect === BLOCK_EFFECTS.SHIELD) {
  block.setEffect(BLOCK_EFFECTS.NONE); // 消耗护盾
  // 播放护盾破碎动画和音效
  return false; // 阻止方块被消除
}
```

### 4. 磁铁🧲 (Magnet) - 正面效果

**触发条件**: 磁铁方块被消除时
**效果机制**:
- 吸引网格中所有相同颜色的方块到磁铁位置
- 被吸引的方块会产生连锁消除
- 吸引范围：整个游戏区域

**视觉效果**:
- 磁铁方块周围显示磁力场动画
- 相同颜色方块被吸引时的移动轨迹
- 磁力线效果连接磁铁和目标方块

**实现要点**:
```javascript
// 磁铁效果实现
_handleMagnetEffect(magnetBlock) {
  const targetColor = magnetBlock.color;
  const magnetPos = { row: magnetBlock.row, col: magnetBlock.col };
  const attractedBlocks = [];
  
  // 找到所有相同颜色的方块
  for (let row = 0; row < this.grid.rows; row++) {
    for (let col = 0; col < this.grid.cols; col++) {
      const block = this.grid.getBlock(row, col);
      if (block && block.color === targetColor && block !== magnetBlock) {
        attractedBlocks.push(block);
      }
    }
  }
  
  // 创建吸引动画和连锁消除
  this._createMagnetAttraction(magnetPos, attractedBlocks);
}
```

### 5. 彩虹🌈 (Rainbow) - 正面效果

**触发条件**: 彩虹方块参与匹配时
**效果机制**:
- 可以与任意颜色的方块匹配
- 匹配时变为目标颜色并触发消除
- 优先级：在匹配检测中作为万能方块

**视觉效果**:
- 方块显示彩虹渐变色
- 匹配时变色动画
- 彩虹粒子效果

**实现要点**:
```javascript
// 在匹配检测中处理彩虹方块
isColorMatch(block1, block2) {
  if (block1.effect === BLOCK_EFFECTS.RAINBOW || 
      block2.effect === BLOCK_EFFECTS.RAINBOW) {
    return true; // 彩虹方块与任意颜色匹配
  }
  return block1.color === block2.color;
}
```

### 6. 病毒🦠 (Virus) - 负面效果

**触发条件**: 每回合结束时或相邻方块被消除时
**效果机制**:
- 每3回合向相邻方块传播一次
- 被感染的方块变为病毒方块
- 病毒方块需要道具才能清除（普通匹配无效）

**视觉效果**:
- 绿色脉动效果
- 传播时的感染动画
- 被感染方块的变色过程

**实现要点**:
```javascript
// 病毒传播逻辑
_handleVirusSpread() {
  const virusBlocks = this._findVirusBlocks();
  
  virusBlocks.forEach(virus => {
    virus.virusAge = (virus.virusAge || 0) + 1;
    
    if (virus.virusAge >= 3) {
      virus.virusAge = 0;
      this._spreadVirusToAdjacent(virus);
    }
  });
}
```

### 7. 水晶💎 (Crystal) - 正面效果

**触发条件**: 水晶方块被消除时
**效果机制**:
- 提供2-5倍的分数奖励
- 消除时产生额外的分数粒子
- 水晶等级影响奖励倍数

**视觉效果**:
- 闪亮的水晶材质
- 消除时的光芒爆发
- 分数粒子飞向分数显示区域

**实现要点**:
```javascript
// 水晶分数奖励
_handleCrystalEffect(crystalBlock) {
  const baseScore = this._calculateBlockScore(crystalBlock);
  const multiplier = 2 + Math.floor(Math.random() * 4); // 2-5倍
  const bonusScore = baseScore * (multiplier - 1);
  
  this._addScore(bonusScore);
  this._createScoreParticles(crystalBlock, bonusScore);
}
```

### 8. 锚点⚓ (Anchor) - 负面效果

**触发条件**: 方块下落时遇到锚点
**效果机制**:
- 阻止上方所有方块下落
- 锚点本身不能被普通匹配消除
- 需要特定道具（如火球）才能破坏

**视觉效果**:
- 重锚链条效果
- 阻挡下落时的碰撞动画
- 锚点周围的固定光环

**实现要点**:
```javascript
// 在下落检测中处理锚点
_checkFallBlocked(row, col) {
  // 检查下方是否有锚点
  for (let checkRow = row + 1; checkRow < this.grid.rows; checkRow++) {
    const block = this.grid.getBlock(checkRow, col);
    if (block && block.effect === BLOCK_EFFECTS.ANCHOR) {
      return true; // 被锚点阻挡
    }
  }
  return false;
}
```

## 🎮 特效平衡性设计

### 正面效果 (帮助玩家)
- **地雷💣**: 消除3×3区域 - 清理效率高
- **磁铁🧲**: 吸引同色方块 - 创造连击机会
- **彩虹🌈**: 万能匹配 - 解决困难局面
- **水晶💎**: 额外分数 - 提升评分

### 负面效果 (增加挑战)
- **冰冻❄️**: 需要两次消除 - 增加操作成本
- **护盾🛡️**: 免疫道具攻击 - 限制道具效果
- **病毒🦠**: 传播感染 - 持续威胁
- **锚点⚓**: 阻止下落 - 改变布局

## 📊 特效出现概率建议

### 按关卡阶段分布
| 阶段 | 冰冻❄️ | 地雷💣 | 护盾🛡️ | 磁铁🧲 | 彩虹🌈 | 病毒🦠 | 水晶💎 | 锚点⚓ |
|------|--------|--------|--------|--------|--------|--------|--------|--------|
| 1-18 | 100% | - | - | - | - | - | - | - |
| 19-27 | 60% | 40% | - | - | - | - | - | - |
| 28-36 | 40% | 30% | 30% | - | - | - | - | - |
| 37-45 | 30% | 25% | 20% | 25% | - | - | - | - |
| 46-54 | 20% | 20% | 15% | 20% | 15% | 10% | - | - |
| 55+ | 15% | 15% | 12% | 15% | 12% | 12% | 12% | 7% |

### 平衡性原则
1. **渐进引入**: 每个阶段最多引入1-2种新特效
2. **正负平衡**: 正面效果总概率 ≈ 负面效果总概率
3. **稀有度控制**: 强力特效概率较低
4. **玩家适应**: 给玩家充分时间学习新特效

## 🔧 实现优先级

### 第一阶段 (高优先级)
1. **护盾🛡️** - 相对简单，主要是道具攻击判断
2. **彩虹🌈** - 修改匹配逻辑，影响面较小

### 第二阶段 (中优先级)
3. **水晶💎** - 主要是分数计算和视觉效果
4. **磁铁🧲** - 需要新的吸引动画系统

### 第三阶段 (低优先级)
5. **病毒🦠** - 需要回合制传播机制
6. **锚点⚓** - 需要修改下落物理系统

## 🎯 用户体验考虑

### 学习曲线
- 每种特效都有明确的视觉标识
- 提供教程关卡介绍新特效
- 游戏内提示系统解释特效机制

### 策略深度
- 特效组合产生不同策略选择
- 道具与特效的交互增加深度
- 正负面特效的平衡创造紧张感

### 视觉反馈
- 每种特效都有独特的动画效果
- 特效触发时有明确的视听反馈
- 特效状态在UI中清晰显示

这个设计方案确保了特效系统的完整性和平衡性，为游戏提供了丰富的策略深度和视觉体验。
