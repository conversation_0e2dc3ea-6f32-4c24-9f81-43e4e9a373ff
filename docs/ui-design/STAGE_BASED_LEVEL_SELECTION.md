# 分阶段关卡选择界面设计

## 🎯 设计概述

根据您的建议，我们重新设计了关卡选择界面，采用分阶段展示的方式，并为每个阶段配置了更贴合游戏内容的中文名称。

## 🏆 七大阶段命名

### 阶段命名理念
- **武侠风格**: 采用武侠小说中常见的修炼境界描述
- **渐进式**: 从初学者到大师的完整成长路径
- **朗朗上口**: 易于记忆和理解的四字成语
- **激励性**: 每个名称都体现了玩家的进步和成就

### 具体阶段命名

#### 第1阶段：初出茅庐 (1-9关)
- **含义**: 刚刚开始学习，初步掌握基础
- **特点**: 新手友好，高辅助，道具奖励翻倍
- **描述**: "初学乍练，掌握基础"
- **颜色**: 3种，特效概率2%，速度0.6倍

#### 第2阶段：渐入佳境 (10-18关)
- **含义**: 技艺逐渐熟练，进入良好状态
- **特点**: 保持简单，稳步提升
- **描述**: "技艺渐熟，稳步提升"
- **颜色**: 3种，特效概率5%，速度0.75倍

#### 第3阶段：小试牛刀 (19-27关)
- **含义**: 初步展示实力，尝试更大挑战
- **特点**: 引入地雷，增加策略性
- **描述**: "初显身手，迎接挑战"
- **颜色**: 4种，特效概率8%，速度0.9倍

#### 第4阶段：游刃有余 (28-36关)
- **含义**: 技法娴熟，应对各种情况都很轻松
- **特点**: 标准难度，技能巩固
- **描述**: "技法娴熟，应对自如"
- **颜色**: 4种，特效概率12%，速度1.0倍

#### 第5阶段：炉火纯青 (37-45关)
- **含义**: 技艺精湛，达到很高水平
- **特点**: 高复杂度，无辅助
- **描述**: "技艺精湛，挥洒自如"
- **颜色**: 5种，特效概率15%，速度1.15倍

#### 第6阶段：登峰造极 (46-54关)
- **含义**: 达到技艺的顶峰
- **特点**: 专家级挑战，负面特效占优
- **描述**: "技压群雄，独步天下"
- **颜色**: 5种，特效概率20%，速度1.3倍

#### 第7阶段：出神入化 (55+关)
- **含义**: 超越常人，达到神奇境界
- **特点**: 终极挑战，无限关卡
- **描述**: "超凡入圣，无人能及"
- **颜色**: 6种，特效概率25%，速度1.5倍

## 🎨 界面设计特点

### 阶段标题区域
- **渐变背景**: 深灰色渐变，突出阶段重要性
- **阶段名称**: 24px白色字体，醒目显示
- **阶段描述**: 14px灰色字体，补充说明
- **进度显示**: 右侧显示星级进度 "X/Y ⭐"
- **关卡范围**: 显示关卡数量范围

### 关卡按钮设计
- **尺寸**: 80×80像素，适合触摸操作
- **布局**: 每行3个，居中对齐
- **颜色编码**:
  - 灰色 (#333333): 未完成关卡
  - 绿色 (#2E7D32): 已完成关卡
  - 深红色 (#8B0000): Boss关卡
- **信息显示**:
  - 关卡编号 (20px白色)
  - 关卡名称 (10px灰色，超长截断)
  - 星级显示 (底部3个圆点)
  - Boss标识 (顶部黄色"BOSS"文字)

### 视觉层次
1. **标题**: 36px居中，最高优先级
2. **阶段标题**: 24px左对齐，次高优先级
3. **关卡按钮**: 20px居中，主要内容
4. **辅助信息**: 10-14px，补充说明

## 🔧 技术实现

### 数据结构
```javascript
// 阶段数据结构
{
  stageNumber: 1,
  stageName: '初出茅庐',
  stageDescription: '初学乍练，掌握基础',
  stageRange: { start: 1, end: 9 },
  levels: [
    {
      id: 1,
      name: '空场开局',
      stars: 3,
      stageInfo: { isBossLevel: false }
    }
    // ... 更多关卡
  ],
  totalStars: 15,
  maxStars: 27
}
```

### 布局计算
```javascript
_calculateStageLayout() {
  let currentY = 120; // 从标题下方开始
  
  this.stageData.forEach((stage, stageIndex) => {
    // 阶段标题区域 (60px高度)
    this.btnAreas.stages.push({
      stageNumber: stage.stageNumber,
      startX: 0, startY: currentY,
      endX: SCREEN_WIDTH, endY: currentY + 60
    });
    
    currentY += 70; // 标题 + 间距
    
    // 关卡按钮布局 (每行3个)
    const levelsPerRow = 3;
    const buttonSize = 80;
    const margin = 20;
    
    stage.levels.forEach((level, index) => {
      const row = Math.floor(index / levelsPerRow);
      const col = index % levelsPerRow;
      // ... 计算位置
    });
    
    currentY += levelRows * (buttonSize + margin) + 30;
  });
}
```

### 渲染流程
1. **背景渲染**: 深色背景 (#1a1a1a)
2. **标题渲染**: 居中显示"关卡选择"
3. **阶段循环渲染**:
   - 阶段标题区域 (渐变背景)
   - 阶段信息 (名称、描述、进度)
   - 关卡按钮 (按行排列)
4. **交互区域**: 计算触摸区域

## 🎮 用户体验提升

### 视觉识别
- **阶段区分**: 每个阶段有独立的标题区域
- **进度可视**: 星级进度一目了然
- **状态明确**: 不同颜色表示不同状态
- **Boss突出**: 特殊标识突出重要关卡

### 信息层次
- **主要信息**: 阶段名称和关卡编号最突出
- **次要信息**: 描述和进度适中显示
- **辅助信息**: 关卡名称和范围较小显示

### 操作便利
- **触摸友好**: 80px按钮适合手指操作
- **布局合理**: 每行3个避免过于拥挤
- **滚动支持**: 支持垂直滚动查看更多阶段

## 📊 设计优势

### 1. **结构清晰**
- 分阶段展示避免信息过载
- 层次分明的视觉设计
- 逻辑清晰的信息组织

### 2. **文化贴合**
- 武侠风格的阶段命名
- 符合中文用户习惯
- 富有激励性的描述

### 3. **功能完善**
- 完整的进度显示
- 明确的状态标识
- 便捷的操作体验

### 4. **可扩展性**
- 支持无限阶段扩展
- 灵活的布局系统
- 易于维护的代码结构

## 🔮 未来优化

### 视觉效果
- 阶段解锁动画
- 关卡完成特效
- 星级获得动画

### 交互优化
- 长按查看关卡详情
- 滑动切换阶段
- 快速跳转功能

### 个性化
- 自定义阶段主题
- 个人成就展示
- 社交分享功能

这个新的分阶段关卡选择界面不仅提升了用户体验，还通过富有文化内涵的命名增强了游戏的代入感和激励性。每个阶段都有明确的目标和特色，让玩家在游戏过程中感受到明显的进步和成就感。
