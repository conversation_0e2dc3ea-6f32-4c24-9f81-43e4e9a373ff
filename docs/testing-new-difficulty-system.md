# 新难度系统测试指南

## 🎯 测试目标

验证新的基于垃圾生成的难度机制是否正常工作，确保：
1. 垃圾生成器正常运行
2. 预警系统正确显示
3. 下落速度保持稳定
4. 游戏平衡性良好

## 🔧 修复的问题

### 1. 导入路径错误
- **问题**: `js/game/garbage-generator.js` 中导入 `../core/constants.js` 不存在
- **修复**: 改为从 `./block.js` 导入 `BLOCK_COLORS` 和 `BLOCK_EFFECTS`

### 2. 关卡配置缺失
- **问题**: 关卡配置中没有启用垃圾生成
- **修复**: 在 `generateLevelConfig` 中添加垃圾生成配置

## 🎮 测试步骤

### 1. 基础功能测试

#### 启动游戏
1. 打开微信开发者工具
2. 加载项目
3. 启动游戏

#### 测试关卡1-3（无垃圾生成）
1. 进入关卡1-3
2. 确认没有垃圾生成预警
3. 验证下落速度稳定

#### 测试关卡4+（有垃圾生成）
1. 进入关卡4或更高
2. 等待垃圾生成预警出现
3. 观察预警UI显示
4. 等待垃圾方块生成

### 2. 垃圾生成系统测试

#### 预警系统
- **预期**: 生成前5秒显示预警UI
- **检查项**:
  - [ ] 预警框正确显示
  - [ ] 倒计时准确
  - [ ] 进度条正常
  - [ ] 紧急状态变红

#### 垃圾生成
- **预期**: 预警结束后生成垃圾方块
- **检查项**:
  - [ ] 方块从底部生成
  - [ ] 现有方块向上推移
  - [ ] 生成密度合理
  - [ ] 特效方块正常

### 3. 难度进展测试

#### 关卡4-10
- **垃圾间隔**: 约40秒 → 34秒
- **密度**: 40% → 52%
- **检查**: 难度递增平滑

#### 关卡11-20
- **垃圾间隔**: 34秒 → 28秒
- **密度**: 52% → 72%
- **检查**: 压力适中增加

#### 关卡21+
- **垃圾间隔**: 继续减少
- **密度**: 继续增加
- **检查**: 高级挑战

### 4. 下落速度测试

#### 速度对比
- **原来**: 关卡10时约15帧，关卡20时约5帧
- **现在**: 关卡10时约28帧，关卡20时约26帧
- **检查**: 速度保持相对稳定

#### 策略时间
- **预期**: 玩家有充分时间思考
- **检查**: 不会因速度过快而手忙脚乱

### 5. 游戏平衡测试

#### 道具重要性
- **预期**: 道具成为应对垃圾的重要工具
- **检查**: 
  - [ ] 火球术清理垃圾有效
  - [ ] 闪电链解冻冰冻方块
  - [ ] 激流清理整行

#### 策略深度
- **预期**: 需要规划和预判
- **检查**:
  - [ ] 空间管理重要
  - [ ] 预警时准备策略
  - [ ] 道具使用时机关键

## 🐛 常见问题排查

### 1. 垃圾生成器不工作
```javascript
// 检查控制台是否有错误
console.log('垃圾生成器状态:', gameController.garbageGenerator.isActive);
console.log('当前间隔:', gameController.garbageGenerator.currentInterval);
```

### 2. 预警UI不显示
```javascript
// 检查预警组件是否初始化
console.log('预警组件:', main.garbageWarning);
console.log('预警可见性:', main.garbageWarning.isVisible);
```

### 3. 导入错误
- 检查所有文件的导入路径
- 确认常量定义位置正确
- 验证模块导出正常

## 📊 性能监控

### 1. 帧率检查
- 垃圾生成不应影响游戏流畅度
- 预警动画应该平滑

### 2. 内存使用
- 垃圾方块应正确回收
- 预警组件不应泄漏

### 3. 事件处理
- 垃圾生成事件正确触发
- UI更新及时响应

## ✅ 验收标准

### 必须通过的测试
1. **基础功能**
   - [ ] 游戏正常启动
   - [ ] 关卡1-3无垃圾生成
   - [ ] 关卡4+有垃圾生成

2. **垃圾生成系统**
   - [ ] 预警正确显示
   - [ ] 垃圾按时生成
   - [ ] 方块正确推移

3. **难度平衡**
   - [ ] 下落速度稳定
   - [ ] 难度递增合理
   - [ ] 道具价值提升

4. **用户体验**
   - [ ] 操作响应及时
   - [ ] 视觉反馈清晰
   - [ ] 学习曲线平滑

### 可选优化项
- [ ] 音效反馈
- [ ] 动画效果
- [ ] 个性化配置

## 🎯 下一步计划

1. **数据收集**: 收集玩家游戏数据
2. **平衡调整**: 根据数据调整参数
3. **功能扩展**: 添加更多难度选项
4. **UI优化**: 改进预警界面设计

## 📝 测试记录

### 测试日期: ___________
### 测试人员: ___________

#### 基础功能测试
- [ ] 游戏启动正常
- [ ] 关卡加载正常
- [ ] 垃圾生成正常

#### 问题记录
1. ________________________________
2. ________________________________
3. ________________________________

#### 改进建议
1. ________________________________
2. ________________________________
3. ________________________________

---

**注意**: 由于这是微信小游戏项目，需要在微信开发者工具中测试，无法使用浏览器直接验证。请确保在正确的环境中进行测试。
