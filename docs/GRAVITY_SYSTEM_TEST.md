# 重力系统测试指南

## 测试目标

验证重力系统的三个核心功能模块是否正常工作：

1. **个体方块重力** - 消除后单个方块的垂直下落
2. **整行平移重力** - 整行消除后的行级下移  
3. **全局悬空检测** - 三点支撑的智能悬空处理

## 测试方法

### 1. 控制台测试

在游戏运行时，打开开发者控制台，执行以下测试代码：

#### 测试个体方块重力

```javascript
// 获取网格对象
const grid = window.gameController?.grid || window.controller?.grid;

if (grid) {
  console.log('=== 测试个体方块重力 ===');
  
  // 模拟消除一些方块后的重力
  const removedPositions = [
    { row: 15, col: 3 },
    { row: 16, col: 4 },
    { row: 17, col: 5 }
  ];
  
  const result = grid.applyGravity(null, null, removedPositions);
  console.log('个体重力测试结果:', result);
} else {
  console.error('无法找到网格对象');
}
```

#### 测试整行消除重力

```javascript
// 测试整行消除
if (grid) {
  console.log('=== 测试整行消除重力 ===');
  
  // 模拟整行被消除
  const fullRowRemoved = [];
  for (let col = 0; col < grid.cols; col++) {
    fullRowRemoved.push({ row: 18, col: col });
  }
  
  const result = grid.applyGravity(null, null, fullRowRemoved);
  console.log('整行消除重力测试结果:', result);
}
```

#### 测试全局悬空检测

```javascript
// 测试悬空检测
if (grid) {
  console.log('=== 测试全局悬空检测 ===');
  
  // 获取当前悬空方块
  const floatingBlocks = grid.getFloatingBlocks();
  console.log('当前悬空方块数量:', floatingBlocks.length);
  
  // 执行悬空处理
  const result = grid.detectAndHandleFloatingBlocks();
  console.log('悬空处理结果:', result);
}
```

#### 测试智能重力检查

```javascript
// 测试智能重力
if (grid) {
  console.log('=== 测试智能重力检查 ===');
  
  const result = grid.performSmartGravity();
  console.log('智能重力检查结果:', result);
}
```

### 2. 游戏内测试

#### 测试场景1：三消匹配后重力

1. 进入游戏
2. 放置方块形成三消匹配
3. 观察消除后上方方块是否正确下落
4. 检查下落动画是否流畅

#### 测试场景2：整行消除后重力

1. 填满一整行（除了冰冻方块）
2. 触发整行消除
3. 观察上方所有行是否整体下移
4. 检查行间距是否正确

#### 测试场景3：特殊道具重力

1. 使用地雷💣道具
2. 观察爆炸范围内方块消除后的重力效果
3. 测试火球🔥、闪电链⚡等道具的重力触发

#### 测试场景4：悬空方块检测

1. 创建悬空方块（通过消除支撑方块）
2. 等待悬空检测触发
3. 观察悬空方块是否正确下落

### 3. 调试功能测试

#### 网格状态调试

```javascript
// 查看网格状态
if (grid) {
  grid.debugGridState('当前网格状态', true);
}
```

#### 系统健康检查

```javascript
// 检查系统健康状态
if (grid) {
  const health = grid.healthCheck();
  console.log('系统健康状态:', health);
}
```

#### 获取统计信息

```javascript
// 获取网格统计
if (grid) {
  const stats = grid.getGridStats();
  console.log('网格统计信息:', stats);
}
```

## 预期结果

### 正常工作的标志

1. **控制台日志**：
   - 看到 `🌊 GravitySystem.applyGravity` 日志
   - 看到 `✅ 整行消除处理完成` 或 `✅ 部分消除重力处理完成` 日志
   - 看到具体的方块移动日志

2. **视觉效果**：
   - 方块下落动画流畅
   - 整行下移效果正确
   - 悬空方块能正确下落

3. **游戏逻辑**：
   - 消除后连锁反应正常
   - combo计数正确
   - 分数计算准确

### 异常情况处理

1. **无重力效果**：
   - 检查控制台是否有错误日志
   - 确认网格对象是否正确初始化
   - 验证重力系统是否正确集成

2. **动画异常**：
   - 检查动画系统是否正确设置
   - 确认方块位置更新是否正确

3. **性能问题**：
   - 监控重力计算时间
   - 检查是否有无限循环

## 故障排除

### 常见问题

1. **重力不触发**：
   ```javascript
   // 检查重力系统是否存在
   console.log('重力系统:', grid?.getGravitySystem());
   ```

2. **动画不显示**：
   ```javascript
   // 检查动画系统
   console.log('动画系统:', grid?.getAnimationSystem());
   ```

3. **方块位置错误**：
   ```javascript
   // 检查网格数据
   grid?.debugGridState('错误状态', true);
   ```

### 调试技巧

1. 开启详细日志模式
2. 使用断点调试重力计算过程
3. 监控方块位置变化
4. 检查动画队列状态

## 测试清单

- [ ] 个体方块重力正常工作
- [ ] 整行消除重力正常工作
- [ ] 全局悬空检测正常工作
- [ ] 下落动画流畅显示
- [ ] 连锁反应正确触发
- [ ] 性能表现良好
- [ ] 无控制台错误
- [ ] 游戏逻辑正确

完成所有测试项目后，重力系统修复验证完成。
