# 俄罗斯方块三消小游戏

这是一款结合了经典俄罗斯方块和三消玩法的微信小游戏。

## 游戏特色

- 结合经典俄罗斯方块和宝石消消乐的三消规则
- 支持多种颜色和附带效果的方块
- 消除和下落动画连贯流畅
- 多种道具系统，增加游戏策略性
- 多关卡设计，不同的挑战难度

## 游戏玩法

- 经典俄罗斯方块下落控制
- 同色三消的消除判定
- 消除后的方块自然下落
- 方块可带有特殊效果：地雷和冰冻
- 3种不同技能道具：火球术、闪电链、激流

## 技术架构

### 核心模块

- **GameController**: 游戏主控制器，负责初始化和管理游戏流程
- **Grid**: 管理游戏网格和方块放置
- **TetrominoFactory**: 负责生成不同形状和颜色的俄罗斯方块
- **Block**: 单个方块，包含颜色和特殊效果
- **MatchChecker**: 检测三消匹配
- **Animation**: 处理方块消除和下落的动画
- **LevelManager**: 管理关卡相关设置和目标
- **ItemManager**: 管理游戏道具系统
- **ScoreManager**: 分数和评级系统
- **UIController**: 处理游戏界面显示和交互

### 游戏流程

1. 玩家控制俄罗斯方块组合下落和旋转
2. 方块组合固定后，检查三消匹配
3. 触发匹配后，执行消除动画
4. 检测并执行方块下落
5. 执行特殊效果（地雷、冰冻等）
6. 生成新的俄罗斯方块组合
7. 检查游戏是否结束或关卡是否完成

## 文件结构

```
js/
  ├── main.js               # 游戏入口
  ├── databus.js            # 游戏数据管理
  ├── render.js             # 渲染系统初始化
  ├── base/                 # 基础类
  │   ├── sprite.js         # 精灵基类
  │   ├── animation.js      # 动画系统
  │   ├── pool.js           # 对象池
  ├── game/                 # 游戏核心模块
  │   ├── controller.js     # 游戏控制器
  │   ├── grid.js           # 游戏网格系统
  │   ├── block.js          # 方块类
  │   ├── tetromino.js      # 俄罗斯方块组合类
  │   ├── match-checker.js  # 匹配检测器
  │   ├── gravity.js        # 重力/下落系统
  │   ├── effect-manager.js # 特效管理
  ├── item/                 # 道具系统
  │   ├── item-manager.js   # 道具管理器
  │   ├── item-factory.js   # 道具工厂
  │   ├── items/            # 具体道具实现
  ├── level/                # 关卡系统
  │   ├── level-manager.js  # 关卡管理器
  │   ├── level-config.js   # 关卡配置
  ├── ui/                   # 用户界面
  │   ├── ui-controller.js  # UI控制器
  │   ├── score-panel.js    # 分数面板
  │   ├── item-panel.js     # 道具面板
  ├── runtime/              # 运行时模块
  │   ├── background.js     # 背景
  │   ├── gameinfo.js       # 游戏信息显示
  │   ├── music.js          # 音乐管理
```

## 开发计划

1. 实现基础的网格系统和俄罗斯方块逻辑
2. 实现方块颜色和三消判定系统
3. 实现方块消除和下落动画
4. 实现特殊效果（地雷和冰冻）
5. 实现道具系统
6. 实现关卡系统和评分
7. 美化UI和添加音效
8. 优化性能和用户体验
