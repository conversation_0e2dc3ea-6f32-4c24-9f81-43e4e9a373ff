#!/usr/bin/env node

/**
 * 更新RefactoredItemManager脚本
 * 集成提取的道具和渲染器到新架构中
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 开始更新RefactoredItemManager...\n');

function updateRefactoredItemManager() {
  const filePath = 'js/item/refactored-item-manager.js';
  
  if (!fs.existsSync(filePath)) {
    console.error('❌ RefactoredItemManager文件不存在');
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  console.log('📖 读取RefactoredItemManager文件');

  // 1. 添加新的导入语句
  console.log('📦 添加新的导入语句...');
  const newImports = `
// 提取的道具实现
import LightningItem from './items/lightning-item.js';
import WaterflowItem from './items/waterflow-item.js';
import EarthquakeItem from './items/earthquake-item.js';

// 提取的渲染器
import LightningRenderer from './animations/lightning-renderer.js';
import EffectRenderer from './animations/effect-renderer.js';`;

  // 在FireballItem导入后添加新导入
  content = content.replace(
    'import FireballItem from \'./items/fireball-item.js\';',
    `import FireballItem from './items/fireball-item.js';${newImports}`
  );

  // 2. 更新道具实例化
  console.log('🔧 更新道具实例化...');
  const newItemsInit = `    // 初始化道具实现（包含提取的完整实现）
    this.items = {
      [ITEM_TYPES.FIREBALL]: new FireballItem(grid, this.targetingSystem),
      [ITEM_TYPES.LIGHTNING]: new LightningItem(grid, this.targetingSystem),
      [ITEM_TYPES.WATERFLOW]: new WaterflowItem(grid, this.targetingSystem),
      [ITEM_TYPES.EARTHQUAKE]: new EarthquakeItem(grid, this.targetingSystem)
    };`;

  content = content.replace(
    /\/\/ 初始化道具实现[\s\S]*?\/\/ TODO: 后续添加其他道具[\s\S]*?},/,
    newItemsInit
  );

  // 3. 更新渲染器初始化
  console.log('🎨 更新渲染器初始化...');
  const newRenderersInit = `    // 初始化渲染器（包含提取的完整渲染器）
    this.explosionRenderer = new ExplosionRenderer();
    this.lightningRenderer = new LightningRenderer();
    this.effectRenderer = new EffectRenderer();`;

  content = content.replace(
    /\/\/ 初始化渲染器[\s\S]*?new ExplosionRenderer\(\);/,
    newRenderersInit
  );

  // 4. 更新动画状态
  console.log('🎬 更新动画状态...');
  const newAnimationState = `    // 动画状态（支持所有道具效果）
    this.animations = {
      timer: 0,
      isActive: false,
      explosionEffect: null,
      lightningEffect: null,
      waterflowEffect: null,
      earthquakeEffect: null
    };`;

  content = content.replace(
    /\/\/ 动画状态[\s\S]*?waterflowEffect: null[\s\S]*?};/,
    newAnimationState
  );

  // 5. 更新useItem方法
  console.log('⚡ 更新useItem方法...');
  const newUseItemLogic = `    // 使用道具（支持所有道具类型）
    let success = false;
    switch (itemType) {
      case ITEM_TYPES.FIREBALL:
        success = this._useFireball(row, col, level);
        break;
      case ITEM_TYPES.LIGHTNING:
        success = this._useLightning(row, col, level);
        break;
      case ITEM_TYPES.WATERFLOW:
        success = this._useWaterflow(row, col, level);
        break;
      case ITEM_TYPES.EARTHQUAKE:
        success = this._useEarthquake(row, col, level);
        break;
      default:
        console.warn(\`未知道具类型: \${itemType}\`);
        break;
    }`;

  content = content.replace(
    /\/\/ 使用道具[\s\S]*?\/\/ TODO: 添加其他道具的使用逻辑/,
    newUseItemLogic
  );

  // 6. 添加新道具使用方法
  console.log('🔨 添加新道具使用方法...');
  const newItemMethods = `
  /**
   * 使用闪电链（从原始实现提取）
   */
  _useLightning(row, col, level) {
    const lightningItem = this.items[ITEM_TYPES.LIGHTNING];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createLightningEffect: (startRow, startCol, chainPath) => {
        const effect = this.lightningRenderer.createLightningEffect(
          startRow, startCol, chainPath
        );
        this.animations.lightningEffect = effect;
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return lightningItem.use(row, col, level, callbacks);
  }

  /**
   * 使用激流（从原始实现提取）
   */
  _useWaterflow(row, col, level) {
    const waterflowItem = this.items[ITEM_TYPES.WATERFLOW];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createWaterflowEffect: (centerRow, centerCol, effectLevel) => {
        const effect = this.effectRenderer.createWaterflowEffect(
          centerRow, centerCol, effectLevel
        );
        this.animations.waterflowEffect = effect;
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return waterflowItem.use(row, col, level, callbacks);
  }

  /**
   * 使用地震术（从原始实现提取）
   */
  _useEarthquake(row, col, level) {
    const earthquakeItem = this.items[ITEM_TYPES.EARTHQUAKE];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createEarthquakeEffect: (startRow, endRow, intensity) => {
        const effect = this.effectRenderer.createEarthquakeEffect(
          startRow, endRow, intensity
        );
        this.animations.earthquakeEffect = effect;
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return earthquakeItem.use(row, col, level, callbacks);
  }`;

  // 在_useFireball方法后添加新方法
  content = content.replace(
    /(\s+return fireballItem\.use\(row, col, level, callbacks\);\s+})/,
    `$1${newItemMethods}`
  );

  // 7. 更新render方法
  console.log('🖼️ 更新render方法...');
  const newRenderMethod = `  /**
   * 渲染所有效果（支持所有道具效果）
   */
  render(ctx) {
    // 渲染爆炸效果
    if (this.animations.explosionEffect) {
      const stillActive = this.explosionRenderer.render(ctx, this.animations.explosionEffect);
      if (!stillActive) {
        this.animations.explosionEffect = null;
      }
    }
    
    // 渲染闪电效果
    if (this.animations.lightningEffect) {
      const stillActive = this.lightningRenderer.render(ctx, this.animations.lightningEffect);
      if (!stillActive) {
        this.animations.lightningEffect = null;
      }
    }
    
    // 渲染水流和地震效果
    this.effectRenderer.renderAll(ctx);
    
    // 清理已完成的效果
    this.lightningRenderer.cleanup();
    this.effectRenderer.cleanup();
  }`;

  content = content.replace(
    /\/\*\*\s+\* 渲染所有效果[\s\S]*?\/\/ TODO: 渲染其他效果\s+}/,
    newRenderMethod
  );

  // 8. 更新动画状态检查
  console.log('🎭 更新动画状态检查...');
  const newAnimationCheck = `    // 检查是否有活跃动画（支持所有效果）
    this.animations.isActive = !!(
      this.animations.explosionEffect ||
      this.animations.lightningEffect ||
      this.animations.waterflowEffect ||
      this.animations.earthquakeEffect ||
      this.lightningRenderer.activeEffects.length > 0 ||
      this.effectRenderer.activeEffects.length > 0
    );`;

  content = content.replace(
    /\/\/ 检查是否有活跃动画[\s\S]*?\);/,
    newAnimationCheck
  );

  // 写入更新后的内容
  fs.writeFileSync(filePath, content);
  console.log('✅ RefactoredItemManager已更新');
  return true;
}

function createFinalMigrationScript() {
  console.log('📝 创建最终迁移脚本...');
  
  const content = `#!/usr/bin/env node

/**
 * 最终迁移脚本
 * 完成从原始ItemManager到新架构的完全迁移
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始最终迁移...');

const filesToUpdate = [
  'js/game/controller.js',
  'js/runtime/gameinfo.js',
  'js/level/level-manager.js',
  'js/main.js'
];

function updateFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(\`⏸️  文件不存在，跳过: \${filePath}\`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;

  // 替换导入语句
  if (content.includes("from './item/item-manager.js'") || 
      content.includes("from '../item/item-manager.js'")) {
    
    // 根据文件路径调整导入路径
    const relativePath = filePath.includes('js/game/') ? '../item/refactored-item-manager.js' :
                         filePath.includes('js/runtime/') ? '../item/refactored-item-manager.js' :
                         filePath.includes('js/level/') ? '../item/refactored-item-manager.js' :
                         './item/refactored-item-manager.js';
    
    content = content.replace(
      /from ['"]\.\.?\/item\/item-manager\.js['"]/g,
      \`from '\${relativePath}'\`
    );
    updated = true;
  }

  // 替换类名引用
  if (content.includes('new ItemManager(')) {
    content = content.replace(/new ItemManager\\(/g, 'new RefactoredItemManager(');
    updated = true;
  }

  // 替换类名导入
  if (content.includes('import ItemManager')) {
    content = content.replace(/import ItemManager/g, 'import RefactoredItemManager');
    updated = true;
  }

  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(\`✅ 已更新: \${filePath}\`);
  } else {
    console.log(\`⏸️  无需更新: \${filePath}\`);
  }
}

function backupOriginalFile() {
  const originalPath = 'js/item/item-manager.js';
  const backupPath = 'js/item/item-manager.js.backup.' + Date.now();
  
  if (fs.existsSync(originalPath)) {
    fs.copyFileSync(originalPath, backupPath);
    console.log(\`📦 已备份原文件: \${backupPath}\`);
    return backupPath;
  }
  
  return null;
}

// 执行迁移
console.log('🔄 更新使用者文件...');
filesToUpdate.forEach(updateFile);

console.log('\\n📦 备份原始文件...');
const backupPath = backupOriginalFile();

console.log('\\n🎉 迁移完成！');
console.log('\\n📋 验证步骤:');
console.log('1. 测试所有道具功能');
console.log('2. 检查渲染效果');
console.log('3. 验证游戏流程');
console.log('4. 如果一切正常，可以删除备份文件');

if (backupPath) {
  console.log(\`\\n🗑️  删除备份命令: rm \${backupPath}\`);
}`;

  fs.writeFileSync('scripts/migrate-to-new-architecture.js', content);
  console.log('✅ 创建最终迁移脚本: scripts/migrate-to-new-architecture.js');
}

// 执行更新
try {
  if (updateRefactoredItemManager()) {
    createFinalMigrationScript();
    
    console.log('\n🎉 RefactoredItemManager更新完成！');
    console.log('\n📋 完整执行流程:');
    console.log('1. node scripts/extract-and-migrate-items.js      # 提取道具实现');
    console.log('2. node scripts/extract-item-renderers.js         # 提取渲染器');
    console.log('3. node scripts/update-refactored-manager.js      # 更新管理器 ✅');
    console.log('4. node scripts/migrate-to-new-architecture.js    # 完成迁移');
    console.log('5. 测试所有功能');
    console.log('6. 删除原始item-manager.js');
  }
  
} catch (error) {
  console.error('❌ 更新过程中出错:', error);
  process.exit(1);
}
