#!/usr/bin/env node

/**
 * 完成ItemManager重构脚本
 * 实现缺失的道具和渲染器，完成3841行巨无霸的拆分
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始完成ItemManager重构...\n');

// 1. 创建缺失的道具实现
function createLightningItem() {
  const content = `/**
 * 闪电链道具实现
 * 连接相同颜色的方块，形成闪电链消除
 */
export default class LightningItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
  }

  /**
   * 使用闪电链道具
   * @param {number} row - 起始行
   * @param {number} col - 起始列
   * @param {number} level - 道具等级
   * @param {Object} callbacks - 回调函数
   */
  use(row, col, level, callbacks) {
    console.log(\`🌩️ 使用闪电链道具 等级\${level} 位置(\${row}, \${col})\`);
    
    // 获取起始方块
    const startBlock = this.grid.getBlock(row, col);
    if (!startBlock) {
      console.log('❌ 起始位置没有方块');
      return false;
    }

    // 查找连接的方块
    const connections = this._findLightningConnections(row, col, startBlock.color, level);
    
    if (connections.length === 0) {
      console.log('❌ 没有找到可连接的方块');
      return false;
    }

    // 标记所有连接的方块
    connections.forEach(pos => {
      const block = this.grid.getBlock(pos.row, pos.col);
      if (block) {
        callbacks.markBlockAffected(block);
        this.grid.removeBlock(pos.row, pos.col);
      }
    });

    // 创建闪电效果
    callbacks.createLightningEffect(row, col, connections);
    
    // 播放音效
    callbacks.playSound('闪电链.mp3');

    console.log(\`⚡ 闪电链连接了 \${connections.length} 个方块\`);
    return true;
  }

  /**
   * 查找闪电连接
   */
  _findLightningConnections(startRow, startCol, targetColor, level) {
    const connections = [];
    const visited = new Set();
    const maxConnections = level * 3; // 等级越高连接越多
    
    // 使用广度优先搜索查找相同颜色的方块
    const queue = [{row: startRow, col: startCol, distance: 0}];
    visited.add(\`\${startRow},\${startCol}\`);
    
    while (queue.length > 0 && connections.length < maxConnections) {
      const current = queue.shift();
      connections.push({row: current.row, col: current.col});
      
      // 查找相邻的相同颜色方块
      const neighbors = this._getNeighbors(current.row, current.col);
      for (const neighbor of neighbors) {
        const key = \`\${neighbor.row},\${neighbor.col}\`;
        if (visited.has(key)) continue;
        
        const block = this.grid.getBlock(neighbor.row, neighbor.col);
        if (block && this._isSameColor(block, targetColor)) {
          visited.add(key);
          queue.push({...neighbor, distance: current.distance + 1});
        }
      }
    }
    
    return connections;
  }

  /**
   * 获取相邻位置
   */
  _getNeighbors(row, col) {
    const neighbors = [];
    const directions = [
      [-1, 0], [1, 0], [0, -1], [0, 1], // 上下左右
      [-1, -1], [-1, 1], [1, -1], [1, 1] // 对角线
    ];
    
    for (const [dr, dc] of directions) {
      const newRow = row + dr;
      const newCol = col + dc;
      if (this.grid.isValidPosition(newRow, newCol)) {
        neighbors.push({row: newRow, col: newCol});
      }
    }
    
    return neighbors;
  }

  /**
   * 检查是否为相同颜色（考虑冰冻状态）
   */
  _isSameColor(block, targetColor) {
    // 如果方块被冰冻，比较原始颜色
    if (block.effect === 'frozen' && block.originalColor) {
      return block.originalColor === targetColor;
    }
    return block.color === targetColor;
  }
}`;

  const filePath = 'js/item/items/lightning-item.js';
  fs.writeFileSync(filePath, content);
  console.log('✅ 创建 lightning-item.js');
}

function createWaterflowItem() {
  const content = `/**
 * 激流道具实现
 * 清除整行或整列的方块，产生水流效果
 */
export default class WaterflowItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
  }

  /**
   * 使用激流道具
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @param {number} level - 道具等级
   * @param {Object} callbacks - 回调函数
   */
  use(row, col, level, callbacks) {
    console.log(\`🌊 使用激流道具 等级\${level} 位置(\${row}, \${col})\`);
    
    let affectedBlocks = [];
    
    // 根据等级决定清除范围
    switch (level) {
      case 1:
        // 等级1: 清除一行
        affectedBlocks = this._clearRow(row);
        break;
      case 2:
        // 等级2: 清除一行一列
        affectedBlocks = [...this._clearRow(row), ...this._clearColumn(col)];
        break;
      case 3:
        // 等级3: 清除十字形区域
        affectedBlocks = this._clearCrossPattern(row, col);
        break;
    }

    if (affectedBlocks.length === 0) {
      console.log('❌ 没有方块可以清除');
      return false;
    }

    // 标记所有受影响的方块
    affectedBlocks.forEach(pos => {
      const block = this.grid.getBlock(pos.row, pos.col);
      if (block) {
        callbacks.markBlockAffected(block);
        this.grid.removeBlock(pos.row, pos.col);
      }
    });

    // 创建水流效果
    callbacks.createWaterflowEffect(row, col, level);
    
    // 播放音效
    callbacks.playSound('激流.mp3');

    console.log(\`🌊 激流清除了 \${affectedBlocks.length} 个方块\`);
    return true;
  }

  /**
   * 清除整行
   */
  _clearRow(row) {
    const blocks = [];
    for (let col = 0; col < this.grid.cols; col++) {
      if (this.grid.getBlock(row, col)) {
        blocks.push({row, col});
      }
    }
    return blocks;
  }

  /**
   * 清除整列
   */
  _clearColumn(col) {
    const blocks = [];
    for (let row = 0; row < this.grid.rows; row++) {
      if (this.grid.getBlock(row, col)) {
        blocks.push({row, col});
      }
    }
    return blocks;
  }

  /**
   * 清除十字形区域
   */
  _clearCrossPattern(centerRow, centerCol) {
    const blocks = [];
    
    // 清除水平线
    blocks.push(...this._clearRow(centerRow));
    
    // 清除垂直线
    blocks.push(...this._clearColumn(centerCol));
    
    // 去重
    const uniqueBlocks = [];
    const seen = new Set();
    for (const block of blocks) {
      const key = \`\${block.row},\${block.col}\`;
      if (!seen.has(key)) {
        seen.add(key);
        uniqueBlocks.push(block);
      }
    }
    
    return uniqueBlocks;
  }
}`;

  const filePath = 'js/item/items/waterflow-item.js';
  fs.writeFileSync(filePath, content);
  console.log('✅ 创建 waterflow-item.js');
}

function createEarthquakeItem() {
  const content = `/**
 * 地震术道具实现
 * 随机消除方块，产生震动效果
 */
export default class EarthquakeItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
  }

  /**
   * 使用地震术道具
   * @param {number} row - 震中行
   * @param {number} col - 震中列
   * @param {number} level - 道具等级
   * @param {Object} callbacks - 回调函数
   */
  use(row, col, level, callbacks) {
    console.log(\`🌍 使用地震术道具 等级\${level} 震中(\${row}, \${col})\`);
    
    // 根据等级决定影响范围和强度
    const intensity = level * 2; // 等级越高影响范围越大
    const blockCount = level * 8; // 等级越高消除方块越多
    
    const affectedBlocks = this._findEarthquakeTargets(row, col, intensity, blockCount);
    
    if (affectedBlocks.length === 0) {
      console.log('❌ 没有方块可以消除');
      return false;
    }

    // 标记所有受影响的方块
    affectedBlocks.forEach(pos => {
      const block = this.grid.getBlock(pos.row, pos.col);
      if (block) {
        callbacks.markBlockAffected(block);
        this.grid.removeBlock(pos.row, pos.col);
      }
    });

    // 创建地震效果
    callbacks.createEarthquakeEffect(row, col, intensity);
    
    // 播放音效
    callbacks.playSound('地震.mp3');

    console.log(\`🌍 地震消除了 \${affectedBlocks.length} 个方块\`);
    return true;
  }

  /**
   * 查找地震影响的方块
   */
  _findEarthquakeTargets(centerRow, centerCol, intensity, maxBlocks) {
    const targets = [];
    const allBlocks = [];
    
    // 收集影响范围内的所有方块
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          const distance = Math.sqrt(
            Math.pow(row - centerRow, 2) + Math.pow(col - centerCol, 2)
          );
          
          if (distance <= intensity) {
            allBlocks.push({
              row, col, distance,
              // 距离震中越近，被选中概率越高
              probability: Math.max(0.1, 1 - distance / intensity)
            });
          }
        }
      }
    }
    
    // 按概率随机选择方块
    allBlocks.sort((a, b) => b.probability - a.probability);
    
    for (const block of allBlocks) {
      if (targets.length >= maxBlocks) break;
      
      if (Math.random() < block.probability) {
        targets.push({row: block.row, col: block.col});
      }
    }
    
    // 确保至少选中一些方块
    if (targets.length === 0 && allBlocks.length > 0) {
      targets.push({row: allBlocks[0].row, col: allBlocks[0].col});
    }
    
    return targets;
  }
}`;

  const filePath = 'js/item/items/earthquake-item.js';
  fs.writeFileSync(filePath, content);
  console.log('✅ 创建 earthquake-item.js');
}

// 执行创建
try {
  // 确保目录存在
  const itemsDir = 'js/item/items';
  if (!fs.existsSync(itemsDir)) {
    fs.mkdirSync(itemsDir, { recursive: true });
  }

  createLightningItem();
  createWaterflowItem();
  createEarthquakeItem();
  
  console.log('\n🎉 所有道具实现已创建完成！');
  console.log('\n📋 下一步:');
  console.log('1. 运行 node scripts/create-item-renderers.js 创建渲染器');
  console.log('2. 更新 RefactoredItemManager 集成新道具');
  console.log('3. 迁移使用者到新架构');
  
} catch (error) {
  console.error('❌ 创建过程中出错:', error);
  process.exit(1);
}
