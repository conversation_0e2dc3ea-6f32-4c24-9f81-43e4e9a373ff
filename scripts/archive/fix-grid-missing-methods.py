#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

with open('js/game/grid.js', 'r', encoding='utf-8') as f:
    content = f.read()

# 在isFullRow方法后添加isRowFull和isRowEmpty方法
pattern = r'(isFullRow\(row\) \{[^}]*\})'
replacement = r'''\1

  /**
   * 检查指定行是否为满行 (controller.js兼容性方法)
   * @param {number} row - 行索引
   * @returns {boolean} 是否为满行
   */
  isRowFull(row) {
    return this.isFullRow(row);
  }

  /**
   * 检查指定行是否为空行
   * @param {number} row - 行索引
   * @returns {boolean} 是否为空行
   */
  isRowEmpty(row) {
    if (row < 0 || row >= this.rows) {
      return false;
    }
    
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[row][col] !== null) {
        return false;
      }
    }
    return true;
  }'''

new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)

with open('js/game/grid.js', 'w', encoding='utf-8') as f:
    f.write(new_content)

print('✅ Grid.js 缺失方法已补全完成') 