/**
 * 修复模块导入问题
 * 解决验证中发现的tinyemitter和其他导入路径问题
 */

console.log('🔧 开始修复模块导入问题...\n');

const fs = require('fs');
const path = require('path');

// 需要修复的文件和对应的修复规则
const fixRules = [
  // 修复 tinyemitter 导入问题
  {
    pattern: /import\s+Emitter\s+from\s+['"]\.\.\/\.\.\/libs\/tinyemitter['"]/g,
    replacement: "import TinyEmitter from '../../libs/tinyemitter.js';\nconst Emitter = TinyEmitter.default || TinyEmitter;",
    description: '修复 tinyemitter 导入路径'
  },
  {
    pattern: /import\s+Emitter\s+from\s+['"]\.\.\/libs\/tinyemitter['"]/g,
    replacement: "import TinyEmitter from '../libs/tinyemitter.js';\nconst Emitter = TinyEmitter.default || TinyEmitter;",
    description: '修复 tinyemitter 导入路径 (短路径)'
  },
  
  // 修复 render 目录导入问题
  {
    pattern: /from\s+['"]\.\.\/\.\.\/render['"]/g,
    replacement: "from '../../render.js'",
    description: '修复 render 目录导入'
  },
  
  // 修复 grid 导入问题
  {
    pattern: /import\s+Grid\s+from\s+['"]\.\/grid['"]/g,
    replacement: "import Grid from './grid.js'",
    description: '修复 grid 模块导入'
  }
];

// 需要处理的文件目录
const targetDirectories = [
  'js/game/state-management',
  'js/game/physics', 
  'js/game/scoring',
  'js/game/matching',
  'js/game/rendering'
];

// 需要处理的单个文件
const targetFiles = [
  'js/game/refactored-controller.js'
];

/**
 * 修复单个文件
 */
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 应用所有修复规则
    fixRules.forEach(rule => {
      if (rule.pattern.test(content)) {
        content = content.replace(rule.pattern, rule.replacement);
        modified = true;
        console.log(`✅ ${filePath}: ${rule.description}`);
      }
    });
    
    // 如果文件被修改了，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`📝 已更新: ${filePath}`);
    }
    
    return modified;
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath} - ${error.message}`);
    return false;
  }
}

/**
 * 处理目录下的所有JS文件
 */
function processDirectory(dirPath) {
  try {
    const files = fs.readdirSync(dirPath);
    let processedCount = 0;
    
    files.forEach(file => {
      if (file.endsWith('.js')) {
        const filePath = path.join(dirPath, file);
        if (fixFile(filePath)) {
          processedCount++;
        }
      }
    });
    
    return processedCount;
  } catch (error) {
    console.error(`❌ 处理目录失败: ${dirPath} - ${error.message}`);
    return 0;
  }
}

// 主修复流程
console.log('🔍 扫描并修复模块导入问题...\n');

let totalFixed = 0;

// 处理目标目录
targetDirectories.forEach(dir => {
  console.log(`📁 处理目录: ${dir}`);
  const fixedCount = processDirectory(dir);
  totalFixed += fixedCount;
  if (fixedCount > 0) {
    console.log(`   修复了 ${fixedCount} 个文件\n`);
  } else {
    console.log(`   无需修复\n`);
  }
});

// 处理单个文件
targetFiles.forEach(file => {
  console.log(`📄 处理文件: ${file}`);
  if (fixFile(file)) {
    totalFixed++;
    console.log(`   已修复\n`);
  } else {
    console.log(`   无需修复\n`);
  }
});

console.log('='.repeat(50));
console.log(`🎉 修复完成！总共修复了 ${totalFixed} 个文件`);
console.log('='.repeat(50));

if (totalFixed > 0) {
  console.log('\n💡 建议重新运行系统验证脚本来检查修复效果：');
  console.log('   node scripts/system-verification.js');
} else {
  console.log('\n�� 没有发现需要修复的导入问题');
} 