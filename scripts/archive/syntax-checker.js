#!/usr/bin/env node

/**
 * 语法检查工具
 * 检查项目中所有JS文件的语法是否正确
 */

const fs = require('fs');
const path = require('path');
const vm = require('vm');

class SyntaxChecker {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.results = {
      totalFiles: 0,
      errorFiles: [],
      successFiles: 0
    };
  }

  /**
   * 开始语法检查
   */
  async check() {
    console.log('🔍 开始语法检查...\n');
    
    const jsFiles = this.findJSFiles(this.projectRoot);
    
    for (const filePath of jsFiles) {
      await this.checkFile(filePath);
    }
    
    this.printResults();
  }

  /**
   * 查找JS文件
   */
  findJSFiles(dir) {
    const files = [];
    const ignoreDirs = ['node_modules', '.git', 'dist', 'build'];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        if (ignoreDirs.includes(item)) continue;
        
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          files.push(...this.findJSFiles(fullPath));
        } else if (item.endsWith('.js')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error.message);
    }
    
    return files;
  }

  /**
   * 检查单个文件的语法
   */
  async checkFile(filePath) {
    const relativePath = path.relative(this.projectRoot, filePath);
    this.results.totalFiles++;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否有非打印字符
      const hasNonPrintable = /[\x00-\x08\x0E-\x1F\x7F-\x9F]/.test(content);
      if (hasNonPrintable) {
        this.results.errorFiles.push({
          file: relativePath,
          error: 'File contains non-printable characters',
          type: 'encoding'
        });
        console.log(`❌ ${relativePath}: 包含非打印字符`);
        return;
      }
      
      // 检查基本语法结构
      this.checkBasicSyntax(content, relativePath);
      
      // 尝试解析为JavaScript
      try {
        // 使用简单的语法检查，而不是完整执行
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line && !line.startsWith('//') && !line.startsWith('*')) {
            // 检查明显的语法错误
            this.checkLineForErrors(line, relativePath, i + 1);
          }
        }
        
        this.results.successFiles++;
        console.log(`✅ ${relativePath}`);
        
      } catch (syntaxError) {
        this.results.errorFiles.push({
          file: relativePath,
          error: syntaxError.message,
          type: 'syntax'
        });
        console.log(`❌ ${relativePath}: ${syntaxError.message}`);
      }
      
    } catch (error) {
      this.results.errorFiles.push({
        file: relativePath,
        error: error.message,
        type: 'read'
      });
      console.log(`❌ ${relativePath}: 无法读取文件 - ${error.message}`);
    }
  }

  /**
   * 检查基本语法结构
   */
  checkBasicSyntax(content, relativePath) {
    // 检查括号匹配
    const brackets = { '(': ')', '[': ']', '{': '}' };
    const stack = [];
    
    for (let i = 0; i < content.length; i++) {
      const char = content[i];
      
      if (brackets[char]) {
        stack.push({ char: brackets[char], pos: i });
      } else if (Object.values(brackets).includes(char)) {
        if (stack.length === 0 || stack.pop().char !== char) {
          throw new Error(`Mismatched bracket at position ${i}`);
        }
      }
    }
    
    if (stack.length > 0) {
      throw new Error(`Unclosed bracket: ${stack[stack.length - 1].char}`);
    }
  }

  /**
   * 检查单行是否有明显错误
   */
  checkLineForErrors(line, relativePath, lineNumber) {
    // 检查常见的语法错误模式
    const errorPatterns = [
      {
        pattern: /^import\s+[^;]*$/,
        test: (line) => {
          // 检查import语句是否正确 - 修复误判问题
          const trimmedLine = line.trim();
          
          // 完整的import语句模式
          const validImportPatterns = [
            /^import\s+['"][^'"]*['"];?\s*(\/\/.*)?$/,  // import './module';
            /^import\s+\w+\s+from\s+['"][^'"]*['"];?\s*(\/\/.*)?$/,  // import Something from './module';
            /^import\s*\{\s*[^}]*\}\s*from\s+['"][^'"]*['"];?\s*(\/\/.*)?$/,  // import { a, b } from './module';
            /^import\s*\*\s*as\s+\w+\s+from\s+['"][^'"]*['"];?\s*(\/\/.*)?$/  // import * as name from './module';
          ];
          
          // 如果是有效的import语句，不报错
          for (const pattern of validImportPatterns) {
            if (pattern.test(trimmedLine)) {
              return null;
            }
          }
          
          // 检查是否是明显的错误import
          if (trimmedLine.startsWith('import ') && !trimmedLine.includes(' from ') && !trimmedLine.endsWith(';')) {
            return 'Incomplete import statement';
          }
          
          return null;
        }
      },
      {
        pattern: /\bimport\b/,
        test: (line) => {
          // 检查是否有裸露的import关键字
          const trimmedLine = line.trim();
          if (trimmedLine === 'import' || 
              (trimmedLine.startsWith('import ') && 
               !trimmedLine.includes('from') && 
               !trimmedLine.match(/^import\s+['"][^'"]*['"];?\s*(\/\/.*)?$/))) {
            return 'Invalid import syntax';
          }
          return null;
        }
      }
    ];

    for (const { test } of errorPatterns) {
      const error = test(line);
      if (error) {
        throw new Error(`${error} at line ${lineNumber}: "${line}"`);
      }
    }
  }

  /**
   * 打印检查结果
   */
  printResults() {
    console.log('\n📊 语法检查结果:');
    console.log(`总文件数: ${this.results.totalFiles}`);
    console.log(`正确文件: ${this.results.successFiles}`);
    console.log(`错误文件: ${this.results.errorFiles.length}`);
    
    if (this.results.errorFiles.length > 0) {
      console.log('\n❌ 发现语法错误的文件:');
      
      const errorsByType = {};
      this.results.errorFiles.forEach(item => {
        if (!errorsByType[item.type]) {
          errorsByType[item.type] = [];
        }
        errorsByType[item.type].push(item);
      });
      
      Object.keys(errorsByType).forEach(type => {
        console.log(`\n🔍 ${type} 错误:`);
        errorsByType[type].forEach(item => {
          console.log(`  📄 ${item.file}:`);
          console.log(`     ❌ ${item.error}`);
        });
      });
      
    } else {
      console.log('\n✅ 所有文件语法检查通过！');
    }
  }
}

// 运行语法检查
if (require.main === module) {
  const checker = new SyntaxChecker();
  checker.check().catch(console.error);
}

module.exports = SyntaxChecker; 