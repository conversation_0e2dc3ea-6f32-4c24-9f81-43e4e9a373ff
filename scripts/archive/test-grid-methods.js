#!/usr/bin/env node

/**
 * Grid方法专项测试
 * 确保所有必需的方法都能正常工作
 */

const path = require('path');
const fs = require('fs');

// 模拟微信小程序环境
global.console = console;

// 加载模块
function loadModule(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    if (fs.existsSync(fullPath)) {
      delete require.cache[fullPath];
      return require(fullPath);
    }
    throw new Error(`文件不存在: ${fullPath}`);
  } catch (error) {
    console.error(`❌ 无法加载模块 ${filePath}:`, error.message);
    return null;
  }
}

// 测试函数
function runTest(testName, testFn) {
  try {
    console.log(`🧪 测试: ${testName}`);
    const result = testFn();
    if (result !== false) {
      console.log(`✅ 通过: ${testName}`);
      return true;
    } else {
      console.log(`❌ 失败: ${testName}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 失败: ${testName} - ${error.message}`);
    return false;
  }
}

// 模拟Block类
class MockBlock {
  constructor(color = 'RED', effect = 'NONE') {
    this.color = color;
    this.effect = effect;
  }
}

async function main() {
  console.log('🚀 开始Grid方法专项测试...\n');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // 测试1: Grid模块加载
  totalTests++;
  const gridModule = loadModule('./js/game/grid.js');
  if (!gridModule || !gridModule.default) {
    console.log('❌ Grid模块加载失败');
    return;
  }
  
  const Grid = gridModule.default;
  const { GRID_COLS, GRID_ROWS } = gridModule;
  
  if (runTest('Grid模块加载', () => {
    console.log(`   Grid类: ${typeof Grid}`);
    console.log(`   GRID_COLS: ${GRID_COLS}`);
    console.log(`   GRID_ROWS: ${GRID_ROWS}`);
    return typeof Grid === 'function' && GRID_COLS && GRID_ROWS;
  })) {
    passedTests++;
  }
  
  // 创建Grid实例
  const grid = new Grid(20, 10);
  
  // 测试2: 基础方法存在性
  totalTests++;
  if (runTest('基础方法存在性', () => {
    const methods = ['removeBlock', 'placeBlock', 'hasBlock', 'getAllBlocks', 'isValidPosition', 'clear'];
    const missing = methods.filter(method => typeof grid[method] !== 'function');
    
    if (missing.length > 0) {
      console.log(`   ❌ 缺失方法: ${missing.join(', ')}`);
      return false;
    }
    
    console.log(`   ✅ 所有方法都存在: ${methods.join(', ')}`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试3: removeBlock方法
  totalTests++;
  if (runTest('removeBlock方法', () => {
    // 测试移除不存在的方块
    const result1 = grid.removeBlock(0, 0);
    if (result1 !== null) {
      console.log('   ❌ 移除空位置应该返回null');
      return false;
    }
    
    // 测试无效位置
    const result2 = grid.removeBlock(-1, 0);
    if (result2 !== null) {
      console.log('   ❌ 移除无效位置应该返回null');
      return false;
    }
    
    console.log('   ✅ removeBlock基础功能正常');
    return true;
  })) {
    passedTests++;
  }
  
  // 测试4: placeBlock方法
  totalTests++;
  if (runTest('placeBlock方法', () => {
    const block = new MockBlock('RED', 'NONE');
    
    // 测试正常放置
    const result1 = grid.placeBlock(block, 19, 5); // 底部中间
    if (!result1) {
      console.log('   ❌ 正常位置放置失败');
      return false;
    }
    
    // 测试重复放置
    const result2 = grid.placeBlock(block, 19, 5);
    if (result2) {
      console.log('   ❌ 重复位置应该放置失败');
      return false;
    }
    
    // 测试无效位置
    const result3 = grid.placeBlock(block, -1, 0);
    if (result3) {
      console.log('   ❌ 无效位置应该放置失败');
      return false;
    }
    
    console.log('   ✅ placeBlock功能正常');
    return true;
  })) {
    passedTests++;
  }
  
  // 测试5: hasBlock方法
  totalTests++;
  if (runTest('hasBlock方法', () => {
    // 检查已放置的方块
    if (!grid.hasBlock(19, 5)) {
      console.log('   ❌ 应该检测到已放置的方块');
      return false;
    }
    
    // 检查空位置
    if (grid.hasBlock(0, 0)) {
      console.log('   ❌ 空位置不应该有方块');
      return false;
    }
    
    // 检查无效位置
    if (grid.hasBlock(-1, 0)) {
      console.log('   ❌ 无效位置不应该有方块');
      return false;
    }
    
    console.log('   ✅ hasBlock功能正常');
    return true;
  })) {
    passedTests++;
  }
  
  // 测试6: getAllBlocks方法
  totalTests++;
  if (runTest('getAllBlocks方法', () => {
    const blocks = grid.getAllBlocks();
    
    if (!Array.isArray(blocks)) {
      console.log('   ❌ getAllBlocks应该返回数组');
      return false;
    }
    
    if (blocks.length !== 1) {
      console.log(`   ❌ 应该有1个方块，实际有${blocks.length}个`);
      return false;
    }
    
    const block = blocks[0];
    if (block.row !== 19 || block.col !== 5) {
      console.log(`   ❌ 方块位置错误: (${block.row}, ${block.col})`);
      return false;
    }
    
    console.log('   ✅ getAllBlocks功能正常');
    return true;
  })) {
    passedTests++;
  }
  
  // 测试7: removeBlock返回值测试
  totalTests++;
  if (runTest('removeBlock返回值测试', () => {
    const removedBlock = grid.removeBlock(19, 5);
    
    if (!removedBlock) {
      console.log('   ❌ removeBlock应该返回被移除的方块');
      return false;
    }
    
    if (removedBlock.color !== 'RED') {
      console.log('   ❌ 返回的方块颜色错误');
      return false;
    }
    
    // 验证方块已被移除
    if (grid.hasBlock(19, 5)) {
      console.log('   ❌ 方块应该已被移除');
      return false;
    }
    
    console.log('   ✅ removeBlock返回值正确');
    return true;
  })) {
    passedTests++;
  }
  
  // 测试8: 批量操作测试
  totalTests++;
  if (runTest('批量操作测试', () => {
    // 放置多个方块
    for (let i = 0; i < 5; i++) {
      const block = new MockBlock('BLUE', 'NONE');
      if (!grid.placeBlock(block, 18, i)) {
        console.log(`   ❌ 批量放置失败: (18, ${i})`);
        return false;
      }
    }
    
    // 验证方块数量
    const blocks = grid.getAllBlocks();
    if (blocks.length !== 5) {
      console.log(`   ❌ 批量放置后应该有5个方块，实际有${blocks.length}个`);
      return false;
    }
    
    // 批量移除
    for (let i = 0; i < 5; i++) {
      const removed = grid.removeBlock(18, i);
      if (!removed) {
        console.log(`   ❌ 批量移除失败: (18, ${i})`);
        return false;
      }
    }
    
    // 验证全部移除
    const remainingBlocks = grid.getAllBlocks();
    if (remainingBlocks.length !== 0) {
      console.log(`   ❌ 批量移除后应该没有方块，实际有${remainingBlocks.length}个`);
      return false;
    }
    
    console.log('   ✅ 批量操作正常');
    return true;
  })) {
    passedTests++;
  }
  
  // 测试9: 兼容性测试（模拟实际使用场景）
  totalTests++;
  if (runTest('兼容性测试', () => {
    // 模拟LevelManager.setupInitialBlocks的使用
    try {
      // 清空网格
      for (let row = 0; row < grid.rows; row++) {
        for (let col = 0; col < grid.cols; col++) {
          grid.removeBlock(row, col);
        }
      }
      
      // 放置初始方块
      const block = new MockBlock('GREEN', 'NONE');
      const success = grid.placeBlock(block, 10, 5);
      
      if (!success) {
        console.log('   ❌ 兼容性测试放置失败');
        return false;
      }
      
      console.log('   ✅ 与LevelManager兼容');
      return true;
    } catch (error) {
      console.log(`   ❌ 兼容性测试异常: ${error.message}`);
      return false;
    }
  })) {
    passedTests++;
  }
  
  // 输出测试结果
  console.log('\n============================================================');
  console.log('🚀 Grid方法专项测试结果');
  console.log('============================================================');
  console.log(`总测试数: ${totalTests}`);
  console.log(`✅ 通过: ${passedTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有Grid方法测试通过！可以解决removeBlock错误');
    console.log('\n🚀 下一步建议:');
    console.log('1. 在微信开发者工具中重新编译');
    console.log('2. 检查是否还有其他方法缺失');
    console.log('3. 测试游戏启动功能');
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步修复');
  }
}

main().catch(console.error); 