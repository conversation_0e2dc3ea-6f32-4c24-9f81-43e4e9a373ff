/**
 * 修复重复导入问题的专项脚本
 * 清理所有由之前修复过程中产生的重复导入
 */

console.log('🧹 开始清理重复导入问题...\n');

const fs = require('fs');
const path = require('path');

let fixCount = 0;

function logFix(message) {
  fixCount++;
  console.log(`✅ 修复 ${fixCount}: ${message}`);
}

/**
 * 清理文件中的重复导入
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @returns {string} 清理后的内容
 */
function cleanDuplicateImports(filePath, content) {
  const lines = content.split('\n');
  const seenImports = new Set();
  const cleanedLines = [];
  let hasChanges = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检查是否是import语句
    if (line.trim().startsWith('import ')) {
      // 标准化import语句用于去重
      const normalizedImport = line.trim().replace(/\s+/g, ' ');
      
      if (seenImports.has(normalizedImport)) {
        // 跳过重复的导入
        hasChanges = true;
        console.log(`   移除重复导入: ${line.trim()}`);
        continue;
      } else {
        seenImports.add(normalizedImport);
      }
    }
    
    cleanedLines.push(line);
  }
  
  if (hasChanges) {
    logFix(`${path.basename(filePath)} 重复导入已清理`);
  }
  
  return cleanedLines.join('\n');
}

/**
 * 递归扫描并修复目录中的文件
 * @param {string} dir - 目录路径
 */
function scanAndFixDirectory(dir) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 递归处理子目录
      scanAndFixDirectory(fullPath);
    } else if (item.endsWith('.js')) {
      // 处理JavaScript文件
      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        const cleanedContent = cleanDuplicateImports(fullPath, content);
        
        if (cleanedContent !== content) {
          fs.writeFileSync(fullPath, cleanedContent, 'utf8');
        }
      } catch (error) {
        console.warn(`处理文件 ${fullPath} 时出错:`, error.message);
      }
    }
  }
}

// 需要检查的目录列表
const directoriesToCheck = [
  'js/game',
  'js/main-system',
  'js/level',
  'js/item',
  'js/platforms',
  'js/logic',
  'js/effects'
];

console.log('📁 扫描目录:');
directoriesToCheck.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`   - ${dir}`);
    scanAndFixDirectory(dir);
  } else {
    console.log(`   - ${dir} (不存在，跳过)`);
  }
});

// 特殊修复：确保.js扩展名的一致性
console.log('\n🔧 修复导入路径扩展名不一致问题...');

const pathFixPairs = [
  { pattern: "from './block'", replacement: "from './block.js'" },
  { pattern: "from './grid'", replacement: "from './grid.js'" },
  { pattern: "from '../game/block'", replacement: "from '../game/block.js'" },
  { pattern: "from '../libs/tinyemitter'", replacement: "from '../libs/tinyemitter.js'" }
];

directoriesToCheck.forEach(dir => {
  if (fs.existsSync(dir)) {
    scanAndFixDirectory(dir);
  }
});

function fixImportExtensions(filePath, content) {
  let fixedContent = content;
  let hasChanges = false;
  
  pathFixPairs.forEach(({ pattern, replacement }) => {
    if (fixedContent.includes(pattern)) {
      fixedContent = fixedContent.replace(new RegExp(pattern, 'g'), replacement);
      hasChanges = true;
    }
  });
  
  if (hasChanges) {
    logFix(`${path.basename(filePath)} 导入路径扩展名已修复`);
  }
  
  return fixedContent;
}

// 重新扫描修复扩展名
directoriesToCheck.forEach(dir => {
  if (fs.existsSync(dir)) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      
      if (item.endsWith('.js')) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          const fixedContent = fixImportExtensions(fullPath, content);
          
          if (fixedContent !== content) {
            fs.writeFileSync(fullPath, fixedContent, 'utf8');
          }
        } catch (error) {
          console.warn(`修复扩展名时处理文件 ${fullPath} 出错:`, error.message);
        }
      }
    }
  }
});

console.log('\n' + '='.repeat(60));
console.log(`🧹 重复导入清理完成！总共修复了 ${fixCount} 个文件`);
console.log('='.repeat(60));

console.log('\n🎯 修复总结:');
console.log('1. ✅ 清理了重复的import语句');
console.log('2. ✅ 统一了导入路径的.js扩展名');
console.log('3. ✅ 优化了导入语句的格式');

console.log('\n🚀 建议下一步:');
console.log('1. 在微信开发者工具中重新编译');
console.log('2. 检查是否还有编译错误');
console.log('3. 运行基础功能测试'); 