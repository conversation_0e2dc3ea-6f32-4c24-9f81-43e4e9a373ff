#!/usr/bin/env python3
# -*- coding: utf-8 -*-

content = '''export const GRID_COLS = 10;
export const GRID_ROWS = 20;

export default class Grid {
  constructor(rows = 20, cols = 10) {
    this.rows = rows;
    this.cols = cols;
    this.blocks = Array(rows).fill().map(() => Array(cols).fill(null));
    
    // 动画系统
    this.animations = [];
    this.animationIdCounter = 0;
    
    // 渲染相关属性
    this.blockSize = 30; // 默认方块大小
    this.offsetX = 50;   // 默认X偏移
    this.offsetY = 50;   // 默认Y偏移
    
    console.log(`🎮 网格系统已初始化: ${rows}x${cols}`);
  }
  
  isValidPosition(x, y) {
    return x >= 0 && x < this.cols && y >= 0 && y < this.rows;
  }
  
  addTetromino(tetromino) {
    if (!tetromino) return false;
    console.log('🔲 添加方块到网格');
    return true;
  }
  
  removeFullRows() {
    const removedRows = [];
    for (let row = 0; row < this.rows; row++) {
      let isFullLine = true;
      for (let col = 0; col < this.cols; col++) {
        if (!this.blocks[row][col]) {
          isFullLine = false;
          break;
        }
      }
      if (isFullLine) {
        removedRows.push(row);
      }
    }
    return removedRows;
  }
  
  render() {
    // console.log('🎨 渲染网格');
  }
  
  clear() {
    this.blocks = Array(this.rows).fill().map(() => Array(this.cols).fill(null));
    this.animations = [];
    console.log('🧹 网格已清空');
  }
  
  getBlock(x, y) {
    if (!this.isValidPosition(x, y)) return null;
    return this.blocks[y][x];
  }
  
  setBlock(x, y, block) {
    if (!this.isValidPosition(x, y)) return false;
    this.blocks[y][x] = block;
    return true;
  }

  /**
   * 移除指定位置的方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {Block|null} 被移除的方块，如果没有方块则返回null
   */
  removeBlock(row, col) {
    if (!this.isValidPosition(col, row)) {
      return null;
    }
    
    const removedBlock = this.blocks[row][col];
    this.blocks[row][col] = null;
    
    if (removedBlock) {
      console.log(`🗑️ 移除方块: (${row}, ${col})`);
    }
    
    return removedBlock;
  }

  /**
   * 在指定位置放置方块
   * @param {Block} block - 要放置的方块
   * @param {number} row - 行索引  
   * @param {number} col - 列索引
   * @returns {boolean} 是否成功放置
   */
  placeBlock(block, row, col) {
    if (!this.isValidPosition(col, row)) {
      console.warn(`❌ 无效位置: (${row}, ${col})`);
      return false;
    }
    
    if (this.blocks[row][col] !== null) {
      console.warn(`❌ 位置已被占用: (${row}, ${col})`);
      return false;
    }
    
    this.blocks[row][col] = block;
    console.log(`✅ 放置方块: (${row}, ${col})`);
    return true;
  }

  /**
   * 检查指定位置是否有方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {boolean} 是否有方块
   */
  hasBlock(row, col) {
    if (!this.isValidPosition(col, row)) {
      return false;
    }
    return this.blocks[row][col] !== null;
  }

  /**
   * 获取所有非空方块的位置和信息
   * @returns {Array} 方块信息数组
   */
  getAllBlocks() {
    const blocks = [];
    for (let row = 0; row < this.rows; row++) {
      for (let col = 0; col < this.cols; col++) {
        if (this.blocks[row][col]) {
          blocks.push({
            row,
            col,
            block: this.blocks[row][col]
          });
        }
      }
    }
    return blocks;
  }

  /**
   * 检查网格是否已满
   * @returns {boolean} 网格是否已满
   */
  isFull() {
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[0][col] !== null) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取指定行的所有方块
   * @param {number} row - 行索引
   * @returns {Array} 该行的方块数组
   */
  getRow(row) {
    if (row < 0 || row >= this.rows) {
      return [];
    }
    return [...this.blocks[row]];
  }

  /**
   * 检查指定行是否为满行
   * @param {number} row - 行索引
   * @returns {boolean} 是否为满行
   */
  isFullRow(row) {
    if (row < 0 || row >= this.rows) {
      return false;
    }
    
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[row][col] === null) {
        return false;
      }
    }
    return true;
  }

  /**
   * 检查指定行是否为满行 (controller.js兼容性方法)
   * @param {number} row - 行索引
   * @returns {boolean} 是否为满行
   */
  isRowFull(row) {
    return this.isFullRow(row);
  }

  /**
   * 检查指定行是否为空行
   * @param {number} row - 行索引
   * @returns {boolean} 是否为空行
   */
  isRowEmpty(row) {
    if (row < 0 || row >= this.rows) {
      return false;
    }
    
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[row][col] !== null) {
        return false;
      }
    }
    return true;
  }

  /**
   * 设置渲染相关属性
   * @param {Object} options - 渲染选项
   */
  setRenderOptions(options = {}) {
    if (options.blockSize) this.blockSize = options.blockSize;
    if (options.offsetX !== undefined) this.offsetX = options.offsetX;
    if (options.offsetY !== undefined) this.offsetY = options.offsetY;
    console.log(`🎨 更新渲染选项: blockSize=${this.blockSize}, offset=(${this.offsetX}, ${this.offsetY})`);
  }

  /**
   * 添加动画
   * @param {Object} animation - 动画对象
   * @returns {number} 动画ID
   */
  addAnimation(animation) {
    if (!animation || typeof animation !== 'object') {
      console.warn('⚠️ 无效的动画对象');
      return -1;
    }
    
    const animationId = this.animationIdCounter++;
    const animationWithId = {
      id: animationId,
      duration: animation.duration || 300,
      startTime: Date.now(),
      progress: 0,
      completed: false,
      ...animation
    };
    
    this.animations.push(animationWithId);
    console.log(`🎬 添加动画: ID=${animationId}, 类型=${animation.type || '未知'}`);
    return animationId;
  }

  /**
   * 移除动画
   * @param {number} animationId - 动画ID
   */
  removeAnimation(animationId) {
    const index = this.animations.findIndex(anim => anim.id === animationId);
    if (index !== -1) {
      this.animations.splice(index, 1);
      console.log(`🗑️ 移除动画: ID=${animationId}`);
    }
  }

  /**
   * 更新所有动画
   * @returns {boolean} 是否还有动画在进行
   */
  updateAnimations() {
    if (this.animations.length === 0) {
      return false;
    }

    const currentTime = Date.now();
    const completedAnimations = [];

    for (let i = 0; i < this.animations.length; i++) {
      const animation = this.animations[i];
      const elapsed = currentTime - animation.startTime;
      animation.progress = Math.min(elapsed / animation.duration, 1.0);

      // 执行动画更新回调
      if (animation.onUpdate && typeof animation.onUpdate === 'function') {
        animation.onUpdate(animation.progress, animation);
      }

      // 检查动画是否完成
      if (animation.progress >= 1.0) {
        animation.completed = true;
        completedAnimations.push(i);

        // 执行完成回调
        if (animation.onComplete && typeof animation.onComplete === 'function') {
          animation.onComplete(animation);
        }
      }
    }

    // 移除已完成的动画（从后往前移除，避免索引错乱）
    for (let i = completedAnimations.length - 1; i >= 0; i--) {
      const index = completedAnimations[i];
      this.animations.splice(index, 1);
    }

    // 返回是否还有未完成的动画
    return this.animations.length > 0;
  }

  /**
   * 清除所有动画
   */
  clearAnimations() {
    this.animations = [];
    console.log('🧹 清除所有动画');
  }

  /**
   * 获取动画数量
   * @returns {number} 当前动画数量
   */
  getAnimationCount() {
    return this.animations.length;
  }

  /**
   * 检查是否有指定类型的动画
   * @param {string} type - 动画类型
   * @returns {boolean} 是否存在该类型的动画
   */
  hasAnimationType(type) {
    return this.animations.some(anim => anim.type === type);
  }

  /**
   * 获取指定类型的所有动画
   * @param {string} type - 动画类型
   * @returns {Array} 指定类型的动画数组
   */
  getAnimationsByType(type) {
    return this.animations.filter(anim => anim.type === type);
  }

  /**
   * 暂停所有动画
   */
  pauseAnimations() {
    const currentTime = Date.now();
    this.animations.forEach(animation => {
      if (!animation.paused) {
        animation.pausedAt = currentTime;
        animation.paused = true;
      }
    });
    console.log('⏸️ 暂停所有动画');
  }

  /**
   * 恢复所有动画
   */
  resumeAnimations() {
    const currentTime = Date.now();
    this.animations.forEach(animation => {
      if (animation.paused) {
        const pauseDuration = currentTime - animation.pausedAt;
        animation.startTime += pauseDuration;
        animation.paused = false;
        delete animation.pausedAt;
      }
    });
    console.log('▶️ 恢复所有动画');
  }
}'''

with open('js/game/grid.js', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ Grid.js 完整修复完成') 