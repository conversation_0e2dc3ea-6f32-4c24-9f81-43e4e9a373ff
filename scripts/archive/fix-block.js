#!/usr/bin/env node

/**
 * 修复 block.js 括号问题的专用工具
 */

const fs = require('fs');

function fixBlockFile() {
  console.log('🔧 开始修复 block.js 文件...');
  
  let content = fs.readFileSync('js/game/block.js', 'utf8');
  const originalContent = content;
  
  // 修复第552行的双重右括号问题
  // 查找并修复这个特定的模式
  const problemPattern = /particle\.color\.replace\('1\)'/g;
  const fixes = [];
  
  let match;
  while ((match = problemPattern.exec(content)) !== null) {
    const start = match.index;
    const lineStart = content.lastIndexOf('\n', start) + 1;
    const lineEnd = content.indexOf('\n', start);
    const line = content.slice(lineStart, lineEnd);
    
    console.log(`找到问题行: ${line.trim()}`);
    
    // 检查是否有双重右括号
    if (line.includes(')`));')) {
      const fixedLine = line.replace(')`));', ')`);');
      content = content.slice(0, lineStart) + fixedLine + content.slice(lineEnd);
      fixes.push({
        original: line.trim(),
        fixed: fixedLine.trim()
      });
    }
  }
  
  if (fixes.length > 0) {
    // 保存修复后的文件
    fs.writeFileSync('js/game/block.js', content);
    
    console.log(`✅ 已修复 ${fixes.length} 个问题:`);
    fixes.forEach((fix, index) => {
      console.log(`  ${index + 1}. ${fix.original}`);
      console.log(`     -> ${fix.fixed}`);
    });
  } else {
    console.log('🔍 未找到需要修复的双重右括号问题');
  }
  
  // 验证修复结果
  try {
    require('./js/game/block.js');
    console.log('🎉 文件修复成功，语法检查通过！');
  } catch (error) {
    console.log('❌ 修复后仍有语法错误:', error.message);
    
    // 恢复原文件
    fs.writeFileSync('js/game/block.js', originalContent);
    console.log('🔄 已恢复原文件');
  }
}

fixBlockFile(); 