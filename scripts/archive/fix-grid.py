#!/usr/bin/env python3
# -*- coding: utf-8 -*-

content = '''export const GRID_COLS = 10;
export const GRID_ROWS = 20;

export default class Grid {
  constructor(rows = 20, cols = 10) {
    this.rows = rows;
    this.cols = cols;
    this.blocks = Array(rows).fill().map(() => Array(cols).fill(null));
    console.log(`🎮 网格系统已初始化: ${rows}x${cols}`);
  }
  
  isValidPosition(x, y) {
    return x >= 0 && x < this.cols && y >= 0 && y < this.rows;
  }
  
  addTetromino(tetromino) {
    if (!tetromino) return false;
    console.log('🔲 添加方块到网格');
    return true;
  }
  
  removeFullRows() {
    const removedRows = [];
    for (let row = 0; row < this.rows; row++) {
      let isFullLine = true;
      for (let col = 0; col < this.cols; col++) {
        if (!this.blocks[row][col]) {
          isFullLine = false;
          break;
        }
      }
      if (isFullLine) {
        removedRows.push(row);
      }
    }
    return removedRows;
  }
  
  render() {
    console.log('🎨 渲染网格');
  }
  
  clear() {
    this.blocks = Array(this.rows).fill().map(() => Array(this.cols).fill(null));
    console.log('🧹 网格已清空');
  }
  
  getBlock(x, y) {
    if (!this.isValidPosition(x, y)) return null;
    return this.blocks[y][x];
  }
  
  setBlock(x, y, block) {
    if (!this.isValidPosition(x, y)) return false;
    this.blocks[y][x] = block;
    return true;
  }
}'''

with open('js/game/grid.js', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ Grid.js 文件已创建完成') 