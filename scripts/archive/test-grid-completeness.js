#!/usr/bin/env node

/**
 * Grid完整性测试
 * 确保所有controller.js需要的功能都已实现
 */

const path = require('path');
const fs = require('fs');

// 模拟微信小程序环境
global.console = console;

// 加载模块
function loadModule(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    if (fs.existsSync(fullPath)) {
      delete require.cache[fullPath];
      return require(fullPath);
    }
    throw new Error(`文件不存在: ${fullPath}`);
  } catch (error) {
    console.error(`❌ 无法加载模块 ${filePath}:`, error.message);
    return null;
  }
}

// 测试函数
function runTest(testName, testFn) {
  try {
    console.log(`🧪 测试: ${testName}`);
    const result = testFn();
    if (result !== false) {
      console.log(`✅ 通过: ${testName}`);
      return true;
    } else {
      console.log(`❌ 失败: ${testName}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 失败: ${testName} - ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 开始Grid完整性测试...\n');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // 测试1: Grid模块加载
  totalTests++;
  const gridModule = loadModule('./js/game/grid.js');
  if (!gridModule || !gridModule.default) {
    console.log('❌ Grid模块加载失败');
    return;
  }
  
  const Grid = gridModule.default;
  const { GRID_COLS, GRID_ROWS } = gridModule;
  
  if (runTest('Grid模块加载', () => {
    console.log(`   Grid类: ${typeof Grid}`);
    console.log(`   GRID_COLS: ${GRID_COLS}`);
    console.log(`   GRID_ROWS: ${GRID_ROWS}`);
    return typeof Grid === 'function' && GRID_COLS && GRID_ROWS;
  })) {
    passedTests++;
  }
  
  // 创建Grid实例
  const grid = new Grid(20, 10);
  
  // 测试2: 基础方法完整性
  totalTests++;
  if (runTest('基础方法完整性', () => {
    const requiredMethods = [
      'removeBlock', 'placeBlock', 'hasBlock', 'getAllBlocks', 
      'isValidPosition', 'clear', 'getBlock', 'setBlock', 'getRow',
      'isFullRow', 'isRowFull', 'isRowEmpty', 'isFull'
    ];
    
    const missing = requiredMethods.filter(method => typeof grid[method] !== 'function');
    
    if (missing.length > 0) {
      console.log(`   ❌ 缺失方法: ${missing.join(', ')}`);
      return false;
    }
    
    console.log(`   ✅ 所有基础方法都存在 (${requiredMethods.length}个)`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试3: 动画系统完整性
  totalTests++;
  if (runTest('动画系统完整性', () => {
    const animationMethods = [
      'addAnimation', 'removeAnimation', 'updateAnimations', 
      'clearAnimations', 'getAnimationCount', 'hasAnimationType',
      'getAnimationsByType', 'pauseAnimations', 'resumeAnimations'
    ];
    
    const missing = animationMethods.filter(method => typeof grid[method] !== 'function');
    
    if (missing.length > 0) {
      console.log(`   ❌ 缺失动画方法: ${missing.join(', ')}`);
      return false;
    }
    
    if (!Array.isArray(grid.animations)) {
      console.log('   ❌ animations属性不是数组');
      return false;
    }
    
    console.log(`   ✅ 动画系统完整 (${animationMethods.length}个方法 + animations属性)`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试4: 渲染属性完整性
  totalTests++;
  if (runTest('渲染属性完整性', () => {
    const renderProps = ['blockSize', 'offsetX', 'offsetY'];
    const missing = renderProps.filter(prop => typeof grid[prop] !== 'number');
    
    if (missing.length > 0) {
      console.log(`   ❌ 缺失渲染属性: ${missing.join(', ')}`);
      return false;
    }
    
    console.log(`   ✅ 渲染属性完整: blockSize=${grid.blockSize}, offset=(${grid.offsetX}, ${grid.offsetY})`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试5: controller.js第739行兼容性 - isRowFull
  totalTests++;
  if (runTest('controller.js:739 isRowFull兼容性', () => {
    // 模拟 if (this.grid.isRowFull(row))
    const result = grid.isRowFull(0);
    
    if (typeof result !== 'boolean') {
      console.log('   ❌ isRowFull应该返回布尔值');
      return false;
    }
    
    console.log(`   ✅ isRowFull(0) = ${result} (兼容controller.js:739)`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试6: controller.js第1889行兼容性 - animations.length
  totalTests++;
  if (runTest('controller.js:1889 animations.length兼容性', () => {
    // 模拟 if (this.grid.animations.length > 0)
    const animationCount = grid.animations.length;
    
    if (typeof animationCount !== 'number') {
      console.log('   ❌ animations.length应该是数字');
      return false;
    }
    
    console.log(`   ✅ animations.length = ${animationCount} (兼容controller.js:1889)`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试7: controller.js第1896行兼容性 - updateAnimations()
  totalTests++;
  if (runTest('controller.js:1896 updateAnimations兼容性', () => {
    // 模拟 const animationsComplete = !this.grid.updateAnimations();
    const hasAnimations = grid.updateAnimations();
    
    if (typeof hasAnimations !== 'boolean') {
      console.log('   ❌ updateAnimations应该返回布尔值');
      return false;
    }
    
    console.log(`   ✅ updateAnimations() = ${hasAnimations} (兼容controller.js:1896)`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试8: 渲染NaN问题修复
  totalTests++;
  if (runTest('渲染NaN问题修复', () => {
    // 模拟 this.grid.offsetX + this.grid.cols * this.grid.blockSize + 20
    const previewX = grid.offsetX + grid.cols * grid.blockSize + 20;
    const previewY = grid.offsetY;
    const previewSize = grid.blockSize * 0.8;
    
    if (isNaN(previewX) || isNaN(previewY) || isNaN(previewSize)) {
      console.log(`   ❌ 渲染计算产生NaN: previewX=${previewX}, previewY=${previewY}, previewSize=${previewSize}`);
      return false;
    }
    
    console.log(`   ✅ 渲染计算正常: previewX=${previewX}, previewY=${previewY}, previewSize=${previewSize.toFixed(1)}`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试9: 实际使用场景模拟
  totalTests++;
  if (runTest('实际使用场景模拟', () => {
    // 模拟 LevelManager.setupInitialBlocks
    for (let row = 0; row < grid.rows; row++) {
      for (let col = 0; col < grid.cols; col++) {
        grid.removeBlock(row, col);
      }
    }
    
    // 模拟 Block.render 参数计算
    const scoreX = grid.offsetX + grid.cols * grid.blockSize + 20;
    const scoreY = grid.offsetY + grid.blockSize * 5;
    
    if (isNaN(scoreX) || isNaN(scoreY)) {
      console.log(`   ❌ 分数渲染计算产生NaN: scoreX=${scoreX}, scoreY=${scoreY}`);
      return false;
    }
    
    // 模拟 controller.js 满行检查
    let hasFullRow = false;
    for (let row = 0; row < grid.rows; row++) {
      if (grid.isRowFull(row)) {
        hasFullRow = true;
        break;
      }
    }
    
    console.log(`   ✅ 场景模拟成功: scorePos=(${scoreX}, ${scoreY}), hasFullRow=${hasFullRow}`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试10: 错误处理和边界条件
  totalTests++;
  if (runTest('错误处理和边界条件', () => {
    // 测试无效位置
    const invalidRemove = grid.removeBlock(-1, -1);
    const invalidPlace = grid.placeBlock(null, 25, 15);
    const invalidRowFull = grid.isRowFull(-1);
    const invalidRowEmpty = grid.isRowEmpty(25);
    
    if (invalidRemove !== null) {
      console.log('   ❌ 无效位置removeBlock应该返回null');
      return false;
    }
    
    if (invalidPlace !== false) {
      console.log('   ❌ 无效位置placeBlock应该返回false');
      return false;
    }
    
    if (invalidRowFull !== false || invalidRowEmpty !== false) {
      console.log('   ❌ 无效行索引应该返回false');
      return false;
    }
    
    console.log('   ✅ 错误处理正常');
    return true;
  })) {
    passedTests++;
  }
  
  // 输出测试结果
  console.log('\n============================================================');
  console.log('🚀 Grid完整性测试结果');
  console.log('============================================================');
  console.log(`总测试数: ${totalTests}`);
  console.log(`✅ 通过: ${passedTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 Grid类完全兼容！可以解决以下错误:');
    console.log('   ✅ TypeError: this.grid.isRowFull is not a function');
    console.log('   ✅ TypeError: Cannot read property \'length\' of undefined');
    console.log('   ✅ Block.render: 收到无效参数 {x: NaN, y: NaN, size: NaN}');
    console.log('   ✅ 所有controller.js兼容性问题');
    console.log('\n🚀 现在可以在微信开发者工具中正常运行！');
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步修复');
  }
}

main().catch(console.error); 