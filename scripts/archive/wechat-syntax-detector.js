#!/usr/bin/env node

/**
 * 微信小游戏语法兼容性检测工具
 * 检测可能导致微信编译器报错但Node.js正常的问题
 */

const fs = require('fs');
const path = require('path');

class WeChatSyntaxDetector {
  constructor() {
    this.issues = [];
  }

  /**
   * 检测项目中的潜在问题
   */
  async detectIssues() {
    console.log('🔍 微信小游戏语法兼容性检测...\n');
    
    const jsFiles = this.findJSFiles();
    
    for (const filePath of jsFiles) {
      await this.checkFile(filePath);
    }
    
    this.printResults();
  }

  /**
   * 查找JS文件
   */
  findJSFiles() {
    const files = [];
    const ignoreDirs = ['node_modules', '.git', 'dist', 'build'];
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          if (ignoreDirs.includes(item)) continue;
          
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            scanDir(fullPath);
          } else if (item.endsWith('.js')) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // 忽略访问错误
      }
    };
    
    scanDir('.');
    return files;
  }

  /**
   * 检查单个文件
   */
  async checkFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative('.', filePath);
      
      // 检查各种潜在问题
      this.checkHiddenCharacters(content, relativePath);
      this.checkEmojis(content, relativePath);
      this.checkTemplateStrings(content, relativePath);
      this.checkES6Features(content, relativePath);
      this.checkComments(content, relativePath);
      
    } catch (error) {
      this.issues.push({
        file: path.relative('.', filePath),
        type: 'read_error',
        message: `无法读取文件: ${error.message}`
      });
    }
  }

  /**
   * 检查隐藏字符
   */
  checkHiddenCharacters(content, filePath) {
    // 检查零宽字符和其他隐藏字符
    const hiddenChars = [
      '\u200B', // 零宽空格
      '\u200C', // 零宽非连字符
      '\u200D', // 零宽连字符
      '\u2060', // 字间连接符
      '\uFEFF'  // BOM
    ];
    
    hiddenChars.forEach(char => {
      if (content.includes(char)) {
        const lines = content.split('\n');
        lines.forEach((line, index) => {
          if (line.includes(char)) {
            this.issues.push({
              file: filePath,
              type: 'hidden_char',
              line: index + 1,
              message: `包含隐藏字符 (Unicode: U+${char.charCodeAt(0).toString(16).toUpperCase()})`
            });
          }
        });
      }
    });
  }

  /**
   * 检查emoji和特殊Unicode字符
   */
  checkEmojis(content, filePath) {
    // emoji范围
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
    
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      const matches = line.match(emojiRegex);
      if (matches) {
        this.issues.push({
          file: filePath,
          type: 'emoji',
          line: index + 1,
          message: `包含emoji字符: ${matches.join(', ')}`
        });
      }
    });
  }

  /**
   * 检查模板字符串
   */
  checkTemplateStrings(content, filePath) {
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      // 检查复杂的模板字符串
      if (line.includes('`') && line.includes('${')) {
        // 检查嵌套的模板字符串或复杂表达式
        const templateRegex = /`[^`]*\$\{[^}]*\}[^`]*`/g;
        const matches = line.match(templateRegex);
        if (matches) {
          matches.forEach(match => {
            // 检查是否有嵌套的引号或复杂结构
            if (match.includes("'") && match.includes('"')) {
              this.issues.push({
                file: filePath,
                type: 'complex_template',
                line: index + 1,
                message: `复杂模板字符串可能有兼容性问题: ${match.substring(0, 50)}...`
              });
            }
          });
        }
      }
    });
  }

  /**
   * 检查ES6特性
   */
  checkES6Features(content, filePath) {
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      // 检查可能有问题的ES6语法
      const problematicPatterns = [
        { pattern: /\?\./g, name: '可选链操作符' },
        { pattern: /\?\?/g, name: '空值合并操作符' },
        { pattern: /\*\*/g, name: '指数操作符' },
        { pattern: /import\s*\(/g, name: '动态import' }
      ];
      
      problematicPatterns.forEach(({ pattern, name }) => {
        if (pattern.test(line)) {
          this.issues.push({
            file: filePath,
            type: 'es6_feature',
            line: index + 1,
            message: `使用了可能不兼容的语法: ${name}`
          });
        }
      });
    });
  }

  /**
   * 检查注释中的特殊字符
   */
  checkComments(content, filePath) {
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
        // 检查注释中的特殊字符
        const specialChars = /[^\x00-\x7F]/g;
        const matches = line.match(specialChars);
        if (matches && matches.length > 3) { // 超过3个非ASCII字符可能有问题
          this.issues.push({
            file: filePath,
            type: 'comment_chars',
            line: index + 1,
            message: `注释包含大量非ASCII字符，可能影响编译`
          });
        }
      }
    });
  }

  /**
   * 打印结果
   */
  printResults() {
    console.log('\n📊 检测结果:');
    
    if (this.issues.length === 0) {
      console.log('✅ 未发现明显的兼容性问题');
      console.log('\n💡 建议:');
      console.log('1. 错误可能来自微信开发者工具的内部处理');
      console.log('2. 如果游戏运行正常，可以安全忽略此错误');
      console.log('3. 尝试重启微信开发者工具或清除缓存');
      return;
    }

    // 按类型分组
    const groupedIssues = {};
    this.issues.forEach(issue => {
      if (!groupedIssues[issue.type]) {
        groupedIssues[issue.type] = [];
      }
      groupedIssues[issue.type].push(issue);
    });

    Object.keys(groupedIssues).forEach(type => {
      const typeNames = {
        hidden_char: '🔍 隐藏字符',
        emoji: '😀 Emoji字符',
        complex_template: '📝 复杂模板字符串',
        es6_feature: '🔧 ES6特性',
        comment_chars: '💬 注释字符',
        read_error: '❌ 读取错误'
      };
      
      console.log(`\n${typeNames[type] || type}:`);
      groupedIssues[type].forEach(issue => {
        if (issue.line) {
          console.log(`  📄 ${issue.file}:${issue.line} - ${issue.message}`);
        } else {
          console.log(`  📄 ${issue.file} - ${issue.message}`);
        }
      });
    });

    console.log('\n💡 修复建议:');
    console.log('1. 移除或替换检测到的特殊字符');
    console.log('2. 简化复杂的模板字符串');
    console.log('3. 检查文件编码是否为UTF-8无BOM');
  }
}

// 运行检测
if (require.main === module) {
  const detector = new WeChatSyntaxDetector();
  detector.detectIssues().catch(console.error);
}

module.exports = WeChatSyntaxDetector; 