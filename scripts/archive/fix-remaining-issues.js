/**
 * 第二轮修复脚本
 * 解决剩余的模块路径和方法缺失问题
 */

console.log('🔧 开始第二轮修复...\n');

const fs = require('fs');
const path = require('path');

// 检查文件是否存在
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch {
    return false;
  }
}

// 修复 GameStateManager 状态历史问题
console.log('🔍 修复 GameStateManager 状态历史问题...');
const gameStateManagerPath = 'js/game/state-management/game-state-manager.js';
if (fileExists(gameStateManagerPath)) {
  let content = fs.readFileSync(gameStateManagerPath, 'utf8');
  
  // 确保状态历史在构造函数中正确初始化
  if (content.includes('this.stateHistory = [];')) {
    content = content.replace(
      'this.stateHistory = [];',
      'this.stateHistory = [{ state: this.state, timestamp: Date.now(), data: null }];'
    );
    
    fs.writeFileSync(gameStateManagerPath, content, 'utf8');
    console.log('✅ 修复了状态历史初始化问题');
  }
}

// 修复 GameFlowManager GameGlobal 问题
console.log('🔍 修复 GameFlowManager GameGlobal 问题...');
const gameFlowManagerPath = 'js/game/state-management/game-flow-manager.js';
if (fileExists(gameFlowManagerPath)) {
  let content = fs.readFileSync(gameFlowManagerPath, 'utf8');
  
  // 添加 GameGlobal 检查
  if (content.includes('GameGlobal.pauseGame')) {
    content = content.replace(
      /GameGlobal\.pauseGame/g,
      '(typeof GameGlobal !== "undefined" && GameGlobal.pauseGame ? GameGlobal.pauseGame : (() => {}))'
    );
    
    content = content.replace(
      /GameGlobal\.resumeGame/g,
      '(typeof GameGlobal !== "undefined" && GameGlobal.resumeGame ? GameGlobal.resumeGame : (() => {}))'
    );
    
    fs.writeFileSync(gameFlowManagerPath, content, 'utf8');
    console.log('✅ 修复了 GameGlobal 引用问题');
  }
}

// 检查并修复 tetromino 和 block 模块
console.log('🔍 检查 tetromino 和 block 模块...');
const tetrominoPath = 'js/game/tetromino.js';
const blockPath = 'js/game/block.js';

if (!fileExists(tetrominoPath)) {
  console.log('❌ 缺少 tetromino.js 文件，创建简化版本...');
  const tetrominoContent = `/**
 * 简化的 Tetromino 类 - 用于测试
 */

export default class Tetromino {
  constructor(type = 'I', x = 0, y = 0) {
    this.type = type;
    this.x = x;
    this.y = y;
    this.rotation = 0;
    this.shape = this.getShape();
  }
  
  getShape() {
    const shapes = {
      'I': [[1, 1, 1, 1]],
      'O': [[1, 1], [1, 1]],
      'T': [[0, 1, 0], [1, 1, 1]],
      'S': [[0, 1, 1], [1, 1, 0]],
      'Z': [[1, 1, 0], [0, 1, 1]],
      'J': [[1, 0, 0], [1, 1, 1]],
      'L': [[0, 0, 1], [1, 1, 1]]
    };
    return shapes[this.type] || shapes['I'];
  }
  
  rotate() {
    // 简化的旋转逻辑
    this.rotation = (this.rotation + 90) % 360;
  }
  
  move(dx, dy) {
    this.x += dx;
    this.y += dy;
  }
}`;

  fs.writeFileSync(tetrominoPath, tetrominoContent, 'utf8');
  console.log('✅ 创建了简化的 tetromino.js');
}

if (!fileExists(blockPath)) {
  console.log('❌ 缺少 block.js 文件，创建简化版本...');
  const blockContent = `/**
 * 简化的 Block 类 - 用于测试
 */

export default class Block {
  constructor(type = 'normal', x = 0, y = 0) {
    this.type = type;
    this.x = x;
    this.y = y;
    this.color = this.getColor();
    this.solid = true;
  }
  
  getColor() {
    const colors = {
      'normal': '#888',
      'special': '#f00',
      'bomb': '#ff0',
      'clear': '#0f0'
    };
    return colors[this.type] || colors['normal'];
  }
  
  destroy() {
    this.solid = false;
  }
}`;

  fs.writeFileSync(blockPath, blockContent, 'utf8');
  console.log('✅ 创建了简化的 block.js');
}

// 修复 ComboManager processAction 方法
console.log('🔍 修复 ComboManager processAction 方法...');
const comboManagerPath = 'js/game/scoring/combo-manager.js';
if (fileExists(comboManagerPath)) {
  let content = fs.readFileSync(comboManagerPath, 'utf8');
  
  // 检查是否缺少 processAction 方法
  if (!content.includes('processAction(')) {
    // 在类的最后添加 processAction 方法
    const insertPoint = content.lastIndexOf('}');
    const methodCode = `
  /**
   * 处理游戏动作并检测连击
   */
  processAction(actionType, actionData = {}) {
    const currentTime = Date.now();
    
    // 检查是否在连击时间窗口内
    if (this.lastActionTime && 
        currentTime - this.lastActionTime > this.options.comboTimeWindow) {
      this.resetCombo();
    }
    
    // 记录动作
    this.actionHistory.push({
      type: actionType,
      data: actionData,
      timestamp: currentTime
    });
    
    // 更新连击状态
    const isCombo = this.actionHistory.length >= this.options.minComboLength;
    if (isCombo) {
      this.comboState.currentCombo = this.actionHistory.length;
      this.comboState.isActive = true;
    }
    
    this.lastActionTime = currentTime;
    
    return {
      isCombo,
      comboCount: this.comboState.currentCombo,
      multiplier: this.calculateMultiplier()
    };
  }
`;
    
    content = content.slice(0, insertPoint) + methodCode + content.slice(insertPoint);
    fs.writeFileSync(comboManagerPath, content, 'utf8');
    console.log('✅ 添加了 processAction 方法');
  }
}

// 修复 MatchEngine checkMatches 方法
console.log('🔍 修复 MatchEngine checkMatches 方法...');
const matchEnginePath = 'js/game/matching/match-engine.js';
if (fileExists(matchEnginePath)) {
  let content = fs.readFileSync(matchEnginePath, 'utf8');
  
  // 检查是否缺少 checkMatches 方法
  if (!content.includes('checkMatches(')) {
    const insertPoint = content.lastIndexOf('}');
    const methodCode = `
  /**
   * 检查匹配模式
   */
  checkMatches() {
    const matches = [];
    
    if (this.options.enableLineClear) {
      const lineMatches = this.findLineMatches();
      matches.push(...lineMatches);
    }
    
    return matches;
  }
  
  /**
   * 查找完整行匹配
   */
  findLineMatches() {
    const matches = [];
    
    for (let row = 0; row < this.grid.rows; row++) {
      let isFullLine = true;
      for (let col = 0; col < this.grid.cols; col++) {
        if (!this.grid.blocks[row][col]) {
          isFullLine = false;
          break;
        }
      }
      
      if (isFullLine) {
        matches.push({
          type: 'line',
          row: row,
          positions: Array.from({ length: this.grid.cols }, (_, col) => ({ row, col }))
        });
      }
    }
    
    return matches;
  }
`;
    
    content = content.slice(0, insertPoint) + methodCode + content.slice(insertPoint);
    fs.writeFileSync(matchEnginePath, content, 'utf8');
    console.log('✅ 添加了 checkMatches 方法');
  }
}

// 修复微信小游戏Canvas问题
console.log('🔍 修复微信小游戏Canvas问题...');
const verificationScriptPath = 'scripts/system-verification.js';
if (fileExists(verificationScriptPath)) {
  let content = fs.readFileSync(verificationScriptPath, 'utf8');
  
  // 完善 wx mock
  content = content.replace(
    /global\.wx = \{[^}]+\};/,
    `global.wx = {
  getSystemInfoSync: () => ({
    platform: 'devtools',
    screenWidth: 375,
    screenHeight: 667
  }),
  createCanvas: () => ({
    width: 375,
    height: 667,
    getContext: (type) => ({
      scale: () => {},
      clearRect: () => {},
      fillRect: () => {},
      strokeRect: () => {},
      drawImage: () => {},
      imageSmoothingEnabled: true,
      imageSmoothingQuality: 'high',
      canvas: { width: 375, height: 667 }
    })
  })
};`
  );
  
  fs.writeFileSync(verificationScriptPath, content, 'utf8');
  console.log('✅ 完善了微信小游戏Canvas mock');
}

console.log('\n' + '='.repeat(50));
console.log('🎉 第二轮修复完成！');
console.log('='.repeat(50));
console.log('\n💡 重新运行验证脚本检查修复效果：');
console.log('   node scripts/system-verification.js'); 