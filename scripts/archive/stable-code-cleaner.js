#!/usr/bin/env node

/**
 * 稳定的代码清理工具
 * 专门修复 import { b } from 'module'; 这种错误
 */

const fs = require('fs');
const path = require('path');

class StableCodeCleaner {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.stats = {
      scannedFiles: 0,
      cleanedFiles: 0,
      errors: 0
    };
  }

  /**
   * 开始清理过程
   */
  async clean() {
    console.log('🧹 启动稳定代码清理工具...\n');
    
    try {
      // 获取所有JS文件
      const jsFiles = this.findJSFiles();
      console.log(`📁 找到 ${jsFiles.length} 个JS文件`);
      
      // 处理每个文件
      for (const filePath of jsFiles) {
        await this.cleanFile(filePath);
      }
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ 清理过程出错:', error);
      this.stats.errors++;
    }
  }

  /**
   * 查找所有JS文件
   */
  findJSFiles() {
    const files = [];
    const ignoreDirs = ['node_modules', '.git', 'dist', 'build'];
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          if (ignoreDirs.includes(item)) continue;
          
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            scanDir(fullPath);
          } else if (item.endsWith('.js') && !item.includes('test') && !item.includes('spec')) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        console.error(`读取目录失败 ${dir}:`, error.message);
      }
    };
    
    scanDir(this.projectRoot);
    return files;
  }

  /**
   * 清理单个文件
   */
  async cleanFile(filePath) {
    const relativePath = path.relative(this.projectRoot, filePath);
    this.stats.scannedFiles++;
    
    try {
      const originalContent = fs.readFileSync(filePath, 'utf8');
      const cleanedContent = this.processContent(originalContent);
      
      if (cleanedContent !== originalContent) {
        // 备份原文件
        const backupPath = filePath + '.backup.' + Date.now();
        fs.writeFileSync(backupPath, originalContent);
        
        // 写入清理后的内容
        fs.writeFileSync(filePath, cleanedContent);
        
        console.log(`✅ 已清理: ${relativePath}`);
        this.stats.cleanedFiles++;
        
        // 清理备份文件（如果清理成功）
        setTimeout(() => {
          if (fs.existsSync(backupPath)) {
            fs.unlinkSync(backupPath);
          }
        }, 1000);
      } else {
        console.log(`⏸️  无需清理: ${relativePath}`);
      }
      
    } catch (error) {
      console.error(`❌ 处理文件失败 ${relativePath}:`, error.message);
      this.stats.errors++;
    }
  }

  /**
   * 处理文件内容
   */
  processContent(content) {
    // 只清理这个特定的错误模式
    const targetPattern = /^import\s*{\s*b\s*}\s*from\s*['"]module['"];?\s*$/gm;
    
    // 计算清理前的行数
    const beforeLines = content.split('\n');
    
    // 执行清理
    let cleanedContent = content.replace(targetPattern, '');
    
    // 清理空行（但保留必要的空行）
    cleanedContent = this.normalizeNewlines(cleanedContent);
    
    return cleanedContent;
  }

  /**
   * 标准化换行符，清理多余的空行
   */
  normalizeNewlines(content) {
    // 将多个连续的空行减少为最多2个
    return content
      .replace(/\n{4,}/g, '\n\n\n')  // 4个或更多空行减少为3个
      .replace(/\n{3}/g, '\n\n');    // 3个空行减少为2个
  }

  /**
   * 打印清理结果
   */
  printResults() {
    console.log('\n📊 清理完成！');
    console.log(`📁 扫描文件: ${this.stats.scannedFiles}`);
    console.log(`✅ 已清理文件: ${this.stats.cleanedFiles}`);
    console.log(`❌ 错误数量: ${this.stats.errors}`);
    
    if (this.stats.cleanedFiles > 0) {
      console.log('\n🎉 代码清理成功！已移除所有 import { b } from "module"; 语句');
    } else if (this.stats.errors === 0) {
      console.log('\n😊 项目代码很干净，没有发现需要清理的内容');
    }
  }
}

// 运行清理工具
if (require.main === module) {
  const cleaner = new StableCodeCleaner();
  cleaner.clean().catch(console.error);
}

module.exports = StableCodeCleaner; 