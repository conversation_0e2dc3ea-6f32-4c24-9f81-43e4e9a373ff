/**
 * 稳定性监控脚本
 * 持续监控系统健康状态和性能指标
 */

console.log('🔍 启动系统稳定性监控...\n');

const fs = require('fs');
const path = require('path');

// 监控配置
const MONITORING_CONFIG = {
  checkInterval: 5000,        // 5秒检查一次
  maxMemoryIncrease: 50,      // 最大内存增长MB
  maxErrorRate: 0.1,          // 最大错误率10%
  enableContinuousMonitoring: false  // 默认关闭持续监控
};

// 系统健康指标
const healthMetrics = {
  systemStartTime: Date.now(),
  totalChecks: 0,
  passedChecks: 0,
  failedChecks: 0,
  memoryBaseline: null,
  lastCheckTime: null,
  errors: []
};

/**
 * 执行单次健康检查
 */
function performHealthCheck() {
  healthMetrics.totalChecks++;
  healthMetrics.lastCheckTime = Date.now();
  
  console.log(`\n🔍 健康检查 #${healthMetrics.totalChecks} - ${new Date().toLocaleTimeString()}`);
  console.log('=' .repeat(50));
  
  try {
    // 1. 内存检查
    checkMemoryUsage();
    
    // 2. 模块可用性检查
    checkModuleAvailability();
    
    // 3. 系统响应时间检查
    checkSystemResponseTime();
    
    healthMetrics.passedChecks++;
    console.log('✅ 健康检查通过');
    
  } catch (error) {
    healthMetrics.failedChecks++;
    healthMetrics.errors.push({
      timestamp: Date.now(),
      error: error.message
    });
    console.error('❌ 健康检查失败:', error.message);
  }
  
  // 输出健康汇总
  printHealthSummary();
}

/**
 * 内存使用检查
 */
function checkMemoryUsage() {
  const memUsage = process.memoryUsage();
  const currentMemoryMB = Math.round(memUsage.heapUsed / 1024 / 1024);
  
  if (!healthMetrics.memoryBaseline) {
    healthMetrics.memoryBaseline = currentMemoryMB;
  }
  
  const memoryIncrease = currentMemoryMB - healthMetrics.memoryBaseline;
  
  console.log(`💾 内存使用: ${currentMemoryMB}MB (基线: ${healthMetrics.memoryBaseline}MB, 增长: +${memoryIncrease}MB)`);
  
  if (memoryIncrease > MONITORING_CONFIG.maxMemoryIncrease) {
    throw new Error(`内存增长过大: +${memoryIncrease}MB (超过${MONITORING_CONFIG.maxMemoryIncrease}MB限制)`);
  }
}

/**
 * 模块可用性检查
 */
function checkModuleAvailability() {
  const criticalModules = [
    '../js/game/state-management/game-state-manager.js',
    '../js/game/scoring/score-manager.js',
    '../js/game/rendering/animation-manager.js'
  ];
  
  let availableModules = 0;
  
  console.log('📦 关键模块检查:');
  
  criticalModules.forEach(modulePath => {
    try {
      delete require.cache[require.resolve(modulePath)]; // 清除缓存
      require(modulePath);
      console.log(`   ✅ ${path.basename(modulePath)}`);
      availableModules++;
    } catch (error) {
      console.log(`   ❌ ${path.basename(modulePath)}: ${error.message}`);
    }
  });
  
  const availability = availableModules / criticalModules.length;
  console.log(`📊 模块可用性: ${(availability * 100).toFixed(1)}% (${availableModules}/${criticalModules.length})`);
  
  if (availability < 0.8) {
    throw new Error(`关键模块可用性过低: ${(availability * 100).toFixed(1)}%`);
  }
}

/**
 * 系统响应时间检查
 */
function checkSystemResponseTime() {
  const startTime = Date.now();
  
  // 模拟系统操作
  try {
    const { GameStateManager } = require('../js/game/state-management/game-state-manager.js');
    const stateManager = new GameStateManager();
    stateManager.getCurrentState();
  } catch (error) {
    // 忽略模块错误，只关注响应时间
  }
  
  const responseTime = Date.now() - startTime;
  console.log(`⚡ 系统响应时间: ${responseTime}ms`);
  
  if (responseTime > 1000) {
    throw new Error(`系统响应时间过长: ${responseTime}ms`);
  }
}

/**
 * 输出健康汇总
 */
function printHealthSummary() {
  const uptime = Date.now() - healthMetrics.systemStartTime;
  const errorRate = healthMetrics.failedChecks / healthMetrics.totalChecks;
  
  console.log('\n📊 系统健康汇总:');
  console.log(`   运行时间: ${Math.round(uptime / 1000)}秒`);
  console.log(`   总检查数: ${healthMetrics.totalChecks}`);
  console.log(`   成功率: ${((healthMetrics.passedChecks / healthMetrics.totalChecks) * 100).toFixed(1)}%`);
  console.log(`   错误率: ${(errorRate * 100).toFixed(1)}%`);
  
  if (errorRate > MONITORING_CONFIG.maxErrorRate) {
    console.warn(`⚠️  警告: 错误率过高 (${(errorRate * 100).toFixed(1)}%)`);
  }
  
  if (healthMetrics.errors.length > 0) {
    console.log('\n🔍 最近错误:');
    healthMetrics.errors.slice(-3).forEach((error, index) => {
      const time = new Date(error.timestamp).toLocaleTimeString();
      console.log(`   ${index + 1}. [${time}] ${error.error}`);
    });
  }
}

/**
 * 持续监控模式
 */
function startContinuousMonitoring() {
  console.log(`🔄 启动持续监控模式 (间隔: ${MONITORING_CONFIG.checkInterval}ms)`);
  console.log('按 Ctrl+C 停止监控\n');
  
  // 立即执行一次检查
  performHealthCheck();
  
  // 设置定时检查
  const monitoringInterval = setInterval(() => {
    performHealthCheck();
  }, MONITORING_CONFIG.checkInterval);
  
  // 优雅关闭
  process.on('SIGINT', () => {
    console.log('\n\n🛑 停止监控...');
    clearInterval(monitoringInterval);
    
    console.log('\n📊 最终健康报告:');
    printHealthSummary();
    
    console.log('\n💡 监控建议:');
    if (healthMetrics.failedChecks === 0) {
      console.log('   ✅ 系统运行稳定，无异常发现');
    } else {
      console.log('   ⚠️  发现一些问题，建议查看错误日志');
    }
    
    process.exit(0);
  });
}

/**
 * 生成稳定性报告
 */
function generateStabilityReport() {
  const reportData = {
    timestamp: new Date().toISOString(),
    uptime: Date.now() - healthMetrics.systemStartTime,
    metrics: healthMetrics,
    systemInfo: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memory: process.memoryUsage()
    }
  };
  
  const reportPath = 'scripts/stability-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  console.log(`📊 稳定性报告已保存到: ${reportPath}`);
}

// 主程序
console.log('🎯 稳定性监控选项:');
console.log('1. 单次健康检查');
console.log('2. 持续监控模式');
console.log('3. 生成稳定性报告');

// 检查命令行参数
const args = process.argv.slice(2);
const mode = args[0] || 'single';

switch (mode) {
  case 'continuous':
    MONITORING_CONFIG.enableContinuousMonitoring = true;
    startContinuousMonitoring();
    break;
    
  case 'report':
    performHealthCheck();
    generateStabilityReport();
    break;
    
  case 'single':
  default:
    console.log('\n▶️  执行单次健康检查...');
    performHealthCheck();
    console.log('\n💡 使用方法:');
    console.log('   node scripts/stability-monitor.js single      # 单次检查');
    console.log('   node scripts/stability-monitor.js continuous # 持续监控');
    console.log('   node scripts/stability-monitor.js report     # 生成报告');
    break;
} 