#!/usr/bin/env python3
# -*- coding: utf-8 -*-

content = '''export const GRID_COLS = 10;
export const GRID_ROWS = 20;

export default class Grid {
  constructor(rows = 20, cols = 10) {
    this.rows = rows;
    this.cols = cols;
    this.blocks = Array(rows).fill().map(() => Array(cols).fill(null));
    console.log(`🎮 网格系统已初始化: ${rows}x${cols}`);
  }
  
  isValidPosition(x, y) {
    return x >= 0 && x < this.cols && y >= 0 && y < this.rows;
  }
  
  addTetromino(tetromino) {
    if (!tetromino) return false;
    console.log('🔲 添加方块到网格');
    return true;
  }
  
  removeFullRows() {
    const removedRows = [];
    for (let row = 0; row < this.rows; row++) {
      let isFullLine = true;
      for (let col = 0; col < this.cols; col++) {
        if (!this.blocks[row][col]) {
          isFullLine = false;
          break;
        }
      }
      if (isFullLine) {
        removedRows.push(row);
      }
    }
    return removedRows;
  }
  
  render() {
    // console.log('🎨 渲染网格');
  }
  
  clear() {
    this.blocks = Array(this.rows).fill().map(() => Array(this.cols).fill(null));
    console.log('🧹 网格已清空');
  }
  
  getBlock(x, y) {
    if (!this.isValidPosition(x, y)) return null;
    return this.blocks[y][x];
  }
  
  setBlock(x, y, block) {
    if (!this.isValidPosition(x, y)) return false;
    this.blocks[y][x] = block;
    return true;
  }

  /**
   * 移除指定位置的方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {Block|null} 被移除的方块，如果没有方块则返回null
   */
  removeBlock(row, col) {
    if (!this.isValidPosition(col, row)) {
      return null;
    }
    
    const removedBlock = this.blocks[row][col];
    this.blocks[row][col] = null;
    
    if (removedBlock) {
      console.log(`🗑️ 移除方块: (${row}, ${col})`);
    }
    
    return removedBlock;
  }

  /**
   * 在指定位置放置方块
   * @param {Block} block - 要放置的方块
   * @param {number} row - 行索引  
   * @param {number} col - 列索引
   * @returns {boolean} 是否成功放置
   */
  placeBlock(block, row, col) {
    if (!this.isValidPosition(col, row)) {
      console.warn(`❌ 无效位置: (${row}, ${col})`);
      return false;
    }
    
    if (this.blocks[row][col] !== null) {
      console.warn(`❌ 位置已被占用: (${row}, ${col})`);
      return false;
    }
    
    this.blocks[row][col] = block;
    console.log(`✅ 放置方块: (${row}, ${col})`);
    return true;
  }

  /**
   * 检查指定位置是否有方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {boolean} 是否有方块
   */
  hasBlock(row, col) {
    if (!this.isValidPosition(col, row)) {
      return false;
    }
    return this.blocks[row][col] !== null;
  }

  /**
   * 获取所有非空方块的位置和信息
   * @returns {Array} 方块信息数组
   */
  getAllBlocks() {
    const blocks = [];
    for (let row = 0; row < this.rows; row++) {
      for (let col = 0; col < this.cols; col++) {
        if (this.blocks[row][col]) {
          blocks.push({
            row,
            col,
            block: this.blocks[row][col]
          });
        }
      }
    }
    return blocks;
  }

  /**
   * 检查网格是否已满
   * @returns {boolean} 网格是否已满
   */
  isFull() {
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[0][col] !== null) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取指定行的所有方块
   * @param {number} row - 行索引
   * @returns {Array} 该行的方块数组
   */
  getRow(row) {
    if (row < 0 || row >= this.rows) {
      return [];
    }
    return [...this.blocks[row]];
  }

  /**
   * 检查指定行是否为满行
   * @param {number} row - 行索引
   * @returns {boolean} 是否为满行
   */
  isFullRow(row) {
    if (row < 0 || row >= this.rows) {
      return false;
    }
    
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[row][col] === null) {
        return false;
      }
    }
    return true;
  }
}'''

with open('js/game/grid.js', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ Grid.js 方法已补全完成') 