/**
 * 快速可用性测试脚本
 * 验证修复后的代码能否正常加载和运行
 */

console.log('🚀 开始快速可用性测试...\n');

// 模拟微信小游戏环境
global.wx = {
  getSystemInfoSync: () => ({ platform: 'devtools', screenWidth: 375, screenHeight: 667 }),
  createCanvas: () => ({
    width: 375, height: 667,
    getContext: () => ({ scale: () => {}, clearRect: () => {}, fillRect: () => {} })
  }),
  getStorageSync: () => null,
  setStorageSync: () => {}
};

global.window = { devicePixelRatio: 1, location: { search: '' } };
global.performance = { now: () => Date.now() };

const results = { total: 0, passed: 0, failed: 0, errors: [] };

function test(name, testFn) {
  results.total++;
  try {
    console.log(`🧪 测试: ${name}`);
    testFn();
    console.log(`✅ 通过: ${name}`);
    results.passed++;
  } catch (error) {
    console.error(`❌ 失败: ${name} - ${error.message}`);
    results.failed++;
    results.errors.push({ test: name, error: error.message });
  }
}

// 清理require缓存
function clearRequireCache() {
  Object.keys(require.cache).forEach(key => {
    if (key.includes('/js/')) {
      delete require.cache[key];
    }
  });
}

// 测试 1: 核心模块加载
test('Block模块加载', () => {
  clearRequireCache();
  const blockModule = require('../js/game/block.js');
  if (!blockModule || !blockModule.default) throw new Error('Block模块加载失败');
  
  const Block = blockModule.default;
  const block = new Block('#FF0000');
  if (!block || !block.color) throw new Error('Block实例创建失败');
  
  console.log('   Block模块加载成功');
});

test('Grid模块加载', () => {
  clearRequireCache();
  const gridModule = require('../js/game/grid.js');
  if (!gridModule || !gridModule.default) throw new Error('Grid模块加载失败');
  
  const Grid = gridModule.default;
  const grid = new Grid(20, 10);
  if (!grid || !grid.isValidPosition) throw new Error('Grid实例创建失败');
  
  console.log('   Grid模块加载成功');
});

test('Tetromino模块加载', () => {
  clearRequireCache();
  const tetrominoModule = require('../js/game/tetromino.js');
  if (!tetrominoModule || !tetrominoModule.default) throw new Error('Tetromino模块加载失败');
  
  const Tetromino = tetrominoModule.default;
  const tetromino = new Tetromino('I');
  if (!tetromino || !tetromino.shape) throw new Error('Tetromino实例创建失败');
  
  console.log('   Tetromino模块加载成功');
});

// 测试 2: 重构后的核心系统
test('状态管理系统加载', () => {
  clearRequireCache();
  const stateModule = require('../js/game/state-management/game-state-manager.js');
  if (!stateModule?.GameStateManager) throw new Error('GameStateManager加载失败');
  
  const stateManager = new stateModule.GameStateManager();
  if (!stateManager.getCurrentState) throw new Error('GameStateManager方法缺失');
  
  console.log('   状态管理系统加载成功');
});

test('分数系统加载', () => {
  clearRequireCache();
  const scoreModule = require('../js/game/scoring/score-manager.js');
  const comboModule = require('../js/game/scoring/combo-manager.js');
  
  if (!scoreModule?.ScoreManager) throw new Error('ScoreManager加载失败');
  if (!comboModule?.ComboManager) throw new Error('ComboManager加载失败');
  
  const scoreManager = new scoreModule.ScoreManager();
  const comboManager = new comboModule.ComboManager();
  
  if (!scoreManager.addScore) throw new Error('ScoreManager方法缺失');
  if (!comboManager.calculateMultiplier) throw new Error('ComboManager.calculateMultiplier方法缺失');
  
  console.log('   分数系统加载成功');
});

test('物理系统加载', () => {
  clearRequireCache();
  const physicsModule = require('../js/game/physics/physics-engine.js');
  const collisionModule = require('../js/game/physics/collision-detector.js');
  
  if (!physicsModule?.PhysicsEngine) throw new Error('PhysicsEngine加载失败');
  if (!collisionModule?.CollisionDetector) throw new Error('CollisionDetector加载失败');
  
  const mockGrid = { rows: 20, cols: 10, blocks: [] };
  const physicsEngine = new physicsModule.PhysicsEngine(mockGrid);
  const collisionDetector = new collisionModule.CollisionDetector(mockGrid);
  
  if (!physicsEngine.update) throw new Error('PhysicsEngine方法缺失');
  if (!collisionDetector.checkCollision) throw new Error('CollisionDetector方法缺失');
  
  console.log('   物理系统加载成功');
});

test('渲染系统加载', () => {
  clearRequireCache();
  const animModule = require('../js/game/rendering/animation-manager.js');
  
  if (!animModule?.AnimationManager) throw new Error('AnimationManager加载失败');
  
  const animationManager = new animModule.AnimationManager();
  if (!animationManager.createAnimation) throw new Error('AnimationManager方法缺失');
  
  console.log('   渲染系统加载成功');
});

test('匹配系统加载', () => {
  clearRequireCache();
  const matchModule = require('../js/game/matching/match-engine.js');
  const effectModule = require('../js/game/matching/effect-processor.js');
  
  if (!matchModule?.MatchEngine) throw new Error('MatchEngine加载失败');
  if (!effectModule?.EffectProcessor) throw new Error('EffectProcessor加载失败');
  
  const mockGrid = { rows: 20, cols: 10, blocks: [], removeFullRows: () => [] };
  const matchEngine = new matchModule.MatchEngine(mockGrid);
  const effectProcessor = new effectModule.EffectProcessor();
  
  if (!matchEngine.checkMatches) throw new Error('MatchEngine方法缺失');
  if (!effectProcessor.processEffect) throw new Error('EffectProcessor方法缺失');
  
  console.log('   匹配系统加载成功');
});

// 测试 3: 基础功能验证
test('基础功能集成测试', () => {
  clearRequireCache();
  
  // 创建基础组件
  const Block = require('../js/game/block.js').default;
  const Grid = require('../js/game/grid.js').default;
  const Tetromino = require('../js/game/tetromino.js').default;
  
  // 创建实例
  const grid = new Grid(20, 10);
  const tetromino = new Tetromino('I');
  const block = new Block('#FF0000');
  
  // 测试基础交互
  const isValid = grid.isValidPosition(5, 5);
  const positions = tetromino.getBlockPositions();
  
  if (!isValid) throw new Error('Grid位置验证失败');
  if (!positions || positions.length === 0) throw new Error('Tetromino位置获取失败');
  
  console.log('   基础功能集成测试通过');
});

// 输出测试结果
setTimeout(() => {
  console.log('\n' + '='.repeat(60));
  console.log('🚀 快速可用性测试结果');
  console.log('='.repeat(60));
  
  const successRate = (results.passed / results.total * 100).toFixed(1);
  console.log(`总测试数: ${results.total}`);
  console.log(`✅ 通过: ${results.passed}`);
  console.log(`❌ 失败: ${results.failed}`);
  console.log(`成功率: ${successRate}%`);
  
  if (results.failed > 0) {
    console.log('\n❌ 失败详情:');
    results.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  console.log('\n🎯 可用性评估:');
  if (successRate >= 95) {
    console.log('🎉 优秀！代码完全可用，可以在微信开发者工具中测试');
  } else if (successRate >= 80) {
    console.log('✨ 良好！大部分功能可用，建议检查失败项');
  } else {
    console.log('⚠️  需要进一步修复再测试');
  }
  
  console.log('\n🚀 下一步建议:');
  console.log('1. 在微信开发者工具中编译测试');
  console.log('2. 检查控制台是否还有错误');
  console.log('3. 测试基础游戏功能');
}, 100); 