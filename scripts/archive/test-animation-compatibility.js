#!/usr/bin/env node

/**
 * 动画兼容性测试
 * 确保Grid类的动画功能与controller.js兼容
 */

const path = require('path');
const fs = require('fs');

// 模拟微信小程序环境
global.console = console;

// 加载模块
function loadModule(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    if (fs.existsSync(fullPath)) {
      delete require.cache[fullPath];
      return require(fullPath);
    }
    throw new Error(`文件不存在: ${fullPath}`);
  } catch (error) {
    console.error(`❌ 无法加载模块 ${filePath}:`, error.message);
    return null;
  }
}

// 测试函数
function runTest(testName, testFn) {
  try {
    console.log(`🧪 测试: ${testName}`);
    const result = testFn();
    if (result !== false) {
      console.log(`✅ 通过: ${testName}`);
      return true;
    } else {
      console.log(`❌ 失败: ${testName}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 失败: ${testName} - ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 开始动画兼容性测试...\n');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // 测试1: Grid模块加载
  totalTests++;
  const gridModule = loadModule('./js/game/grid.js');
  if (!gridModule || !gridModule.default) {
    console.log('❌ Grid模块加载失败');
    return;
  }
  
  const Grid = gridModule.default;
  
  if (runTest('Grid模块加载', () => {
    console.log(`   Grid类: ${typeof Grid}`);
    return typeof Grid === 'function';
  })) {
    passedTests++;
  }
  
  // 创建Grid实例
  const grid = new Grid(20, 10);
  
  // 测试2: 动画属性存在性
  totalTests++;
  if (runTest('动画属性存在性', () => {
    const hasAnimations = Array.isArray(grid.animations);
    const hasUpdateAnimations = typeof grid.updateAnimations === 'function';
    
    console.log(`   animations: ${typeof grid.animations} (${hasAnimations ? '数组' : '非数组'})`);
    console.log(`   updateAnimations: ${typeof grid.updateAnimations}`);
    console.log(`   初始动画数量: ${grid.animations.length}`);
    
    return hasAnimations && hasUpdateAnimations;
  })) {
    passedTests++;
  }
  
  // 测试3: controller.js第1889行兼容性 - grid.animations.length
  totalTests++;
  if (runTest('animations.length兼容性', () => {
    // 模拟controller.js第1889行的调用
    const animationCount = grid.animations.length;
    
    if (typeof animationCount !== 'number') {
      console.log('   ❌ animations.length应该返回数字');
      return false;
    }
    
    if (animationCount !== 0) {
      console.log(`   ❌ 初始动画数量应该为0，实际为${animationCount}`);
      return false;
    }
    
    console.log(`   ✅ animations.length = ${animationCount} (正确)`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试4: controller.js第1896行兼容性 - grid.updateAnimations()
  totalTests++;
  if (runTest('updateAnimations()兼容性', () => {
    // 模拟controller.js第1896行的调用
    const hasAnimations = grid.updateAnimations();
    
    if (typeof hasAnimations !== 'boolean') {
      console.log('   ❌ updateAnimations()应该返回布尔值');
      return false;
    }
    
    if (hasAnimations !== false) {
      console.log('   ❌ 无动画时应该返回false');
      return false;
    }
    
    console.log(`   ✅ updateAnimations() = ${hasAnimations} (正确)`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试5: 添加动画后的行为
  totalTests++;
  if (runTest('添加动画后的行为', () => {
    // 添加一个测试动画
    const animationId = grid.addAnimation({
      type: 'test',
      duration: 500,
      onUpdate: (progress) => {
        // 动画更新回调
      },
      onComplete: () => {
        console.log('   动画完成回调执行');
      }
    });
    
    // 检查动画数量
    if (grid.animations.length !== 1) {
      console.log(`   ❌ 添加动画后数量应该为1，实际为${grid.animations.length}`);
      return false;
    }
    
    // 检查updateAnimations返回值
    const hasAnimations = grid.updateAnimations();
    if (!hasAnimations) {
      console.log('   ❌ 有动画时updateAnimations应该返回true');
      return false;
    }
    
    console.log(`   ✅ 动画添加和更新正常 (ID: ${animationId})`);
    return true;
  })) {
    passedTests++;
  }
  
  // 测试6: 模拟完整的controller.js使用场景
  totalTests++;
  if (runTest('完整controller.js场景模拟', () => {
    try {
      // 模拟controller.js update方法中的代码片段
      
      // 第1889行: if (this.grid.animations.length > 0)
      if (grid.animations.length > 0) {
        console.log(`   模拟第1889行: 检测到${grid.animations.length}个动画`);
        
        // 第1896行: const animationsComplete = !this.grid.updateAnimations();
        const animationsComplete = !grid.updateAnimations();
        console.log(`   模拟第1896行: animationsComplete = ${animationsComplete}`);
        
        // 检查动画是否完成的逻辑
        if (animationsComplete) {
          console.log('   动画已完成，可以继续后续逻辑');
        } else {
          console.log('   动画仍在进行，等待下一帧');
        }
      } else {
        console.log('   模拟: 没有动画在进行');
      }
      
      // 第2150行: if (this.grid.animations.length > 0)
      const finalCheck = grid.animations.length > 0;
      console.log(`   模拟第2150行: 最终动画检查 = ${finalCheck}`);
      
      console.log('   ✅ 完整场景模拟成功');
      return true;
    } catch (error) {
      console.log(`   ❌ 场景模拟失败: ${error.message}`);
      return false;
    }
  })) {
    passedTests++;
  }
  
  // 测试7: 动画清理测试
  totalTests++;
  if (runTest('动画清理测试', () => {
    // 清除所有动画
    grid.clearAnimations();
    
    if (grid.animations.length !== 0) {
      console.log(`   ❌ 清理后动画数量应该为0，实际为${grid.animations.length}`);
      return false;
    }
    
    // 再次检查updateAnimations
    const hasAnimations = grid.updateAnimations();
    if (hasAnimations) {
      console.log('   ❌ 清理后updateAnimations应该返回false');
      return false;
    }
    
    console.log('   ✅ 动画清理功能正常');
    return true;
  })) {
    passedTests++;
  }
  
  // 测试8: 异常情况处理
  totalTests++;
  if (runTest('异常情况处理', () => {
    // 测试无效动画添加
    try {
      grid.addAnimation(null);
      console.log('   ⚠️  添加null动画不应该抛出异常');
    } catch (error) {
      console.log(`   ❌ 添加null动画抛出异常: ${error.message}`);
      return false;
    }
    
    // 测试多次updateAnimations调用
    grid.updateAnimations();
    grid.updateAnimations();
    
    console.log('   ✅ 异常情况处理正常');
    return true;
  })) {
    passedTests++;
  }
  
  // 输出测试结果
  console.log('\n============================================================');
  console.log('🚀 动画兼容性测试结果');
  console.log('============================================================');
  console.log(`总测试数: ${totalTests}`);
  console.log(`✅ 通过: ${passedTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有动画兼容性测试通过！');
    console.log('\n✅ 可以解决以下错误:');
    console.log('   - TypeError: Cannot read property \'length\' of undefined');
    console.log('   - controller.js:1889 动画长度检查错误');
    console.log('   - controller.js:1896 updateAnimations调用错误');
    console.log('\n🚀 现在可以在微信开发者工具中正常运行！');
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步修复');
  }
}

main().catch(console.error); 