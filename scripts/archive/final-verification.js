/**
 * 最终验证脚本 - 针对修复后的系统
 */

console.log('🏁 最终验证开始...');

// 环境设置
global.wx = {
  getSystemInfoSync: () => ({ platform: 'devtools', screenWidth: 375, screenHeight: 667 }),
  createCanvas: () => ({
    width: 375, height: 667,
    getContext: () => ({ scale: () => {}, clearRect: () => {}, fillRect: () => {}, drawImage: () => {} })
  })
};

global.window = { devicePixelRatio: 1, location: { search: '' } };
global.performance = { now: () => Date.now() };

const results = { total: 0, passed: 0, failed: 0, errors: [] };

function test(name, testFn) {
  results.total++;
  try {
    console.log(`🧪 测试: ${name}`);
    testFn();
    console.log(`✅ 通过: ${name}`);
    results.passed++;
  } catch (error) {
    console.error(`❌ 失败: ${name} - ${error.message}`);
    results.failed++;
    results.errors.push({ test: name, error: error.message });
  }
}

// 清除模块缓存
function clearModuleCache() {
  Object.keys(require.cache).forEach(key => {
    if (key.includes('/js/game/')) {
      delete require.cache[key];
    }
  });
}

// 测试 1: 状态管理系统 - 修复后测试
test('状态管理系统（修复后）', () => {
  clearModuleCache();
  const stateModule = require('../js/game/state-management/game-state-manager.js');
  const flowModule = require('../js/game/state-management/game-flow-manager.js');
  
  if (!stateModule?.GameStateManager) throw new Error('GameStateManager 加载失败');
  if (!flowModule?.GameFlowManager) throw new Error('GameFlowManager 加载失败');
  
  const stateManager = new stateModule.GameStateManager();
  const mockController = { stateManager };
  const flowManager = new flowModule.GameFlowManager(mockController, stateManager);
  
  // 测试游戏启动（应该成功）
  const startResult = flowManager.start({ level: 1 });
  if (!startResult) throw new Error('游戏启动仍然失败');
  
  console.log('   状态管理系统修复成功');
});

// 测试 2: 分数系统 - 修复后测试
test('分数系统（修复后）', () => {
  clearModuleCache();
  const scoreModule = require('../js/game/scoring/score-manager.js');
  const comboModule = require('../js/game/scoring/combo-manager.js');
  
  if (!scoreModule?.ScoreManager) throw new Error('ScoreManager 加载失败');
  if (!comboModule?.ComboManager) throw new Error('ComboManager 加载失败');
  
  const scoreManager = new scoreModule.ScoreManager();
  const comboManager = new comboModule.ComboManager();
  
  // 测试分数计算
  scoreManager.addScore(100, 'line_clear');
  
  // 测试连击倍率计算（修复后应该成功）
  if (typeof comboManager.calculateMultiplier === 'function') {
    const multiplier = comboManager.calculateMultiplier(3);
    if (typeof multiplier !== 'number' || multiplier <= 0) {
      throw new Error('连击倍率计算返回值错误');
    }
  } else {
    throw new Error('calculateMultiplier 方法仍然缺失');
  }
  
  console.log('   分数系统修复成功');
});

// 测试 3: 物理系统 - 修复后测试
test('物理系统（修复后）', () => {
  clearModuleCache();
  const physicsModule = require('../js/game/physics/physics-engine.js');
  const collisionModule = require('../js/game/physics/collision-detector.js');
  
  if (!physicsModule?.PhysicsEngine) throw new Error('PhysicsEngine 加载失败');
  if (!collisionModule?.CollisionDetector) throw new Error('CollisionDetector 加载失败');
  
  const mockGrid = {
    rows: 20, cols: 10,
    blocks: Array(20).fill().map(() => Array(10).fill(null))
  };
  
  const collisionDetector = new collisionModule.CollisionDetector(mockGrid);
  
  // 测试碰撞检测方法（修复后应该存在）
  if (typeof collisionDetector.checkCollision !== 'function') {
    throw new Error('checkCollision 方法仍然缺失');
  }
  
  // 测试碰撞检测功能
  const mockTetromino = {
    x: 0, y: 0, width: 2, height: 2,
    shape: [[1, 1], [1, 1]]
  };
  
  const hasCollision = collisionDetector.checkCollision(mockTetromino, 0, 0);
  if (typeof hasCollision !== 'boolean') {
    throw new Error('碰撞检测返回值类型错误');
  }
  
  console.log('   物理系统修复成功');
});

// 测试 4: 匹配系统稳定性
test('匹配系统稳定性', () => {
  clearModuleCache();
  const matchModule = require('../js/game/matching/match-engine.js');
  const effectModule = require('../js/game/matching/effect-processor.js');
  
  const mockGrid = {
    rows: 20, cols: 10,
    blocks: Array(20).fill().map(() => Array(10).fill(null)),
    removeFullRows: () => []
  };
  
  const matchEngine = new matchModule.MatchEngine(mockGrid);
  const effectProcessor = new effectModule.EffectProcessor();
  
  // 多次执行测试稳定性
  for (let i = 0; i < 3; i++) {
    const matches = matchEngine.checkMatches();
    const effect = effectProcessor.processEffect('combo', { count: i + 1 });
    
    if (!Array.isArray(matches)) throw new Error('匹配检测不稳定');
    if (!effect || typeof effect.then !== 'function') throw new Error('特效处理不稳定');
  }
  
  console.log('   匹配系统稳定性验证通过');
});

// 测试 5: 渲染系统性能
test('渲染系统性能', () => {
  clearModuleCache();
  const animModule = require('../js/game/rendering/animation-manager.js');
  
  const animationManager = new animModule.AnimationManager();
  
  // 批量创建动画测试性能
  const animIds = [];
  for (let i = 0; i < 5; i++) {
    const animId = animationManager.createAnimation({
      type: 'slide',
      target: { x: i * 10 },
      from: { x: 0 },
      to: { x: i * 10 },
      duration: 200
    });
    animIds.push(animId);
  }
  
  if (animIds.length !== 5) throw new Error('批量动画创建失败');
  if (animIds.some(id => !id)) throw new Error('动画ID生成失败');
  
  console.log('   渲染系统性能验证通过');
});

// 测试 6: 集成系统测试
test('集成系统测试', () => {
  // 模拟完整的游戏流程
  clearModuleCache();
  
  const stateModule = require('../js/game/state-management/game-state-manager.js');
  const scoreModule = require('../js/game/scoring/score-manager.js');
  const animModule = require('../js/game/rendering/animation-manager.js');
  
  const stateManager = new stateModule.GameStateManager();
  const scoreManager = new scoreModule.ScoreManager();
  const animationManager = new animModule.AnimationManager();
  
  // 模拟游戏开始
  stateManager.setState(stateModule.GAME_STATE.PLAYING);
  
  // 模拟得分
  scoreManager.addScore(400, 'line_clear');
  
  // 模拟动画
  const animId = animationManager.createAnimation({
    type: 'score_popup',
    target: { scale: 1.2 },
    duration: 300
  });
  
  // 验证集成效果
  if (stateManager.getCurrentState() !== stateModule.GAME_STATE.PLAYING) {
    throw new Error('状态管理集成失败');
  }
  
  if (scoreManager.getCurrentScore() <= 0) {
    throw new Error('分数管理集成失败');
  }
  
  if (!animId) {
    throw new Error('动画管理集成失败');
  }
  
  console.log('   集成系统验证通过');
});

// 输出结果
setTimeout(() => {
  console.log('\n' + '='.repeat(60));
  console.log('🏁 最终验证结果汇总');
  console.log('='.repeat(60));
  
  const successRate = (results.passed / results.total * 100).toFixed(1);
  console.log(`总测试数: ${results.total}`);
  console.log(`✅ 通过: ${results.passed}`);
  console.log(`❌ 失败: ${results.failed}`);
  console.log(`🎯 最终成功率: ${successRate}%`);
  
  if (results.failed > 0) {
    console.log('\n❌ 剩余问题:');
    results.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  console.log('\n🎊 成功率等级评定:');
  if (successRate >= 95) {
    console.log('🏆 完美！验证成功率95%+，系统完全稳定！');
  } else if (successRate >= 90) {
    console.log('🥇 卓越！验证成功率90%+，系统高度稳定！');
  } else if (successRate >= 85) {
    console.log('🥈 优秀！验证成功率85%+，系统稳定可靠！');
  } else if (successRate >= 80) {
    console.log('🥉 良好！验证成功率80%+，系统基本稳定！');
  } else {
    console.log('⚠️  仍需优化，但已有显著改善');
  }
  
  const improvement = successRate - 61.9; // 从61.9%开始
  console.log(`\n📈 验证成功率提升: +${improvement.toFixed(1)}% (从61.9%)`);
  
  if (successRate >= 85) {
    console.log('\n🎉 恭喜！已达成85%+目标！');
  }
}, 100);
