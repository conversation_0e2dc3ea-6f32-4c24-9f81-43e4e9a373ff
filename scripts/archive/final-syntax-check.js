#!/usr/bin/env node

/**
 * 最终语法检查工具
 * 简单有效地验证主要文件的语法正确性
 */

const fs = require('fs');
const path = require('path');

class FinalSyntaxCheck {
  constructor() {
    this.results = [];
  }

  /**
   * 检查重要文件的语法
   */
  async checkImportantFiles() {
    const importantFiles = [
      'game.js',
      'js/main.js',
      'js/new-main.js',
      'js/render.js',
      'js/game/block.js',
      'js/game/grid.js',
      'js/game/controller.js',
      'js/game/refactored-controller.js',
      'js/core/game-engine.js',
      'js/core/scene-manager.js',
      'js/core/input-manager.js',
      'js/core/config-manager.js',
      'js/render/render-manager.js'
    ];

    console.log('🔍 开始最终语法检查...\n');

    for (const file of importantFiles) {
      await this.checkSingleFile(file);
    }

    this.printSummary();
  }

  /**
   * 检查单个文件
   */
  async checkSingleFile(filePath) {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        this.results.push({
          file: filePath,
          status: 'missing',
          message: '文件不存在'
        });
        console.log(`⚠️  ${filePath}: 文件不存在`);
        return;
      }

      // 读取并检查语法
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 基本语法检查
      this.checkBasicSyntax(content, filePath);
      
      this.results.push({
        file: filePath,
        status: 'ok',
        message: '语法正确'
      });
      console.log(`✅ ${filePath}: 语法正确`);

    } catch (error) {
      this.results.push({
        file: filePath,
        status: 'error',
        message: error.message
      });
      console.log(`❌ ${filePath}: ${error.message}`);
    }
  }

  /**
   * 基本语法检查
   */
  checkBasicSyntax(content, filePath) {
    // 检查括号匹配
    const brackets = { '(': ')', '[': ']', '{': '}' };
    const stack = [];
    
    for (let i = 0; i < content.length; i++) {
      const char = content[i];
      
      if (brackets[char]) {
        stack.push({ char: brackets[char], pos: i });
      } else if (Object.values(brackets).includes(char)) {
        if (stack.length === 0) {
          throw new Error(`位置 ${i} 处多余的 '${char}'`);
        }
        const last = stack.pop();
        if (last.char !== char) {
          throw new Error(`位置 ${i} 处括号不匹配`);
        }
      }
    }
    
    if (stack.length > 0) {
      const unclosed = stack[stack.length - 1];
      throw new Error(`位置 ${unclosed.pos} 处有未闭合的括号`);
    }

    // 检查明显的导入问题
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 检查残留的错误导入
      if (line.includes('import { b } from')) {
        throw new Error(`第 ${i + 1} 行有错误的导入语句`);
      }
    }
  }

  /**
   * 打印总结
   */
  printSummary() {
    console.log('\n📊 最终语法检查结果:');
    
    const okFiles = this.results.filter(r => r.status === 'ok');
    const errorFiles = this.results.filter(r => r.status === 'error');
    const missingFiles = this.results.filter(r => r.status === 'missing');
    
    console.log(`✅ 正确文件: ${okFiles.length}`);
    console.log(`❌ 错误文件: ${errorFiles.length}`);
    console.log(`⚠️  缺失文件: ${missingFiles.length}`);
    
    if (errorFiles.length > 0) {
      console.log('\n❌ 错误详情:');
      errorFiles.forEach(item => {
        console.log(`  📄 ${item.file}: ${item.message}`);
      });
    }
    
    if (missingFiles.length > 0) {
      console.log('\n⚠️  缺失文件:');
      missingFiles.forEach(item => {
        console.log(`  📄 ${item.file}`);
      });
    }

    if (errorFiles.length === 0 && missingFiles.length === 0) {
      console.log('\n🎉 所有重要文件语法检查通过！项目代码完整性良好！');
    }
  }
}

// 运行检查
if (require.main === module) {
  const checker = new FinalSyntaxCheck();
  checker.checkImportantFiles().catch(console.error);
}

module.exports = FinalSyntaxCheck; 