#!/usr/bin/env node

/**
 * 提取道具渲染器脚本
 * 从原始item-manager.js中提取完整的渲染实现
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 开始提取道具渲染器...\n');

// 读取原始文件
function readOriginalItemManager() {
  const filePath = 'js/item/item-manager.js';
  if (!fs.existsSync(filePath)) {
    throw new Error('原始item-manager.js文件不存在');
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  console.log(`📖 读取原始文件进行渲染器提取`);
  return content;
}

// 提取闪电渲染器
function extractLightningRenderer(originalContent) {
  console.log('⚡ 提取闪电渲染器...');
  
  // 提取闪电相关的渲染方法
  const lightningRenderMethods = [
    '_createLightningEffect',
    '_generateLightningZigzag',
    '_convertPathToScreenCoordinates',
    '_getGridBounds',
    '_renderLightningEffect'
  ];
  
  let extractedMethods = '';
  
  lightningRenderMethods.forEach(methodName => {
    const methodRegex = new RegExp(
      `\\/\\*\\*[\\s\\S]*?${methodName}[\\s\\S]*?\\*\\/[\\s\\S]*?${methodName}\\([\\s\\S]*?(?=\\n  \\/\\*\\*|\\n  _[a-zA-Z]|\\n  [a-zA-Z]|\\nclass|\\nexport)`,
      'g'
    );
    
    const methodMatch = originalContent.match(methodRegex);
    if (methodMatch) {
      extractedMethods += '\n\n' + methodMatch[0];
      console.log(`  ✅ 提取渲染方法: ${methodName}`);
    }
  });
  
  return _createLightningRendererClass(extractedMethods);
}

// 创建闪电渲染器类
function _createLightningRendererClass(extractedMethods) {
  const rendererCode = `/**
 * 闪电效果渲染器
 * 从原始ItemManager提取并适配到新架构
 */
export default class LightningRenderer {
  constructor() {
    this.activeEffects = [];
  }

  /**
   * 创建闪电效果
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {Array} chainPath - 闪电链路径
   */
  createLightningEffect(startRow, startCol, chainPath) {
    const effect = this._createLightningEffectData(chainPath);
    this.activeEffects.push(effect);
    return effect;
  }

  /**
   * 渲染闪电效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} effect - 效果对象
   * @returns {boolean} 是否仍在活跃
   */
  render(ctx, effect) {
    if (!effect || !effect.isActive) {
      return false;
    }

    try {
      this._renderLightningEffect(ctx, effect);
      
      // 更新效果状态
      effect.timer = (effect.timer || 0) + 1;
      if (effect.timer >= (effect.duration || 30)) {
        effect.isActive = false;
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('闪电效果渲染错误:', error);
      return false;
    }
  }

  /**
   * 清理已完成的效果
   */
  cleanup() {
    this.activeEffects = this.activeEffects.filter(effect => effect.isActive);
  }

  // 以下是从原始实现中提取的方法
${_adaptLightningRenderMethods(extractedMethods)}
}`;

  return rendererCode;
}

// 适配闪电渲染方法
function _adaptLightningRenderMethods(extractedMethods) {
  return extractedMethods
    // 移除原始类的this引用，适配到新类
    .replace(/this\.grid\./g, 'this._getGridReference().')
    // 适配方法调用
    .replace(/this\._/g, 'this._')
    // 移除不需要的导入和常量引用
    .replace(/ITEM_TYPES\./g, '')
    // 清理格式
    .replace(/\n\s*\n\s*\n/g, '\n\n');
}

// 提取通用效果渲染器
function extractEffectRenderer(originalContent) {
  console.log('🌊🌍 提取通用效果渲染器...');
  
  // 提取水流和地震相关的渲染方法
  const effectRenderMethods = [
    '_createWaterflowEffect',
    '_renderWaterflowEffect',
    '_createEarthquakeEffect',
    '_renderEarthquakeEffect',
    '_createEarthquakeParticles',
    '_updateAndRenderEarthquakeParticles',
    '_renderEarthquakeHighlight',
    '_renderEarthquakeWaves'
  ];
  
  let extractedMethods = '';
  
  effectRenderMethods.forEach(methodName => {
    const methodRegex = new RegExp(
      `\\/\\*\\*[\\s\\S]*?${methodName}[\\s\\S]*?\\*\\/[\\s\\S]*?${methodName}\\([\\s\\S]*?(?=\\n  \\/\\*\\*|\\n  _[a-zA-Z]|\\n  [a-zA-Z]|\\nclass|\\nexport)`,
      'g'
    );
    
    const methodMatch = originalContent.match(methodRegex);
    if (methodMatch) {
      extractedMethods += '\n\n' + methodMatch[0];
      console.log(`  ✅ 提取渲染方法: ${methodName}`);
    }
  });
  
  return _createEffectRendererClass(extractedMethods);
}

// 创建通用效果渲染器类
function _createEffectRendererClass(extractedMethods) {
  const rendererCode = `/**
 * 通用效果渲染器
 * 从原始ItemManager提取，负责水流、地震等效果渲染
 */
export default class EffectRenderer {
  constructor() {
    this.activeEffects = [];
  }

  /**
   * 创建水流效果
   * @param {number} row - 中心行
   * @param {number} col - 中心列
   * @param {number} level - 效果等级
   */
  createWaterflowEffect(row, col, level) {
    const effect = this._createWaterflowEffectData(row, col, level);
    this.activeEffects.push(effect);
    return effect;
  }

  /**
   * 创建地震效果
   * @param {number} startRow - 起始行
   * @param {number} endRow - 结束行
   * @param {number} level - 效果等级
   */
  createEarthquakeEffect(startRow, endRow, level) {
    const effect = this._createEarthquakeEffectData(startRow, endRow, level);
    this.activeEffects.push(effect);
    return effect;
  }

  /**
   * 渲染所有效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderAll(ctx) {
    this.activeEffects = this.activeEffects.filter(effect => {
      return this.renderEffect(ctx, effect);
    });
  }

  /**
   * 渲染单个效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} effect - 效果对象
   * @returns {boolean} 是否仍在活跃
   */
  renderEffect(ctx, effect) {
    if (!effect || !effect.isActive) {
      return false;
    }

    try {
      switch (effect.type) {
        case 'waterflow':
          this._renderWaterflowEffect(ctx, effect);
          break;
        case 'earthquake':
          this._renderEarthquakeEffect(ctx, effect);
          break;
      }
      
      // 更新效果状态
      effect.timer = (effect.timer || 0) + 1;
      if (effect.timer >= (effect.duration || 60)) {
        effect.isActive = false;
        return false;
      }
      
      return true;
    } catch (error) {
      console.error(\`\${effect.type}效果渲染错误:\`, error);
      return false;
    }
  }

  /**
   * 清理已完成的效果
   */
  cleanup() {
    this.activeEffects = this.activeEffects.filter(effect => effect.isActive);
  }

  // 以下是从原始实现中提取的方法
${_adaptEffectRenderMethods(extractedMethods)}
}`;

  return rendererCode;
}

// 适配效果渲染方法
function _adaptEffectRenderMethods(extractedMethods) {
  return extractedMethods
    // 移除原始类的this引用，适配到新类
    .replace(/this\.grid\./g, 'this._getGridReference().')
    // 适配方法调用
    .replace(/this\._/g, 'this._')
    // 移除不需要的导入和常量引用
    .replace(/ITEM_TYPES\./g, '')
    // 清理格式
    .replace(/\n\s*\n\s*\n/g, '\n\n');
}

// 主执行函数
async function main() {
  try {
    // 1. 读取原始文件
    const originalContent = readOriginalItemManager();
    
    // 2. 确保目录存在
    const animationsDir = 'js/item/animations';
    if (!fs.existsSync(animationsDir)) {
      fs.mkdirSync(animationsDir, { recursive: true });
    }
    
    // 3. 提取并创建闪电渲染器
    console.log('\n⚡ 处理闪电渲染器...');
    const lightningRendererCode = extractLightningRenderer(originalContent);
    fs.writeFileSync('js/item/animations/lightning-renderer.js', lightningRendererCode);
    console.log('✅ 创建 lightning-renderer.js');
    
    // 4. 提取并创建通用效果渲染器
    console.log('\n🎨 处理通用效果渲染器...');
    const effectRendererCode = extractEffectRenderer(originalContent);
    fs.writeFileSync('js/item/animations/effect-renderer.js', effectRendererCode);
    console.log('✅ 创建 effect-renderer.js');
    
    console.log('\n🎉 所有渲染器提取完成！');
    console.log('\n📋 下一步:');
    console.log('1. 运行 node scripts/update-refactored-manager.js 更新管理器');
    console.log('2. 运行 node scripts/migrate-to-new-architecture.js 完成迁移');
    console.log('3. 测试所有道具和渲染效果');
    
  } catch (error) {
    console.error('❌ 提取过程中出错:', error);
    process.exit(1);
  }
}

// 执行主流程
main();
