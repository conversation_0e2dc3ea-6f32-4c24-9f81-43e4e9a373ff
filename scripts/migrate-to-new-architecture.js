#!/usr/bin/env node

/**
 * 最终迁移脚本
 * 完成从原始ItemManager到新架构的完全迁移
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始最终迁移...');

const filesToUpdate = [
  'js/game/controller.js',
  'js/runtime/gameinfo.js',
  'js/level/level-manager.js',
  'js/main.js'
];

function updateFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⏸️  文件不存在，跳过: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;

  // 替换导入语句
  if (content.includes("from './item/item-manager.js'") || 
      content.includes("from '../item/item-manager.js'")) {
    
    // 根据文件路径调整导入路径
    const relativePath = filePath.includes('js/game/') ? '../item/refactored-item-manager.js' :
                         filePath.includes('js/runtime/') ? '../item/refactored-item-manager.js' :
                         filePath.includes('js/level/') ? '../item/refactored-item-manager.js' :
                         './item/refactored-item-manager.js';
    
    content = content.replace(
      /from ['"]\.\.?\/item\/item-manager\.js['"]/g,
      `from '${relativePath}'`
    );
    updated = true;
  }

  // 替换类名引用
  if (content.includes('new ItemManager(')) {
    content = content.replace(/new ItemManager\(/g, 'new RefactoredItemManager(');
    updated = true;
  }

  // 替换类名导入
  if (content.includes('import ItemManager')) {
    content = content.replace(/import ItemManager/g, 'import RefactoredItemManager');
    updated = true;
  }

  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ 已更新: ${filePath}`);
  } else {
    console.log(`⏸️  无需更新: ${filePath}`);
  }
}

function backupOriginalFile() {
  const originalPath = 'js/item/item-manager.js';
  const backupPath = 'js/item/item-manager.js.backup.' + Date.now();
  
  if (fs.existsSync(originalPath)) {
    fs.copyFileSync(originalPath, backupPath);
    console.log(`📦 已备份原文件: ${backupPath}`);
    return backupPath;
  }
  
  return null;
}

// 执行迁移
console.log('🔄 更新使用者文件...');
filesToUpdate.forEach(updateFile);

console.log('\n📦 备份原始文件...');
const backupPath = backupOriginalFile();

console.log('\n🎉 迁移完成！');
console.log('\n📋 验证步骤:');
console.log('1. 测试所有道具功能');
console.log('2. 检查渲染效果');
console.log('3. 验证游戏流程');
console.log('4. 如果一切正常，可以删除备份文件');

if (backupPath) {
  console.log(`\n🗑️  删除备份命令: rm ${backupPath}`);
}