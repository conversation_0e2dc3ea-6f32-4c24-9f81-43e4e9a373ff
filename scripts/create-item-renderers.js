#!/usr/bin/env node

/**
 * 创建道具渲染器脚本
 * 为闪电链、激流、地震术创建专门的渲染器
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 开始创建道具渲染器...\n');

function createLightningRenderer() {
  const content = `/**
 * 闪电效果渲染器
 * 负责渲染闪电链的连接效果和动画
 */
export default class LightningRenderer {
  constructor() {
    this.activeEffects = [];
  }

  /**
   * 创建闪电效果
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {Array} connections - 连接的方块位置
   */
  createLightningEffect(startRow, startCol, connections) {
    const effect = {
      id: Date.now() + Math.random(),
      startRow,
      startCol,
      connections,
      timer: 0,
      duration: 30, // 30帧动画
      segments: this._generateLightningSegments(connections)
    };
    
    this.activeEffects.push(effect);
    return effect;
  }

  /**
   * 渲染闪电效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} effect - 效果对象
   * @returns {boolean} 是否仍在活跃
   */
  render(ctx, effect) {
    if (!effect || effect.timer >= effect.duration) {
      return false;
    }

    ctx.save();
    
    try {
      // 设置闪电样式
      const alpha = 1 - (effect.timer / effect.duration);
      ctx.globalAlpha = alpha;
      ctx.strokeStyle = \`rgba(255, 255, 0, \${alpha})\`; // 黄色闪电
      ctx.lineWidth = 3;
      ctx.shadowColor = 'white';
      ctx.shadowBlur = 10;

      // 绘制闪电连接线
      this._drawLightningSegments(ctx, effect);
      
      // 绘制闪电节点
      this._drawLightningNodes(ctx, effect);
      
    } catch (error) {
      console.error('闪电效果渲染错误:', error);
    } finally {
      ctx.restore();
    }

    effect.timer++;
    return effect.timer < effect.duration;
  }

  /**
   * 生成闪电路径段
   */
  _generateLightningSegments(connections) {
    const segments = [];
    
    for (let i = 0; i < connections.length - 1; i++) {
      const start = connections[i];
      const end = connections[i + 1];
      
      // 生成锯齿状闪电路径
      const path = this._generateZigzagPath(start, end);
      segments.push(path);
    }
    
    return segments;
  }

  /**
   * 生成锯齿路径
   */
  _generateZigzagPath(start, end) {
    const path = [];
    const steps = 5; // 路径分段数
    
    for (let i = 0; i <= steps; i++) {
      const t = i / steps;
      const x = start.col + (end.col - start.col) * t;
      const y = start.row + (end.row - start.row) * t;
      
      // 添加随机偏移制造锯齿效果
      const offset = (Math.random() - 0.5) * 0.3;
      path.push({
        x: x + offset,
        y: y + offset
      });
    }
    
    return path;
  }

  /**
   * 绘制闪电路径段
   */
  _drawLightningSegments(ctx, effect) {
    const blockSize = 30; // 假设方块大小
    const offsetX = 50;   // 网格偏移
    const offsetY = 100;
    
    for (const segment of effect.segments) {
      ctx.beginPath();
      
      for (let i = 0; i < segment.length; i++) {
        const point = segment[i];
        const screenX = offsetX + point.x * blockSize + blockSize / 2;
        const screenY = offsetY + point.y * blockSize + blockSize / 2;
        
        if (i === 0) {
          ctx.moveTo(screenX, screenY);
        } else {
          ctx.lineTo(screenX, screenY);
        }
      }
      
      ctx.stroke();
    }
  }

  /**
   * 绘制闪电节点
   */
  _drawLightningNodes(ctx, effect) {
    const blockSize = 30;
    const offsetX = 50;
    const offsetY = 100;
    
    // 绘制连接点的闪光效果
    for (const connection of effect.connections) {
      const screenX = offsetX + connection.col * blockSize + blockSize / 2;
      const screenY = offsetY + connection.row * blockSize + blockSize / 2;
      
      // 绘制闪光圆圈
      ctx.beginPath();
      ctx.arc(screenX, screenY, 8, 0, Math.PI * 2);
      ctx.fillStyle = \`rgba(255, 255, 255, \${ctx.globalAlpha})\`;
      ctx.fill();
    }
  }

  /**
   * 清理已完成的效果
   */
  cleanup() {
    this.activeEffects = this.activeEffects.filter(effect => 
      effect.timer < effect.duration
    );
  }
}`;

  const filePath = 'js/item/animations/lightning-renderer.js';
  fs.writeFileSync(filePath, content);
  console.log('✅ 创建 lightning-renderer.js');
}

function createEffectRenderer() {
  const content = `/**
 * 通用效果渲染器
 * 负责渲染水流、地震等通用特效
 */
export default class EffectRenderer {
  constructor() {
    this.activeEffects = [];
  }

  /**
   * 创建水流效果
   * @param {number} row - 中心行
   * @param {number} col - 中心列
   * @param {number} level - 效果等级
   */
  createWaterflowEffect(row, col, level) {
    const effect = {
      id: Date.now() + Math.random(),
      type: 'waterflow',
      centerRow: row,
      centerCol: col,
      level,
      timer: 0,
      duration: 40,
      waves: this._generateWaterWaves(row, col, level)
    };
    
    this.activeEffects.push(effect);
    return effect;
  }

  /**
   * 创建地震效果
   * @param {number} row - 震中行
   * @param {number} col - 震中列
   * @param {number} intensity - 震动强度
   */
  createEarthquakeEffect(row, col, intensity) {
    const effect = {
      id: Date.now() + Math.random(),
      type: 'earthquake',
      epicenterRow: row,
      epicenterCol: col,
      intensity,
      timer: 0,
      duration: 50,
      shakeOffset: { x: 0, y: 0 }
    };
    
    this.activeEffects.push(effect);
    return effect;
  }

  /**
   * 渲染所有效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderAll(ctx) {
    this.activeEffects = this.activeEffects.filter(effect => {
      return this.renderEffect(ctx, effect);
    });
  }

  /**
   * 渲染单个效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} effect - 效果对象
   * @returns {boolean} 是否仍在活跃
   */
  renderEffect(ctx, effect) {
    if (!effect || effect.timer >= effect.duration) {
      return false;
    }

    ctx.save();
    
    try {
      switch (effect.type) {
        case 'waterflow':
          this._renderWaterflow(ctx, effect);
          break;
        case 'earthquake':
          this._renderEarthquake(ctx, effect);
          break;
      }
    } catch (error) {
      console.error(\`\${effect.type}效果渲染错误:\`, error);
    } finally {
      ctx.restore();
    }

    effect.timer++;
    return effect.timer < effect.duration;
  }

  /**
   * 渲染水流效果
   */
  _renderWaterflow(ctx, effect) {
    const blockSize = 30;
    const offsetX = 50;
    const offsetY = 100;
    const progress = effect.timer / effect.duration;
    const alpha = 1 - progress;
    
    ctx.globalAlpha = alpha;
    
    // 绘制水波纹
    for (const wave of effect.waves) {
      const radius = wave.maxRadius * progress;
      const centerX = offsetX + effect.centerCol * blockSize + blockSize / 2;
      const centerY = offsetY + effect.centerRow * blockSize + blockSize / 2;
      
      // 绘制波纹圆圈
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.strokeStyle = \`rgba(0, 150, 255, \${alpha * 0.7})\`;
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // 绘制内部水流
      if (radius > 10) {
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius - 5, 0, Math.PI * 2);
        ctx.fillStyle = \`rgba(0, 200, 255, \${alpha * 0.3})\`;
        ctx.fill();
      }
    }
  }

  /**
   * 渲染地震效果
   */
  _renderEarthquake(ctx, effect) {
    const blockSize = 30;
    const offsetX = 50;
    const offsetY = 100;
    const progress = effect.timer / effect.duration;
    
    // 计算震动偏移
    const shakeIntensity = effect.intensity * (1 - progress);
    effect.shakeOffset.x = (Math.random() - 0.5) * shakeIntensity * 2;
    effect.shakeOffset.y = (Math.random() - 0.5) * shakeIntensity * 2;
    
    // 应用震动变换
    ctx.translate(effect.shakeOffset.x, effect.shakeOffset.y);
    
    // 绘制震中效果
    const centerX = offsetX + effect.epicenterCol * blockSize + blockSize / 2;
    const centerY = offsetY + effect.epicenterRow * blockSize + blockSize / 2;
    
    // 绘制震动波
    const waveCount = 3;
    for (let i = 0; i < waveCount; i++) {
      const waveProgress = (progress + i * 0.3) % 1;
      const radius = waveProgress * effect.intensity * blockSize;
      const alpha = 1 - waveProgress;
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.strokeStyle = \`rgba(139, 69, 19, \${alpha * 0.8})\`; // 棕色地震波
      ctx.lineWidth = 3;
      ctx.stroke();
    }
    
    // 绘制震中标记
    ctx.beginPath();
    ctx.arc(centerX, centerY, 5, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
    ctx.fill();
  }

  /**
   * 生成水波
   */
  _generateWaterWaves(centerRow, centerCol, level) {
    const waves = [];
    const waveCount = level + 1;
    
    for (let i = 0; i < waveCount; i++) {
      waves.push({
        delay: i * 5,
        maxRadius: (level + 1) * 30 + i * 20
      });
    }
    
    return waves;
  }

  /**
   * 清理已完成的效果
   */
  cleanup() {
    this.activeEffects = this.activeEffects.filter(effect => 
      effect.timer < effect.duration
    );
  }
}`;

  const filePath = 'js/item/animations/effect-renderer.js';
  fs.writeFileSync(filePath, content);
  console.log('✅ 创建 effect-renderer.js');
}

// 执行创建
try {
  // 确保目录存在
  const animationsDir = 'js/item/animations';
  if (!fs.existsSync(animationsDir)) {
    fs.mkdirSync(animationsDir, { recursive: true });
  }

  createLightningRenderer();
  createEffectRenderer();
  
  console.log('\n🎉 所有渲染器已创建完成！');
  console.log('\n📋 下一步:');
  console.log('1. 运行 node scripts/update-refactored-item-manager.js 更新主管理器');
  console.log('2. 测试所有道具功能');
  console.log('3. 迁移使用者到新架构');
  
} catch (error) {
  console.error('❌ 创建过程中出错:', error);
  process.exit(1);
}
