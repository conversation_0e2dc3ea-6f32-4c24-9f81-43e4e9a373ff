#!/usr/bin/env node

/**
 * 提取和迁移道具实现脚本
 * 从原始item-manager.js中提取完整的道具实现，适配到新架构
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始提取和迁移道具实现...\n');

// 读取原始文件
function readOriginalItemManager() {
  const filePath = 'js/item/item-manager.js';
  if (!fs.existsSync(filePath)) {
    throw new Error('原始item-manager.js文件不存在');
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  console.log(`📖 读取原始文件: ${content.length} 字符, ${content.split('\n').length} 行`);
  return content;
}

// 提取闪电链实现
function extractLightningImplementation(originalContent) {
  console.log('⚡ 提取闪电链实现...');
  
  // 提取_useLightning方法
  const lightningMatch = originalContent.match(
    /\/\*\*[\s\S]*?使用闪电链道具[\s\S]*?\*\/[\s\S]*?_useLightning\([\s\S]*?(?=\n  \/\*\*|\n  _[a-zA-Z]|\n  [a-zA-Z]|\nclass|\nexport)/
  );
  
  if (!lightningMatch) {
    throw new Error('无法找到闪电链实现');
  }
  
  // 提取相关的辅助方法
  const helperMethods = [
    '_calculateLightningChain',
    '_findLightningTargets',
    '_createLightningEffect',
    '_generateLightningZigzag'
  ];
  
  let extractedCode = lightningMatch[0];
  
  // 提取辅助方法
  helperMethods.forEach(methodName => {
    const methodRegex = new RegExp(
      `\\/\\*\\*[\\s\\S]*?${methodName}[\\s\\S]*?\\*\\/[\\s\\S]*?${methodName}\\([\\s\\S]*?(?=\\n  \\/\\*\\*|\\n  _[a-zA-Z]|\\n  [a-zA-Z]|\\nclass|\\nexport)`,
      'g'
    );
    
    const methodMatch = originalContent.match(methodRegex);
    if (methodMatch) {
      extractedCode += '\n\n' + methodMatch[0];
      console.log(`  ✅ 提取辅助方法: ${methodName}`);
    }
  });
  
  return _adaptLightningToNewArchitecture(extractedCode);
}

// 适配闪电链到新架构
function _adaptLightningToNewArchitecture(extractedCode) {
  // 创建新的闪电链道具类
  const adaptedCode = `/**
 * 闪电链道具实现
 * 从原始ItemManager提取并适配到新架构
 */
export default class LightningItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
    
    // 从原始实现中提取的配置
    this.itemEffects = {
      chainCount: [3, 5, 8] // 等级1-3的连锁数量
    };
  }

  /**
   * 使用闪电链道具
   * @param {number} row - 目标行
   * @param {number} col - 目标列  
   * @param {number} level - 道具等级
   * @param {Object} callbacks - 回调函数
   */
  use(row, col, level, callbacks) {
    console.log(\`⚡ 使用闪电链道具 等级\${level} 位置(\${row}, \${col})\`);
    
    // 检查目标位置是否有效
    if (!this._isValidPosition(row, col)) {
      console.log('闪电链目标位置无效:', row, col);
      return false;
    }
    
    // 获取目标方块
    const targetBlock = this.grid.getBlock(row, col);
    if (!targetBlock) {
      console.log('闪电链目标位置没有方块');
      return false;
    }
    
    // 执行闪电链逻辑（从原始实现适配）
    const result = this._executeLightningChain(row, col, targetBlock, level, callbacks);
    
    if (result.success) {
      // 创建闪电效果
      callbacks.createLightningEffect(row, col, result.chainPath);
      
      // 播放音效
      callbacks.playSound('闪电链.mp3');
      
      console.log(\`⚡ 闪电链连接了 \${result.affectedCount} 个方块\`);
      return true;
    }
    
    return false;
  }

  // 以下是从原始实现中提取的方法，适配到新架构
${_adaptMethodsToNewArchitecture(extractedCode)}

  /**
   * 检查位置是否有效
   */
  _isValidPosition(row, col) {
    return row >= 0 && row < this.grid.rows && col >= 0 && col < this.grid.cols;
  }
}`;

  return adaptedCode;
}

// 适配方法到新架构
function _adaptMethodsToNewArchitecture(extractedCode) {
  // 移除原始类的上下文，适配到新的类结构
  let adaptedMethods = extractedCode
    // 移除原始的_useLightning方法签名，我们已经重写了use方法
    .replace(/\/\*\*[\s\S]*?_useLightning\([^{]*\{/, '')
    // 将this.grid引用保持不变
    // 将this.itemEffects引用适配
    .replace(/this\.itemEffects\[ITEM_TYPES\.LIGHTNING\]/g, 'this.itemEffects')
    // 将this.affectedBlocks适配为回调
    .replace(/this\.affectedBlocks\.add\([^)]+\)/g, 'callbacks.markBlockAffected($1)')
    // 移除原始方法的结尾大括号
    .replace(/\s*}\s*$/, '');

  return adaptedMethods;
}

// 适配激流方法到新架构
function _adaptWaterflowMethodsToNewArchitecture(extractedCode) {
  let adaptedMethods = extractedCode
    // 移除原始的_useWaterflow方法签名
    .replace(/\/\*\*[\s\S]*?_useWaterflow\([^{]*\{/, '')
    // 适配itemEffects引用
    .replace(/this\.itemEffects\[ITEM_TYPES\.WATERFLOW\]/g, 'this.itemEffects')
    // 适配affectedBlocks引用
    .replace(/this\.affectedBlocks\.add\([^)]+\)/g, 'callbacks.markBlockAffected($1)')
    // 移除原始方法的结尾大括号
    .replace(/\s*}\s*$/, '');

  return adaptedMethods;
}

// 适配地震术到新架构
function _adaptEarthquakeToNewArchitecture(extractedCode) {
  const adaptedCode = `/**
 * 地震术道具实现
 * 从原始ItemManager提取并适配到新架构
 */
export default class EarthquakeItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;

    // 从原始实现中提取的配置
    this.itemEffects = {
      compressionRatio: [0.25, 0.33, 0.5, 0.67, 0.75] // 等级1-5的压缩比例
    };
  }

  /**
   * 使用地震术道具
   * @param {number} row - 震中行
   * @param {number} col - 震中列
   * @param {number} level - 道具等级
   * @param {Object} callbacks - 回调函数
   */
  use(row, col, level, callbacks) {
    console.log(\`🌍 使用地震术道具 等级\${level} 震中(\${row}, \${col})\`);

    // 执行地震术逻辑（从原始实现适配）
    const result = this._executeEarthquake(level, callbacks);

    if (result.success) {
      // 创建地震效果
      callbacks.createEarthquakeEffect(result.startRow, result.endRow, level);

      // 播放音效
      callbacks.playSound('地震术.mp3');

      console.log(\`🌍 地震压缩了 \${result.affectedCount} 个方块\`);
      return true;
    }

    return false;
  }

  // 以下是从原始实现中提取的方法，适配到新架构
${_adaptEarthquakeMethodsToNewArchitecture(extractedCode)}
}`;

  return adaptedCode;
}

// 适配地震术方法到新架构
function _adaptEarthquakeMethodsToNewArchitecture(extractedCode) {
  let adaptedMethods = extractedCode
    // 移除原始的_useEarthquake方法签名
    .replace(/\/\*\*[\s\S]*?_useEarthquake\([^{]*\{/, '')
    // 适配itemEffects引用
    .replace(/this\.itemEffects\[ITEM_TYPES\.EARTHQUAKE\]/g, 'this.itemEffects')
    // 适配affectedBlocks引用
    .replace(/this\.affectedBlocks\.add\([^)]+\)/g, 'callbacks.markBlockAffected($1)')
    // 移除原始方法的结尾大括号
    .replace(/\s*}\s*$/, '');

  return adaptedMethods;
}

// 提取激流实现
function extractWaterflowImplementation(originalContent) {
  console.log('🌊 提取激流实现...');
  
  // 提取_useWaterflow方法和相关辅助方法
  const waterflowMatch = originalContent.match(
    /\/\*\*[\s\S]*?使用激流道具[\s\S]*?\*\/[\s\S]*?_useWaterflow\([\s\S]*?(?=\n  \/\*\*|\n  _[a-zA-Z]|\n  [a-zA-Z]|\nclass|\nexport)/
  );
  
  if (!waterflowMatch) {
    throw new Error('无法找到激流实现');
  }
  
  // 提取相关辅助方法
  const helperMethods = [
    '_clearBottomRows',
    '_processWaterflowEffect',
    '_createWaterflowEffect'
  ];
  
  let extractedCode = waterflowMatch[0];
  
  helperMethods.forEach(methodName => {
    const methodRegex = new RegExp(
      `\\/\\*\\*[\\s\\S]*?${methodName}[\\s\\S]*?\\*\\/[\\s\\S]*?${methodName}\\([\\s\\S]*?(?=\\n  \\/\\*\\*|\\n  _[a-zA-Z]|\\n  [a-zA-Z]|\\nclass|\\nexport)`,
      'g'
    );
    
    const methodMatch = originalContent.match(methodRegex);
    if (methodMatch) {
      extractedCode += '\n\n' + methodMatch[0];
      console.log(`  ✅ 提取辅助方法: ${methodName}`);
    }
  });
  
  return _adaptWaterflowToNewArchitecture(extractedCode);
}

// 适配激流到新架构
function _adaptWaterflowToNewArchitecture(extractedCode) {
  const adaptedCode = `/**
 * 激流道具实现
 * 从原始ItemManager提取并适配到新架构
 */
export default class WaterflowItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
    
    // 从原始实现中提取的配置
    this.itemEffects = {
      rowCount: [1, 2, 3] // 等级1-3的消除行数
    };
  }

  /**
   * 使用激流道具
   * @param {number} row - 目标行（激流不需要具体位置）
   * @param {number} col - 目标列（激流不需要具体位置）
   * @param {number} level - 道具等级
   * @param {Object} callbacks - 回调函数
   */
  use(row, col, level, callbacks) {
    console.log(\`🌊 使用激流道具 等级\${level}\`);
    
    // 执行激流逻辑（从原始实现适配）
    const result = this._executeWaterflow(level, callbacks);
    
    if (result.success) {
      // 创建水流效果
      callbacks.createWaterflowEffect(result.centerRow, result.centerCol, level);
      
      // 播放音效
      callbacks.playSound('激流.mp3');
      
      console.log(\`🌊 激流清除了 \${result.affectedCount} 个方块\`);
      return true;
    }
    
    return false;
  }

  // 以下是从原始实现中提取的方法，适配到新架构
${_adaptWaterflowMethodsToNewArchitecture(extractedCode)}
}`;

  return adaptedCode;
}

// 提取地震术实现
function extractEarthquakeImplementation(originalContent) {
  console.log('🌍 提取地震术实现...');
  
  // 提取_useEarthquake方法和相关辅助方法
  const earthquakeMatch = originalContent.match(
    /\/\*\*[\s\S]*?使用地震术道具[\s\S]*?\*\/[\s\S]*?_useEarthquake\([\s\S]*?(?=\n  \/\*\*|\n  _[a-zA-Z]|\n  [a-zA-Z]|\nclass|\nexport)/
  );
  
  if (!earthquakeMatch) {
    throw new Error('无法找到地震术实现');
  }
  
  // 提取相关辅助方法
  const helperMethods = [
    '_executeEarthquakeCompression',
    '_createEarthquakeEffect',
    '_createEarthquakeParticles'
  ];
  
  let extractedCode = earthquakeMatch[0];
  
  helperMethods.forEach(methodName => {
    const methodRegex = new RegExp(
      `\\/\\*\\*[\\s\\S]*?${methodName}[\\s\\S]*?\\*\\/[\\s\\S]*?${methodName}\\([\\s\\S]*?(?=\\n  \\/\\*\\*|\\n  _[a-zA-Z]|\\n  [a-zA-Z]|\\nclass|\\nexport)`,
      'g'
    );
    
    const methodMatch = originalContent.match(methodRegex);
    if (methodMatch) {
      extractedCode += '\n\n' + methodMatch[0];
      console.log(`  ✅ 提取辅助方法: ${methodName}`);
    }
  });
  
  return _adaptEarthquakeToNewArchitecture(extractedCode);
}

// 主执行函数
async function main() {
  try {
    // 1. 读取原始文件
    const originalContent = readOriginalItemManager();
    
    // 2. 确保目录存在
    const itemsDir = 'js/item/items';
    if (!fs.existsSync(itemsDir)) {
      fs.mkdirSync(itemsDir, { recursive: true });
    }
    
    // 3. 提取并创建闪电链实现
    console.log('\n⚡ 处理闪电链道具...');
    const lightningCode = extractLightningImplementation(originalContent);
    fs.writeFileSync('js/item/items/lightning-item.js', lightningCode);
    console.log('✅ 创建 lightning-item.js');
    
    // 4. 提取并创建激流实现
    console.log('\n🌊 处理激流道具...');
    const waterflowCode = extractWaterflowImplementation(originalContent);
    fs.writeFileSync('js/item/items/waterflow-item.js', waterflowCode);
    console.log('✅ 创建 waterflow-item.js');
    
    // 5. 提取并创建地震术实现
    console.log('\n🌍 处理地震术道具...');
    const earthquakeCode = extractEarthquakeImplementation(originalContent);
    fs.writeFileSync('js/item/items/earthquake-item.js', earthquakeCode);
    console.log('✅ 创建 earthquake-item.js');
    
    console.log('\n🎉 所有道具实现提取完成！');
    console.log('\n📋 下一步:');
    console.log('1. 运行 node scripts/extract-item-renderers.js 提取渲染器');
    console.log('2. 运行 node scripts/update-refactored-manager.js 更新管理器');
    console.log('3. 测试所有道具功能');
    
  } catch (error) {
    console.error('❌ 提取过程中出错:', error);
    process.exit(1);
  }
}

// 执行主流程
main();
