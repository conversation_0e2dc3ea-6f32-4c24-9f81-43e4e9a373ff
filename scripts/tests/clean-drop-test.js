import Grid from './js/game/grid.js';
import Tetromino from './js/game/tetromino.js';

console.log('🔍 纯净测试 - 无预放置方块');

const grid = new Grid();
const tetromino = new Tetromino('I');

console.log('I型方块初始位置:', tetromino.position);
console.log('初始占据位置:', tetromino.getBlockPositions().map(p => `(${p.row}, ${p.col})`).join(', '));

let steps = 0;
while (tetromino.canMoveDown(grid) && steps < 25) {
  tetromino.moveDown();
  steps++;
}

console.log(`下落步数: ${steps}`);
console.log(`最终位置: (${tetromino.position.row}, ${tetromino.position.col})`);
console.log('最终占据位置:', tetromino.getBlockPositions().map(p => `(${p.row}, ${p.col})`).join(', '));

const finalPositions = tetromino.getBlockPositions();
const maxRow = Math.max(...finalPositions.map(p => p.row));
console.log(`最底部方块在第${maxRow}行 (网格有${grid.rows}行，最大行索引是${grid.rows-1})`);

if (maxRow === grid.rows - 1) {
  console.log('✅ 方块正确下落到了底部');
} else {
  console.log('❌ 方块没有下落到底部，可能有问题');
} 