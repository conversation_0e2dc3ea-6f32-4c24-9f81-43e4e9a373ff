// 快速Grid碰撞检测修复测试
console.log('🔍 开始Grid碰撞检测问题诊断');

// 模拟Grid类的当前问题方法
class TestGrid {
  constructor() {
    this.rows = 20;
    this.cols = 10;
    this.blocks = Array(this.rows).fill().map(() => Array(this.cols).fill(null));
  }

  // 当前的有问题的getBlock方法
  getBlockBuggy(row, col) {
    if (!this.isValidPosition(row, col) && (row < 0 || row >= this.rows || col < 0 || col >= this.cols)) {
      return null;
    }
    return this.blocks[row][col];
  }

  // 修复后的getBlock方法
  getBlockFixed(row, col) {
    if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
      return null;
    }
    return this.blocks[row][col];
  }

  isValidPosition(row, col) {
    return row >= 0 && row < this.rows && col >= 0 && col < this.cols && !this.getBlockFixed(row, col);
  }

  placeBlock(block, row, col) {
    if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
      return false;
    }
    this.blocks[row][col] = block;
    return true;
  }
}

// 测试
const grid = new TestGrid();

// 在第19行放置一个方块（最底行）
const testBlock = { color: 'red', id: 'test1' };
grid.placeBlock(testBlock, 19, 5);

console.log('📍 测试场景：网格第19行第5列有方块，测试第18行方块是否能向下移动到第19行');

// 模拟Tetromino在第18行，想移动到第19行第5列
const tetrominoRow = 18;
const tetrominoCol = 5;
const nextRow = 19;
const nextCol = 5;

console.log('\n🐛 使用有问题的isValidPosition (依赖buggy getBlock):');
// 模拟当前有问题的逻辑
console.log(`- 第19行第5列是否有方块(buggy): ${grid.getBlockBuggy(nextRow, nextCol) !== null}`);
console.log(`- 第19行第5列位置是否有效(buggy): ${grid.isValidPosition(nextRow, nextCol)}`);

console.log('\n✅ 使用修复后的getBlock:');
console.log(`- 第19行第5列是否有方块(fixed): ${grid.getBlockFixed(nextRow, nextCol) !== null}`);
console.log(`- 第19行第5列位置是否有效(应该false): ${row >= 0 && row < grid.rows && col >= 0 && col < grid.cols && !grid.getBlockFixed(nextRow, nextCol)}`);

console.log('\n🎯 结论：');
console.log('- 有问题的getBlock可能无法正确检测已占用位置');
console.log('- 导致isValidPosition返回true，认为已占用位置可以移动到');
console.log('- 这就是为什么方块在中途"锁定"的原因！');

// 创建Grid修复补丁
const gridFix = `
/**
 * 🚨 Grid.js 碰撞检测修复补丁
 * 问题：getBlock方法的边界检查逻辑错误
 * 修复：简化边界检查逻辑
 */

// 修复前的getBlock方法：
/*
getBlock(row, col) {
  if (!this.isValidPosition(row, col) && (row < 0 || row >= this.rows || col < 0 || col >= this.cols)) {
    return null;
  }
  return this.blocks[row][col];
}
*/

// 修复后的getBlock方法：
getBlock(row, col) {
  if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
    return null;
  }
  return this.blocks[row][col];
}
`;

console.log('\n📝 修复代码：');
console.log(gridFix); 