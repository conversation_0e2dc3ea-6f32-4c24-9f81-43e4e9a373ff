#!/usr/bin/env node

/**
 * 更新RefactoredItemManager脚本
 * 集成新创建的道具和渲染器
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 开始更新RefactoredItemManager...\n');

function updateRefactoredItemManager() {
  const filePath = 'js/item/refactored-item-manager.js';
  
  if (!fs.existsSync(filePath)) {
    console.error('❌ RefactoredItemManager文件不存在');
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');

  // 1. 添加新的导入语句
  const newImports = `
// 新增道具实现
import LightningItem from './items/lightning-item.js';
import WaterflowItem from './items/waterflow-item.js';
import EarthquakeItem from './items/earthquake-item.js';

// 新增渲染器
import LightningRenderer from './animations/lightning-renderer.js';
import EffectRenderer from './animations/effect-renderer.js';`;

  // 在FireballItem导入后添加新导入
  content = content.replace(
    'import FireballItem from \'./items/fireball-item.js\';',
    `import FireballItem from './items/fireball-item.js';${newImports}`
  );

  // 2. 更新道具实例化
  const newItemsInit = `    // 初始化道具实现
    this.items = {
      [ITEM_TYPES.FIREBALL]: new FireballItem(grid, this.targetingSystem),
      [ITEM_TYPES.LIGHTNING]: new LightningItem(grid, this.targetingSystem),
      [ITEM_TYPES.WATERFLOW]: new WaterflowItem(grid, this.targetingSystem),
      [ITEM_TYPES.EARTHQUAKE]: new EarthquakeItem(grid, this.targetingSystem)
    };`;

  content = content.replace(
    /\/\/ 初始化道具实现[\s\S]*?};/,
    newItemsInit
  );

  // 3. 更新渲染器初始化
  const newRenderersInit = `    // 初始化渲染器
    this.explosionRenderer = new ExplosionRenderer();
    this.lightningRenderer = new LightningRenderer();
    this.effectRenderer = new EffectRenderer();`;

  content = content.replace(
    /\/\/ 初始化渲染器[\s\S]*?new ExplosionRenderer\(\);/,
    newRenderersInit
  );

  // 4. 更新useItem方法
  const newUseItemLogic = `    // 使用道具
    let success = false;
    switch (itemType) {
      case ITEM_TYPES.FIREBALL:
        success = this._useFireball(row, col, level);
        break;
      case ITEM_TYPES.LIGHTNING:
        success = this._useLightning(row, col, level);
        break;
      case ITEM_TYPES.WATERFLOW:
        success = this._useWaterflow(row, col, level);
        break;
      case ITEM_TYPES.EARTHQUAKE:
        success = this._useEarthquake(row, col, level);
        break;
      default:
        console.warn(\`未知道具类型: \${itemType}\`);
        break;
    }`;

  content = content.replace(
    /\/\/ 使用道具[\s\S]*?\/\/ TODO: 添加其他道具的使用逻辑/,
    newUseItemLogic
  );

  // 5. 添加新道具使用方法
  const newItemMethods = `
  /**
   * 使用闪电链
   */
  _useLightning(row, col, level) {
    const lightningItem = this.items[ITEM_TYPES.LIGHTNING];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createLightningEffect: (startRow, startCol, connections) => {
        const effect = this.lightningRenderer.createLightningEffect(
          startRow, startCol, connections
        );
        this.animations.lightningEffect = effect;
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return lightningItem.use(row, col, level, callbacks);
  }

  /**
   * 使用激流
   */
  _useWaterflow(row, col, level) {
    const waterflowItem = this.items[ITEM_TYPES.WATERFLOW];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createWaterflowEffect: (centerRow, centerCol, effectLevel) => {
        const effect = this.effectRenderer.createWaterflowEffect(
          centerRow, centerCol, effectLevel
        );
        this.animations.waterflowEffect = effect;
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return waterflowItem.use(row, col, level, callbacks);
  }

  /**
   * 使用地震术
   */
  _useEarthquake(row, col, level) {
    const earthquakeItem = this.items[ITEM_TYPES.EARTHQUAKE];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createEarthquakeEffect: (epicenterRow, epicenterCol, intensity) => {
        const effect = this.effectRenderer.createEarthquakeEffect(
          epicenterRow, epicenterCol, intensity
        );
        this.animations.earthquakeEffect = effect;
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return earthquakeItem.use(row, col, level, callbacks);
  }`;

  // 在_useFireball方法后添加新方法
  content = content.replace(
    /(\s+return fireballItem\.use\(row, col, level, callbacks\);\s+})/,
    `$1${newItemMethods}`
  );

  // 6. 更新render方法
  const newRenderMethod = `  /**
   * 渲染所有效果
   */
  render(ctx) {
    // 渲染爆炸效果
    if (this.animations.explosionEffect) {
      const stillActive = this.explosionRenderer.render(ctx, this.animations.explosionEffect);
      if (!stillActive) {
        this.animations.explosionEffect = null;
      }
    }
    
    // 渲染闪电效果
    if (this.animations.lightningEffect) {
      const stillActive = this.lightningRenderer.render(ctx, this.animations.lightningEffect);
      if (!stillActive) {
        this.animations.lightningEffect = null;
      }
    }
    
    // 渲染水流和地震效果
    this.effectRenderer.renderAll(ctx);
    
    // 清理已完成的效果
    this.effectRenderer.cleanup();
  }`;

  content = content.replace(
    /\/\*\*\s+\* 渲染所有效果[\s\S]*?\/\/ TODO: 渲染其他效果\s+}/,
    newRenderMethod
  );

  // 7. 更新动画状态检查
  const newAnimationCheck = `    // 检查是否有活跃动画
    this.animations.isActive = !!(
      this.animations.explosionEffect ||
      this.animations.lightningEffect ||
      this.animations.waterflowEffect ||
      this.animations.earthquakeEffect ||
      this.effectRenderer.activeEffects.length > 0
    );`;

  content = content.replace(
    /\/\/ 检查是否有活跃动画[\s\S]*?\);/,
    newAnimationCheck
  );

  // 写入更新后的内容
  fs.writeFileSync(filePath, content);
  console.log('✅ RefactoredItemManager已更新');
  return true;
}

function createMigrationScript() {
  const content = `#!/usr/bin/env node

/**
 * ItemManager迁移脚本
 * 将所有使用原ItemManager的地方替换为RefactoredItemManager
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 开始迁移ItemManager使用者...');

const filesToUpdate = [
  'js/game/controller.js',
  'js/runtime/gameinfo.js',
  'js/level/level-manager.js',
  'js/main.js'
];

function updateFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(\`⏸️  文件不存在，跳过: \${filePath}\`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;

  // 替换导入语句
  if (content.includes("from './item/item-manager.js'") || 
      content.includes("from '../item/item-manager.js'")) {
    content = content.replace(
      /from ['"]\.\.?\/item\/item-manager\.js['"]/g,
      "from './item/refactored-item-manager.js'"
    );
    updated = true;
  }

  // 替换类名引用
  if (content.includes('new ItemManager(')) {
    content = content.replace(/new ItemManager\\(/g, 'new RefactoredItemManager(');
    updated = true;
  }

  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(\`✅ 已更新: \${filePath}\`);
  } else {
    console.log(\`⏸️  无需更新: \${filePath}\`);
  }
}

// 执行迁移
filesToUpdate.forEach(updateFile);

console.log('\\n🎉 ItemManager迁移完成！');
console.log('\\n📋 下一步:');
console.log('1. 备份原item-manager.js');
console.log('2. 测试所有功能');
console.log('3. 删除原文件');`;

  fs.writeFileSync('scripts/migrate-item-manager.js', content);
  console.log('✅ 创建迁移脚本: scripts/migrate-item-manager.js');
}

// 执行更新
try {
  if (updateRefactoredItemManager()) {
    createMigrationScript();
    
    console.log('\n🎉 RefactoredItemManager更新完成！');
    console.log('\n📋 下一步操作:');
    console.log('1. node scripts/complete-item-manager-refactor.js  # 创建道具');
    console.log('2. node scripts/create-item-renderers.js          # 创建渲染器');
    console.log('3. node scripts/migrate-item-manager.js           # 迁移使用者');
    console.log('4. 测试所有道具功能');
    console.log('5. 备份并删除原item-manager.js');
  }
  
} catch (error) {
  console.error('❌ 更新过程中出错:', error);
  process.exit(1);
}
