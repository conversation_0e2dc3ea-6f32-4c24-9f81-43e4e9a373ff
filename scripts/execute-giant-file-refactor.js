#!/usr/bin/env node

/**
 * 巨型文件拆分执行脚本
 * 按优先级执行所有巨型文件的拆分
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始执行巨型文件拆分计划...\n');

// 当前巨型文件状况
const giantFiles = [
  { file: 'js/item/item-manager.js', lines: 3841, priority: 1, status: 'in-progress' },
  { file: 'js/game/controller.js', lines: 3329, priority: 2, status: 'has-refactored' },
  { file: 'js/runtime/gameinfo.js', lines: 2379, priority: 3, status: 'pending' },
  { file: 'js/game/refactored-controller.js', lines: 1348, priority: 4, status: 'needs-migration' },
  { file: 'js/game/rendering/animation-manager.js', lines: 1165, priority: 5, status: 'pending' },
  { file: 'js/game/grid.js', lines: 1025, priority: 6, status: 'has-refactored' }
];

function displayCurrentStatus() {
  console.log('📊 当前巨型文件状况:');
  console.log('=' .repeat(80));
  
  giantFiles.forEach((file, index) => {
    const statusIcon = {
      'completed': '✅',
      'in-progress': '🔄',
      'has-refactored': '🔧',
      'needs-migration': '⚠️',
      'pending': '📋'
    }[file.status] || '❓';
    
    console.log(`${index + 1}. ${statusIcon} ${file.file}`);
    console.log(`   📏 ${file.lines}行 | 优先级: ${file.priority} | 状态: ${file.status}`);
  });
  
  console.log('=' .repeat(80));
}

function executePhase3AContinuation() {
  console.log('\n🎯 Phase 3A-续: 完成ItemManager拆分 (3841行)');
  console.log('-' .repeat(50));
  
  try {
    console.log('1️⃣ 创建缺失的道具实现...');
    execSync('node scripts/complete-item-manager-refactor.js', { stdio: 'inherit' });
    
    console.log('\n2️⃣ 创建道具渲染器...');
    execSync('node scripts/create-item-renderers.js', { stdio: 'inherit' });
    
    console.log('\n3️⃣ 更新RefactoredItemManager...');
    execSync('node scripts/update-refactored-item-manager.js', { stdio: 'inherit' });
    
    console.log('\n✅ Phase 3A-续 完成！ItemManager拆分成功');
    return true;
    
  } catch (error) {
    console.error('❌ Phase 3A-续 执行失败:', error.message);
    return false;
  }
}

function planPhase3F() {
  console.log('\n🎯 Phase 3F: GameController完全替换 (3329行)');
  console.log('-' .repeat(50));
  console.log('📋 计划:');
  console.log('1. 完善refactored-controller.js功能覆盖');
  console.log('2. 迁移所有使用者到新控制器');
  console.log('3. 删除原controller.js');
  console.log('⏰ 预计时间: 2天');
}

function planPhase3G() {
  console.log('\n🎯 Phase 3G: GameInfo拆分 (2379行)');
  console.log('-' .repeat(50));
  console.log('📋 计划架构:');
  console.log('js/ui-system/');
  console.log('├── refactored-gameinfo.js (主协调器)');
  console.log('├── components/');
  console.log('│   ├── score-display.js');
  console.log('│   ├── level-display.js');
  console.log('│   ├── item-display.js');
  console.log('│   └── status-display.js');
  console.log('├── layouts/');
  console.log('│   ├── game-layout.js');
  console.log('│   └── menu-layout.js');
  console.log('└── rendering/');
  console.log('    ├── ui-renderer.js');
  console.log('    └── text-renderer.js');
  console.log('⏰ 预计时间: 3天');
}

function createNextPhaseScripts() {
  // 创建Phase 3F脚本
  const phase3FContent = `#!/usr/bin/env node

/**
 * Phase 3F: GameController完全替换
 */

console.log('🎯 Phase 3F: GameController完全替换');
console.log('⚠️  此阶段需要手动执行以下步骤:');
console.log('');
console.log('1. 检查refactored-controller.js功能完整性');
console.log('2. 更新所有引用controller.js的文件');
console.log('3. 测试游戏核心功能');
console.log('4. 备份并删除原controller.js');
console.log('');
console.log('📁 主要文件:');
console.log('- js/game/refactored-controller.js (新版本)');
console.log('- js/game/controller.js (待删除)');
console.log('- js/runtime/gameinfo.js (需要更新引用)');
console.log('- js/main.js (需要更新引用)');`;

  fs.writeFileSync('scripts/phase-3f-controller-replacement.js', phase3FContent);
  
  // 创建Phase 3G脚本
  const phase3GContent = `#!/usr/bin/env node

/**
 * Phase 3G: GameInfo拆分脚本
 */

console.log('🎯 Phase 3G: GameInfo拆分 (2379行)');
console.log('⚠️  此阶段需要创建UI组件系统');
console.log('');
console.log('📋 执行步骤:');
console.log('1. 创建ui-system目录结构');
console.log('2. 拆分GameInfo为多个UI组件');
console.log('3. 实现组件化渲染系统');
console.log('4. 迁移使用者到新架构');
console.log('');
console.log('🏗️ 目标架构:');
console.log('js/ui-system/');
console.log('├── refactored-gameinfo.js');
console.log('├── components/ (UI组件)');
console.log('├── layouts/ (布局管理)');
console.log('└── rendering/ (渲染系统)');`;

  fs.writeFileSync('scripts/phase-3g-gameinfo-refactor.js', phase3GContent);
  
  console.log('✅ 创建了下一阶段脚本:');
  console.log('   - scripts/phase-3f-controller-replacement.js');
  console.log('   - scripts/phase-3g-gameinfo-refactor.js');
}

function generateProgressReport() {
  const report = `# 巨型文件拆分进度报告

## 📊 总体进度

### 已完成 ✅
- **Main系统** (2138行 → 40行) - 98.1%减少
- **Grid系统** (2059行 → 422行) - 79%减少

### 进行中 🔄
- **ItemManager** (3841行) - Phase 3A-续 执行中
- **GameController** (3329行) - 已有重构版本，待完全替换

### 待开始 📋
- **GameInfo** (2379行) - Phase 3G 计划中
- **AnimationManager** (1165行) - 后续优化

## 🎯 下一步行动

### 立即执行 (今天)
1. 完成ItemManager拆分 (Phase 3A-续)
2. 测试所有道具功能
3. 备份原item-manager.js

### 本周计划
1. GameController完全替换 (Phase 3F)
2. 开始GameInfo拆分规划 (Phase 3G)

### 预期收益
- **代码质量**: 单文件复杂度降低90%+
- **可维护性**: 每个bug定位到具体文件
- **可扩展性**: 新功能开发更简单
- **团队协作**: 并行开发无冲突

## 📈 成功指标
- 最大文件行数 < 500行
- 平均文件行数 < 300行
- 所有文件单一职责
- 100%向后兼容

---
生成时间: ${new Date().toLocaleString()}
`;

  fs.writeFileSync('docs/GIANT_FILE_REFACTOR_PROGRESS.md', report);
  console.log('✅ 生成进度报告: docs/GIANT_FILE_REFACTOR_PROGRESS.md');
}

// 主执行流程
function main() {
  displayCurrentStatus();
  
  console.log('\n🎯 开始执行拆分计划...');
  
  // Phase 3A-续: 完成ItemManager拆分
  const phase3ASuccess = executePhase3AContinuation();
  
  if (phase3ASuccess) {
    console.log('\n🎉 Phase 3A-续 执行成功！');
    
    // 规划下一阶段
    planPhase3F();
    planPhase3G();
    
    // 创建下一阶段脚本
    createNextPhaseScripts();
    
    // 生成进度报告
    generateProgressReport();
    
    console.log('\n🏆 巨型文件拆分计划启动成功！');
    console.log('\n📋 后续步骤:');
    console.log('1. 测试ItemManager所有功能');
    console.log('2. 运行 node scripts/phase-3f-controller-replacement.js');
    console.log('3. 运行 node scripts/phase-3g-gameinfo-refactor.js');
    
  } else {
    console.log('\n❌ Phase 3A-续 执行失败，请检查错误并重试');
    process.exit(1);
  }
}

// 执行主流程
main();
