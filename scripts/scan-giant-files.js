#!/usr/bin/env node

/**
 * 巨型文件扫描工具
 * 识别代码库中需要重构的大文件
 */

const fs = require('fs');
const path = require('path');

class GiantFileScanner {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.results = [];
    this.thresholds = {
      giant: 2000,      // 巨型文件 (>2000行)
      large: 1000,      // 大文件 (1000-2000行)
      medium: 500,      // 中等文件 (500-1000行)
      small: 200        // 小文件 (200-500行)
    };
  }

  /**
   * 扫描所有文件
   */
  async scan() {
    console.log('🔍 扫描代码库中的巨型文件...\n');
    
    const jsFiles = this.findJSFiles();
    
    for (const filePath of jsFiles) {
      const fileInfo = this.analyzeFile(filePath);
      if (fileInfo) {
        this.results.push(fileInfo);
      }
    }
    
    // 按行数排序
    this.results.sort((a, b) => b.lines - a.lines);
    
    this.generateReport();
  }

  /**
   * 查找所有JS文件
   */
  findJSFiles() {
    const files = [];
    const ignoreDirs = ['node_modules', '.git', 'dist', 'build', 'docs'];
    const ignoreFiles = ['test', 'spec', 'backup', 'archive'];
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          if (ignoreDirs.includes(item)) continue;
          
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            scanDir(fullPath);
          } else if (item.endsWith('.js') && !ignoreFiles.some(ignore => item.includes(ignore))) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // 忽略访问错误
      }
    };
    
    scanDir(this.projectRoot);
    return files;
  }

  /**
   * 分析单个文件
   */
  analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n').length;
      const relativePath = path.relative(this.projectRoot, filePath);
      
      // 只关注中等以上的文件
      if (lines < this.thresholds.small) {
        return null;
      }
      
      // 分析文件内容
      const analysis = this.analyzeContent(content, relativePath);
      
      return {
        path: relativePath,
        lines: lines,
        size: this.getFileSize(filePath),
        category: this.categorizeFile(lines),
        priority: this.calculatePriority(lines, relativePath),
        complexity: analysis.complexity,
        refactorability: analysis.refactorability,
        status: this.getRefactorStatus(relativePath),
        recommendations: analysis.recommendations
      };
      
    } catch (error) {
      console.error(`分析文件失败 ${filePath}:`, error.message);
      return null;
    }
  }

  /**
   * 分析文件内容
   */
  analyzeContent(content, filePath) {
    const lines = content.split('\n');
    let complexity = 0;
    let classCount = 0;
    let functionCount = 0;
    let importCount = 0;
    let commentLines = 0;
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // 计算复杂度指标
      if (trimmed.startsWith('class ')) classCount++;
      if (trimmed.includes('function ') || trimmed.includes('=>')) functionCount++;
      if (trimmed.startsWith('import ')) importCount++;
      if (trimmed.startsWith('//') || trimmed.startsWith('*')) commentLines++;
      
      // 复杂度计算
      if (trimmed.includes('if ') || trimmed.includes('else')) complexity++;
      if (trimmed.includes('for ') || trimmed.includes('while')) complexity++;
      if (trimmed.includes('switch ') || trimmed.includes('case ')) complexity++;
      if (trimmed.includes('try ') || trimmed.includes('catch')) complexity++;
    }
    
    // 计算可重构性
    const refactorability = this.calculateRefactorability({
      lines: lines.length,
      classCount,
      functionCount,
      importCount,
      commentLines,
      complexity
    });
    
    // 生成建议
    const recommendations = this.generateRecommendations({
      lines: lines.length,
      classCount,
      functionCount,
      complexity,
      filePath
    });
    
    return {
      complexity,
      refactorability,
      recommendations,
      stats: {
        classCount,
        functionCount,
        importCount,
        commentLines
      }
    };
  }

  /**
   * 计算可重构性评分
   */
  calculateRefactorability(stats) {
    let score = 100;
    
    // 行数惩罚
    if (stats.lines > 2000) score -= 40;
    else if (stats.lines > 1000) score -= 20;
    else if (stats.lines > 500) score -= 10;
    
    // 复杂度惩罚
    if (stats.complexity > 100) score -= 30;
    else if (stats.complexity > 50) score -= 15;
    
    // 类数量奖励（多类文件更容易拆分）
    if (stats.classCount > 3) score += 20;
    else if (stats.classCount > 1) score += 10;
    
    // 函数数量（适中最好）
    if (stats.functionCount > 50) score -= 15;
    else if (stats.functionCount > 20) score += 5;
    
    // 注释比例奖励
    const commentRatio = stats.commentLines / stats.lines;
    if (commentRatio > 0.2) score += 10;
    else if (commentRatio > 0.1) score += 5;
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * 生成重构建议
   */
  generateRecommendations(stats) {
    const recommendations = [];
    
    if (stats.lines > 2000) {
      recommendations.push('🔴 极高优先级：立即拆分，文件过于庞大');
    } else if (stats.lines > 1000) {
      recommendations.push('🟡 高优先级：建议拆分为多个模块');
    } else if (stats.lines > 500) {
      recommendations.push('🟢 中优先级：考虑模块化重构');
    }
    
    if (stats.classCount > 3) {
      recommendations.push('📦 按类拆分：每个类独立成文件');
    }
    
    if (stats.functionCount > 30) {
      recommendations.push('🔧 按功能拆分：相关函数组成模块');
    }
    
    if (stats.complexity > 100) {
      recommendations.push('🧠 降低复杂度：简化逻辑，提取子函数');
    }
    
    // 特定文件建议
    if (stats.filePath.includes('controller')) {
      recommendations.push('🎮 控制器拆分：状态管理、事件处理、业务逻辑分离');
    }
    
    if (stats.filePath.includes('manager')) {
      recommendations.push('📋 管理器拆分：按职责分离子系统');
    }
    
    if (stats.filePath.includes('renderer') || stats.filePath.includes('animation')) {
      recommendations.push('🎨 渲染器拆分：按效果类型分离');
    }
    
    return recommendations;
  }

  /**
   * 文件分类
   */
  categorizeFile(lines) {
    if (lines >= this.thresholds.giant) return '🔴 巨型';
    if (lines >= this.thresholds.large) return '🟡 大型';
    if (lines >= this.thresholds.medium) return '🟢 中等';
    return '🔵 小型';
  }

  /**
   * 计算重构优先级
   */
  calculatePriority(lines, filePath) {
    let priority = 0;
    
    // 基于文件大小
    if (lines > 3000) priority += 100;
    else if (lines > 2000) priority += 80;
    else if (lines > 1000) priority += 60;
    else if (lines > 500) priority += 40;
    
    // 基于文件类型
    if (filePath.includes('controller')) priority += 30;
    if (filePath.includes('manager')) priority += 25;
    if (filePath.includes('main') || filePath.includes('index')) priority += 20;
    if (filePath.includes('game')) priority += 15;
    
    // 基于重构状态
    const status = this.getRefactorStatus(filePath);
    if (status === 'pending') priority += 20;
    if (status === 'in-progress') priority += 10;
    
    return priority;
  }

  /**
   * 获取重构状态
   */
  getRefactorStatus(filePath) {
    // 检查是否已有重构版本
    if (filePath.includes('refactored-')) return 'completed';
    
    // 检查是否有对应的重构文件
    const refactoredPath = filePath.replace(/([^/]+)\.js$/, 'refactored-$1.js');
    if (fs.existsSync(path.join(this.projectRoot, refactoredPath))) {
      return 'has-refactored';
    }
    
    // 特定文件状态
    if (filePath.includes('item-manager')) return 'completed';
    if (filePath.includes('controller.js')) return 'has-refactored';
    if (filePath.includes('grid.js')) return 'has-refactored';
    
    return 'pending';
  }

  /**
   * 获取文件大小
   */
  getFileSize(filePath) {
    try {
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(1);
      return `${sizeKB}KB`;
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * 生成报告
   */
  generateReport() {
    console.log('📊 巨型文件分析报告');
    console.log('='.repeat(80));
    
    // 统计信息
    const giantFiles = this.results.filter(f => f.lines >= this.thresholds.giant);
    const largeFiles = this.results.filter(f => f.lines >= this.thresholds.large && f.lines < this.thresholds.giant);
    const mediumFiles = this.results.filter(f => f.lines >= this.thresholds.medium && f.lines < this.thresholds.large);
    
    console.log(`\n📈 文件分布统计:`);
    console.log(`🔴 巨型文件 (>2000行): ${giantFiles.length}个`);
    console.log(`🟡 大型文件 (1000-2000行): ${largeFiles.length}个`);
    console.log(`🟢 中等文件 (500-1000行): ${mediumFiles.length}个`);
    console.log(`📁 总扫描文件: ${this.results.length}个`);
    
    // 按优先级排序的重构清单
    const refactorList = this.results
      .filter(f => f.lines >= this.thresholds.medium)
      .sort((a, b) => b.priority - a.priority);
    
    console.log(`\n🎯 重构优先级清单:`);
    console.log('-'.repeat(80));
    
    refactorList.forEach((file, index) => {
      console.log(`\n${index + 1}. ${file.category} ${file.path}`);
      console.log(`   📏 ${file.lines}行 | 📦 ${file.size} | 🎯 优先级: ${file.priority}`);
      console.log(`   🔧 可重构性: ${file.refactorability}% | 📊 复杂度: ${file.complexity}`);
      console.log(`   📋 状态: ${file.status}`);
      
      if (file.recommendations.length > 0) {
        console.log(`   💡 建议:`);
        file.recommendations.forEach(rec => {
          console.log(`      ${rec}`);
        });
      }
    });
    
    // 生成Markdown报告
    this.generateMarkdownReport(refactorList);
    
    console.log(`\n✅ 详细报告已保存到: docs/GIANT_FILES_ANALYSIS.md`);
  }

  /**
   * 生成Markdown报告
   */
  generateMarkdownReport(refactorList) {
    const report = `# 🔍 巨型文件分析报告

## 📊 概览统计

| 类型 | 数量 | 阈值 |
|------|------|------|
| 🔴 巨型文件 | ${this.results.filter(f => f.lines >= this.thresholds.giant).length} | >2000行 |
| 🟡 大型文件 | ${this.results.filter(f => f.lines >= this.thresholds.large && f.lines < this.thresholds.giant).length} | 1000-2000行 |
| 🟢 中等文件 | ${this.results.filter(f => f.lines >= this.thresholds.medium && f.lines < this.thresholds.large).length} | 500-1000行 |

## 🎯 重构优先级清单

${refactorList.map((file, index) => `
### ${index + 1}. ${file.category} \`${file.path}\`

- **文件大小**: ${file.lines}行 (${file.size})
- **优先级**: ${file.priority}
- **可重构性**: ${file.refactorability}%
- **复杂度**: ${file.complexity}
- **状态**: ${file.status}

**重构建议**:
${file.recommendations.map(rec => `- ${rec}`).join('\n')}
`).join('\n')}

## 📋 下一步行动

### 立即处理 (🔴 高优先级)
${refactorList.filter(f => f.priority > 80).map(f => `- \`${f.path}\` (${f.lines}行)`).join('\n')}

### 计划处理 (🟡 中优先级)  
${refactorList.filter(f => f.priority > 60 && f.priority <= 80).map(f => `- \`${f.path}\` (${f.lines}行)`).join('\n')}

### 后续优化 (🟢 低优先级)
${refactorList.filter(f => f.priority <= 60).map(f => `- \`${f.path}\` (${f.lines}行)`).join('\n')}

---
生成时间: ${new Date().toLocaleString()}
`;

    fs.writeFileSync('docs/GIANT_FILES_ANALYSIS.md', report);
  }
}

// 执行扫描
if (require.main === module) {
  const scanner = new GiantFileScanner();
  scanner.scan().catch(console.error);
}

module.exports = { GiantFileScanner };
