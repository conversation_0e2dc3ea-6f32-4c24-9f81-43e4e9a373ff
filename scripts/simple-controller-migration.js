#!/usr/bin/env node

/**
 * 简化版Controller.js迁移脚本
 * 安全地执行controller替换
 */

const fs = require('fs');
const path = require('path');

class SimpleControllerMigration {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.results = {
      success: false,
      errors: [],
      warnings: [],
      changes: []
    };
  }

  /**
   * 执行迁移
   */
  async execute() {
    console.log('🚀 开始Controller.js迁移');
    console.log('='.repeat(50));

    try {
      // Step 1: 检查文件状态
      this.checkFileStatus();
      
      // Step 2: 创建备份
      this.createBackup();
      
      // Step 3: 查找引用文件
      this.findReferences();
      
      // Step 4: 更新引用
      this.updateReferences();
      
      // Step 5: 验证结果
      this.verifyMigration();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 迁移失败:', error.message);
      this.results.errors.push(error.message);
      this.generateReport();
    }
  }

  /**
   * 检查文件状态
   */
  checkFileStatus() {
    console.log('\n📋 Step 1: 检查文件状态');
    
    const originalPath = path.join(this.projectRoot, 'js/game/controller.js');
    const refactoredPath = path.join(this.projectRoot, 'js/game/refactored-controller.js');
    
    // 检查原始文件
    if (fs.existsSync(originalPath)) {
      const stats = fs.statSync(originalPath);
      const lines = fs.readFileSync(originalPath, 'utf8').split('\n').length;
      console.log('   ✅ 原始controller.js存在: ' + lines + '行');
    } else {
      console.log('   ⚠️  原始controller.js不存在');
    }
    
    // 检查重构文件
    if (!fs.existsSync(refactoredPath)) {
      throw new Error('refactored-controller.js不存在，无法执行迁移');
    }
    
    const refactoredStats = fs.statSync(refactoredPath);
    const refactoredLines = fs.readFileSync(refactoredPath, 'utf8').split('\n').length;
    console.log('   ✅ refactored-controller.js存在: ' + refactoredLines + '行');
    
    // 检查关键内容
    const content = fs.readFileSync(refactoredPath, 'utf8');
    if (!content.includes('class') && !content.includes('export')) {
      this.results.warnings.push('refactored-controller.js可能缺少主要类定义');
    }
    
    console.log('✅ 文件状态检查完成');
  }

  /**
   * 创建备份
   */
  createBackup() {
    console.log('\n💾 Step 2: 创建备份');
    
    const backupDir = path.join(this.projectRoot, 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const originalPath = path.join(this.projectRoot, 'js/game/controller.js');
    if (fs.existsSync(originalPath)) {
      const timestamp = Date.now();
      const backupPath = path.join(backupDir, 'controller-original-' + timestamp + '.js');
      
      fs.copyFileSync(originalPath, backupPath);
      console.log('   ✅ 备份已创建: ' + backupPath);
      this.results.changes.push('创建备份: ' + backupPath);
    }
    
    console.log('✅ 备份创建完成');
  }

  /**
   * 查找引用文件
   */
  findReferences() {
    console.log('\n🔍 Step 3: 查找引用文件');
    
    this.referencingFiles = [];
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          if (['node_modules', '.git', 'backups'].includes(item)) continue;
          
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            scanDir(fullPath);
          } else if (item.endsWith('.js') && !item.includes('controller')) {
            try {
              const content = fs.readFileSync(fullPath, 'utf8');
              if (content.includes('controller.js') || content.includes('from \'./controller\'') || content.includes('from "../game/controller"')) {
                this.referencingFiles.push(path.relative(this.projectRoot, fullPath));
              }
            } catch (error) {
              // 忽略读取错误
            }
          }
        }
      } catch (error) {
        // 忽略目录访问错误
      }
    };
    
    scanDir(this.projectRoot);
    
    console.log('   📊 找到 ' + this.referencingFiles.length + ' 个引用文件:');
    this.referencingFiles.forEach(file => {
      console.log('      - ' + file);
    });
    
    console.log('✅ 引用文件查找完成');
  }

  /**
   * 更新引用
   */
  updateReferences() {
    console.log('\n🔄 Step 4: 更新引用');
    
    let updatedCount = 0;
    
    for (const filePath of this.referencingFiles) {
      try {
        const fullPath = path.join(this.projectRoot, filePath);
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // 替换引用
        let updatedContent = content;
        updatedContent = updatedContent.replace(/from\s+['"](.*)controller(\.js)?['"]/gi, "from '$1refactored-controller.js'");
        updatedContent = updatedContent.replace(/import\s+(.*)from\s+['"](.*)controller(\.js)?['"]/gi, "import $1from '$2refactored-controller.js'");
        
        if (updatedContent !== content) {
          // 创建备份
          const backupPath = fullPath + '.backup.' + Date.now();
          fs.writeFileSync(backupPath, content);
          
          // 写入更新内容
          fs.writeFileSync(fullPath, updatedContent);
          
          console.log('   ✅ 已更新: ' + filePath);
          this.results.changes.push('更新引用: ' + filePath);
          updatedCount++;
          
          // 5秒后删除备份
          setTimeout(() => {
            if (fs.existsSync(backupPath)) {
              fs.unlinkSync(backupPath);
            }
          }, 5000);
        }
      } catch (error) {
        console.log('   ❌ 更新失败: ' + filePath + ' - ' + error.message);
        this.results.errors.push('更新失败: ' + filePath);
      }
    }
    
    console.log('   📊 共更新了 ' + updatedCount + ' 个文件');
    console.log('✅ 引用更新完成');
  }

  /**
   * 验证迁移
   */
  verifyMigration() {
    console.log('\n🧪 Step 5: 验证迁移');
    
    // 检查refactored-controller.js是否存在
    const refactoredPath = path.join(this.projectRoot, 'js/game/refactored-controller.js');
    if (!fs.existsSync(refactoredPath)) {
      throw new Error('refactored-controller.js文件丢失');
    }
    
    // 检查语法
    try {
      const content = fs.readFileSync(refactoredPath, 'utf8');
      if (content.includes('SyntaxError')) {
        this.results.warnings.push('可能存在语法错误');
      }
    } catch (error) {
      this.results.errors.push('文件读取失败: ' + error.message);
    }
    
    console.log('✅ 迁移验证完成');
  }

  /**
   * 生成报告
   */
  generateReport() {
    console.log('\n📋 生成迁移报告');
    
    const success = this.results.errors.length === 0;
    this.results.success = success;
    
    const report = `# Controller.js 迁移报告

## 📊 迁移结果

- **状态**: ${success ? '✅ 成功' : '❌ 失败'}
- **时间**: ${new Date().toLocaleString()}
- **更新文件数**: ${this.results.changes.length}

## 🔄 执行的变更

${this.results.changes.map(change => `- ${change}`).join('\n')}

## ⚠️ 警告信息

${this.results.warnings.length > 0 ? 
  this.results.warnings.map(warning => `- ${warning}`).join('\n') : 
  '无警告'}

## ❌ 错误信息

${this.results.errors.length > 0 ? 
  this.results.errors.map(error => `- ${error}`).join('\n') : 
  '无错误'}

## 🎯 下一步

${success ? 
  `✅ 迁移成功！建议：
1. 测试游戏功能是否正常
2. 删除原始controller.js文件
3. 将refactored-controller.js重命名为controller.js` :
  `❌ 迁移失败，需要：
1. 检查并修复错误
2. 从备份恢复文件
3. 重新执行迁移`}

---
生成时间: ${new Date().toLocaleString()}
`;

    fs.writeFileSync('docs/CONTROLLER_MIGRATION_REPORT.md', report);
    
    console.log('\n' + '='.repeat(50));
    console.log('📋 迁移摘要');
    console.log('状态: ' + (success ? '✅ 成功' : '❌ 失败'));
    console.log('更新文件: ' + this.results.changes.length + '个');
    console.log('警告: ' + this.results.warnings.length + '个');
    console.log('错误: ' + this.results.errors.length + '个');
    console.log('报告保存到: docs/CONTROLLER_MIGRATION_REPORT.md');
    
    if (success) {
      console.log('\n🎉 Controller.js迁移成功完成！');
      console.log('\n📋 后续步骤:');
      console.log('1. 测试游戏功能');
      console.log('2. 删除原始controller.js');
      console.log('3. 重命名refactored-controller.js');
    } else {
      console.log('\n❌ 迁移失败，请检查错误并重试');
    }
  }
}

// 执行迁移
if (require.main === module) {
  const migration = new SimpleControllerMigration();
  migration.execute().catch(console.error);
}

module.exports = { SimpleControllerMigration };
