/**
 * 道具升级系统
 * 负责管理道具的等级、使用次数和解锁状态
 */
export default class UpgradeSystem {
  constructor() {
    // 道具等级
    this.itemLevels = {
      fireball: 1,
      lightning: 1,
      waterflow: 1,
      earthquake: 1
    };
    
    // 道具剩余使用次数
    this.itemUses = {
      fireball: 30,  // 初始30次火球术
      lightning: 20, // 初始20次闪电链
      waterflow: 15, // 初始15次激流
      earthquake: 10 // 初始10次地震术
    };

    // 道具解锁状态
    this.itemUnlockLevels = {
      fireball: 2,   // 第2关解锁火球术
      lightning: 5,  // 第5关解锁闪电链
      waterflow: 8,  // 第8关解锁激流
      earthquake: 11 // 第11关解锁地震术（第二阶段第2关）
    };

    // 当前解锁的道具
    this.unlockedItems = new Set();

    // 当前关卡ID
    this.currentLevelId = 1;
  }

  /**
   * 设置当前关卡并检查道具解锁
   */
  setCurrentLevel(levelId) {
    this.currentLevelId = levelId;
    this._checkItemUnlocks();
  }

  /**
   * 检查道具解锁状态
   */
  _checkItemUnlocks() {
    Object.entries(this.itemUnlockLevels).forEach(([itemType, unlockLevel]) => {
      if (this.currentLevelId >= unlockLevel) {
        this.unlockedItems.add(itemType);
      }
    });
  }

  /**
   * 检查道具是否已解锁
   */
  isItemUnlocked(itemType) {
    return this.unlockedItems.has(itemType);
  }

  /**
   * 获取已解锁的道具列表
   */
  getUnlockedItems() {
    return Array.from(this.unlockedItems);
  }

  /**
   * 检查道具是否可以使用（有使用次数且已解锁）
   */
  canUseItem(itemType) {
    return this.isItemUnlocked(itemType) && this.itemUses[itemType] > 0;
  }

  /**
   * 使用道具（减少使用次数）
   */
  useItem(itemType) {
    if (this.canUseItem(itemType)) {
      this.itemUses[itemType]--;
      return true;
    }
    return false;
  }

  /**
   * 升级道具
   */
  upgradeItem(itemType) {
    if (this.isItemUnlocked(itemType) && this.itemLevels[itemType] < 5) {
      this.itemLevels[itemType]++;
      return true;
    }
    return false;
  }

  /**
   * 获取道具等级
   */
  getItemLevel(itemType) {
    return this.itemLevels[itemType];
  }

  /**
   * 获取道具使用次数
   */
  getItemUses(itemType) {
    return this.itemUses[itemType];
  }

  /**
   * 重置道具使用次数
   */
  resetItemUses(itemType, uses) {
    this.itemUses[itemType] = uses;
  }

  /**
   * 增加道具使用次数
   */
  addItemUses(itemType, amount = 1) {
    this.itemUses[itemType] += amount;
  }

  /**
   * 获取道具完整信息
   */
  getItemInfo(itemType) {
    return {
      level: this.itemLevels[itemType],
      uses: this.itemUses[itemType],
      unlocked: this.isItemUnlocked(itemType),
      canUse: this.canUseItem(itemType)
    };
  }
} 