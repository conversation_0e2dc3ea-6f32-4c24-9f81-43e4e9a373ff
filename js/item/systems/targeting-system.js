/**
 * 道具目标选择系统
 * 负责为道具选择最佳目标位置
 */
export default class TargetingSystem {
  constructor(grid) {
    this.grid = grid;
  }

  /**
   * 为火球术找到最佳目标
   */
  findBestFireballTarget(level) {
    const range = this._getFireballRange(level);
    let bestTarget = null;
    let maxScore = 0;
    let totalBlocks = 0;

    // 遍历网格寻找最佳位置
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          totalBlocks++;
          const score = this._calculateFireballScore(row, col, range);
          if (score > maxScore) {
            maxScore = score;
            bestTarget = { row, col };
          }
        }
      }
    }

    console.log(`🎯 火球术目标: ${bestTarget ? `(${bestTarget.row}, ${bestTarget.col})` : '无目标'}, 网格方块数: ${totalBlocks}`);
    return bestTarget;
  }

  /**
   * 为闪电链找到最佳目标
   */
  findBestLightningTarget(level) {
    const maxChainCount = this._getLightningChainCount(level);
    let bestTarget = null;
    let maxChainLength = 0;

    // 遍历所有方块寻找最长链
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          const chainLength = this._calculatePotentialChainLength(row, col, block.color, maxChainCount);
          if (chainLength > maxChainLength) {
            maxChainLength = chainLength;
            bestTarget = { row, col };
          }
        }
      }
    }

    return bestTarget;
  }

  /**
   * 找到最高的方块位置
   */
  findHighestBlockPosition() {
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          return { row, col };
        }
      }
    }
    return null;
  }

  /**
   * 找到随机目标位置
   */
  findRandomTarget() {
    const validTargets = [];
    
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.blocks[row] && this.grid.blocks[row][col];
        if (block && !block.isEmpty) {
          validTargets.push({ row, col });
        }
      }
    }

    if (validTargets.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * validTargets.length);
    return validTargets[randomIndex];
  }

  /**
   * 计算火球术影响的方块数量
   */
  _calculateFireballScore(centerRow, centerCol, range) {
    let score = 0;
    
    for (let row = Math.max(0, centerRow - range); row <= Math.min(this.grid.rows - 1, centerRow + range); row++) {
      for (let col = Math.max(0, centerCol - range); col <= Math.min(this.grid.cols - 1, centerCol + range); col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          // 距离中心越近，分数越高
          const distance = Math.abs(row - centerRow) + Math.abs(col - centerCol);
          score += Math.max(1, range + 1 - distance);
        }
      }
    }
    
    return score;
  }

  /**
   * 计算潜在的闪电链长度
   */
  _calculatePotentialChainLength(startRow, startCol, targetColor, maxChainCount) {
    const visited = new Set();
    const queue = [{ row: startRow, col: startCol }];
    let chainLength = 0;

    while (queue.length > 0 && chainLength < maxChainCount) {
      const { row, col } = queue.shift();
      const key = `${row},${col}`;
      
      if (visited.has(key)) continue;
      visited.add(key);
      
      const block = this.grid.getBlock(row, col);
      if (!block || this._getBlockOriginalColor(block) !== targetColor) {
        continue;
      }
      
      chainLength++;
      
      // 添加相邻方块到队列
      const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]];
      for (const [dr, dc] of directions) {
        const newRow = row + dr;
        const newCol = col + dc;
        if (this._isValidPosition(newRow, newCol) && !visited.has(`${newRow},${newCol}`)) {
          queue.push({ row: newRow, col: newCol });
        }
      }
    }

    return chainLength;
  }

  /**
   * 获取火球术范围
   */
  _getFireballRange(level) {
    const ranges = [1, 1, 2, 2, 2]; // 等级1-5的范围
    return ranges[level - 1] || 1;
  }

  /**
   * 获取闪电链连锁数量
   */
  _getLightningChainCount(level) {
    const chainCounts = [5, 5, 8, 8, 8]; // 等级1-5的连锁数量
    return chainCounts[level - 1] || 5;
  }

  /**
   * 获取方块的原始颜色（处理冰冻状态）
   */
  _getBlockOriginalColor(block) {
    return block.originalColor || block.color;
  }

  /**
   * 检查位置是否有效
   */
  _isValidPosition(row, col) {
    return row >= 0 && row < this.grid.rows && col >= 0 && col < this.grid.cols;
  }
} 