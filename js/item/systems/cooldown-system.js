/**
 * 道具冷却系统
 * 负责管理道具的冷却时间和使用限制
 */
export default class CooldownSystem {
  constructor() {
    // 道具冷却时间（帧数）- 初始化为0表示可以立即使用
    this.cooldowns = {
      fireball: 0,
      lightning: 0,
      waterflow: 0,
      earthquake: 0
    };
    
    // 道具的最大冷却时间（帧数）
    this.maxCooldowns = {
      fireball: 180,   // 3秒 (60帧/秒)
      lightning: 300,  // 5秒
      waterflow: 420,  // 7秒
      earthquake: 600  // 10秒
    };
  }

  /**
   * 更新冷却时间
   */
  update() {
    // 减少所有道具的冷却时间
    Object.keys(this.cooldowns).forEach(itemType => {
      if (this.cooldowns[itemType] > 0) {
        this.cooldowns[itemType]--;
      }
    });
  }

  /**
   * 检查道具是否可以使用（冷却完成）
   */
  isItemReady(itemType) {
    return this.cooldowns[itemType] <= 0;
  }

  /**
   * 应用道具冷却
   */
  applyCooldown(itemType) {
    this.cooldowns[itemType] = this.maxCooldowns[itemType];
  }

  /**
   * 获取道具冷却进度 (0-1)
   */
  getCooldownProgress(itemType) {
    const maxCooldown = this.maxCooldowns[itemType];
    const currentCooldown = this.cooldowns[itemType];
    return maxCooldown > 0 ? (maxCooldown - currentCooldown) / maxCooldown : 1;
  }

  /**
   * 重置道具冷却
   */
  resetCooldown(itemType) {
    this.cooldowns[itemType] = 0;
  }

  /**
   * 获取剩余冷却时间（秒）
   */
  getRemainingCooldownSeconds(itemType) {
    return Math.ceil(this.cooldowns[itemType] / 60);
  }
} 