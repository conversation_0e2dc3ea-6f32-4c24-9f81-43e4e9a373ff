/**
 * 地震术道具实现
 */
export default class EarthquakeItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
  }

  use(row, col, level, callbacks) {
    console.log('🗂️ 网格布局');
    this.grid.debugGridState('地震术使用前的网格状态', true);
    console.log(`🌍 使用地震术道具 等级${level}`);

    // 根据等级确定影响的行数（从底部开始）
    const affectedRows = level + 2; // 1级影响3行，2级影响4行，3级影响5行
    const startRow = Math.max(0, this.grid.rows - affectedRows);
    const endRow = this.grid.rows - 1;

    console.log(`🌍 地震术影响第${startRow}行到第${endRow}行（共${affectedRows}行）`);

    // 地震术让受影响区域的方块下落
    const hasFallen = this._executeEarthquakeGravity(startRow, endRow);

    if (hasFallen) {
      callbacks.createEarthquakeEffect(startRow, endRow, level);
      callbacks.playSound('地震术.mp3');
      console.log(`🌍 地震术让底部${affectedRows}行的方块下落`);
      return true;
    }

    console.log(`🌍 地震术没有产生效果`);
    return false;
  }

  /**
   * 执行地震重力效果
   */
  _executeEarthquakeGravity(startRow, endRow) {
    try {
      // 检查受影响区域是否有方块可以下落
      let hasBlocksToFall = false;

      for (let row = startRow; row <= endRow; row++) {
        for (let col = 0; col < this.grid.cols; col++) {
          const block = this.grid.getBlock(row, col);
          if (block) {
            // 检查下方是否有空位
            if (row < this.grid.rows - 1 && !this.grid.getBlock(row + 1, col)) {
              hasBlocksToFall = true;
              break;
            }
          }
        }
        if (hasBlocksToFall) break;
      }

      if (hasBlocksToFall) {
        // 应用重力到受影响的列
        const affectedCols = [];
        for (let col = 0; col < this.grid.cols; col++) {
          affectedCols.push(col);
        }

        if (this.grid.applyGravity) {
          this.grid.applyGravity(affectedCols);
        } else if (this.grid.applyFullGridGravity) {
          this.grid.applyFullGridGravity();
        }

        console.log('🌍 地震术触发了方块下落');
        return true;
      } else {
        console.log('🌍 地震术影响区域没有方块需要下落');
        return false;
      }
    } catch (error) {
      console.error('地震术重力处理失败:', error);
      return false;
    }
  }
}
