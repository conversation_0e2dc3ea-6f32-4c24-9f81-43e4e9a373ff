/**
 * 火球术道具
 * 负责火球术的逻辑处理
 */
export default class FireballItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
    
    // 火球术效果配置
    this.effects = {
      range: [1, 1, 2, 2, 2], // 等级1-5的范围
      fireballCount: [1, 1, 1, 2, 2], // 等级1-5的火球数量
      targeting: ['manual', 'smart_highest', 'smart_highest', 'smart_highest', 'dual_smart'] // 瞄准方式
    };
    
    this.affectedBlocks = new Set();
  }

  /**
   * 使用火球术
   * @param {number} row - 忽略，火球术使用智能目标查找
   * @param {number} col - 忽略，火球术使用智能目标查找
   * @param {number} level - 道具等级
   * @param {Object} callbacks - 回调函数
   */
  use(_row, _col, level, callbacks = {}) {
    console.log('🗂️ 网格布局');
    this.grid.debugGridState('火球术使用前的网格状态', true);
    console.log('🔥 处理火球术使用，等级:', level);
    
    // 确定火球发射次数
    const fireballCount = this.effects.fireballCount[level - 1];
    console.log(`等级${level}火球术将发射${fireballCount}发火球`);
    
    let success = false;
    
    for (let i = 0; i < fireballCount; i++) {
      console.log(`🔥 发射第${i + 1}发火球`);
      
      // 火球术总是使用智能目标查找，忽略传入的坐标
      const target = this._findSmartTarget(level);
      if (!target) {
        console.log(`第${i + 1}发火球没有找到有效目标`);
        continue;
      }

      const targetRow = target.row;
      const targetCol = target.col;
      console.log(`🎯 第${i + 1}发火球目标: (${targetRow}, ${targetCol})`);
      
      // 发射火球
      const fireballSuccess = this._executeFireball(targetRow, targetCol, level, callbacks);
      if (fireballSuccess) {
        success = true;
        
        // 如果是多发火球，等待第一发结算完成后再发射下一发
        if (i < fireballCount - 1) {
          this._scheduleDelayedFireball(i + 1, level, 800, callbacks); // 800ms延迟
        }
      }
    }
    
    return success;
  }

  /**
   * 查找智能目标
   */
  _findSmartTarget(level) {
    const target = this.targetingSystem.findBestFireballTarget(level);
    console.log(`🎯 火球术目标查找结果:`, target);
    return target;
  }

  /**
   * 执行单发火球
   */
  _executeFireball(row, col, level, callbacks = {}) {
    // 检查目标位置是否有效
    if (!this._isValidPosition(row, col)) {
      console.log('目标位置无效');
      return false;
    }
    
    // 获取火球影响范围
    const range = this.effects.range[level - 1];
    console.log(`火球术使用，等级: ${level}, 范围: ${range}`);
    
    // 计算爆炸范围
    const minRow = Math.max(0, row - range);
    const maxRow = Math.min(this.grid.rows - 1, row + range);
    const minCol = Math.max(0, col - range);
    const maxCol = Math.min(this.grid.cols - 1, col + range);
    
    console.log(`爆炸范围: (${minRow},${minCol}) 到 (${maxRow},${maxCol})`);
    
    // 遍历爆炸范围内的所有方块
    let hasValidTarget = false;
    let blockCount = 0;
    
    for (let r = minRow; r <= maxRow; r++) {
      for (let c = minCol; c <= maxCol; c++) {
        // 计算与中心的距离，实现圆形爆炸效果
        const distance = Math.sqrt(Math.pow(r - row, 2) + Math.pow(c - col, 2));
        
        // 如果距离在范围内（圆形效果）
        if (distance <= range) {
          const block = this.grid.getBlock(r, c);
          
          if (block) {
            blockCount++;
            hasValidTarget = true;
            this.affectedBlocks.add(block);
            if (callbacks.markBlockAffected) {
              callbacks.markBlockAffected(block);
            }
          }
        }
      }
    }
    
    console.log('范围内方块数量:', blockCount);
    
    if (!hasValidTarget) {
      console.log('目标区域内没有可消除的方块');
      return false;
    }
    
    // 创建火球动画效果和播放音效
    if (callbacks.createExplosionEffect) {
      callbacks.createExplosionEffect(row, col, range);
    }
    if (callbacks.playSound) {
      callbacks.playSound('fireball');
    }
    
    console.log('火球术使用成功');
    return true;
  }

  /**
   * 延迟发射火球
   */
  _scheduleDelayedFireball(fireballIndex, level, delay, callbacks) {
    console.log(`📅 安排${delay}ms后发射第${fireballIndex + 1}发火球`);
    
    setTimeout(() => {
      console.log(`⏰ 开始发射延迟的第${fireballIndex + 1}发火球`);
      
      // 寻找新的目标（因为第一发可能已经改变了布局）
      const target = this._findSmartTarget(level);
      if (target) {
        // 创建新的临时受影响方块集合
        const previousAffected = new Set(this.affectedBlocks);
        this.affectedBlocks.clear();
        
        const success = this._executeFireball(target.row, target.col, level, callbacks);
        
        if (success) {
          // 合并受影响的方块
          for (const block of previousAffected) {
            this.affectedBlocks.add(block);
          }
          
          console.log(`✅ 第${fireballIndex + 1}发火球发射成功`);
        }
      }
    }, delay);
  }

  /**
   * 计算火球在指定位置影响的方块数
   */
  countAffectedBlocks(row, col, level) {
    const range = this.effects.range[level - 1];
    let blockCount = 0;
    
    // 计算爆炸范围
    const minRow = Math.max(0, row - range);
    const maxRow = Math.min(this.grid.rows - 1, row + range);
    const minCol = Math.max(0, col - range);
    const maxCol = Math.min(this.grid.cols - 1, col + range);
    
    // 遍历爆炸范围内的所有方块
    for (let r = minRow; r <= maxRow; r++) {
      for (let c = minCol; c <= maxCol; c++) {
        // 计算与中心的距离，实现圆形爆炸效果
        const distance = Math.sqrt(Math.pow(r - row, 2) + Math.pow(c - col, 2));
        
        // 如果距离在范围内（圆形效果）
        if (distance <= range) {
          const block = this.grid.getBlock(r, c);
          if (block) {
            blockCount++;
          }
        }
      }
    }
    
    return blockCount;
  }

  /**
   * 获取受影响的方块
   */
  getAffectedBlocks() {
    return this.affectedBlocks;
  }

  /**
   * 清空受影响的方块
   */
  clearAffectedBlocks() {
    this.affectedBlocks.clear();
  }

  /**
   * 检查位置是否有效
   */
  _isValidPosition(row, col) {
    return row >= 0 && row < this.grid.rows && col >= 0 && col < this.grid.cols;
  }

  /**
   * 获取火球术配置信息
   */
  getEffectConfig(level) {
    return {
      range: this.effects.range[level - 1],
      fireballCount: this.effects.fireballCount[level - 1],
      targeting: this.effects.targeting[level - 1]
    };
  }
} 