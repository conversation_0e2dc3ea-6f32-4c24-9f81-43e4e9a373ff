/**
 * 闪电链道具实现
 * 从原始ItemManager提取并适配到新架构
 */
export default class LightningItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
    
    // 从原始实现中提取的配置
    this.itemEffects = {
      chainCount: [5, 8, 12] // 等级1-3的连锁数量
    };
  }

  /**
   * 使用闪电链道具
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @param {number} level - 道具等级
   * @param {Object} callbacks - 回调函数
   */
  use(row, col, level, callbacks) {
    console.log('🗂️ 网格布局');
    this.grid.debugGridState('闪电链使用前的网格状态', true);
    console.log(`⚡ 使用闪电链道具 等级${level} 位置(${row}, ${col})`);
    
    // 自动寻找最佳目标
    const target = this._findBestTarget();
    if (!target) {
      console.log('闪电链未找到有效目标');
      return false;
    }
    
    // 执行闪电链逻辑
    const chainPath = this._calculateLightningChain(target.row, target.col, target.color, level);
    
    if (chainPath.length > 0) {
      // 标记所有连接的方块
      chainPath.forEach(pos => {
        const block = this.grid.getBlock(pos.row, pos.col);
        if (block) {
          callbacks.markBlockAffected(block);
        }
      });
      
      // 创建闪电效果
      callbacks.createLightningEffect(target.row, target.col, chainPath);
      
      // 播放音效
      callbacks.playSound('闪电链.mp3');
      
      console.log(`⚡ 闪电链连接了 ${chainPath.length} 个方块`);
      return true;
    }
    
    return false;
  }

  /**
   * 寻找最佳目标
   */
  _findBestTarget() {
    const targets = [];
    
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          targets.push({ row, col, color: block.color, block });
        }
      }
    }
    
    if (targets.length === 0) return null;
    
    // 随机选择一个目标
    return targets[Math.floor(Math.random() * targets.length)];
  }

  /**
   * 计算闪电链路径
   */
  _calculateLightningChain(startRow, startCol, targetColor, level) {
    const chainPath = [];
    const visited = new Set();
    const maxChain = this.itemEffects.chainCount[level - 1] || 5;
    
    const queue = [{row: startRow, col: startCol}];
    visited.add(`${startRow},${startCol}`);
    
    while (queue.length > 0 && chainPath.length < maxChain) {
      const current = queue.shift();
      chainPath.push(current);
      
      // 查找相邻的相同颜色方块
      const neighbors = this._getNeighbors(current.row, current.col);
      for (const neighbor of neighbors) {
        const key = `${neighbor.row},${neighbor.col}`;
        if (visited.has(key)) continue;
        
        const block = this.grid.getBlock(neighbor.row, neighbor.col);
        if (block && block.color === targetColor) {
          visited.add(key);
          queue.push(neighbor);
        }
      }
    }
    
    return chainPath;
  }

  /**
   * 获取相邻位置
   */
  _getNeighbors(row, col) {
    const neighbors = [];
    const directions = [[-1,0], [1,0], [0,-1], [0,1], [-1,-1], [-1,1], [1,-1], [1,1]];
    
    for (const [dr, dc] of directions) {
      const newRow = row + dr;
      const newCol = col + dc;
      if (this._isValidPosition(newRow, newCol)) {
        neighbors.push({row: newRow, col: newCol});
      }
    }
    
    return neighbors;
  }

  /**
   * 检查位置是否有效
   */
  _isValidPosition(row, col) {
    return row >= 0 && row < this.grid.rows && col >= 0 && col < this.grid.cols;
  }
}
