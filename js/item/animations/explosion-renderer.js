/**
 * 爆炸效果渲染器
 * 负责渲染火球术的爆炸动画效果
 */
export default class ExplosionRenderer {
  constructor() {
    // 预加载爆炸帧图片
    this.explosionFrames = [];
    this._preloadExplosionFrames();
  }

  /**
   * 预加载爆炸帧图片
   */
  _preloadExplosionFrames() {
    const frames = [];
    let loadedCount = 0;
    let errorCount = 0;
    
    console.log('开始预加载爆炸帧图片...');
    
    try {
      const basePath = 'images/';
      const inWechatGameEnv = typeof wx !== 'undefined' && wx.createImage && wx.getFileSystemManager;
      
      for (let i = 1; i <= 19; i++) {
        const filename = `explosion${i}.png`; // 使用实际的文件名格式
        
        let img;
        if (inWechatGameEnv) {
          img = wx.createImage();
        } else {
          img = new Image();
        }
        
        img.onload = () => {
          loadedCount++;
          console.log(`爆炸帧 ${filename} 加载完成 (${loadedCount}/19)`);
          
          if (loadedCount === 19) {
            console.log('所有爆炸帧图片预加载完成');
            this.explosionFrames = frames;
          }
        };
        
        img.onerror = () => {
          errorCount++;
          console.warn(`爆炸帧 ${filename} 加载失败 (${errorCount}/19)`);
        };
        
        img.src = basePath + filename;
        frames[i-1] = img;
      }
    } catch (e) {
      console.error('预加载爆炸帧图片失败:', e);
    }
  }

  /**
   * 创建爆炸效果
   */
  createExplosionEffect(centerRow, centerCol, range, grid, level = 1) {
    // 计算爆炸中心的屏幕坐标
    const cellSize = grid.blockSize || 30;
    const offsetX = grid.offsetX || 0;
    const offsetY = grid.offsetY || 0;
    
    const centerX = offsetX + centerCol * cellSize + cellSize / 2;
    const centerY = offsetY + centerRow * cellSize + cellSize / 2;
    
    // 计算真实的覆盖直径（范围×2+1）×单元格大小
    const coverageDiameter = (range * 2 + 1) * cellSize;
    
    console.log(`创建爆炸效果，坐标:(${centerX},${centerY}), 范围:${range}, 覆盖直径:${coverageDiameter}px`);
    
    // 使用预加载的帧动画或简单圆形效果
    if (this.explosionFrames && this.explosionFrames.length > 0) {
      console.log(`✅ 使用预加载的${this.explosionFrames.length}帧爆炸动画，火球等级:${level}`);
      return this._createFrameAnimation(centerX, centerY, coverageDiameter, level);
    } else {
      console.log(`⚠️ 使用简单圆形爆炸效果 (预加载帧不可用)，火球等级:${level}`);
      console.log(`🔍 调试信息: explosionFrames=${this.explosionFrames}, length=${this.explosionFrames ? this.explosionFrames.length : 'undefined'}`);
      return this._createSimpleAnimation(centerX, centerY, coverageDiameter, level);
    }
  }

  /**
   * 创建帧动画效果
   */
  _createFrameAnimation(x, y, coverageDiameter, level) {
    // 计算合适的动画尺寸
    let animSize = coverageDiameter;
    const visualBonus = level * 10;
    animSize += visualBonus;
    
    if (animSize < 80) {
      animSize = 80;
    }
    
    console.log(`启动爆炸帧动画，位置:(${x},${y}), 实际尺寸:${animSize}px, 火球等级:${level}`);
    
    return {
      type: 'frame',
      x: x,
      y: y,
      size: animSize,
      currentFrame: 0,
      frameDuration: 35,
      lastFrameTime: Date.now(),
      frames: this.explosionFrames,
      level: level
    };
  }

  /**
   * 创建简单圆形动画效果
   */
  _createSimpleAnimation(centerX, centerY, coverageDiameter, level) {
    const maxRadius = coverageDiameter / 2;
    
    return {
      type: 'simple',
      x: centerX,
      y: centerY,
      radius: 0,
      maxRadius: maxRadius,
      frame: 0,
      maxFrames: 25,
      level: level
    };
  }

  /**
   * 渲染爆炸效果
   */
  render(ctx, effect) {
    if (!ctx || !effect) return false;

    if (effect.type === 'frame') {
      return this._renderFrameAnimation(ctx, effect);
    } else if (effect.type === 'simple') {
      return this._renderSimpleAnimation(ctx, effect);
    }

    return false;
  }

  /**
   * 渲染帧动画
   */
  _renderFrameAnimation(ctx, effect) {
    const now = Date.now();
    
    // 检查是否需要切换到下一帧
    if (now - effect.lastFrameTime > effect.frameDuration) {
      effect.currentFrame++;
      effect.lastFrameTime = now;
      
      // 动画结束
      if (effect.currentFrame >= effect.frames.length) {
        return false;
      }
    }
    
    // 绘制当前帧
    try {
      const img = effect.frames[effect.currentFrame];
      if (img && img.complete && img.naturalWidth > 0) {
        const halfSize = effect.size / 2;
        const level = effect.level || 1;
        
        // 根据等级设置不同的合成操作
        ctx.globalCompositeOperation = level > 1 ? 'lighter' : 'source-over';
        
        // 设置阴影效果，随等级增强
        ctx.shadowBlur = 10 + level * 8; 
        ctx.shadowColor = `rgba(255, ${150 - level * 25}, 0, 0.8)`;
        
        // 绘制帧图片
        ctx.drawImage(img, effect.x - halfSize, effect.y - halfSize, effect.size, effect.size);
        
        // 高等级添加内核光效
        if (level > 1) {
          const innerSize = effect.size * 0.5;
          const innerX = effect.x - innerSize / 2;
          const innerY = effect.y - innerSize / 2;
          
          ctx.globalAlpha = 0.3 + level * 0.15;
          ctx.drawImage(img, innerX, innerY, innerSize, innerSize);
          ctx.globalAlpha = 1.0;
        }
        
        // 重置合成操作和阴影
        ctx.globalCompositeOperation = 'source-over';
        ctx.shadowBlur = 0;

        // 调试信息（仅在第一帧和最后几帧输出）
        if (effect.currentFrame === 0 || effect.currentFrame >= effect.frames.length - 3) {
          console.log(`🎬 渲染爆炸帧 ${effect.currentFrame + 1}/${effect.frames.length}, 位置:(${effect.x}, ${effect.y}), 尺寸:${effect.size}`);
        }
      } else {
        console.warn(`⚠️ 爆炸帧 ${effect.currentFrame + 1} 图片未完成加载或无效`);
      }
    } catch (e) {
      console.error('渲染爆炸帧失败:', e);
    }
    
    return true;
  }

  /**
   * 渲染简单圆形动画
   */
  _renderSimpleAnimation(ctx, effect) {
    try {
      const { x, y, frame, maxFrames, level } = effect;
      
      // 更新动画状态
      effect.radius = (effect.maxRadius * frame) / maxFrames;
      effect.frame++;
      
      // 动画结束
      if (effect.frame >= maxFrames) {
        return false;
      }
      
      const alpha = 1 - frame / maxFrames;
      const fireLevel = level || 1;
      
      // 外围光环
      ctx.beginPath();
      ctx.arc(x, y, effect.radius, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, ${165 - fireLevel * 25}, 0, ${alpha * 0.5})`;
      ctx.fill();
      
      // 中间层
      ctx.beginPath();
      ctx.arc(x, y, effect.radius * 0.7, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, ${110 - fireLevel * 30}, 0, ${alpha * (0.7 + fireLevel * 0.1)})`;
      ctx.fill();
      
      // 内核
      ctx.beginPath();
      ctx.arc(x, y, effect.radius * 0.4, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, ${60 - fireLevel * 20}, ${fireLevel * 30}, ${alpha * 0.9})`;
      ctx.fill();
      
      // 最内层（高等级）
      if (fireLevel > 1) {
        ctx.beginPath();
        ctx.arc(x, y, effect.radius * 0.2, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, ${180 + fireLevel * 20}, ${alpha * 0.95})`;
        ctx.fill();
      }
    } catch (e) {
      console.error('渲染圆形爆炸效果失败:', e);
    }
    
    return true;
  }
} 