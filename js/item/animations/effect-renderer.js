/**
 * 通用效果渲染器
 */
export default class EffectRenderer {
  constructor() {
    this.activeEffects = [];
  }

  createWaterflowEffect(affectedRows, level) {
    // 创建水波效果
    const wavePoints = this._createWaterWaves(affectedRows, level);

    const effect = {
      id: Date.now() + Math.random(),
      type: 'waterflow',
      affectedRows: affectedRows,
      level,
      wavePoints: wavePoints,
      startTime: Date.now(),
      duration: 400, // 400ms动画时长
      isActive: true
    };

    this.activeEffects.push(effect);
    console.log(`🌊 创建激流动画，影响行:${affectedRows}，波浪数:${wavePoints.length}，等级:${level}`);
    return effect;
  }

  createEarthquakeEffect(startRow, endRow, level) {
    const effect = {
      id: Date.now() + Math.random(),
      type: 'earthquake',
      startRow,
      endRow,
      level,
      startTime: Date.now(),
      duration: 800, // 800ms动画时长
      // 震动参数
      shakeIntensity: 5 + level * 3,
      shakeFrequency: 0.2,
      // 高亮效果
      highlightAlpha: 0,
      highlightDirection: 1,
      // 波动效果
      wavePhase: 0,
      waveAmplitude: 10 + level * 5,
      // 粒子效果
      particles: this._createEarthquakeParticles(startRow, endRow, level),
      isActive: true
    };

    this.activeEffects.push(effect);
    console.log(`🌍 创建地震动画，影响第${startRow}行到第${endRow}行，等级${level}`);
    return effect;
  }

  renderAll(ctx) {
    this.activeEffects = this.activeEffects.filter(effect => {
      return this.renderEffect(ctx, effect);
    });
  }

  renderEffect(ctx, effect) {
    if (!effect || !effect.isActive) {
      return false;
    }

    try {
      switch (effect.type) {
        case 'waterflow':
          return this._renderWaterflow(ctx, effect);
        case 'earthquake':
          return this._renderEarthquake(ctx, effect);
      }
      
      return true;
    } catch (error) {
      console.error('效果渲染错误:', error);
      return false;
    }
  }

  _renderWaterflow(ctx, effect) {
    // 计算动画进度
    const now = Date.now();
    const elapsed = now - effect.startTime;
    const progress = Math.min(1, elapsed / effect.duration);

    // 如果动画结束，清理
    if (progress >= 1) {
      effect.isActive = false;
      return false;
    }

    // 获取网格信息
    const blockSize = 30;
    const offsetX = 50;
    const gridWidth = 10 * blockSize; // 假设10列

    // 保存上下文
    ctx.save();

    // 设置合成模式
    ctx.globalCompositeOperation = 'lighter';

    try {
      // 渲染每个波浪点
      effect.wavePoints.forEach(wave => {
        // 更新波浪相位
        wave.phase += wave.speed * 0.1;

        // 计算波浪Y偏移
        const waveOffset = Math.sin(wave.phase) * wave.amplitude * (1 - progress);

        // 设置波浪颜色和透明度
        const alpha = (1 - progress) * 0.8;
        const blueIntensity = 100 + effect.level * 30;
        ctx.fillStyle = `rgba(0, ${blueIntensity}, 255, ${alpha})`;

        // 绘制波浪
        ctx.beginPath();
        ctx.ellipse(
          wave.x + wave.width / 2,
          wave.y + waveOffset,
          wave.width / 2,
          wave.amplitude,
          0, 0, Math.PI * 2
        );
        ctx.fill();
      });
    } catch (error) {
      console.error('渲染激流效果失败:', error);
    }

    ctx.restore();
    return true;
  }

  _renderEarthquake(ctx, effect) {
    // 计算动画进度
    const now = Date.now();
    const elapsed = now - effect.startTime;
    const progress = Math.min(1, elapsed / effect.duration);

    // 如果动画结束，清理
    if (progress >= 1) {
      effect.isActive = false;
      return false;
    }

    const blockSize = 30;
    const offsetX = 50;
    const offsetY = 100;
    const gridWidth = 10 * blockSize;

    ctx.save();

    try {
      // 更新震动效果
      const shakeX = Math.sin(elapsed * effect.shakeFrequency) * effect.shakeIntensity * (1 - progress);
      const shakeY = Math.cos(elapsed * effect.shakeFrequency * 1.3) * effect.shakeIntensity * 0.5 * (1 - progress);

      // 应用震动偏移
      ctx.translate(shakeX, shakeY);

      // 更新高亮效果
      effect.highlightAlpha += effect.highlightDirection * 0.05;
      if (effect.highlightAlpha >= 1 || effect.highlightAlpha <= 0) {
        effect.highlightDirection *= -1;
      }

      // 渲染受影响区域的高亮
      const alpha = (1 - progress) * effect.highlightAlpha * 0.3;
      ctx.fillStyle = `rgba(139, 69, 19, ${alpha})`;

      for (let row = effect.startRow; row <= effect.endRow; row++) {
        const y = offsetY + row * blockSize;

        // 添加波动效果
        effect.wavePhase += 0.1;
        const waveOffset = Math.sin(effect.wavePhase + row * 0.5) * effect.waveAmplitude * (1 - progress);

        ctx.fillRect(offsetX + waveOffset, y, gridWidth, blockSize);
      }

      // 渲染粒子效果
      this._renderEarthquakeParticles(ctx, effect, progress);

    } catch (error) {
      console.error('渲染地震效果失败:', error);
    }

    ctx.restore();
    return true;
  }

  cleanup() {
    this.activeEffects = this.activeEffects.filter(effect => effect.isActive);
  }

  /**
   * 创建水波效果
   */
  _createWaterWaves(rows, level) {
    const wavePoints = [];
    const blockSize = 30;
    const offsetX = 50;
    const offsetY = 100;
    const gridWidth = 10 * blockSize;
    const waveCount = 3 + level; // 波浪数量随等级增加

    // 为每一行创建水波效果
    rows.forEach(row => {
      // 计算行的Y坐标
      const rowY = offsetY + row * blockSize;

      for (let i = 0; i < waveCount; i++) {
        // 水波起始X位置（均匀分布）
        const startX = offsetX + (i * gridWidth / waveCount);

        wavePoints.push({
          x: startX,
          y: rowY,
          amplitude: 5 + level * 2, // 波幅随等级增大
          frequency: 0.05 + level * 0.01, // 频率随等级增大
          speed: 2 + level, // 速度随等级增大
          phase: Math.random() * Math.PI * 2, // 随机相位
          width: gridWidth / waveCount * 1.2, // 波宽
          row: row
        });
      }
    });

    return wavePoints;
  }

  /**
   * 创建地震粒子效果
   */
  _createEarthquakeParticles(startRow, endRow, level) {
    const particles = [];
    const particleCount = 10 + level * 5;
    const blockSize = 30;
    const offsetX = 50;
    const offsetY = 100;

    for (let i = 0; i < particleCount; i++) {
      const row = startRow + Math.random() * (endRow - startRow + 1);
      const col = Math.random() * 10; // 假设10列

      particles.push({
        x: offsetX + col * blockSize + Math.random() * blockSize,
        y: offsetY + row * blockSize + Math.random() * blockSize,
        vx: (Math.random() - 0.5) * 4,
        vy: (Math.random() - 0.5) * 4,
        life: 1.0,
        decay: 0.02 + Math.random() * 0.02,
        size: 2 + Math.random() * 3
      });
    }

    return particles;
  }

  /**
   * 渲染地震粒子效果
   */
  _renderEarthquakeParticles(ctx, effect, progress) {
    if (!effect.particles) return;

    effect.particles.forEach(particle => {
      if (particle.life <= 0) return;

      // 更新粒子
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.life -= particle.decay;

      // 渲染粒子
      const alpha = particle.life * (1 - progress);
      ctx.fillStyle = `rgba(139, 69, 19, ${alpha})`;
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      ctx.fill();
    });

    // 清理死亡的粒子
    effect.particles = effect.particles.filter(p => p.life > 0);
  }
}
