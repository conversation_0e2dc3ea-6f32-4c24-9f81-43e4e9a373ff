/**
 * 闪电效果渲染器
 */
export default class LightningRenderer {
  constructor(grid) {
    this.activeEffects = [];
    this.grid = grid;
  }

  createLightningEffect(startRow, startCol, chainPath, color = 'blue', level = 1) {
    const effect = {
      id: Date.now() + Math.random(),
      startRow,
      startCol,
      chainPath,
      color,
      level,
      startTimestamp: Date.now(),
      duration: 600, // 600ms动画时长
      path: this._convertPathToScreenCoordinates(chainPath),
      branches: this._generateLightningBranches(chainPath, level),
      isActive: true
    };

    this.activeEffects.push(effect);
    console.log(`⚡ 创建闪电效果，路径长度:${chainPath.length}, 等级:${level}, 颜色:${color}`);
    return effect;
  }

  render(ctx, effect) {
    if (!effect || !effect.isActive) {
      return false;
    }

    try {
      // 计算动画进度
      const now = Date.now();
      const elapsed = now - effect.startTimestamp;
      const progress = Math.min(1, elapsed / effect.duration);

      // 如果动画结束，清除效果
      if (progress >= 1) {
        effect.isActive = false;
        return false;
      }

      // 计算闪电透明度波动，使闪电闪烁
      const flashFrequency = 15; // 闪烁频率
      const flashProgress = (elapsed % flashFrequency) / flashFrequency;
      const alphaVariation = Math.sin(flashProgress * Math.PI) * 0.2;

      // 闪电基础透明度，随时间减少
      const baseAlpha = Math.max(0, 0.9 - progress * 0.9);
      const alpha = baseAlpha + alphaVariation;

      // 渲染主闪电路径
      this._renderLightningPaths(ctx, effect.path, alpha, 3 + effect.level, effect.color);

      // 渲染分支
      if (effect.branches && effect.branches.length > 0) {
        for (const branch of effect.branches) {
          // 分支的宽度和透明度都小于主路径
          this._renderLightningPaths(ctx, branch, alpha * 0.8, 2, effect.color);
        }
      }

      return true;
    } catch (error) {
      console.error('闪电效果渲染错误:', error);
      effect.isActive = false;
      return false;
    }
  }

  cleanup() {
    this.activeEffects = this.activeEffects.filter(effect => effect.isActive);
  }

  /**
   * 将网格路径转换为屏幕坐标
   */
  _convertPathToScreenCoordinates(chainPath) {
    // 使用网格的实际参数
    const blockSize = this.grid?.blockSize || 30;
    const offsetX = this.grid?.offsetX || 50;
    const offsetY = this.grid?.offsetY || 100;

    return chainPath.map(point => ({
      x: offsetX + point.col * blockSize + blockSize / 2,
      y: offsetY + point.row * blockSize + blockSize / 2
    }));
  }

  /**
   * 生成闪电分支
   */
  _generateLightningBranches(chainPath, level) {
    const branches = [];
    const branchCount = Math.min(level, 3); // 最多3个分支

    for (let i = 0; i < branchCount && i < chainPath.length - 1; i++) {
      const startIndex = Math.floor(Math.random() * (chainPath.length - 1));
      const startPoint = this._convertPathToScreenCoordinates([chainPath[startIndex]])[0];

      // 创建短分支
      const branchLength = 2 + Math.random() * 3;
      const angle = Math.random() * Math.PI * 2;
      const endX = startPoint.x + Math.cos(angle) * branchLength * 20;
      const endY = startPoint.y + Math.sin(angle) * branchLength * 20;

      branches.push([startPoint, { x: endX, y: endY }]);
    }

    return branches;
  }

  /**
   * 渲染闪电路径
   */
  _renderLightningPaths(ctx, path, alpha, lineWidth, color) {
    if (!path || path.length < 2) return;

    ctx.save();

    // 设置闪电样式
    ctx.globalAlpha = alpha;
    ctx.lineWidth = lineWidth;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 根据颜色设置样式
    const colorMap = {
      'blue': `rgba(100, 150, 255, ${alpha})`,
      'yellow': `rgba(255, 255, 100, ${alpha})`,
      'white': `rgba(255, 255, 255, ${alpha})`
    };

    ctx.strokeStyle = colorMap[color] || colorMap['yellow'];
    ctx.shadowColor = 'white';
    ctx.shadowBlur = 8;

    // 绘制路径
    ctx.beginPath();
    for (let i = 0; i < path.length; i++) {
      const point = path[i];
      if (i === 0) {
        ctx.moveTo(point.x, point.y);
      } else {
        // 添加一些随机偏移使闪电看起来更自然
        const jitter = 2;
        const jitterX = (Math.random() - 0.5) * jitter;
        const jitterY = (Math.random() - 0.5) * jitter;
        ctx.lineTo(point.x + jitterX, point.y + jitterY);
      }
    }
    ctx.stroke();

    ctx.restore();
  }
}
