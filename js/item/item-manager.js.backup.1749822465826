/**
 * 道具管理器
 * 负责管理游戏中的道具系统
 * 
 * 更新说明：
 * 1. 修复了恢复游戏状态时的TypeError错误，增强了方块位置更新的健壮性
 * 2. 修复了闪电链动画效果，优先使用网格内置的动画系统
 * 3. 修复了闪电链连接逻辑，确保只连接相同颜色的方块
 * 4. 增强了火球术的自动目标选择功能，更智能地找到效果最佳的位置
 * 5. 修复了火球术的帧动画效果，使用预加载方式确保explosion1-19.png动画序列正确显示
 * 6. 解决了在某些环境下无法获取画布导致的"无法获取画布，爆炸效果创建失败"错误
 * 7. 添加了详细的调试日志，帮助排查动画显示问题
 * 8. 优化了火球术动画大小，使其与实际覆盖范围精确匹配
 * 9. 根据火球术等级(1-3)增强视觉效果，高等级火球有更鲜艳的爆炸效果
 * 10. 优化闪电链算法，确保只连接相同颜色的方块，并改进闪电视觉效果
 * 11. 增强了激流道具的动画效果，随等级增加波浪效果
 * 12. 修复了闪电链连接冰冻方块的问题，确保只连接相同原始颜色的方块，即使是冰冻状态
 */
import Emitter from '../libs/tinyemitter.js';
import { BLOCK_COLORS, BLOCK_EFFECTS } from '../game/block.js';

// 道具类型
 export const ITEM_TYPES = {
  FIREBALL: 'fireball',   // 火球术
  LIGHTNING: 'lightning', // 闪电链
  WATERFLOW: 'waterflow', // 激流
  EARTHQUAKE: 'earthquake' // 地震术
 };

export default class ItemManager extends Emitter {
  /**
   * 创建道具管理器
   * @param {Grid} grid - 游戏网格
   * @param {Object} options - 配置选项
   * @param {Function} options.getGameController - 获取游戏控制器的函数
   * @param {Function} options.getMusicManager - 获取音效管理器的函数
   * @param {Function} options.getTetrominoClass - 获取Tetromino类的函数
   */
  constructor(grid, options = {}) {
    super();
    
    this.grid = grid;
    
    // 依赖注入，减少全局耦合
    this.dependencies = {
      getGameController: options.getGameController || (() => GameGlobal.gameController),
      getMusicManager: options.getMusicManager || (() => GameGlobal.musicManager),
      getTetrominoClass: options.getTetrominoClass || (() => GameGlobal.Tetromino)
    };
    
    // 道具等级
    this.itemLevels = {
      [ITEM_TYPES.FIREBALL]: 1,
      [ITEM_TYPES.LIGHTNING]: 1,
      [ITEM_TYPES.WATERFLOW]: 1,
      [ITEM_TYPES.EARTHQUAKE]: 1  // 新增地震术
    };
    
    // 道具剩余使用次数
    this.itemUses = {
      [ITEM_TYPES.FIREBALL]: 30,  // 初始30次火球术
      [ITEM_TYPES.LIGHTNING]: 20, // 初始20次闪电链
      [ITEM_TYPES.WATERFLOW]: 15, // 初始15次激流
      [ITEM_TYPES.EARTHQUAKE]: 10 // 初始10次地震术
    };

    // 道具解锁状态
    this.itemUnlockLevels = {
      [ITEM_TYPES.FIREBALL]: 2,   // 第2关解锁火球术
      [ITEM_TYPES.LIGHTNING]: 5,  // 第5关解锁闪电链
      [ITEM_TYPES.WATERFLOW]: 8,  // 第8关解锁激流
      [ITEM_TYPES.EARTHQUAKE]: 11 // 第11关解锁地震术（第二阶段第2关）
    };

    // 当前解锁的道具
    this.unlockedItems = new Set();

    // 当前关卡ID
    this.currentLevelId = 1;
    
    // 道具冷却时间（帧数）- 初始化为0表示可以立即使用
    this.cooldowns = {
      [ITEM_TYPES.FIREBALL]: 0,
      [ITEM_TYPES.LIGHTNING]: 0,
      [ITEM_TYPES.WATERFLOW]: 0,
      [ITEM_TYPES.EARTHQUAKE]: 0
    };
    
    // 道具的最大冷却时间（帧数）
    this.maxCooldowns = {
      [ITEM_TYPES.FIREBALL]: 180,   // 3秒 (60帧/秒)
      [ITEM_TYPES.LIGHTNING]: 300,  // 5秒
      [ITEM_TYPES.WATERFLOW]: 420,  // 7秒
      [ITEM_TYPES.EARTHQUAKE]: 600  // 10秒
    };
    
    // 道具的效果数据
    this.itemEffects = {
      // 火球术: 随等级增加范围和火球数量
      [ITEM_TYPES.FIREBALL]: {
        range: [1, 1, 2, 2, 2], // 等级1-5的范围
        fireballCount: [1, 1, 1, 2, 2], // 等级1-5的火球数量
        targeting: ['manual', 'smart_highest', 'smart_highest', 'smart_highest', 'dual_smart'] // 瞄准方式
      },
      // 闪电链: 随等级增加连锁数量和闪电数量
      [ITEM_TYPES.LIGHTNING]: {
        chainCount: [5, 5, 8, 8, 8], // 等级1-5的连锁数量
        lightningCount: [1, 1, 1, 2, 2], // 等级1-5的闪电数量
        targeting: ['random', 'smart_highest', 'smart_highest', 'smart_highest', 'dual_smart'] // 瞄准方式
      },
      // 激流: 随等级增加影响行数
      [ITEM_TYPES.WATERFLOW]: {
        rowCount: [1, 2, 3, 4, 6] // 等级1-5的行数
      },
      // 地震术: 随等级增加影响行数（从底部开始）
      [ITEM_TYPES.EARTHQUAKE]: {
        affectedRows: [3, 4, 5, 6, 7] // 等级1-5影响的行数
      }
    };
    
    // 道具激活的方块
    this.affectedBlocks = new Set();
    
    // 动画状态
    this.animations = {
      timer: 0,
      isActive: false,
      explosionEffect: null,
      lightningEffect: null,
      waterflowEffect: null
    };
    
    // 游戏状态保存
    this.savedGameState = null;
    this.savedTetromino = null;
    
    // 预加载爆炸帧图片
    this.explosionFrames = [];
    this._preloadExplosionFrames();
  }

  /**
   * 获取调试配置（通过全局访问）
   * @returns {Object} 调试配置函数
   * @private
   */
  _getDebugConfig() {
    // 通过全局函数访问调试配置
    if (!this._debugConfig) {
      // 检查全局是否有调试函数
      const hasGlobalDebug = (typeof global !== 'undefined' && global.isDebugMode) || 
                            (typeof window !== 'undefined' && window.isDebugMode);
      
      if (hasGlobalDebug) {
        const debugModule = (typeof global !== 'undefined') ? global : window;
        this._debugConfig = {
          isDebugMode: debugModule.isDebugMode || (() => false),
          getConfig: (path) => {
            // 简单的配置获取逻辑
            if (path === 'debug.infiniteItems') {
              return debugModule.isDebugMode && debugModule.isDebugMode();
            }
            if (path === 'debug.noCooldown') {
              return debugModule.isDebugMode && debugModule.isDebugMode();
            }
            return false;
          }
        };
      } else {
        // 默认配置
        this._debugConfig = {
          isDebugMode: () => false,
          getConfig: () => false
        };
      }
    }
    return this._debugConfig;
  }
  
  /**
   * 预加载爆炸帧图片
   * @private
   */
  _preloadExplosionFrames() {
    // 创建爆炸帧图片数组
    const frames = [];
    
    // 定义加载进度计数器
    let loadedCount = 0;
    let errorCount = 0;
    
    console.log('开始预加载爆炸帧图片...');
    
    try {
      // 获取图片基础路径
      const basePath = 'images/';
      
      // 检查是否在微信小游戏环境中
      const inWechatGameEnv = typeof wx !== 'undefined' && wx.createImage && wx.getFileSystemManager;
      
      // 加载19帧爆炸图片
      for (let i = 1; i <= 19; i++) {
        try {
          // 创建图片对象
          let img;
          
          if (inWechatGameEnv) {
            img = wx.createImage();
          } else if (typeof Image !== 'undefined') {
            img = new Image();
          } else {
            console.error('当前环境不支持创建图片对象');
            continue;
          }
          
          // 图片路径
          const imgPath = `${basePath}explosion${i}.png`;
          
          // 设置加载完成回调
          img.onload = () => {
            loadedCount++;
            console.log(`爆炸帧${i}加载成功，进度: ${loadedCount}/19，尺寸: ${img.width}x${img.height}`);
          };
          
          // 设置加载错误回调
          img.onerror = (e) => {
            errorCount++;
            console.error(`爆炸帧${i}加载失败，路径: ${imgPath}`, e);
            
            // 当所有图片尝试加载完成但有错误时通知
            if (loadedCount + errorCount === 19) {
              console.log(`爆炸帧图片加载完成，成功: ${loadedCount}，失败: ${errorCount}`);
            }
          };
          
          // 设置图片源
          img.src = imgPath;
          frames.push(img);
          
          console.log(`预加载爆炸帧${i}，路径: ${imgPath}`);
        } catch (e) {
          console.error(`创建爆炸帧${i}图片对象失败:`, e);
        }
      }
    } catch (e) {
      console.error('预加载爆炸帧图片过程中发生错误:', e);
    }
    
    console.log(`尝试预加载${frames.length}个爆炸帧图片`);
    
    // 保存到实例
    this.explosionFrames = frames;
  }
  
  /**
   * 获取依赖对象
   * @param {string} dependencyName - 依赖名称
   * @returns {Object} 依赖对象
   * @private
   */
  _getDependency(dependencyName) {
    if (!this.dependencies[dependencyName]) {
      console.warn(`依赖 ${dependencyName} 未定义`);
      return null;
    }
    return this.dependencies[dependencyName]();
  }
  
  /**
   * 更新道具冷却时间和动画
   */
  update() {
    // 更新所有道具的冷却时间
    Object.keys(this.cooldowns).forEach(item => {
      if (this.cooldowns[item] > 0) {
        this.cooldowns[item]--;
      }
    });
    
    // 更新动画
    if (this.animations.isActive) {
      this.animations.timer++;
      
      // 动画结束（10帧）
      if (this.animations.timer >= 10) {
        this.animations.isActive = false;
        this.animations.timer = 0;
        
        // 🔧 修复：地震术不需要移除方块，只需要清理状态
        if (this.animations.type === 'earthquake') {
          console.log('🌍 地震术动画结束，仅清理状态');
          this.affectedBlocks.clear();
          
          // 🎯 改进：地震术的消除检查已在重力完成时触发，这里不重复触发
          console.log('🌍 地震术动画完成（消除检查已在重力完成时触发）');
        } else {
          // 其他道具需要移除受影响的方块
          this._finalizeItemEffect();
        }
      }
    }
    
    // 更新圆形爆炸效果动画
    if (this.animations.explosionEffect && this.animations.explosionEffect.radius !== undefined) {
      const effect = this.animations.explosionEffect;
      effect.frame++;
      
      // 更新半径 - 圆形爆炸效果的直径
      effect.radius = (effect.frame / effect.maxFrames) * effect.maxRadius;
      
      // 动画结束
      if (effect.frame >= effect.maxFrames) {
        this.animations.explosionEffect = null;
      }
    }
    
    // 更新闪电链动画（如果有）
    if (this.animations.lightningEffect && typeof this.animations.lightningEffect === 'object') {
      // 检查基于时间的动画是否结束
      if (this.animations.lightningEffect.startTimestamp) {
        const now = Date.now();
        const elapsed = now - this.animations.lightningEffect.startTimestamp;
        if (elapsed >= this.animations.lightningEffect.duration) {
          this.animations.lightningEffect = null;
        }
      }
      // 检查基于帧的动画是否结束
      else if (this.animations.lightningEffect.frame !== undefined) {
        this.animations.lightningEffect.frame++;
        if (this.animations.lightningEffect.frame >= this.animations.lightningEffect.maxFrames) {
          this.animations.lightningEffect = null;
        }
      }
    }
    
    // 更新激流效果动画（如果有）
    if (this.animations.waterflowEffect && typeof this.animations.waterflowEffect === 'object') {
      // 检查基于时间的动画是否结束
      if (this.animations.waterflowEffect.startTime) {
        const now = Date.now();
        const elapsed = now - this.animations.waterflowEffect.startTime;
        if (elapsed >= this.animations.waterflowEffect.duration) {
          this.animations.waterflowEffect = null;
        }
      }
    }
  }
  
  /**
   * 使用道具
   * @param {string} itemType - 道具类型
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @returns {boolean} 是否成功使用道具
   */
  useItem(itemType, row, col) {
    console.log('使用道具', itemType, row, col, this.cooldowns[itemType]);
    
    // 检查道具是否在冷却中或已用完
    if (!this._canUseItem(itemType)) {
      return false;
    }
    
    // 保存当前游戏状态
    this._saveGameState();
    
    // 清空之前的受影响方块集合
    this.affectedBlocks.clear();
    
    // 应用道具效果
    let success = false;
    const level = this.itemLevels[itemType];
    
    // 使用道具处理函数映射
    const itemHandlers = {
      [ITEM_TYPES.FIREBALL]: this._handleFireball.bind(this),
      [ITEM_TYPES.LIGHTNING]: this._handleLightning.bind(this),
      [ITEM_TYPES.WATERFLOW]: this._handleWaterflow.bind(this),
      [ITEM_TYPES.EARTHQUAKE]: this._handleEarthquake.bind(this)
    };
    
    // 调用对应的处理函数
    if (itemHandlers[itemType]) {
      success = itemHandlers[itemType](row, col, level);
    }
    
        // 激流道具特殊处理：有自己的冷却和使用次数管理
    if (itemType === ITEM_TYPES.WATERFLOW) {
      // 激流道具在_handleWaterflow中已经处理了冷却和使用次数
      if (!success) {
        this._restoreGameState();
      }
      return success;
    }
    
    // 闪电链特殊处理：即使没有消除方块，成功解冻也算使用成功
    if (itemType === ITEM_TYPES.LIGHTNING && success) {
      this._applyItemCooldownAndUse(itemType);
      this.resetItemUses(itemType, 5);
      return true;
    }
    
    // 如果成功使用，设置冷却时间并减少使用次数
        if (success) {
      this._applyItemCooldownAndUse(itemType);
        } else {
      // 如果道具使用失败，恢复保存的游戏状态
          this._restoreGameState();
        }
        
    // 重置闪电链的使用次数
    if (itemType === ITEM_TYPES.LIGHTNING) {
        this.resetItemUses(itemType, 5);
    }
    
    return success;
  }
  
  /**
   * 设置当前关卡ID并检查道具解锁
   * @param {number} levelId - 关卡ID
   */
  setCurrentLevel(levelId) {
    this.currentLevelId = levelId;
    this._checkItemUnlocks();
  }

  /**
   * 检查并解锁道具
   * @private
   */
  _checkItemUnlocks() {
    Object.entries(this.itemUnlockLevels).forEach(([itemType, unlockLevel]) => {
      if (this.currentLevelId >= unlockLevel && !this.unlockedItems.has(itemType)) {
        this.unlockedItems.add(itemType);
        console.log(`道具 ${itemType} 在第${unlockLevel}关解锁`);
        this.emit('item:unlock', { type: itemType, level: unlockLevel });
      }
    });
  }

  /**
   * 检查道具是否已解锁
   * @param {string} itemType - 道具类型
   * @returns {boolean} 是否已解锁
   */
  isItemUnlocked(itemType) {
    return this.unlockedItems.has(itemType);
  }

  /**
   * 获取所有已解锁的道具
   * @returns {Array} 已解锁的道具类型数组
   */
  getUnlockedItems() {
    return Array.from(this.unlockedItems);
  }

  /**
   * 检查道具是否可以使用
   * @param {string} itemType - 道具类型
   * @returns {boolean} 是否可以使用
   * @private
   */
  _canUseItem(itemType) {
    // 从 game-config.js 导入调试模式检查
    const { isDebugMode, getConfig } = this._getDebugConfig();
    
    // 检查道具是否已解锁
    if (!this.isItemUnlocked(itemType)) {
      console.log(`道具${itemType}尚未解锁`);
      this.emit('item:locked', { type: itemType });
      return false;
    }

    // 调试模式下跳过冷却检查
    if (!isDebugMode() || !getConfig('debug.noCooldown')) {
      // 检查道具是否在冷却中
      if (this.cooldowns[itemType] > 0) {
        console.log(`道具冷却中，剩余${Math.ceil(this.cooldowns[itemType] / 60)}秒`);
        this.emit('item:cooldown', { type: itemType, cooldown: this.cooldowns[itemType] });
        return false;
      }
    }

    // 调试模式下跳过使用次数检查
    if (!isDebugMode() || !getConfig('debug.infiniteItems')) {
      // 检查道具剩余使用次数
      if (this.itemUses[itemType] <= 0) {
        console.log(`道具${itemType}已用完`);
        this.emit('item:noUses', { type: itemType });
        return false;
      }
    }

    return true;
  }
  
  /**
   * 应用道具冷却和减少使用次数
   * @param {string} itemType - 道具类型
   * @private
   */
  _applyItemCooldownAndUse(itemType) {
    const { isDebugMode, getConfig } = this._getDebugConfig();
    
    // 调试模式下不设置冷却时间
    if (!isDebugMode() || !getConfig('debug.noCooldown')) {
      // 设置冷却时间
      this.cooldowns[itemType] = this.maxCooldowns[itemType];
    }

    // 调试模式下不减少使用次数
    if (!isDebugMode() || !getConfig('debug.infiniteItems')) {
      // 减少道具使用次数
      this.itemUses[itemType]--;
    }

    // 计算道具消除得分
    const itemScore = this._calculateItemScore(itemType, this.affectedBlocks.size);

    // 启动动画
    this.animations.isActive = true;
    this.animations.timer = 0;

    // 触发道具使用事件
    this.emit('item:use', {
      type: itemType,
      level: this.itemLevels[itemType],
      uses: this.itemUses[itemType],
      affectedBlocks: this.affectedBlocks.size,
      score: itemScore
    });
  }

  /**
   * 计算道具消除得分
   * @param {string} itemType - 道具类型
   * @param {number} blocksCount - 消除的方块数量
   * @returns {number} 得分
   * @private
   */
  _calculateItemScore(itemType, blocksCount) {
    if (blocksCount === 0) return 0;

    // 基础分数：每个方块20分
    const baseScore = blocksCount * 20;

    // 道具类型加成
    const itemMultipliers = {
      [ITEM_TYPES.FIREBALL]: 1.5,   // 火球术：1.5倍加成
      [ITEM_TYPES.LIGHTNING]: 2.0,  // 闪电链：2.0倍加成
      [ITEM_TYPES.WATERFLOW]: 1.2   // 激流：1.2倍加成
    };

    const multiplier = itemMultipliers[itemType] || 1.0;

    // 道具等级加成：每级额外10%
    const levelBonus = 1 + (this.itemLevels[itemType] - 1) * 0.1;

    // 方块数量加成：消除越多方块，单个方块分数越高
    const quantityBonus = 1 + Math.min(blocksCount / 10, 2.0); // 最多3倍加成

    const finalScore = Math.floor(baseScore * multiplier * levelBonus * quantityBonus);

    console.log(`道具得分计算: ${itemType}, 方块数: ${blocksCount}, 基础分: ${baseScore}, 最终分: ${finalScore}`);

    return finalScore;
  }
  
    /**
   * 寻找最高方块的位置（用于智能瞄准）
   * @returns {Object|null} 最高方块的位置 {row, col} 或 null
   * @private
   */
  _findHighestBlockPosition() {
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          console.log(`找到最高方块位置: [${row}, ${col}]`);
          return { row, col };
        }
      }
    }
    console.log('没有找到任何方块');
    return null;
  }

  /**
   * 智能火球术目标选择
   * @param {number} level - 道具等级
   * @returns {Object|null} 目标位置
   * @private
   */
  _findSmartFireballTarget(level) {
    const targeting = this.itemEffects[ITEM_TYPES.FIREBALL].targeting[level - 1];
    
    // 智能瞄准最高方块
    if (targeting === 'smart_highest' || targeting === 'dual_smart') {
      const highestBlock = this._findHighestBlockPosition();
      if (highestBlock) {
        console.log(`🎯 智能火球术瞄准最高方块: [${highestBlock.row}, ${highestBlock.col}]`);
        return highestBlock;
      }
    }
    
    // 手动瞄准或没有找到最高方块时：使用原有的最佳目标逻辑
    return this._findBestFireballTarget(level);
  }

  /**
   * 处理火球术使用（支持多发火球）
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @param {number} level - 道具等级
   * @returns {boolean} 是否成功使用
   * @private
   */
  _handleFireball(row, col, level) {
    console.log('🔥 处理火球术使用，等级:', level);
    
    // 确定火球发射次数
    const fireballCount = this.itemEffects[ITEM_TYPES.FIREBALL].fireballCount[level - 1];
    console.log(`等级${level}火球术将发射${fireballCount}发火球`);
    
    let success = false;
    
    for (let i = 0; i < fireballCount; i++) {
      console.log(`🔥 发射第${i + 1}发火球`);
      
      let targetRow = row;
      let targetCol = col;
      
      // 如果没有指定位置，或需要自动选择目标
      if (row === undefined || col === undefined || i > 0) {
        const target = this._findSmartFireballTarget(level);
        if (!target) {
          console.log(`第${i + 1}发火球没有找到有效目标`);
          continue;
        }
        targetRow = target.row;
        targetCol = target.col;
      }
      
      // 发射火球
      const fireballSuccess = this._useFireball(targetRow, targetCol, level);
      if (fireballSuccess) {
        success = true;
        
        // 如果是多发火球，等待第一发结算完成后再发射下一发
        if (i < fireballCount - 1) {
          this._scheduleDelayedFireball(i + 1, level, 800); // 800ms延迟
        }
      }
    }
    
    return success;
  }

  /**
   * 延迟发射火球
   * @param {number} fireballIndex - 火球序号
   * @param {number} level - 道具等级
   * @param {number} delay - 延迟时间（毫秒）
   * @private
   */
  _scheduleDelayedFireball(fireballIndex, level, delay) {
    console.log(`📅 安排${delay}ms后发射第${fireballIndex + 1}发火球`);
    
    setTimeout(() => {
      console.log(`⏰ 开始发射延迟的第${fireballIndex + 1}发火球`);
      
      // 寻找新的目标（因为第一发可能已经改变了布局）
      const target = this._findSmartFireballTarget(level);
      if (target) {
        // 创建新的临时受影响方块集合
        const previousAffected = new Set(this.affectedBlocks);
        this.affectedBlocks.clear();
        
        const success = this._useFireball(target.row, target.col, level);
        
        if (success) {
          // 合并受影响的方块
          for (const block of previousAffected) {
            this.affectedBlocks.add(block);
          }
          
          console.log(`✅ 第${fireballIndex + 1}发火球发射成功`);
        }
      }
    }, delay);
  }
  
  /**
   * 查找最佳火球术目标
   * @param {number} level - 道具等级
   * @returns {Object|null} 目标位置对象{row, col}或null
   * @private
   */
  _findBestFireballTarget(level) {
    // 火球的影响范围
    const range = this.itemEffects[ITEM_TYPES.FIREBALL].range[level - 1];
    
    // 存储每个位置及其影响的方块数
    const targetScores = [];
    
    // 遍历网格中的每个位置
    for (let r = 0; r < this.grid.rows; r++) {
      for (let c = 0; c < this.grid.cols; c++) {
        // 计算该位置作为目标时影响的方块数
        const blockCount = this._countAffectedBlocks(r, c, range);
        
        // 只考虑有影响的位置
        if (blockCount > 0) {
          // 计算额外得分因子 - 优先考虑中心位置和下半部分的目标
          const centerRowFactor = 1 - Math.abs(r - this.grid.rows / 2) / (this.grid.rows / 2) * 0.2;
          const lowerHalfBonus = r > this.grid.rows / 2 ? 1.2 : 1.0;
          
          // 计算最终得分（方块数 * 位置因子）
          const finalScore = blockCount * centerRowFactor * lowerHalfBonus;
          
          targetScores.push({
            row: r,
            col: c,
            score: blockCount,
            finalScore: finalScore
          });
        }
      }
    }
    
    // 如果没有找到任何可能的目标，返回null
    if (targetScores.length === 0) {
      return null;
    }
    
    // 按最终得分排序，选择最高得分的目标
    targetScores.sort((a, b) => b.finalScore - a.finalScore);
    
    // 从得分最高的前几个目标中随机选择一个
    const selectionRange = Math.min(3, targetScores.length);
    const selectedIndex = Math.floor(Math.random() * selectionRange);
    
    return targetScores[selectedIndex];
  }
  
  /**
   * 计算火球在指定位置影响的方块数
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @param {number} range - 影响范围
   * @returns {number} 影响的方块数
   * @private
   */
  _countAffectedBlocks(row, col, range) {
    let blockCount = 0;
    
    // 计算爆炸范围
    const minRow = Math.max(0, row - range);
    const maxRow = Math.min(this.grid.rows - 1, row + range);
    const minCol = Math.max(0, col - range);
    const maxCol = Math.min(this.grid.cols - 1, col + range);
    
    // 遍历爆炸范围内的所有方块
    for (let r = minRow; r <= maxRow; r++) {
      for (let c = minCol; c <= maxCol; c++) {
        // 计算与中心的距离，实现圆形爆炸效果
        const distance = Math.sqrt(Math.pow(r - row, 2) + Math.pow(c - col, 2));
        
        // 如果距离在范围内（圆形效果）
        if (distance <= range) {
          const block = this.grid.getBlock(r, c);
          if (block) {
            blockCount++;
          }
        }
      }
    }
    
    return blockCount;
  }
  
  /**
   * 使用火球术
   * @param {number} row - 目标行
   * @param {number} col - 目标列 
   * @param {number} level - 道具等级
   * @returns {boolean} 是否成功使用
   * @private
   */
  _useFireball(row, col, level) {
    // 检查目标位置是否有效
    if (!this._isValidPosition(row, col)) {
      console.log('目标位置无效');
      return false;
    }
    
    // 记录动画状态，用于调试
    this._logDebugInfo('useFireball-before');
    
    // 获取火球影响范围
    const range = this.itemEffects[ITEM_TYPES.FIREBALL].range[level - 1];
    console.log(`火球术使用，等级: ${level}, 范围: ${range}`);
    
    // 计算爆炸范围
    const minRow = Math.max(0, row - range);
    const maxRow = Math.min(this.grid.rows - 1, row + range);
    const minCol = Math.max(0, col - range);
    const maxCol = Math.min(this.grid.cols - 1, col + range);
    
    console.log(`爆炸范围: (${minRow},${minCol}) 到 (${maxRow},${maxCol})`);
    
    // 遍历爆炸范围内的所有方块
    let hasValidTarget = false;
    let blockCount = 0;
    
    for (let r = minRow; r <= maxRow; r++) {
      for (let c = minCol; c <= maxCol; c++) {
        // 计算与中心的距离，实现圆形爆炸效果
        const distance = Math.sqrt(Math.pow(r - row, 2) + Math.pow(c - col, 2));
        
        // 如果距离在范围内（圆形效果）
        if (distance <= range) {
          const block = this.grid.getBlock(r, c);
          
          if (block) {
            blockCount++;
            hasValidTarget = true;
            this._markBlockAffected(block);
          }
        }
      }
    }
    
    console.log('范围内方块数量:', blockCount);
    
    if (!hasValidTarget) {
      console.log('目标区域内没有可消除的方块');
      return false;
    }
    
    // 创建火球动画效果和播放音效
    this._createAnimationEffect('explosion', {
      centerRow: row,
      centerCol: col,
      range: range
    });
    this._playSound('fireball');
    
    // 记录动画状态，用于调试
    this._logDebugInfo('useFireball-after');
    
    console.log('火球术使用成功');
    return true;
  }
  
  /**
   * 记录调试信息
   * @param {string} label - 调试标记
   * @private
   */
  _logDebugInfo(label) {
    console.log(`[调试信息:${label}] 动画状态:`, {
      explosionActive: !!this.animations.explosionEffect,
      explosionType: this.animations.explosionEffect ? 
        (this.animations.explosionEffect.frames ? '帧动画' : '圆形动画') : '无',
      lightningActive: !!this.animations.lightningEffect,
      isAnimActive: this.animations.isActive,
      timer: this.animations.timer,
      explosionFramesLoaded: this.explosionFrames ? this.explosionFrames.length : 0
    });
    
    // 记录网格信息
    console.log(`[调试信息:${label}] 网格状态:`, {
      rows: this.grid.rows,
      cols: this.grid.cols,
      blockSize: this.grid.blockSize,
      offsetX: this.grid.offsetX,
      offsetY: this.grid.offsetY
    });
    
    // 记录环境信息
    console.log(`[调试信息:${label}] 环境信息:`, {
      hasWx: typeof wx !== 'undefined',
      hasCreateImage: typeof wx !== 'undefined' && !!wx.createImage,
      hasCanvas: typeof wx !== 'undefined' && !!wx.getSharedCanvas,
      affectedBlocks: this.affectedBlocks.size
    });
  }
  
  /**
   * 创建基于帧的爆炸效果
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} size - 爆炸大小
   * @param {Array} frames - 爆炸帧图片
   * @private
   */
  _createFrameBasedExplosionEffect(centerX, centerY, size, frames) {
    // 删除此方法，它已不再使用
  }
  
  /**
   * 加载爆炸帧图片
   * @returns {Array} 图片数组
   * @private
   */
  _loadExplosionFrames() {
    // 删除此方法，它已不再使用
  }
  
  /**
   * 确定资源路径
   * @returns {string} 资源基础路径
   * @private
   */
  _determineResourcePath() {
    // 删除此方法，它已不再使用
  }
  
  /**
   * 创建简单的圆形爆炸效果
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} coverageDiameter - 覆盖直径
   * @param {number} level - 火球等级
   * @private
   */
  _createSimpleExplosionEffect(centerX, centerY, coverageDiameter, level = 1) {
    // 将直径转换为半径
    const maxRadius = coverageDiameter / 2;
    
    // 创建动画状态对象
    this.animations.explosionEffect = {
      x: centerX,
      y: centerY,
      radius: 0,
      maxRadius: maxRadius,
      frame: 0,
      maxFrames: 25, // 25帧动画
      level: level // 火球等级
    };
    
    console.log(`创建简单圆形爆炸效果，位置:(${centerX},${centerY}), 半径:${maxRadius}px, 火球等级:${level}`);
  }
  
  /**
   * 创建爆炸效果
   * @param {number} centerRow - 中心行
   * @param {number} centerCol - 中心列
   * @param {number} range - 范围
   * @private
   */
  _createExplosionEffect(centerRow, centerCol, range) {
    // 计算爆炸中心的屏幕坐标
    // 使用网格的实际尺寸和偏移量来计算
    const cellSize = this.grid.blockSize || 30; // 使用默认值30如果blockSize不存在
    const offsetX = this.grid.offsetX || 0;
    const offsetY = this.grid.offsetY || 0;
    
    // 计算中心坐标
    const centerX = offsetX + centerCol * cellSize + cellSize / 2;
    const centerY = offsetY + centerRow * cellSize + cellSize / 2;
    
    // 计算真实的覆盖直径（范围×2+1）×单元格大小
    const coverageDiameter = (range * 2 + 1) * cellSize;
    
    console.log(`创建爆炸效果，坐标:(${centerX},${centerY}), 范围:${range}, 覆盖直径:${coverageDiameter}px, 网格偏移:(${offsetX},${offsetY}), 单元格大小:${cellSize}`);
    
    // 获取火球术等级，用于视觉效果增强
    const fireballLevel = this.itemLevels[ITEM_TYPES.FIREBALL] || 1;
    
    // 直接启动爆炸帧动画，不依赖于canvas对象
    if (this.explosionFrames && this.explosionFrames.length > 0) {
      console.log(`使用预加载的${this.explosionFrames.length}帧爆炸动画，火球等级:${fireballLevel}`);
      this._startExplosionFrameAnimation(centerX, centerY, coverageDiameter, fireballLevel);
    } else {
      // 使用简单的圆形爆炸效果作为后备
      console.log('使用简单圆形爆炸效果 (预加载帧不可用)，火球等级:' + fireballLevel);
      this._createSimpleExplosionEffect(centerX, centerY, coverageDiameter, fireballLevel);
    }
  }
  
  /**
   * 启动爆炸帧动画
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} coverageDiameter - 覆盖直径
   * @param {number} level - 火球等级
   * @private
   */
  _startExplosionFrameAnimation(x, y, coverageDiameter, level = 1) {
    // 检查预加载的帧是否可用
    if (!this.explosionFrames || this.explosionFrames.length === 0) {
      console.warn('没有可用的爆炸帧图片，改用简单动画');
      this._createSimpleExplosionEffect(x, y, coverageDiameter, level);
      return;
    }
    
    // 计算合适的动画尺寸（精确匹配覆盖直径）
    let animSize = coverageDiameter;
    
    // 为了美观，根据等级添加额外的视觉效果尺寸
    const visualBonus = level * 10; // 每级增加10px视觉效果
    animSize += visualBonus;
    
    // 如果尺寸太小，调整到合理的最小值
    if (animSize < 80) {
      animSize = 80;
    }
    
    console.log(`启动爆炸帧动画，位置:(${x},${y}), 实际尺寸:${animSize}px, 火球等级:${level}, 帧数:${this.explosionFrames.length}`);
    
    // 创建帧状态对象
    const frameState = {
      x: x,
      y: y,
      size: animSize,
      currentFrame: 0,
      frameDuration: 35,  // 略微加快动画速度，每帧持续时间(毫秒)
      lastFrameTime: Date.now(),
      frames: this.explosionFrames,
      level: level, // 保存等级信息用于渲染
      debug: {
        started: Date.now(),
        frameUpdates: []
      }
    };
    
    // 保存到动画状态
    this.animations.explosionEffect = frameState;
  }
  
  /**
   * 渲染爆炸效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderExplosionEffect(ctx) {
    if (!ctx) {
      console.warn('渲染爆炸效果时画布上下文不可用');
      return;
    }
    
    const effect = this.animations.explosionEffect;
    if (!effect) return;
    
    // 处理帧动画
    if (effect.frames && Array.isArray(effect.frames)) {
      const now = Date.now();
      
      // 如果到达下一帧的时间
      if (now - effect.lastFrameTime > effect.frameDuration) {
        effect.currentFrame++;
        effect.lastFrameTime = now;
        
        // 如果动画结束，清除效果
        if (effect.currentFrame >= effect.frames.length) {
          this.animations.explosionEffect = null;
          return;
        }
      }
      
      // 绘制当前帧
      try {
        const img = effect.frames[effect.currentFrame];
        if (img && img.complete && img.width > 0) {
          const halfSize = effect.size / 2;
          
          // 获取火球等级，用于视觉增强
          const level = effect.level || 1;
          
          // 根据等级设置不同的合成操作
          ctx.globalCompositeOperation = level > 1 ? 'lighter' : 'source-over';
          
          // 设置阴影效果，随等级增强
          ctx.shadowBlur = 10 + level * 8; 
          ctx.shadowColor = `rgba(255, ${150 - level * 25}, 0, 0.8)`;
          
          // 绘制帧图片
          ctx.drawImage(img, effect.x - halfSize, effect.y - halfSize, effect.size, effect.size);
          
          // 如果等级大于1，添加内核光效
          if (level > 1) {
            const innerSize = effect.size * 0.5;
            const innerX = effect.x - innerSize / 2;
            const innerY = effect.y - innerSize / 2;
            
            // 设置内核光效颜色，随等级增强
            ctx.globalAlpha = 0.3 + level * 0.15;
            ctx.drawImage(img, innerX, innerY, innerSize, innerSize);
            ctx.globalAlpha = 1.0;
          }
          
          // 重置合成操作和阴影
          ctx.globalCompositeOperation = 'source-over';
          ctx.shadowBlur = 0;
        } else {
          console.log(`爆炸帧 ${effect.currentFrame + 1} 图片未完成加载，跳过渲染`);
        }
      } catch (e) {
        console.error('渲染爆炸帧失败:', e);
      }
    } 
    // 处理简单圆形效果
    else if (effect.radius !== undefined) {
      // 渲染简单圆形爆炸效果
      try {
        const { x, y, radius, frame, maxFrames, level } = effect;
        const alpha = 1 - frame / maxFrames; // 透明度随帧数减小
        const fireLevel = level || 1;
        
        // 外围光环 - 随等级颜色更鲜艳
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, ${165 - fireLevel * 25}, 0, ${alpha * 0.5})`;
        ctx.fill();
        
        // 中间层 - 随等级更明亮
        ctx.beginPath();
        ctx.arc(x, y, radius * 0.7, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, ${110 - fireLevel * 30}, 0, ${alpha * (0.7 + fireLevel * 0.1)})`;
        ctx.fill();
        
        // 内核 - 随等级更强烈
        ctx.beginPath();
        ctx.arc(x, y, radius * 0.4, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, ${60 - fireLevel * 20}, ${fireLevel * 30}, ${alpha * 0.9})`;
        ctx.fill();
        
        // 最内层 - 只在高等级显示
        if (fireLevel > 1) {
          ctx.beginPath();
          ctx.arc(x, y, radius * 0.2, 0, Math.PI * 2);
          ctx.fillStyle = `rgba(255, 255, ${180 + fireLevel * 20}, ${alpha * 0.95})`;
          ctx.fill();
        }
      } catch (e) {
        console.error('渲染圆形爆炸效果失败:', e);
      }
    }
  }
  
  /**
   * 智能闪电链目标选择
   * @param {number} level - 道具等级
   * @returns {Object|null} 目标位置
   * @private
   */
  _findSmartLightningTarget(level) {
    const targeting = this.itemEffects[ITEM_TYPES.LIGHTNING].targeting[level - 1];
    
    // 智能瞄准最高方块
    if (targeting === 'smart_highest' || targeting === 'dual_smart') {
      const highestBlock = this._findHighestBlockPosition();
      if (highestBlock) {
        console.log(`⚡ 智能闪电链瞄准最高方块: [${highestBlock.row}, ${highestBlock.col}]`);
        return highestBlock;
      }
    }
    
    // 随机瞄准或没有找到最高方块时：使用随机目标
    return this._findRandomTarget();
  }

  /**
   * 处理闪电链使用（支持多发闪电链）
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @param {number} level - 道具等级
   * @returns {boolean} 是否成功使用
   * @private
   */
  _handleLightning(row, col, level) {
    console.log('⚡ 处理闪电链使用，等级:', level);
    
    // 确定闪电链发射次数
    const lightningCount = this.itemEffects[ITEM_TYPES.LIGHTNING].lightningCount[level - 1];
    console.log(`等级${level}闪电链将发射${lightningCount}条闪电`);
    
    let success = false;
    
    for (let i = 0; i < lightningCount; i++) {
      console.log(`⚡ 发射第${i + 1}条闪电链`);
      
      let targetRow = row;
      let targetCol = col;
      
      // 如果没有指定位置，或需要自动选择目标
      if (row === undefined || col === undefined || i > 0) {
        const target = this._findSmartLightningTarget(level);
        if (!target) {
          console.log(`第${i + 1}条闪电链没有找到有效目标`);
          continue;
        }
        targetRow = target.row;
        targetCol = target.col;
      }
      
      // 发射闪电链
      const lightningSuccess = this._useLightning(targetRow, targetCol, level);
      if (lightningSuccess) {
        success = true;
        
        // 如果是多发闪电链，等待第一发结算完成后再发射下一发
        if (i < lightningCount - 1) {
          this._scheduleDelayedLightning(i + 1, level, 600); // 600ms延迟
        }
      }
    }
    
    return success;
  }

  /**
   * 延迟发射闪电链
   * @param {number} lightningIndex - 闪电链序号
   * @param {number} level - 道具等级
   * @param {number} delay - 延迟时间（毫秒）
   * @private
   */
  _scheduleDelayedLightning(lightningIndex, level, delay) {
    console.log(`📅 安排${delay}ms后发射第${lightningIndex + 1}条闪电链`);
    
    setTimeout(() => {
      console.log(`⏰ 开始发射延迟的第${lightningIndex + 1}条闪电链`);
      
      // 寻找新的目标（因为第一发可能已经改变了布局）
      const target = this._findSmartLightningTarget(level);
      if (target) {
        // 创建新的临时受影响方块集合
        const previousAffected = new Set(this.affectedBlocks);
        this.affectedBlocks.clear();
        
        const success = this._useLightning(target.row, target.col, level);
        
        if (success) {
          // 合并受影响的方块
          for (const block of previousAffected) {
            this.affectedBlocks.add(block);
          }
          
          console.log(`✅ 第${lightningIndex + 1}条闪电链发射成功`);
        }
      }
    }, delay);
  }
  
  /**
   * 为闪电链查找随机目标
   * @returns {Object|null} 目标位置对象{row, col}或null
   * @private
   */
  _findRandomTarget() {
    // 获取网格中所有非空的块
    const validBlocks = [];
    
    for (let r = 0; r < this.grid.rows; r++) {
      for (let c = 0; c < this.grid.cols; c++) {
        const block = this.grid.getBlock(r, c);
        if (block) {
          validBlocks.push({ row: r, col: c, block });
        }
      }
    }
    
    // 如果没有有效块，返回null
    if (validBlocks.length === 0) {
      return null;
    }
    
    // 随机选择一个块
    const randomIndex = Math.floor(Math.random() * validBlocks.length);
    return validBlocks[randomIndex];
  }
  
  /**
   * 使用闪电链道具
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @param {number} level - 道具等级
   * @returns {boolean} 是否成功使用
   * @private
   */
  _useLightning(row, col, level) {
    // 检查目标位置是否有效
    if (!this._isValidPosition(row, col)) {
      console.log('闪电链目标位置无效:', row, col);
      return false;
    }
    
    // 获取目标方块
    const targetBlock = this.grid.getBlock(row, col);
    if (!targetBlock) {
      console.log('闪电链目标位置没有方块');
      return false;
    }
    
    // 获取闪电链连锁数量和目标颜色（考虑冰冻效果）
    const maxChainCount = this.itemEffects[ITEM_TYPES.LIGHTNING].chainCount[level - 1];
    const targetColor = targetBlock.effect === BLOCK_EFFECTS.FROZEN ? 
      targetBlock.originalColor : targetBlock.color;
      
    console.log(`闪电链最大连锁数量: ${maxChainCount}, 目标颜色: ${targetColor}, 冰冻: ${targetBlock.effect === BLOCK_EFFECTS.FROZEN}`);
    
    // 计算闪电链路径
    const { chainPath, processedBlockCount } = this._calculateLightningChain(
      row, col, targetBlock, targetColor, maxChainCount
    );
    
    console.log(`闪电链影响了${this.affectedBlocks.size}个方块，处理了${processedBlockCount}个方块(包含冰冻)，路径点数:${chainPath.length}`);
    
    // 修改判断条件：如果有处理过的方块(包括解冻方块)，即使没有实际消除的方块，也创建闪电动画
    if (processedBlockCount > 0) {
      // 创建闪电动画效果
      this._createAnimationEffect('lightning', { 
        chainPath,
        color: targetColor,
        level
      });
      
      // 播放闪电音效
      this._playSound('lightning');
      
      return true;
    }
    
    console.log('闪电链未影响任何方块');
    return false;
  }
  
  /**
   * 计算闪电链路径和受影响的方块
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {Object} targetBlock - 目标方块
   * @param {string} targetColor - 目标颜色
   * @param {number} maxChainCount - 最大连锁数量
   * @returns {Object} 包含chainPath和processedBlockCount的对象
   * @private
   */
  _calculateLightningChain(startRow, startCol, targetBlock, targetColor, maxChainCount) {
    console.log(`开始计算闪电链: 起点(${startRow},${startCol}), 目标颜色:${targetColor}, 最大连锁:${maxChainCount}`);

    // 获取起始方块的真实颜色（考虑冰冻效果）
    const startBlockRealColor = targetBlock.effect === BLOCK_EFFECTS.FROZEN ?
      targetBlock.originalColor : targetBlock.color;

    // 找到所有同色方块
    const allSameColorBlocks = this._findAllSameColorBlocks(startBlockRealColor);
    console.log(`找到${allSameColorBlocks.length}个同色方块`);

    // 如果没有其他同色方块，只处理起始方块
    if (allSameColorBlocks.length <= 1) {
      const chainPath = [{ row: startRow, col: startCol }];
      let processedBlockCount = 0;

      if (targetBlock.effect === BLOCK_EFFECTS.FROZEN) {
        targetBlock.applyEffect();
        processedBlockCount++;
      } else {
        this._markBlockAffected(targetBlock);
        processedBlockCount++;
      }

      return { chainPath, processedBlockCount };
    }

    // 使用智能路径算法连接同色方块
    const { chainPath, connectedBlocks } = this._buildSmartLightningPath(
      startRow, startCol, allSameColorBlocks, maxChainCount
    );

    // 处理所有连接的方块
    let processedBlockCount = 0;
    for (const blockInfo of connectedBlocks) {
      const block = this.grid.getBlock(blockInfo.row, blockInfo.col);
      if (block) {
        if (block.effect === BLOCK_EFFECTS.FROZEN) {
          block.applyEffect();
          processedBlockCount++;
        } else {
          this._markBlockAffected(block);
          processedBlockCount++;
        }
      }
    }

    console.log(`闪电链完成: 路径点数${chainPath.length}, 连接方块${connectedBlocks.length}, 处理方块${processedBlockCount}`);
    return { chainPath, processedBlockCount };
  }
  
  /**
   * 找到所有同色方块
   * @param {string} targetColor - 目标颜色
   * @returns {Array} 同色方块位置数组
   * @private
   */
  _findAllSameColorBlocks(targetColor) {
    const sameColorBlocks = [];

    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          // 获取方块的真实颜色（考虑冰冻效果）
          const blockRealColor = block.effect === BLOCK_EFFECTS.FROZEN ?
            block.originalColor : block.color;

          if (blockRealColor === targetColor) {
            sameColorBlocks.push({ row, col, block });
          }
        }
      }
    }

    return sameColorBlocks;
  }
  
  /**
   * 构建智能闪电路径
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {Array} allSameColorBlocks - 所有同色方块
   * @param {number} maxChainCount - 最大连锁数量
   * @returns {Object} 包含chainPath和connectedBlocks的对象
   * @private
   */
  _buildSmartLightningPath(startRow, startCol, allSameColorBlocks, maxChainCount) {
    // 按距离排序所有同色方块
    const sortedBlocks = allSameColorBlocks
      .filter(block => !(block.row === startRow && block.col === startCol)) // 排除起始方块
      .map(block => ({
        ...block,
        distance: this._calculateDistance(startRow, startCol, block.row, block.col)
      }))
      .sort((a, b) => a.distance - b.distance);

    // 选择要连接的方块（从近到远，但不超过最大连锁数）
    const targetBlocks = [{ row: startRow, col: startCol }]; // 包含起始方块
    const maxTargets = Math.min(maxChainCount, sortedBlocks.length + 1);

    // 贪心算法：优先选择距离近的方块，但也考虑一些随机性
    for (let i = 0; i < sortedBlocks.length && targetBlocks.length < maxTargets; i++) {
      const block = sortedBlocks[i];

      // 添加一些随机性，让路径不总是完全按距离排序
      const randomFactor = Math.random();
      const shouldInclude = randomFactor > 0.2 || i < 3; // 前3个方块优先选择

      if (shouldInclude) {
        targetBlocks.push(block);
      }
    }

    console.log(`选择了${targetBlocks.length}个目标方块进行连接`);

    // 构建连接路径
    const chainPath = this._buildConnectionPath(targetBlocks);

    return {
      chainPath,
      connectedBlocks: targetBlocks
    };
  }
  
  /**
   * 计算两点间的距离
   * @param {number} r1 - 第一个点的行
   * @param {number} c1 - 第一个点的列
   * @param {number} r2 - 第二个点的行
   * @param {number} c2 - 第二个点的列
   * @returns {number} 欧几里得距离
   * @private
   */
  _calculateDistance(r1, c1, r2, c2) {
    return Math.sqrt((r1 - r2) * (r1 - r2) + (c1 - c2) * (c1 - c2));
  }
  
  /**
   * 构建连接路径
   * @param {Array} targetBlocks - 目标方块数组
   * @returns {Array} 连接路径
   * @private
   */
  _buildConnectionPath(targetBlocks) {
    if (targetBlocks.length <= 1) {
      return targetBlocks.map(block => ({ row: block.row, col: block.col }));
    }

    const chainPath = [];

    // 从第一个方块开始
    let currentBlock = targetBlocks[0];
    chainPath.push({ row: currentBlock.row, col: currentBlock.col });

    // 使用最近邻算法连接剩余方块
    const remainingBlocks = [...targetBlocks.slice(1)];

    while (remainingBlocks.length > 0) {
      // 找到距离当前方块最近的下一个方块
      let nearestIndex = 0;
      let nearestDistance = this._calculateDistance(
        currentBlock.row, currentBlock.col,
        remainingBlocks[0].row, remainingBlocks[0].col
      );

      for (let i = 1; i < remainingBlocks.length; i++) {
        const distance = this._calculateDistance(
          currentBlock.row, currentBlock.col,
          remainingBlocks[i].row, remainingBlocks[i].col
        );

        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestIndex = i;
        }
      }

      // 连接到最近的方块
      const nextBlock = remainingBlocks[nearestIndex];

      // 生成从当前方块到下一个方块的路径
      const pathSegment = this._generatePathSegment(
        currentBlock.row, currentBlock.col,
        nextBlock.row, nextBlock.col
      );

      // 添加路径段（跳过第一个点，因为它已经在路径中）
      chainPath.push(...pathSegment.slice(1));

      // 更新当前方块
      currentBlock = nextBlock;

      // 从剩余方块中移除已连接的方块
      remainingBlocks.splice(nearestIndex, 1);
    }

    return chainPath;
  }
  
  /**
   * 生成两点间的路径段
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {number} endRow - 结束行
   * @param {number} endCol - 结束列
   * @returns {Array} 路径段
   * @private
   */
  _generatePathSegment(startRow, startCol, endRow, endCol) {
    const path = [];

    // 添加起始点
    path.push({ row: startRow, col: startCol });

    // 如果起始点和结束点相同，直接返回
    if (startRow === endRow && startCol === endCol) {
      return path;
    }

    // 计算方向
    const deltaRow = endRow - startRow;
    const deltaCol = endCol - startCol;

    // 添加一些中间点来创建更自然的路径
    const steps = Math.max(Math.abs(deltaRow), Math.abs(deltaCol));

    if (steps > 1) {
      // 创建曲线路径而不是直线
      for (let i = 1; i < steps; i++) {
        const progress = i / steps;

        // 线性插值
        const interpolatedRow = startRow + deltaRow * progress;
        const interpolatedCol = startCol + deltaCol * progress;

        // 添加一些随机偏移来创建更自然的闪电效果
        const randomOffset = 0.3;
        const offsetRow = interpolatedRow + (Math.random() - 0.5) * randomOffset;
        const offsetCol = interpolatedCol + (Math.random() - 0.5) * randomOffset;

        path.push({
          row: Math.round(offsetRow),
          col: Math.round(offsetCol)
        });
      }
    }

    // 添加结束点
    path.push({ row: endRow, col: endCol });

    return path;
  }
  

  
  /**
   * 处理激流道具使用
   * @param {number} row - 目标行（激流道具不需要指定位置）
   * @param {number} col - 目标列（激流道具不需要指定位置）
   * @param {number} level - 道具等级
   * @returns {boolean} 是否成功使用
   * @private
   */
  _handleWaterflow(row, col, level) {
    console.log('处理激流道具使用，等级:', level);
    
    const success = this._useWaterflow(level);
    
    if (success) {
      // 激流道具成功使用，立即应用冷却和减少使用次数
      this._applyItemCooldownAndUse(ITEM_TYPES.WATERFLOW);
    }
    
    return success;
  }

  /**
   * 使用激流道具
   * @param {number} level - 道具等级
   * @returns {boolean} 是否成功使用
   * @private
   */
  _useWaterflow(level) {
    console.log('🌊 使用激流道具，等级:', level);
    
    // 获取影响的行数（支持1-5级）
    const rowCount = this.itemEffects[ITEM_TYPES.WATERFLOW].rowCount[level - 1];
    
    // 检查是否有足够的行
    if (rowCount > this.grid.rows) {
      console.log(`激流道具影响行数(${rowCount})超出网格行数(${this.grid.rows})`);
      return false;
    }
    
    // 检查底部是否有方块
    let hasBlocksAtBottom = false;
    const targetRows = [];
    
    // 从底部开始，确定要消除的行（无论是否有方块）
    for (let row = this.grid.rows - 1; row >= this.grid.rows - rowCount && row >= 0; row--) {
      targetRows.push(row);
      
      // 检查这一行是否有方块
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.getBlock(row, col)) {
          hasBlocksAtBottom = true;
        }
      }
    }
    
    if (!hasBlocksAtBottom) {
      console.log('底部没有方块，激流无效');
      return false;
    }
    
    console.log(`💥 等级${level}激流将消除底部${rowCount}行:`, targetRows);
    
    // 创建激流动画
    this._createWaterflowAnimation(targetRows, level);
    
    // 直接消除底部指定行数的所有方块
    this._clearBottomRows(targetRows);
    
    // 播放激流音效
    this._playSound('torrent');
    
    return true;
  }

  /**
   * 处理地震术使用
   * @param {number} row - 目标行（地震术不需要）
   * @param {number} col - 目标列（地震术不需要）
   * @param {number} level - 道具等级
   * @returns {boolean} 是否成功使用
   * @private
   */
  _handleEarthquake(row, col, level) {
    console.log('🌍 使用地震术，等级:', level);
    
    // 地震术不需要目标位置，直接使用
    return this._useEarthquake(level);
  }

  /**
   * 使用地震术道具
   * @param {number} level - 道具等级
   * @returns {boolean} 是否成功使用
   * @private
   */
  _useEarthquake(level) {
    console.log('🌍 执行地震术，等级:', level);
    
    // 🔍 调试：记录地震术触发前的网格状态
    const beforeState = this.grid.debugGridState(`🌍 地震术触发前 - 等级${level}`, true);
    
    // 检查是否有方块
    if (this._isEmpty()) {
      console.log('场地为空，地震术无效果');
      return false;
    }
    
    // 根据等级确定影响的行数（从底部开始计算）
    const affectedRows = this._getEarthquakeAffectedRows(level);
    console.log(`💥 等级${level}地震术影响行数: ${affectedRows}行（从底部开始）`);
    
    if (affectedRows === 0) {
      console.log('影响行数为0，地震术无效果');
      return false;
    }
    
    // 执行地震效果：让底部N行的方块进行重力下落
    const affectedCount = this._executeEarthquakeGravity(affectedRows, beforeState);
    
    if (affectedCount > 0) {
      // 创建地震动画效果
      this._createEarthquakeAnimation(affectedRows, level);
      
      // 播放地震音效
      this._playSound('earthquake');
      
      console.log(`✅ 地震术成功影响了${affectedCount}个方块`);
      return true;
    }
    
    console.log('地震术没有影响任何方块');
    return false;
  }

  /**
   * 检查场地是否为空
   * @returns {boolean} 是否为空
   * @private
   */
  _isEmpty() {
    // 检查网格中是否有任何方块
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.getBlock(row, col)) {
          return false;
        }
      }
    }
    return true;
  }

  /**
   * 计算当前方块的总高度
   * @returns {number} 方块总高度
   * @private
   */
  _calculateCurrentBlockHeight() {
    let topRow = this.grid.rows;
    
    // 从上往下找第一行有方块的位置
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.getBlock(row, col)) {
          topRow = row;
          break;
        }
      }
      if (topRow !== this.grid.rows) break;
    }
    
    // 返回从顶部有方块行到底部的高度
    return topRow === this.grid.rows ? 0 : (this.grid.rows - topRow);
  }

  /**
   * 根据等级获取地震术影响的行数
   * @param {number} level - 地震术等级
   * @returns {number} 影响的行数
   * @private
   */
  _getEarthquakeAffectedRows(level) {
    // 根据等级确定影响的行数：1级影响3行，2级影响4行，以此类推
    const baseRows = 2;
    return baseRows + level;
  }

  /**
   * 执行地震重力效果
   * @param {number} affectedRows - 影响的行数（从底部开始）
   * @param {Object} beforeState - 地震前的网格状态（用于调试对比）
   * @returns {number} 受影响的方块数量
   * @private
   */
  _executeEarthquakeGravity(affectedRows, beforeState) {
    console.log('🌍 执行地震重力效果，影响行数:', affectedRows);
    
    let affectedCount = 0;
    this.affectedBlocks.clear();
    
    // 计算影响的行范围（从底部开始）
    const startRow = Math.max(0, this.grid.rows - affectedRows);
    const endRow = this.grid.rows - 1;
    
    console.log(`🌍 影响行范围: ${startRow} 到 ${endRow}`);
    
    // 收集所有受影响的列，用于后续的重力应用
    const affectedColumns = new Set();
    
    // 逐列处理地震重力效果
    for (let col = 0; col < this.grid.cols; col++) {
      let hasMovement = false;
      
      console.log(`🔍 检查列 ${col}:`);
      
      // 简化逻辑：直接检查影响区域内是否有方块可以下落
      for (let row = startRow; row <= endRow; row++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          console.log(`  行 ${row}: 有方块 (${block.type || '未知'}, ${block.color || '未知'})`);
          
          // 检查这个方块下方是否有空位
          let canFall = false;
          let fallTarget = -1;
          
          for (let checkRow = row + 1; checkRow < this.grid.rows; checkRow++) {
            const belowBlock = this.grid.getBlock(checkRow, col);
            if (!belowBlock) {
              canFall = true;
              if (fallTarget === -1) fallTarget = checkRow;
              console.log(`    下方行 ${checkRow}: 空位`);
            } else {
              console.log(`    下方行 ${checkRow}: 有方块 (${belowBlock.type || '未知'}, ${belowBlock.color || '未知'})`);
              break; // 遇到方块就停止检查
            }
          }
          
          if (canFall) {
            hasMovement = true;
            affectedCount++;
            
            // 🔧 确保方块位置信息正确
            if (block.row !== row || block.col !== col) {
              console.log(`  ⚠️ 方块位置信息不一致: 网格位置[${row}, ${col}] vs 方块位置[${block.row}, ${block.col}]`);
              // 更新方块位置为实际网格位置
              block.row = row;
              block.col = col;
              console.log(`  🔧 已修正方块位置为: [${row}, ${col}]`);
            }
            
            this.affectedBlocks.add(block);
            console.log(`  ✅ 发现可下落方块: [${row}, ${col}] → [${fallTarget}, ${col}] (${block.type || '未知'}, ${block.color || '未知'})`);
          } else {
            console.log(`  ❌ 方块无法下落: [${row}, ${col}] (下方无空位)`);
          }
        } else {
          console.log(`  行 ${row}: 空位`);
        }
      }
      
      if (hasMovement) {
        affectedColumns.add(col);
        console.log(`  📌 列 ${col} 标记为受影响`);
      } else {
        console.log(`  ⏸️  列 ${col} 无需处理`);
      }
    }
    
    console.log(`地震重力效果预计影响${affectedCount}个方块，涉及${affectedColumns.size}列`);
    
    // 如果有方块会移动，应用重力效果
    if (affectedCount > 0) {
          // 🔍 调试：记录重力应用前的状态
    const beforeGravityState = this.grid.debugGridState('🌍 重力应用前', false);
    
    console.log(`🌍 调用重力系统，受影响列: [${Array.from(affectedColumns).join(', ')}]`);
    console.log(`🌍 受影响方块数: ${this.affectedBlocks.size}`);
    
    // 🔧 详细诊断：检查每个方块的状态
    console.log('🔬 地震术方块状态诊断:');
    for (const block of this.affectedBlocks) {
      const blockInGrid = this.grid.getBlock(block.row, block.col) === block;
      console.log(`  方块[${block.row}, ${block.col}]: 在网格中=${blockInGrid}, 类型=${block.type}, 颜色=${block.color}`);
      
      // 如果方块不在预期位置，尝试修复
      if (!blockInGrid) {
        console.log(`  🔍 方块位置异常，搜索网格...`);
        let found = false;
        for (let r = 0; r < this.grid.rows && !found; r++) {
          for (let c = 0; c < this.grid.cols && !found; c++) {
            if (this.grid.getBlock(r, c) === block) {
              console.log(`  🎯 在[${r}, ${c}]找到方块，更新位置信息`);
              block.row = r;
              block.col = c;
              found = true;
            }
          }
        }
        if (!found) {
          console.log(`  ❌ 方块在网格中丢失`);
        }
      }
    }
    
    // 使用网格的重力系统来处理方块下落
    const columnsArray = Array.from(affectedColumns);
    const blocksArray = Array.from(this.affectedBlocks);
    
    console.log(`🔧 即将调用 applyGravity，参数: 列=[${columnsArray.join(',')}], 方块数=${blocksArray.length}`);
    
    // 🔍 最后检查：确保参数类型正确
    console.log(`🔍 参数类型检查:`);
    console.log(`  列数组类型: ${Array.isArray(columnsArray) ? 'Array' : typeof columnsArray}, 长度: ${columnsArray.length}`);
    console.log(`  方块集合类型: ${this.affectedBlocks.constructor.name}, 大小: ${this.affectedBlocks.size}`);
    console.log(`  方块数组类型: ${Array.isArray(blocksArray) ? 'Array' : typeof blocksArray}, 长度: ${blocksArray.length}`);
    
    // 传递 Set 类型而不是数组，因为 grid.js 中的代码期望 Set
    const hasActualMovement = this.grid.applyGravity(columnsArray, this.affectedBlocks, []);
    
    console.log(`🌍 重力系统返回结果: ${hasActualMovement ? '有下落' : '无下落'}`);
    
    // 🔍 调试：记录重力应用后的状态
    const afterGravityState = this.grid.debugGridState('🌍 重力应用后', true);
      
      // 🔍 调试：对比前后状态
      if (beforeState) {
        this.grid.constructor.compareGridStates(beforeState, afterGravityState, '地震术重力效果');
      }
      
      if (hasActualMovement) {
        console.log('✅ 地震重力效果成功应用，方块开始下落');
        
        // 🔍 调试：记录重力应用后的状态
        const afterGravityState = this.grid.debugGridState('🌍 重力应用后', false);
        console.log('🔍 地震术重力流程完毕');
        
        // 🎯 改进：立即触发消除检查，不等动画完成
        console.log('🌍 立即检查是否有满行或匹配可消除');
        setTimeout(() => {
          console.log('🌍 地震术重力完成，立即触发消除检查');
          this.emit('check:matches');
        }, 100); // 很短的延迟，确保重力动画开始但立即检查消除
        
      } else {
        console.log('🌍 地震效果：重力系统报告无实际移动，但预期有移动 - 可能是重力系统的问题');
        console.log('🌍 尝试备用方案：直接触发全网格重力检查');
        
        // 备用方案：使用全网格重力检查
        const fullGridResult = this.grid.applyFullGridGravity();
        console.log(`🌍 全网格重力结果: ${fullGridResult ? '有下落' : '无下落'}`);
        
        if (fullGridResult) {
          setTimeout(() => {
            console.log('🌍 备用方案成功，触发消除检查');
            this.emit('check:matches');
          }, 100); // 同样使用较短延迟
        } else {
          // 立即触发检查，确保游戏流程继续
          this.emit('check:matches');
        }
      }
    } else {
      console.log('🌍 地震效果：没有方块需要移动');
      // 立即触发检查，确保游戏流程继续
      this.emit('check:matches');
    }
    
    return affectedCount;
  }

  /**
   * 创建地震动画效果
   * @param {number} affectedRows - 影响行数
   * @param {number} level - 地震等级
   * @private
   */
  _createEarthquakeAnimation(affectedRows, level) {
    console.log(`🌍 创建地震动画，影响${affectedRows}行，等级${level}`);
    
    // 🔧 修复：设置地震术专用动画状态
    this.animations.isActive = true;
    this.animations.timer = 0;
    this.animations.type = 'earthquake'; // 标记为地震术动画
    
    // 创建地震震动效果
    this._createAnimationEffect('earthquake', {
      affectedRows,
      level,
      duration: 800
    });
  }

  /**
   * 清除底部指定行
   * @param {Array<number>} targetRows - 要清除的行数组
   * @private
   */
  _clearBottomRows(targetRows) {
    console.log('清除底部行:', targetRows);
    
    // 清空之前可能残留的受影响方块集合
    this.affectedBlocks.clear();
    console.log('已清空残留的受影响方块集合');
    
    let clearedBlockCount = 0;
    
    // 清除指定行的所有方块
    for (const row of targetRows) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          // 启动消除动画
          block.startDestroyAnimation();
          
          // 标记为受影响的方块
          this.affectedBlocks.add(block);
          clearedBlockCount++;
          
          console.log(`标记激流消除方块 [${row}, ${col}]`);
        }
      }
    }
    
    console.log(`激流消除了${clearedBlockCount}个方块`);
    
    // 立即处理方块移除和下落，不需要等待动画
    // 激流道具的效果应该是立即的
    this._processWaterflowEffect(targetRows);
  }

  /**
   * 处理激流效果后的方块下落
   * @param {Array<number>} clearedRows - 被清除的行
   * @private
   */
  _processWaterflowEffect(clearedRows) {
    console.log('处理激流效果后的下落，清除的行:', clearedRows);
    console.log('当前受影响的方块数量:', this.affectedBlocks.size);
    
    // 验证受影响的方块是否都在预期的行中，并移除它们
    let validBlockCount = 0;
    let invalidBlockCount = 0;
    
    for (const block of this.affectedBlocks) {
      if (block && block.row >= 0 && block.col >= 0) {
        if (clearedRows.includes(block.row)) {
          console.log(`移除预期的激流方块 [${block.row}, ${block.col}]`);
          this.grid.removeBlock(block.row, block.col);
          validBlockCount++;
        } else {
          console.warn(`发现意外的受影响方块 [${block.row}, ${block.col}]，不在清除行列表中`);
          invalidBlockCount++;
          // 不移除意外的方块，可能是其他道具的残留
        }
      }
    }
    
    console.log(`激流移除了${validBlockCount}个有效方块，跳过了${invalidBlockCount}个无效方块`);
    
    // 清空受影响的方块集合
    this.affectedBlocks.clear();
    
    // 使用激流专用的简单下落逻辑，不调用游戏控制器的复杂逻辑
    const hasFallen = this._applyWaterflowGravity(clearedRows);
    
    // 等待下落动画完成后恢复游戏状态
    if (hasFallen) {
      setTimeout(() => {
        this.emit('check:matches');
        this._restoreGameState();
      }, 300);
    } else {
      this.emit('check:matches');
      this._restoreGameState();
    }
  }

  /**
   * 激流专用的重力处理
   * @param {Array<number>} clearedRows - 被清除的行
   * @returns {boolean} 是否有方块下落
   * @private
   */
  _applyWaterflowGravity(clearedRows) {
    console.log('应用激流专用重力，清除的行:', clearedRows);
    
    // 排序清除的行，从下到上
    const sortedClearedRows = [...clearedRows].sort((a, b) => b - a);
    let hasFallen = false;
    
    // 对每个被清除的行，让其上方的方块下落
    for (const clearedRow of sortedClearedRows) {
      // 从被清除行的上方开始，向上扫描
      for (let sourceRow = clearedRow - 1; sourceRow >= 0; sourceRow--) {
        // 检查这一行是否有方块需要下落
        let hasBlocksInRow = false;
        for (let col = 0; col < this.grid.cols; col++) {
          if (this.grid.getBlock(sourceRow, col)) {
            hasBlocksInRow = true;
            break;
          }
        }
        
        if (hasBlocksInRow) {
          // 找到这行方块可以下落到的最低位置
          const targetRow = this._findLowestAvailableRow(sourceRow);
          
          if (targetRow > sourceRow) {
            console.log(`激流下落：第${sourceRow}行 -> 第${targetRow}行`);
            
            // 移动整行方块
            for (let col = 0; col < this.grid.cols; col++) {
              const block = this.grid.getBlock(sourceRow, col);
              if (block) {
                // 移除原位置
                this.grid.removeBlock(sourceRow, col);
                
                // 放置到新位置
                this.grid.placeBlock(block, targetRow, col);
                
                // 创建下落动画
                this.grid.addFallingAnimation(block, sourceRow, col, targetRow, col);
                
                hasFallen = true;
              }
            }
          }
        }
      }
    }
    
    console.log('激流重力处理完成，有方块下落:', hasFallen);
    return hasFallen;
  }

  /**
   * 找到指定行的方块可以下落到的最低位置
   * @param {number} row - 源行
   * @returns {number} 目标行
   * @private
   */
  _findLowestAvailableRow(row) {
    // 从当前行开始向下查找
    for (let targetRow = this.grid.rows - 1; targetRow > row; targetRow--) {
      // 检查这一行是否完全为空
      let isEmpty = true;
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.getBlock(targetRow, col)) {
          isEmpty = false;
          break;
        }
      }
      
      if (isEmpty) {
        return targetRow;
      }
    }
    
    // 如果没有找到空行，返回原行号（不移动）
    return row;
  }
  
  /**
   * 创建激流动画
   * @param {Array} rows - 受影响的行
   * @param {number} level - 道具等级
   * @private
   */
  _createWaterflowAnimation(rows, level) {
    if (rows.length === 0) return;
    
    // 获取网格信息
    const cellSize = this.grid.blockSize || 30;
    const offsetX = this.grid.offsetX || 0;
    const offsetY = this.grid.offsetY || 0;
    const gridWidth = this.grid.cols * cellSize;
    
    // 创建波浪点
    const wavePoints = [];
    const waveCount = 3 + level; // 等级越高，波浪越多
    
    // 为每一行创建水波效果
    rows.forEach(row => {
      // 计算行的Y坐标
      const rowY = offsetY + row * cellSize;
      
      for (let i = 0; i < waveCount; i++) {
        // 水波起始X位置（均匀分布）
        const startX = offsetX + (i * gridWidth / waveCount);
        
        wavePoints.push({
          x: startX,
          y: rowY,
          amplitude: 5 + level * 2, // 波幅随等级增大
          frequency: 0.05 + level * 0.01, // 频率随等级增大
          speed: 2 + level, // 速度随等级增大
          phase: Math.random() * Math.PI * 2, // 随机相位
          width: gridWidth / waveCount * 1.2, // 波宽
          row: row
        });
      }
    });
    
    // 存储动画状态
    this.animations.waterflowEffect = {
      wavePoints: wavePoints,
      startTime: Date.now(),
      duration: 400, // 动画持续时间（毫秒）
      level: level
    };
    
    console.log(`创建激流动画，波浪数:${wavePoints.length}，等级:${level}`);
  }
  
  /**
   * 渲染激流效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderWaterflowEffect(ctx) {
    if (!ctx) return;
    
    const effect = this.animations.waterflowEffect;
    if (!effect) return;
    
    // 计算动画进度
    const now = Date.now();
    const elapsed = now - effect.startTime;
    const progress = Math.min(1, elapsed / effect.duration);
    
    // 如果动画结束，清理
    if (progress >= 1) {
      this.animations.waterflowEffect = null;
      return;
    }
    
    // 获取网格信息
    const cellSize = this.grid.blockSize || 30;
    const offsetX = this.grid.offsetX || 0;
    const gridWidth = this.grid.cols * cellSize;
    
    // 保存上下文
    ctx.save();
    
    // 设置合成模式
    ctx.globalCompositeOperation = 'lighter';
    
    // 遍历所有波浪点
    for (const wave of effect.wavePoints) {
      // 波浪颜色随等级不同
      let waveColor;
      switch (effect.level) {
        case 1:
          waveColor = `rgba(100, 150, 255, ${0.7 * (1 - progress)})`;
          break;
        case 2:
          waveColor = `rgba(70, 130, 255, ${0.75 * (1 - progress)})`;
          break;
        case 3:
        default:
          waveColor = `rgba(50, 100, 255, ${0.8 * (1 - progress)})`;
          break;
      }
      
      // 绘制路径
      ctx.beginPath();
      
      // 波浪起点
      let x = wave.x;
      ctx.moveTo(x, wave.y);
      
      // 绘制波浪
      while (x < wave.x + wave.width) {
        // 计算当前相位
        const phase = wave.phase + x * wave.frequency + elapsed * 0.01 * wave.speed;
        
        // 计算Y偏移
        const yOffset = Math.sin(phase) * wave.amplitude;
        
        // 添加点
        ctx.lineTo(x, wave.y + yOffset);
        x += 5; // 点的间隔
      }
      
      // 连接到底部
      ctx.lineTo(wave.x + wave.width, wave.y + cellSize);
      ctx.lineTo(wave.x, wave.y + cellSize);
      ctx.closePath();
      
      // 填充波浪
      const gradient = ctx.createLinearGradient(0, wave.y, 0, wave.y + cellSize);
      gradient.addColorStop(0, waveColor);
      gradient.addColorStop(1, `rgba(150, 200, 255, ${0.3 * (1 - progress)})`);
      
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // 添加光晕边缘
      ctx.strokeStyle = `rgba(200, 220, 255, ${0.6 * (1 - progress)})`;
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // 添加粒子效果（只有等级2以上的道具）
      if (effect.level >= 2 && Math.random() < 0.3) {
        const particleX = wave.x + Math.random() * wave.width;
        const particleY = wave.y + Math.random() * cellSize * 0.8;
        const particleSize = 1 + Math.random() * 3;
        
        ctx.beginPath();
        ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(220, 240, 255, ${0.7 * (1 - progress)})`;
        ctx.fill();
      }
    }
    
    // 恢复上下文
    ctx.restore();
  }

  /**
   * 渲染地震术效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderEarthquakeEffect(ctx) {
    const effect = this.animations.earthquakeEffect;
    if (!effect) return;

    const now = Date.now();
    const elapsed = now - effect.startTime;
    const progress = Math.min(1, elapsed / effect.duration);

    // 动画结束，清除效果
    if (progress >= 1) {
      this.animations.earthquakeEffect = null;
      return;
    }

    const cellSize = this.grid.blockSize || 30;
    const offsetX = this.grid.offsetX || 0;
    const offsetY = this.grid.offsetY || 0;

    // 计算震动偏移
    const shakeTime = elapsed * effect.shakeFrequency;
    const shakeX = Math.sin(shakeTime * 2) * effect.shakeIntensity * (1 - progress);
    const shakeY = Math.cos(shakeTime * 3) * effect.shakeIntensity * (1 - progress);

    ctx.save();
    
    // 应用震动偏移
    ctx.translate(shakeX, shakeY);

    // 绘制影响区域高亮
    this._renderEarthquakeHighlight(ctx, effect, cellSize, offsetX, offsetY, progress);
    
    // 绘制波动效果
    this._renderEarthquakeWaves(ctx, effect, cellSize, offsetX, offsetY, elapsed, progress);
    
    // 更新并绘制粒子
    this._updateAndRenderEarthquakeParticles(ctx, effect, progress);

    ctx.restore();
  }

  /**
   * 渲染地震术影响区域高亮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} effect - 效果对象
   * @param {number} cellSize - 格子大小
   * @param {number} offsetX - X偏移
   * @param {number} offsetY - Y偏移
   * @param {number} progress - 动画进度
   * @private
   */
  _renderEarthquakeHighlight(ctx, effect, cellSize, offsetX, offsetY, progress) {
    // 更新高亮透明度
    effect.highlightAlpha += effect.highlightDirection * 0.05;
    if (effect.highlightAlpha >= 0.6) {
      effect.highlightAlpha = 0.6;
      effect.highlightDirection = -1;
    } else if (effect.highlightAlpha <= 0.2) {
      effect.highlightAlpha = 0.2;
      effect.highlightDirection = 1;
    }

    // 绘制影响区域背景
    ctx.fillStyle = `rgba(139, 69, 19, ${effect.highlightAlpha * (1 - progress * 0.5)})`; // 棕色高亮
    const regionStartY = offsetY + effect.startRow * cellSize;
    const regionHeight = (effect.endRow - effect.startRow + 1) * cellSize;
    const regionWidth = this.grid.cols * cellSize;
    
    ctx.fillRect(offsetX, regionStartY, regionWidth, regionHeight);

    // 绘制区域边框
    ctx.strokeStyle = `rgba(160, 82, 45, ${0.8 * (1 - progress * 0.5)})`; // 土黄色边框
    ctx.lineWidth = 2;
    ctx.strokeRect(offsetX, regionStartY, regionWidth, regionHeight);
  }

  /**
   * 渲染地震术波动效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} effect - 效果对象
   * @param {number} cellSize - 格子大小
   * @param {number} offsetX - X偏移
   * @param {number} offsetY - Y偏移
   * @param {number} elapsed - 已过时间
   * @param {number} progress - 动画进度
   * @private
   */
  _renderEarthquakeWaves(ctx, effect, cellSize, offsetX, offsetY, elapsed, progress) {
    // 更新波动相位
    effect.wavePhase += 0.3;
    
    const regionWidth = this.grid.cols * cellSize;
    const waveCount = 3 + effect.level; // 波动数量随等级增加
    
    ctx.strokeStyle = `rgba(205, 133, 63, ${0.7 * (1 - progress)})`; // 沙棕色波动
    ctx.lineWidth = 2;
    
    // 绘制多条波动线
    for (let w = 0; w < waveCount; w++) {
      const waveY = offsetY + (effect.startRow + w * (effect.endRow - effect.startRow) / (waveCount - 1)) * cellSize;
      const phaseOffset = w * Math.PI / 3;
      
      ctx.beginPath();
      for (let x = 0; x <= regionWidth; x += 5) {
        const waveOffset = Math.sin((x / 30 + effect.wavePhase + phaseOffset)) * 
                          effect.waveAmplitude * (1 - progress);
        if (x === 0) {
          ctx.moveTo(offsetX + x, waveY + waveOffset);
        } else {
          ctx.lineTo(offsetX + x, waveY + waveOffset);
        }
      }
      ctx.stroke();
    }
  }

  /**
   * 更新并渲染地震术粒子
   * @param {CanvasRenderingContext2D} ctx - 画布上下文  
   * @param {Object} effect - 效果对象
   * @param {number} progress - 动画进度
   * @private
   */
  _updateAndRenderEarthquakeParticles(ctx, effect, progress) {
    if (!effect.particles || effect.particles.length === 0) return;

    // 更新粒子
    for (let i = effect.particles.length - 1; i >= 0; i--) {
      const particle = effect.particles[i];
      
      // 更新位置
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      // 更新生命值
      particle.life -= particle.decay;
      
      // 移除死亡粒子
      if (particle.life <= 0) {
        effect.particles.splice(i, 1);
        continue;
      }
      
      // 重力效果
      particle.vy += 0.2;
      
      // 渲染粒子
      ctx.save();
      ctx.globalAlpha = particle.life * (1 - progress * 0.5);
      ctx.fillStyle = particle.color;
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      ctx.fill();
      ctx.restore();
    }
  }
  
  /**
   * 完成道具效果，移除受影响的方块
   * @private
   */
  _finalizeItemEffect() {
    // 如果没有受影响的方块，直接返回
    if (this.affectedBlocks.size === 0) {
      console.log('没有受影响的方块，跳过处理');
      this._restoreGameState();
      return;
    }
    
    // 记录需要检查的列和移除的方块位置
    const affectedCols = new Set();
    const removedPositions = [];
    
    console.log(`处理${this.affectedBlocks.size}个受影响的方块`);
    
    // 收集被移除的方块位置
    for (const block of this.affectedBlocks) {
      if (block && block.row >= 0 && block.col >= 0) {
        // 标记列为受影响
        affectedCols.add(block.col);
        
        // 记录位置
        removedPositions.push({
          row: block.row,
          col: block.col
        });
      }
    }
    
    // 触发方块移除事件
    this.emit('blocks:removed');
    
    // 使用较短的等待时间，确保动画有足够时间显示但不会卡住游戏
    setTimeout(() => {
      this._processBlockRemoval(affectedCols, removedPositions);
    }, 150);
  }
  
  /**
   * 处理方块移除和下落
   * @param {Set} affectedCols - 受影响的列
   * @param {Array} removedPositions - 被移除的位置
   * @private
   */
  _processBlockRemoval(affectedCols, removedPositions) {
    try {
      // 清理动画
      this._cleanupAnimations();
      
      // 移除受影响的方块
      this._removeAffectedBlocks();
      
      // 处理方块下落
      if (affectedCols.size > 0) {
        const hasFallen = this._applyGravityToColumns(affectedCols, removedPositions);
        
        // 如果有方块下落，等待下落完成后再恢复游戏状态
        if (hasFallen) {
          setTimeout(() => {
            this.emit('check:matches');
            this._restoreGameState();
          }, 300);
        } else {
          this.emit('check:matches');
          this._restoreGameState();
        }
      } else {
        this.emit('check:matches');
        this._restoreGameState();
      }
    } catch (error) {
      console.error('完成道具效果时出错:', error);
      this.emit('check:matches');
      this._restoreGameState();
    }
  }
  
  /**
   * 清理动画
   * @private
   */
  _cleanupAnimations() {
    if (this.grid && this.grid.animations) {
      // 筛选出闪电相关的动画并移除
      for (let i = this.grid.animations.length - 1; i >= 0; i--) {
        const anim = this.grid.animations[i];
        if (anim && (anim.type === 'lightning' || anim.type === 'lightning-hit')) {
          this.grid.animations.splice(i, 1);
        }
      }
    }
  }
  
  /**
   * 移除所有受影响的方块
   * @private
   */
  _removeAffectedBlocks() {
    for (const block of this.affectedBlocks) {
      if (block && block.row >= 0 && block.col >= 0) {
        // 移除网格中的方块
        this.grid.removeBlock(block.row, block.col);
      }
    }
    
    // 清空受影响的方块集合
    this.affectedBlocks.clear();
  }
  
  /**
   * 对受影响的列应用重力
   * @param {Set} affectedCols - 受影响的列
   * @param {Array} removedPositions - 被移除的位置
   * @returns {boolean} 是否有方块下落
   * @private
   */
  _applyGravityToColumns(affectedCols, removedPositions) {
    console.log('触发方块下落，影响列：', Array.from(affectedCols));
    
    // 统一使用网格的重力系统
    if (this.grid && typeof this.grid.applyGravity === 'function') {
      const hasFallen = this.grid.applyGravity(
        Array.from(affectedCols),  // 受影响的列
        null,                      // 不需要特别指定要检查的方块
        removedPositions           // 被移除的方块位置
      );
      console.log('网格重力应用结果:', hasFallen);
      return hasFallen;
    } else {
      console.warn('网格重力方法不可用');
      return false;
    }
  }
  

  
  /**
   * 恢复游戏状态
   * @private
   */
  _restoreGameState() {
    // 如果没有保存的游戏状态，直接返回
    if (!this.savedGameState) {
      return;
    }

    // 获取游戏控制器
    const gameController = this._getDependency('getGameController');
    if (!gameController) {
      console.warn('无法恢复游戏状态：找不到游戏控制器');
      this._clearSavedState();
      return;
    }
    
    try {
      // 恢复状态
      gameController.state = this.savedGameState.previousState || this.savedGameState.state;
      
      // 恢复下落方块
      this._restoreTetromino(gameController);
      
      // 恢复计时器状态
      if (this.savedGameState.fallTimer) {
        gameController.fallTimer = this.savedGameState.fallTimer;
      }
      
      if (this.savedGameState.lockTimer) {
        gameController.lockTimer = this.savedGameState.lockTimer;
      }
      
      // 重置游戏速度
      if (gameController.level && typeof gameController.level === 'number') {
        gameController.updateSpeed();
      }
      
      // 确保游戏继续运行
      if (gameController.state === 'paused' && this.savedGameState.previousState === 'playing') {
        gameController.state = 'playing';
      }
    } catch (error) {
      console.error('恢复游戏状态失败:', error);
    }
    
    // 清除保存的状态
    this._clearSavedState();
  }
  
  /**
   * 恢复Tetromino
   * @param {Object} gameController - 游戏控制器
   * @private
   */
  _restoreTetromino(gameController) {
    // 如果没有保存的状态，跳过
    if (!this.savedGameState || !this.savedGameState.currentTetromino) {
      return;
    }
    
    // 检查是否有活动的Tetromino
    if (gameController.currentTetromino) {
      // 当前的Tetromino和保存的是同一个，不需要重新创建
      if (this.savedTetromino === gameController.currentTetromino) {
        // 恢复位置和旋转状态
        gameController.currentTetromino.position = { ...this.savedGameState.currentTetromino.position };
        gameController.currentTetromino.rotation = this.savedGameState.currentTetromino.rotation;
        
        // 检查updateBlockPositions方法是否存在
        if (typeof gameController.currentTetromino.updateBlockPositions === 'function') {
          gameController.currentTetromino.updateBlockPositions();
        } else if (typeof gameController.currentTetromino.update === 'function') {
          // 尝试调用update方法作为替代
          gameController.currentTetromino.update();
        } else {
          // 手动更新方块位置
          this._manuallyUpdateBlockPositions(gameController.currentTetromino);
        }
      } else {
        // 保存的Tetromino已经被替换，需要重新创建
        this._recreateTetromino(gameController);
      }
    } else {
      // 没有活动的Tetromino，需要重新创建
      this._recreateTetromino(gameController);
    }
  }
  
  /**
   * 手动更新方块位置
   * @param {Object} tetromino - Tetromino对象
   * @private
   */
  _manuallyUpdateBlockPositions(tetromino) {
    if (!tetromino || !tetromino.blocks || !tetromino.shape) {
      console.log('无法更新方块位置：无效的Tetromino对象');
      return;
    }
    
    // 确保有位置信息，如果没有则使用默认值
    const position = tetromino.position || { row: 0, col: 0 };
    
    try {
      // 根据形状和位置手动更新方块位置
      const shape = tetromino.shape;
      const rotation = tetromino.rotation || 0;
      
      // 获取旋转后的形状数据
      let shapeData;
      if (typeof shape === 'string' && GameGlobal.TETROMINO_SHAPES && GameGlobal.TETROMINO_SHAPES[shape]) {
        // 如果shape是字符串且存在全局形状定义，使用它
        shapeData = GameGlobal.TETROMINO_SHAPES[shape];
      } else if (typeof shape.getRotatedShape === 'function') {
        // 如果形状对象有获取旋转形状的方法，使用它
        shapeData = shape.getRotatedShape(rotation);
      } else if (Array.isArray(shape)) {
        // 如果形状本身是数组，直接使用
        shapeData = shape;
      } else if (typeof shape === 'object' && shape !== null) {
        // 如果形状是对象，可能包含形状信息
        shapeData = shape.blocks || shape.pattern || shape.data || [];
      } else {
        // 备用方案：尝试直接使用tetromino的getBlockPositions方法
        if (typeof tetromino.getBlockPositions === 'function') {
          try {
            const positions = tetromino.getBlockPositions();
            positions.forEach(pos => {
              if (pos.block) {
                pos.block.row = pos.row;
                pos.block.col = pos.col;
                pos.block.isMoving = true;
              }
            });
            return; // 使用getBlockPositions成功，直接返回
          } catch (e) {
            console.error('使用getBlockPositions方法失败:', e);
          }
        }
        
        // 如果以上所有方法都失败，尝试简单方案
        shapeData = [];
        for (let i = 0; i < tetromino.blocks.length; i++) {
          shapeData.push([i % 2, Math.floor(i / 2)]); // 创建一个简单的2x2形状
        }
      }
      
      // 为每个方块设置位置
      if (Array.isArray(tetromino.blocks) && (Array.isArray(shapeData) || typeof shapeData === 'object')) {
        tetromino.blocks.forEach((block, index) => {
          if (!block) return;
          
          let shapePoint;
          if (Array.isArray(shapeData)) {
            shapePoint = index < shapeData.length ? shapeData[index] : null;
          } else if (typeof shapeData === 'object' && shapeData[index]) {
            shapePoint = shapeData[index];
          }
          
          if (shapePoint) {
            // 设置实际网格位置
            block.row = position.row + (Array.isArray(shapePoint) ? shapePoint[0] : (shapePoint.row || 0));
            block.col = position.col + (Array.isArray(shapePoint) ? shapePoint[1] : (shapePoint.col || 0));
            
            // 标记为移动中
            block.isMoving = true;
          } else {
            // 如果没有形状点信息，使用简单的直线排列
            block.row = position.row;
            block.col = position.col + index;
            block.isMoving = true;
          }
        });
      }
    } catch (error) {
      console.error('手动更新方块位置失败:', error);
      
      // 最终的备用方案，确保方块有位置
      if (Array.isArray(tetromino.blocks)) {
        tetromino.blocks.forEach((block, index) => {
          if (block) {
            block.row = position.row;
            block.col = position.col + index;
            block.isMoving = true;
          }
        });
      }
    }
  }
  
  /**
   * 重新创建Tetromino
   * @param {Object} gameController - 游戏控制器
   * @private
   */
  _recreateTetromino(gameController) {
    try {
      // 获取Tetromino类
      const Tetromino = this._getDependency('getTetrominoClass');
      if (!Tetromino) {
        console.error('无法获取Tetromino类');
        return;
      }
      
      // 创建新的Tetromino实例
      const savedData = this.savedGameState.currentTetromino;
      
      // 如果没有保存方块形状或位置，无法重新创建
      if (!savedData.shape || !savedData.position) {
        console.error('保存的Tetromino数据不完整');
        return;
      }
      
      // 创建新的Tetromino
      const tetromino = new Tetromino(
        savedData.shape,
        savedData.position,
        savedData.rotation
      );
      
      // 如果有保存方块颜色和效果，重新设置
      if (savedData.blocks && savedData.blocks.length > 0) {
        tetromino.blocks.forEach((block, index) => {
          if (index < savedData.blocks.length) {
            const savedBlock = savedData.blocks[index];
            if (savedBlock.color) {
              block.color = savedBlock.color;
            }
            if (savedBlock.effect) {
              block.effect = savedBlock.effect;
            }
          }
        });
      }
      
      // 设置为当前活动的Tetromino
      gameController.currentTetromino = tetromino;
      
      // 确保位置更新
      if (typeof tetromino.updateBlockPositions === 'function') {
        tetromino.updateBlockPositions();
      } else if (typeof tetromino.update === 'function') {
        tetromino.update();
      } else {
        this._manuallyUpdateBlockPositions(tetromino);
      }
    } catch (error) {
      console.error('重新创建Tetromino失败:', error);
    }
  }
  
  /**
   * 清除保存的状态
   * @private
   */
  _clearSavedState() {
    this.savedGameState = null;
    this.savedTetromino = null;
  }
  
  /**
   * 保存当前游戏状态
   * @private
   */
  _saveGameState() {
    // 获取游戏控制器
    const gameController = this._getDependency('getGameController');
    if (!gameController || !gameController.currentTetromino) {
      return;
    }
    
    console.log('保存当前游戏状态以便道具使用后恢复');
    
    // 保存当前Tetromino信息
    this.savedGameState = {
      currentTetromino: {
        shape: gameController.currentTetromino.shape,
        position: { ...gameController.currentTetromino.position },
        rotation: gameController.currentTetromino.rotation,
        blocks: gameController.currentTetromino.blocks.map(block => ({
          row: block.row,
          col: block.col,
          color: block.color,
          effect: block.effect
        }))
      },
      fallTimer: gameController.fallTimer,
      lockTimer: gameController.lockTimer,
      state: gameController.state
    };
    
    // 暂停游戏逻辑，但不清除当前方块
    this.savedGameState.previousState = gameController.state;
    gameController.state = 'paused';
    
    // 保存当前渲染中的方块引用
    this.savedTetromino = gameController.currentTetromino;
  }
  
  /**
   * 渲染道具效果和冷却
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!ctx) return;
    
    try {
      // 渲染爆炸效果
      this._renderExplosionEffect(ctx);
      
      // 渲染闪电效果
      this._renderLightningEffect(ctx);
      
      // 渲染激流效果
      this._renderWaterflowEffect(ctx);
      
      // 渲染地震术效果
      this._renderEarthquakeEffect(ctx);
    } catch (e) {
      console.error('渲染道具效果失败:', e);
    }
  }
  
  /**
   * 创建闪电效果
   * @param {Array} chainPath - 闪电链路径
   * @param {string} targetColor - 目标颜色
   * @param {number} level - 闪电等级
   * @returns {Object} 闪电效果对象
   * @private
   */
  _createLightningEffect(chainPath, targetColor = 'blue', level = 1) {
    // 获取单元格大小
    const cellSize = this.grid.blockSize || 30;
    
    // 获取网格的边界
    const gridBounds = this._getGridBounds(cellSize);
    
    // 将网格坐标转换为屏幕坐标
    const screenPath = this._convertPathToScreenCoordinates(chainPath, cellSize);
    
    // 用于控制闪电的曲折度
    const baseZigzagFactor = 10;
    
    // 根据等级增加曲折度和复杂性
    const levelZigzagBonus = level * 2;
    const zigzagFactor = baseZigzagFactor + levelZigzagBonus;
    
    // 生成闪电的曲折路径
    const zigzagPath = this._generateLightningZigzag(screenPath, zigzagFactor, gridBounds);
    
    // 为高等级添加分支
    const branches = [];
    if (level > 1) {
      // 为每个连接点创建分支的概率
      const branchProbability = level === 2 ? 0.4 : 0.7;
      
      // 遍历路径，在每个点有一定概率创建分支
      for (let i = 1; i < zigzagPath.length; i++) {
        if (Math.random() < branchProbability) {
          const midX = (zigzagPath[i].x + zigzagPath[i-1].x) / 2;
          const midY = (zigzagPath[i].y + zigzagPath[i-1].y) / 2;
          
          // 创建一个分支
          const branch = this._createLightningBranch(midX, midY, zigzagFactor * 0.8, gridBounds);
          if (branch.length > 0) {
            branches.push(branch);
          }
        }
      }
    }
    
    // 获取闪电颜色
    let lightningColor;
    
    // 根据方块颜色选择闪电颜色
    switch (targetColor) {
      case BLOCK_COLORS.RED:
        lightningColor = '#FF5555';
        break;
      case BLOCK_COLORS.GREEN:
        lightningColor = '#55FF55';
        break;
      case BLOCK_COLORS.BLUE:
        lightningColor = '#5555FF';
        break;
      case BLOCK_COLORS.YELLOW:
        lightningColor = '#FFFF55';
        break;
      case BLOCK_COLORS.ORANGE:
        lightningColor = '#FF8855';
        break;
      case BLOCK_COLORS.GRAY:
        lightningColor = '#AAAAAA';
        break;
      case BLOCK_COLORS.BLACK:
        lightningColor = '#666666';
        break;
      default:
        lightningColor = '#55AAFF'; // 默认蓝色调
    }
    
    // 创建闪电效果对象
    return {
      path: zigzagPath,
      branches: branches,
      startTimestamp: Date.now(),
      duration: 400 + level * 50, // 持续时间随等级增加
      color: lightningColor,
      level: level,
      // 闪电粒子
      particles: level > 1 ? this._createLightningParticles(zigzagPath, 5 + level * 3) : []
    };
  }
  
  /**
   * 创建闪电粒子
   * @param {Array} path - 闪电路径
   * @param {number} count - 粒子数量
   * @returns {Array} 粒子数组
   * @private
   */
  _createLightningParticles(path, count) {
    const particles = [];
    
    // 如果路径过短则不创建粒子
    if (path.length < 2) return particles;
    
    // 为路径上的随机点创建粒子
    for (let i = 0; i < count; i++) {
      // 选择路径上的随机位置
      const pathIndex = Math.floor(Math.random() * (path.length - 1)) + 1;
      const point = path[pathIndex];
      
      // 创建粒子
      particles.push({
        x: point.x + (Math.random() * 20 - 10),
        y: point.y + (Math.random() * 20 - 10),
        size: 2 + Math.random() * 4,
        speed: 0.5 + Math.random() * 1.5,
        lifetime: 0,
        maxLifetime: 20 + Math.random() * 30
      });
    }
    
    return particles;
  }
  
  /**
   * 渲染闪电效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderLightningEffect(ctx) {
    if (!ctx) return;
    
    const effect = this.animations.lightningEffect;
    if (!effect) return;
    
    // 计算流逝时间进度
    const now = Date.now();
    const elapsed = now - effect.startTimestamp;
    const progress = Math.min(1, elapsed / effect.duration);
    
    // 如果动画结束，清除效果
    if (progress >= 1) {
      this.animations.lightningEffect = null;
      return;
    }
    
    // 计算闪电透明度波动，使闪电闪烁
    const flashFrequency = 15; // 闪烁频率
    const flashProgress = (elapsed % flashFrequency) / flashFrequency;
    const alphaVariation = Math.sin(flashProgress * Math.PI) * 0.2;
    
    // 闪电基础透明度，随时间减少
    const baseAlpha = Math.max(0, 0.9 - progress * 0.9);
    const alpha = baseAlpha + alphaVariation;
    
    // 渲染主闪电路径
    this._renderLightningPaths(ctx, effect.path, alpha, 3 + effect.level, effect.color);
    
    // 渲染分支
    if (effect.branches && effect.branches.length > 0) {
      for (const branch of effect.branches) {
        // 分支的宽度和透明度都小于主路径
        this._renderLightningPaths(ctx, branch, alpha * 0.8, 2, effect.color);
      }
    }
    
    // 渲染光晕
    this._renderLightningGlows(ctx, effect.path, alpha * 0.6, effect.color);
    
    // 渲染粒子
    if (effect.particles && effect.particles.length > 0) {
      this._renderLightningParticles(ctx, effect.particles, alpha, effect.color);
      
      // 更新粒子位置
      this._updateLightningParticles(effect.particles);
    }
  }
  
  /**
   * 渲染闪电路径
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} path - 路径点
   * @param {number} alpha - 透明度
   * @param {number} width - 线宽
   * @param {string} color - 颜色
   * @private
   */
  _renderLightningPaths(ctx, path, alpha, width, color) {
    if (!path || path.length < 2) return;
    
    // 保存上下文
    ctx.save();
    
    // 设置线条样式
    ctx.strokeStyle = color || '#55AAFF';
    ctx.lineWidth = width || 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.globalAlpha = alpha;
    
    // 绘制路径
    ctx.beginPath();
    ctx.moveTo(path[0].x, path[0].y);
    
    for (let i = 1; i < path.length; i++) {
      ctx.lineTo(path[i].x, path[i].y);
    }
    
    ctx.stroke();
    
    // 恢复上下文
    ctx.restore();
  }
  
  /**
   * 渲染闪电光晕
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} path - 路径点
   * @param {number} alpha - 透明度
   * @param {string} color - 颜色
   * @private
   */
  _renderLightningGlows(ctx, path, alpha, color) {
    if (!path || path.length < 2) return;
    
    // 保存上下文
    ctx.save();
    
    // 绘制光晕函数
    const drawGlow = (x, y, size = 6) => {
      // 创建径向渐变
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, size);
      gradient.addColorStop(0, `${color}CC`); // 半透明中心
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)'); // 透明边缘
      
      // 绘制光晕圆形
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();
    };
    
    // 设置合成模式和透明度
    ctx.globalCompositeOperation = 'lighter';
    ctx.globalAlpha = alpha * 0.7;
    
    // 在路径的每个点绘制光晕
    for (let i = 0; i < path.length; i++) {
      // 随机光晕大小
      const glowSize = 3 + Math.random() * 6;
      drawGlow(path[i].x, path[i].y, glowSize);
    }
    
    // 恢复上下文
    ctx.restore();
  }
  
  /**
   * 渲染闪电粒子
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} particles - 粒子数组
   * @param {number} alpha - 透明度
   * @param {string} color - 颜色
   * @private
   */
  _renderLightningParticles(ctx, particles, alpha, color) {
    // 保存上下文
    ctx.save();
    
    // 设置合成模式和透明度
    ctx.globalCompositeOperation = 'lighter';
    ctx.globalAlpha = alpha * 0.8;
    
    // 遍历所有粒子
    for (const particle of particles) {
      // 如果粒子寿命超过最大寿命，则跳过
      if (particle.lifetime >= particle.maxLifetime) continue;
      
      // 计算粒子透明度
      const particleAlpha = 1 - (particle.lifetime / particle.maxLifetime);
      
      // 创建径向渐变
      const gradient = ctx.createRadialGradient(
        particle.x, particle.y, 0,
        particle.x, particle.y, particle.size
      );
      gradient.addColorStop(0, `${color}FF`);
      gradient.addColorStop(1, `${color}00`);
      
      // 绘制粒子
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      ctx.fill();
    }
    
    // 恢复上下文
    ctx.restore();
  }
  
  /**
   * 更新闪电粒子
   * @param {Array} particles - 粒子数组
   * @private
   */
  _updateLightningParticles(particles) {
    for (const particle of particles) {
      // 更新粒子生命周期
      particle.lifetime++;
      
      // 随机移动粒子
      particle.x += Math.random() * particle.speed * 2 - particle.speed;
      particle.y += Math.random() * particle.speed * 2 - particle.speed;
      
      // 随机改变粒子大小
      particle.size *= 0.98;
    }
  }
  
  /**
   * 生成闪电的曲折路径
   * @param {Array} screenPath - 屏幕坐标路径
   * @param {number} zigzagFactor - 曲折因子
   * @param {Object} gridBounds - 网格边界
   * @returns {Array} 曲折路径
   * @private
   */
  _generateLightningZigzag(screenPath, zigzagFactor, gridBounds) {
    // 如果路径太短，返回原始路径
    if (screenPath.length < 2) return screenPath;
    
    // 结果路径
    const result = [];
    
    // 添加第一个点
    result.push({ ...screenPath[0] });
    
    // 处理每个线段
    for (let i = 1; i < screenPath.length; i++) {
      const startPoint = screenPath[i - 1];
      const endPoint = screenPath[i];
      
      // 计算线段的中点
      const midX = (startPoint.x + endPoint.x) / 2;
      const midY = (startPoint.y + endPoint.y) / 2;
      
      // 计算垂直于线段的方向向量
      const dx = endPoint.x - startPoint.x;
      const dy = endPoint.y - startPoint.y;
      
      // 线段长度
      const length = Math.sqrt(dx * dx + dy * dy);
      
      // 如果线段太短，则不添加曲折
      if (length < 10) {
        result.push({ ...endPoint });
        continue;
      }
      
      // 标准化向量
      const normalizedX = dx / length;
      const normalizedY = dy / length;
      
      // 垂直向量
      const perpX = -normalizedY;
      const perpY = normalizedX;
      
      // 随机偏移量，基于线段长度和曲折因子
      const offset = ((Math.random() * 2) - 1) * zigzagFactor * Math.min(length / 100, 1);
      
      // 计算扭曲点
      const zigPointX = midX + perpX * offset;
      const zigPointY = midY + perpY * offset;
      
      // 确保点在网格内
      const boundedX = Math.max(gridBounds.minX, Math.min(gridBounds.maxX, zigPointX));
      const boundedY = Math.max(gridBounds.minY, Math.min(gridBounds.maxY, zigPointY));
      
      // 添加曲折点
      result.push({ x: boundedX, y: boundedY });
      
      // 添加终点
      result.push({ ...endPoint });
      
      // 有概率在长线段中添加额外的曲折点
      if (length > 50 && Math.random() < 0.4) {
        // 在起点和中点之间添加额外的曲折点
        const extraX = (startPoint.x + midX) / 2;
        const extraY = (startPoint.y + midY) / 2;
        
        // 添加随机偏移
        const extraOffset = ((Math.random() * 2) - 1) * zigzagFactor * 0.7;
        const extraZigX = extraX + perpX * extraOffset;
        const extraZigY = extraY + perpY * extraOffset;
        
        // 在适当的位置插入额外点
        result.splice(result.length - 2, 0, { 
          x: Math.max(gridBounds.minX, Math.min(gridBounds.maxX, extraZigX)), 
          y: Math.max(gridBounds.minY, Math.min(gridBounds.maxY, extraZigY)) 
        });
      }
    }
    
    return result;
  }
  
  /**
   * 创建闪电分支
   * @param {number} midX - 中点X坐标
   * @param {number} midY - 中点Y坐标
   * @param {number} zigzagFactor - 曲折因子
   * @param {Object} gridBounds - 网格边界
   * @returns {Array} 分支路径
   * @private
   */
  _createLightningBranch(midX, midY, zigzagFactor, gridBounds) {
    // 分支的点数
    const pointCount = 2 + Math.floor(Math.random() * 3);
    
    // 分支的起点
    const branch = [{ x: midX, y: midY }];
    
    // 随机方向角度（弧度）
    const angle = Math.random() * Math.PI * 2;
    
    // 初始位置和方向
    let x = midX;
    let y = midY;
    let dirX = Math.cos(angle);
    let dirY = Math.sin(angle);
    
    // 生成分支的点
    for (let i = 0; i < pointCount; i++) {
      // 每个段的长度
      const segmentLength = 10 + Math.random() * 30;
      
      // 随机改变方向
      dirX += (Math.random() - 0.5) * 0.5;
      dirY += (Math.random() - 0.5) * 0.5;
      
      // 标准化方向
      const norm = Math.sqrt(dirX * dirX + dirY * dirY);
      dirX /= norm;
      dirY /= norm;
      
      // 计算新点位置
      x += dirX * segmentLength;
      y += dirY * segmentLength;
      
      // 确保点在网格内
      const boundedX = Math.max(gridBounds.minX, Math.min(gridBounds.maxX, x));
      const boundedY = Math.max(gridBounds.minY, Math.min(gridBounds.maxY, y));
      
      // 添加点
      branch.push({ x: boundedX, y: boundedY });
    }
    
    return branch;
  }
  
  /**
   * 升级道具
   * @param {string} itemType - 道具类型
   * @returns {boolean} 是否成功升级
   */
  upgradeItem(itemType) {
    // 获取当前等级和最大等级
    const currentLevel = this.itemLevels[itemType];
    const maxLevel = this.itemEffects[itemType].range?.length || 
                     this.itemEffects[itemType].chainCount?.length || 
                     this.itemEffects[itemType].rowCount?.length || 3;
    
    // 检查是否已达到最大等级
    if (currentLevel >= maxLevel) {
      console.log(`道具${itemType}已达到最大等级${maxLevel}`);
      return false;
    }
    
    // 升级道具
    this.itemLevels[itemType]++;
    console.log(`道具${itemType}升级到${this.itemLevels[itemType]}级`);
    
    // 触发道具更新事件
    this.emit('item:upgrade', { 
      type: itemType, 
      level: this.itemLevels[itemType] 
    });
    
    return true;
  }
  
  /**
   * 检查道具是否准备好（冷却完成）
   * @param {string} itemType - 道具类型
   * @returns {boolean} 是否冷却完成
   */
  isItemReady(itemType) {
    const { isDebugMode, getConfig } = this._getDebugConfig();
    
    // 检查冷却状态（调试模式下跳过）
    const cooldownReady = (isDebugMode() && getConfig('debug.noCooldown')) || 
                         this.cooldowns[itemType] <= 0;
    
    // 检查使用次数（调试模式下跳过）
    const hasUses = (isDebugMode() && getConfig('debug.infiniteItems')) || 
                    this.itemUses[itemType] > 0;
    
    return cooldownReady && hasUses;
  }
  
  /**
   * 获取道具冷却进度
   * @param {string} itemType - 道具类型
   * @returns {number} 冷却进度（0-1）
   */
  getItemCooldownProgress(itemType) {
    if (this.cooldowns[itemType] <= 0) return 1;
    return 1 - (this.cooldowns[itemType] / this.maxCooldowns[itemType]);
  }
  
  /**
   * 获取道具的剩余使用次数
   * @param {string} itemType - 道具类型
   * @returns {number} 剩余使用次数
   */
  getItemUses(itemType) {
    const { isDebugMode, getConfig } = this._getDebugConfig();
    
    // 调试模式下返回无限次数
    if (isDebugMode() && getConfig('debug.infiniteItems')) {
      return 999; // 显示为很大的数字
    }
    
    return this.itemUses[itemType] || 0;
  }
  
  /**
   * 获取道具的所有信息
   * @param {string} itemType - 道具类型
   * @returns {Object} 道具信息对象
   */
  getItemInfo(itemType) {
    const { isDebugMode, getConfig } = this._getDebugConfig();
    
    return {
      type: itemType,
      level: this.itemLevels[itemType],
      uses: this.getItemUses(itemType), // 使用 getItemUses 获取正确的次数
      cooldown: this.cooldowns[itemType],
      maxCooldown: this.maxCooldowns[itemType],
      isReady: this.isItemReady(itemType),
      hasUses: (isDebugMode() && getConfig('debug.infiniteItems')) || this.itemUses[itemType] > 0
    };
  }
  
  /**
   * 重置道具使用次数
   * @param {string} itemType - 道具类型
   * @param {number} uses - 使用次数
   */
  resetItemUses(itemType, uses) {
    this.itemUses[itemType] = uses;
    this.emit('item:update', this.getItemInfo(itemType));
  }
  
  /**
   * 增加道具使用次数
   * @param {string} itemType - 道具类型
   * @param {number} amount - 增加的数量
   */
  addItemUses(itemType, amount = 1) {
    this.itemUses[itemType] = (this.itemUses[itemType] || 0) + amount;
    this.emit('item:update', this.getItemInfo(itemType));
  }
  
  /**
   * 检查位置是否有效
   * @param {number} row - 行
   * @param {number} col - 列
   * @returns {boolean} 是否有效
   * @private
   */
  _isValidPosition(row, col) {
    return row >= 0 && row < this.grid.rows && col >= 0 && col < this.grid.cols;
  }
  
  /**
   * 生成网格坐标的唯一键
   * @param {number} row - 行
   * @param {number} col - 列
   * @returns {string} 唯一键
   * @private
   */
  _positionKey(row, col) {
    return `${row},${col}`;
  }
  
  /**
   * 标记方块受影响
   * @param {Object} block - 方块对象
   * @private
   */
  _markBlockAffected(block) {
    if (!block) return;

    // 如果是护盾方块，先消耗护盾效果
    if (block.effect === BLOCK_EFFECTS.SHIELD) {
      const result = block.applyEffect();
      if (result.blocked) {
        console.log(`护盾方块阻挡了道具攻击: (${block.row}, ${block.col})`);
        // 创建护盾破碎动画效果
        this._createShieldBreakEffect(block);
        return; // 护盾阻挡攻击，方块不被消除
      }
    }
    // 如果是冰冻方块，先消耗冰冻效果
    else if (block.effect === BLOCK_EFFECTS.FROZEN) {
      block.applyEffect();
    }
    // 如果是水晶方块，处理额外分数
    else if (block.effect === BLOCK_EFFECTS.CRYSTAL) {
      const result = block.applyEffect();
      if (result.scoreMultiplier) {
        // 这里可以触发额外分数事件
        this._handleCrystalBonus(block, result.scoreMultiplier);
      }
    }

    // 标记为受影响，准备消除
    this.affectedBlocks.add(block);
    // 启动消除动画
    block.startDestroyAnimation();
  }
  
  /**
   * 创建动画效果
   * @param {string} type - 动画类型
   * @param {Object} params - 动画参数
   * @private
   */
  _createAnimationEffect(type, params) {
    console.log(`创建动画效果: ${type}`, params);
    
    switch (type) {
      case 'explosion':
        // 直接调用创建爆炸效果方法
        this._createExplosionEffect(
          params.centerRow, 
          params.centerCol, 
          params.range
        );
        break;
        
      case 'lightning':
        // 优先使用网格内置的闪电链动画
        if (this.grid && typeof this.grid.createLightningAnimation === 'function') {
          console.log('使用网格内置的闪电链动画');
          
          // 将等级信息传递给网格的动画系统
          const lightningLevel = params.level || this.itemLevels[ITEM_TYPES.LIGHTNING] || 1;
          
          // 将颜色信息传递给动画
          const color = params.color || 'blue';
          
          // 调用网格的闪电动画创建方法
          this.grid.createLightningAnimation(params.chainPath, {
            level: lightningLevel,
            color: color
          });
        } else {
          // 如果网格不支持，使用自己的渲染方法
          console.log('使用自定义闪电链动画');
          this.animations.lightningEffect = this._createLightningEffect(
            params.chainPath,
            params.color,
            params.level || this.itemLevels[ITEM_TYPES.LIGHTNING] || 1
          );
        }
        break;
        
      case 'earthquake':
        console.log('🌍 地震术动画效果启动');
        // 创建地震震动效果，包含影响区域高亮和震动动画
        this._createEarthquakeVisualEffect(params.affectedRows, params.level, params.duration);
        break;
        
      default:
        console.warn(`未知的动画类型: ${type}`);
    }
  }
  
  /**
   * 播放音效
   * @param {string} soundType - 音效类型
   * @private
   */
  _playSound(soundType) {
    const musicManager = this._getDependency('getMusicManager');
    if (!musicManager) return;
    
    // 使用统一的音效播放接口
    musicManager.playEffect(soundType);
  }
  
  /**
   * 获取网格边界
   * @param {number} cellSize - 单元格大小
   * @returns {Object} 网格边界
   * @private
   */
  _getGridBounds(cellSize) {
    const offsetX = this.grid.offsetX || 0;
    const offsetY = this.grid.offsetY || 0;
    
    return {
      minX: offsetX,
      minY: offsetY,
      maxX: offsetX + this.grid.cols * cellSize,
      maxY: offsetY + this.grid.rows * cellSize,
      width: this.grid.cols * cellSize,
      height: this.grid.rows * cellSize
    };
  }
  
  /**
   * 将网格路径转换为屏幕坐标
   * @param {Array} chainPath - 网格路径
   * @param {number} cellSize - 单元格大小
   * @returns {Array} 屏幕坐标路径
   * @private
   */
  _convertPathToScreenCoordinates(chainPath, cellSize) {
    const offsetX = this.grid.offsetX || 0;
    const offsetY = this.grid.offsetY || 0;
    const screenPath = [];
    
    // 遍历路径上的每个点
    for (let i = 0; i < chainPath.length; i++) {
      const point = chainPath[i];
      
      // 计算屏幕坐标
      const screenX = offsetX + point.col * cellSize + cellSize / 2;
      const screenY = offsetY + point.row * cellSize + cellSize / 2;
      
      // 创建屏幕点对象
      const screenPoint = { x: screenX, y: screenY };
      
      // 如果点有来源信息，也转换
      if (point.fromCol !== undefined && point.fromRow !== undefined) {
        const fromScreenX = offsetX + point.fromCol * cellSize + cellSize / 2;
        const fromScreenY = offsetY + point.fromRow * cellSize + cellSize / 2;
        screenPoint.fromX = fromScreenX;
        screenPoint.fromY = fromScreenY;
      } else if (i > 0) {
        // 对于没有明确来源的点，使用前一个点作为来源
        screenPoint.fromX = screenPath[i - 1].x;
        screenPoint.fromY = screenPath[i - 1].y;
      }
      
      screenPath.push(screenPoint);
    }
    
    return screenPath;
  }

  /**
   * 创建护盾破碎效果
   * @param {Object} block - 护盾方块
   * @private
   */
  _createShieldBreakEffect(block) {
    // 创建护盾破碎的视觉效果
    if (this.grid && this.grid.createExplosionAnimation) {
      // 使用较小的爆炸效果表示护盾破碎
      this.grid.createExplosionAnimation(block.row, block.col, 0.5);
    }

    // 播放护盾破碎音效
    if (GameGlobal.musicManager) {
      GameGlobal.musicManager.playEffect('shield_break');
    }

    console.log(`护盾破碎效果已创建: (${block.row}, ${block.col})`);
  }

  /**
   * 处理水晶奖励
   * @param {Object} block - 水晶方块
   * @param {number} multiplier - 分数倍数
   * @private
   */
  _handleCrystalBonus(block, multiplier) {
    // 计算奖励分数
    const baseScore = 100; // 基础分数
    const bonusScore = baseScore * (multiplier - 1);

    // 触发分数奖励事件
    this.emit('crystal_bonus', {
      row: block.row,
      col: block.col,
      bonusScore: bonusScore,
      multiplier: multiplier
    });

    // 创建分数粒子效果
    this._createScoreParticles(block, bonusScore);

    // 播放水晶音效
    if (GameGlobal.musicManager) {
      GameGlobal.musicManager.playEffect('crystal');
    }

    console.log(`水晶奖励: ${bonusScore}分 (${multiplier}倍)`);
  }

  /**
   * 创建分数粒子效果
   * @param {Object} block - 方块
   * @param {number} score - 分数
   * @private
   */
  _createScoreParticles(block, score) {
    // 这里可以创建分数粒子飞向分数显示区域的动画
    // 暂时只输出日志
    console.log(`创建分数粒子效果: +${score} 从 (${block.row}, ${block.col})`);
  }

  /**
   * 创建地震术视觉效果
   * @param {number} affectedRows - 影响行数
   * @param {number} level - 地震等级
   * @param {number} duration - 动画持续时间
   * @private
   */
  _createEarthquakeVisualEffect(affectedRows, level, duration) {
    console.log(`创建地震术视觉效果，影响${affectedRows}行，等级${level}，持续${duration}ms`);
    
    // 计算影响区域
    const startRow = Math.max(0, this.grid.rows - affectedRows);
    const endRow = this.grid.rows - 1;
    
    // 创建地震动画效果对象
    this.animations.earthquakeEffect = {
      startTime: Date.now(),
      duration: duration,
      level: level,
      affectedRows: affectedRows,
      startRow: startRow,
      endRow: endRow,
      // 震动参数
      shakeIntensity: 5 + level * 3, // 震动强度随等级增加
      shakeFrequency: 0.2, // 震动频率
      // 高亮效果
      highlightAlpha: 0,
      highlightDirection: 1,
      // 波动效果
      wavePhase: 0,
      waveAmplitude: 10 + level * 5,
      // 粒子效果
      particles: this._createEarthquakeParticles(startRow, endRow, level)
    };
    
    console.log(`地震术效果区域: 第${startRow}行到第${endRow}行`);
  }

  /**
   * 创建地震术粒子效果
   * @param {number} startRow - 开始行
   * @param {number} endRow - 结束行  
   * @param {number} level - 地震等级
   * @returns {Array} 粒子数组
   * @private
   */
  _createEarthquakeParticles(startRow, endRow, level) {
    const particles = [];
    const particleCount = 20 + level * 10; // 粒子数量随等级增加
    
    const cellSize = this.grid.blockSize || 30;
    const offsetX = this.grid.offsetX || 0;
    const offsetY = this.grid.offsetY || 0;
    
    for (let i = 0; i < particleCount; i++) {
      // 在影响区域内随机分布粒子
      const row = startRow + Math.random() * (endRow - startRow + 1);
      const col = Math.random() * this.grid.cols;
      
      particles.push({
        x: offsetX + col * cellSize + Math.random() * cellSize,
        y: offsetY + row * cellSize + Math.random() * cellSize,
        vx: (Math.random() - 0.5) * 4,
        vy: (Math.random() - 0.5) * 4,
        size: 2 + Math.random() * 4,
        life: 1.0,
        decay: 0.02 + Math.random() * 0.02,
        color: `hsl(${30 + Math.random() * 60}, 70%, 50%)` // 土黄色系
      });
    }
    
    return particles;
  }
}