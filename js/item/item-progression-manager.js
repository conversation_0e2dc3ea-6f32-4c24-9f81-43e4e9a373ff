/**
 * 道具进度管理器
 * 负责道具解锁、升级、获取和平衡性管理
 */
import Emitter from '../libs/tinyemitter.js';
import { ITEM_TYPES } from './refactored-item-manager.js';

// 道具解锁条件类型
export const UNLOCK_CONDITIONS = {
  LEVEL: 'level',           // 关卡解锁
  SCORE: 'score',          // 分数解锁
  ACHIEVEMENT: 'achievement', // 成就解锁
  TIME_PLAYED: 'time_played', // 游戏时长解锁
  ITEMS_USED: 'items_used'   // 道具使用次数解锁
};

// 道具获取方式
export const ACQUISITION_METHODS = {
  LEVEL_REWARD: 'level_reward',     // 关卡奖励
  DAILY_TASK: 'daily_task',         // 每日任务
  ACHIEVEMENT: 'achievement',        // 成就奖励
  AD_REWARD: 'ad_reward',           // 广告奖励
  SOCIAL_SHARE: 'social_share',     // 社交分享
  PURCHASE: 'purchase',             // 购买
  LOGIN_BONUS: 'login_bonus',       // 登录奖励
  LEVEL_UP: 'level_up'             // 升级奖励
};

// 道具配置数据
export const ITEM_PROGRESSION_CONFIG = {
  [ITEM_TYPES.FIREBALL]: {
    name: '火球术',
    description: '发射火球消除范围内的方块',
    unlockConditions: [
      { type: UNLOCK_CONDITIONS.LEVEL, value: 1 }
    ],
    maxLevel: 5,
    upgradeRequirements: [
      { level: 2, skillPoints: 10, coins: 500 },
      { level: 3, skillPoints: 20, coins: 1000 },
      { level: 4, skillPoints: 35, coins: 2000 },
      { level: 5, skillPoints: 50, coins: 3500 }
    ],
    levelEffects: {
      1: { range: 1, damage: 1.0, cooldown: 180 },
      2: { range: 1.5, damage: 1.2, cooldown: 170 },
      3: { range: 2, damage: 1.5, cooldown: 160 },
      4: { range: 2.5, damage: 1.8, cooldown: 150 },
      5: { range: 3, damage: 2.0, cooldown: 140 }
    },
    acquisitionMethods: {
      [ACQUISITION_METHODS.LEVEL_REWARD]: { baseAmount: 3, scaleFactor: 0.5 },
      [ACQUISITION_METHODS.DAILY_TASK]: { baseAmount: 2, probability: 0.6 },
      [ACQUISITION_METHODS.AD_REWARD]: { baseAmount: 5, cooldown: 1800 }, // 30分钟
      [ACQUISITION_METHODS.SOCIAL_SHARE]: { baseAmount: 3, dailyLimit: 3 }
    }
  },
  
  [ITEM_TYPES.LIGHTNING]: {
    name: '闪电链',
    description: '连锁消除相同颜色的方块',
    unlockConditions: [
      { type: UNLOCK_CONDITIONS.LEVEL, value: 5 },
      { type: UNLOCK_CONDITIONS.ITEMS_USED, itemType: ITEM_TYPES.FIREBALL, value: 10 }
    ],
    maxLevel: 5,
    upgradeRequirements: [
      { level: 2, skillPoints: 15, coins: 800 },
      { level: 3, skillPoints: 25, coins: 1500 },
      { level: 4, skillPoints: 40, coins: 2500 },
      { level: 5, skillPoints: 60, coins: 4000 }
    ],
    levelEffects: {
      1: { chainCount: 5, range: 1, cooldown: 300 },
      2: { chainCount: 7, range: 1.2, cooldown: 280 },
      3: { chainCount: 10, range: 1.5, cooldown: 260 },
      4: { chainCount: 13, range: 1.8, cooldown: 240 },
      5: { chainCount: 16, range: 2.0, cooldown: 220 }
    },
    acquisitionMethods: {
      [ACQUISITION_METHODS.LEVEL_REWARD]: { baseAmount: 2, scaleFactor: 0.3 },
      [ACQUISITION_METHODS.ACHIEVEMENT]: { baseAmount: 5, specific: ['combo_master', 'chain_expert'] },
      [ACQUISITION_METHODS.AD_REWARD]: { baseAmount: 3, cooldown: 2400 }, // 40分钟
      [ACQUISITION_METHODS.SOCIAL_SHARE]: { baseAmount: 2, dailyLimit: 2 }
    }
  },
  
  [ITEM_TYPES.WATERFLOW]: {
    name: '激流',
    description: '清除底部行的所有方块',
    unlockConditions: [
      { type: UNLOCK_CONDITIONS.LEVEL, value: 10 },
      { type: UNLOCK_CONDITIONS.SCORE, value: 50000 }
    ],
    maxLevel: 5,
    upgradeRequirements: [
      { level: 2, skillPoints: 20, coins: 1200 },
      { level: 3, skillPoints: 35, coins: 2000 },
      { level: 4, skillPoints: 50, coins: 3000 },
      { level: 5, skillPoints: 75, coins: 5000 }
    ],
    levelEffects: {
      1: { rowCount: 1, waveStrength: 1.0, cooldown: 420 },
      2: { rowCount: 2, waveStrength: 1.3, cooldown: 400 },
      3: { rowCount: 3, waveStrength: 1.5, cooldown: 380 },
      4: { rowCount: 4, waveStrength: 1.8, cooldown: 360 },
      5: { rowCount: 6, waveStrength: 2.0, cooldown: 340 }
    },
    acquisitionMethods: {
      [ACQUISITION_METHODS.LEVEL_REWARD]: { baseAmount: 1, scaleFactor: 0.2 },
      [ACQUISITION_METHODS.DAILY_TASK]: { baseAmount: 1, probability: 0.3 },
      [ACQUISITION_METHODS.AD_REWARD]: { baseAmount: 2, cooldown: 3600 }, // 60分钟
      [ACQUISITION_METHODS.LOGIN_BONUS]: { baseAmount: 1, streakBonus: true }
    }
  },

  // 地震术配置
  [ITEM_TYPES.EARTHQUAKE]: {
    name: '地震术',
    description: '震动底部区域，让方块自由下落重新排列，填补空洞',
    unlockConditions: [
      { type: UNLOCK_CONDITIONS.LEVEL, value: 11 },
      { type: UNLOCK_CONDITIONS.SCORE, value: 60000 },
      { type: UNLOCK_CONDITIONS.ITEMS_USED, itemType: ITEM_TYPES.WATERFLOW, value: 5 }
    ],
    maxLevel: 5,
    upgradeRequirements: [
      { level: 2, skillPoints: 30, coins: 1500 },
      { level: 3, skillPoints: 50, coins: 2500 },
      { level: 4, skillPoints: 75, coins: 4000 },
      { level: 5, skillPoints: 100, coins: 6000 }
    ],
    levelEffects: {
      1: { affectedRows: 3, cooldown: 600 },
      2: { affectedRows: 4, cooldown: 570 },
      3: { affectedRows: 5, cooldown: 540 },
      4: { affectedRows: 6, cooldown: 510 },
      5: { affectedRows: 7, cooldown: 480 }
    },
    acquisitionMethods: {
      [ACQUISITION_METHODS.LEVEL_REWARD]: { baseAmount: 1, scaleFactor: 0.1 },
      [ACQUISITION_METHODS.DAILY_TASK]: { baseAmount: 1, probability: 0.2 },
      [ACQUISITION_METHODS.AD_REWARD]: { baseAmount: 1, cooldown: 7200 }, // 120分钟
      [ACQUISITION_METHODS.LOGIN_BONUS]: { baseAmount: 1, streakBonus: false }
    }
  }
};

// 技能树节点类型
export const SKILL_NODE_TYPES = {
  DAMAGE: 'damage',         // 伤害增强
  RANGE: 'range',          // 范围增强
  COOLDOWN: 'cooldown',    // 冷却减少
  EFFICIENCY: 'efficiency', // 效率提升
  SPECIAL: 'special'       // 特殊效果
};

export default class ItemProgressionManager extends Emitter {
  constructor(progressionManager, itemManager) {
    super();
    
    this.progressionManager = progressionManager;
    this.itemManager = itemManager;
    
    // 道具解锁状态
    this.unlockedItems = new Set([ITEM_TYPES.FIREBALL]); // 默认解锁火球术
    
    // 技能点数
    this.skillPoints = 0;
    
    // 道具使用统计
    this.itemUsageStats = {
      [ITEM_TYPES.FIREBALL]: 0,
      [ITEM_TYPES.LIGHTNING]: 0,
      [ITEM_TYPES.WATERFLOW]: 0,
      [ITEM_TYPES.EARTHQUAKE]: 0  // 地震术使用统计
    };
    
    // 技能树状态
    this.skillTree = this._initializeSkillTree();
    
    // 获取冷却状态
    this.acquisitionCooldowns = new Map();
    
    // 每日限制计数
    this.dailyLimits = new Map();
    
    this._loadData();
    this._checkUnlockConditions();
  }
  
  /**
   * 检查道具解锁条件
   */
  _checkUnlockConditions() {
    // 如果progressionManager未初始化，跳过检查
    if (!this.progressionManager || !this.progressionManager.playerData) {
      return;
    }

    for (const [itemType, config] of Object.entries(ITEM_PROGRESSION_CONFIG)) {
      if (this.unlockedItems.has(itemType)) continue;

      const allConditionsMet = config.unlockConditions.every(condition => {
        return this._checkCondition(condition);
      });

      if (allConditionsMet) {
        this.unlockItem(itemType);
      }
    }
  }
  
  /**
   * 检查单个解锁条件
   * @param {Object} condition - 解锁条件
   * @returns {boolean} 是否满足条件
   * @private
   */
  _checkCondition(condition) {
    // 安全检查
    if (!this.progressionManager || !this.progressionManager.playerData) {
      return false;
    }

    const playerData = this.progressionManager.playerData;

    switch (condition.type) {
      case UNLOCK_CONDITIONS.LEVEL:
        return (playerData.level || 1) >= condition.value;

      case UNLOCK_CONDITIONS.SCORE:
        return (playerData.totalScore || 0) >= condition.value;

      case UNLOCK_CONDITIONS.ITEMS_USED:
        return (this.itemUsageStats[condition.itemType] || 0) >= condition.value;

      case UNLOCK_CONDITIONS.TIME_PLAYED:
        return (playerData.totalPlayTime || 0) >= condition.value;

      case UNLOCK_CONDITIONS.ACHIEVEMENT:
        if (!this.progressionManager.achievements) return false;
        const achievement = this.progressionManager.achievements.find(a => a.id === condition.achievementId);
        return achievement && achievement.completed;

      default:
        return false;
    }
  }
  
  /**
   * 解锁道具
   * @param {string} itemType - 道具类型
   */
  unlockItem(itemType) {
    if (this.unlockedItems.has(itemType)) return;

    this.unlockedItems.add(itemType);

    // 给予初始道具数量
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    if (config && this.progressionManager && this.progressionManager.itemInventory) {
      const initialAmount = config.acquisitionMethods[ACQUISITION_METHODS.LEVEL_REWARD]?.baseAmount || 3;
      this.progressionManager.itemInventory[itemType] =
        (this.progressionManager.itemInventory[itemType] || 0) + initialAmount;
    }

    this.emit('item:unlocked', { itemType, config });
    this._saveData();
  }
  
  /**
   * 升级道具
   * @param {string} itemType - 道具类型
   * @returns {boolean} 是否成功升级
   */
  upgradeItem(itemType) {
    if (!this.unlockedItems.has(itemType)) return false;
    
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const currentLevel = this.itemManager.itemLevels[itemType] || 1;
    
    if (currentLevel >= config.maxLevel) {
      this.emit('item:max_level', { itemType });
      return false;
    }
    
    const requirement = config.upgradeRequirements.find(req => req.level === currentLevel + 1);
    if (!requirement) return false;
    
    // 检查资源是否足够
    if (this.skillPoints < requirement.skillPoints) {
      this.emit('item:insufficient_skill_points', { 
        required: requirement.skillPoints, 
        current: this.skillPoints 
      });
      return false;
    }
    
    if (this.progressionManager.playerData.coins < requirement.coins) {
      this.emit('item:insufficient_coins', { 
        required: requirement.coins, 
        current: this.progressionManager.playerData.coins 
      });
      return false;
    }
    
    // 消耗资源
    this.skillPoints -= requirement.skillPoints;
    this.progressionManager.playerData.coins -= requirement.coins;
    
    // 升级道具
    this.itemManager.itemLevels[itemType] = currentLevel + 1;
    
    // 应用新的效果
    this._applyLevelEffects(itemType, currentLevel + 1);
    
    this.emit('item:upgraded', { 
      itemType, 
      newLevel: currentLevel + 1,
      effects: config.levelEffects[currentLevel + 1]
    });
    
    this._saveData();
    return true;
  }
  
  /**
   * 应用等级效果
   * @param {string} itemType - 道具类型
   * @param {number} level - 等级
   * @private
   */
  _applyLevelEffects(itemType, level) {
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const effects = config.levelEffects[level];
    
    if (!effects) return;
    
    // 更新道具管理器中的效果数据
    if (this.itemManager.itemEffects[itemType]) {
      Object.assign(this.itemManager.itemEffects[itemType], effects);
    }
    
    // 更新冷却时间
    if (effects.cooldown && this.itemManager.maxCooldowns[itemType]) {
      this.itemManager.maxCooldowns[itemType] = effects.cooldown;
    }
  }
  
  /**
   * 获得道具
   * @param {string} itemType - 道具类型
   * @param {number} amount - 数量
   * @param {string} method - 获取方式
   * @returns {boolean} 是否成功获得
   */
  acquireItem(itemType, amount, method) {
    if (!this.unlockedItems.has(itemType)) {
      this.emit('item:not_unlocked', { itemType });
      return false;
    }
    
    // 检查获取方式的限制
    if (!this._checkAcquisitionLimits(itemType, method)) {
      return false;
    }
    
    // 添加到库存
    this.progressionManager.itemInventory[itemType] = 
      (this.progressionManager.itemInventory[itemType] || 0) + amount;
    
    // 更新获取限制
    this._updateAcquisitionLimits(itemType, method);
    
    this.emit('item:acquired', { itemType, amount, method });
    this._saveData();
    
    return true;
  }
  
  /**
   * 观看广告获得道具
   * @param {string} itemType - 道具类型
   * @returns {Promise<boolean>} 是否成功
   */
  async watchAdForItem(itemType) {
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const adConfig = config?.acquisitionMethods[ACQUISITION_METHODS.AD_REWARD];
    
    if (!adConfig) return false;
    
    // 检查冷却时间
    const cooldownKey = `ad_${itemType}`;
    const lastTime = this.acquisitionCooldowns.get(cooldownKey) || 0;
    const now = Date.now();
    
    if (now - lastTime < adConfig.cooldown * 1000) {
      const remainingTime = Math.ceil((adConfig.cooldown * 1000 - (now - lastTime)) / 1000);
      this.emit('item:ad_cooldown', { itemType, remainingTime });
      return false;
    }
    
    try {
      // 这里应该调用微信小游戏的激励视频广告API
      // const success = await this._showRewardedAd();
      const success = true; // 模拟广告观看成功
      
      if (success) {
        this.acquisitionCooldowns.set(cooldownKey, now);
        return this.acquireItem(itemType, adConfig.baseAmount, ACQUISITION_METHODS.AD_REWARD);
      }
    } catch (error) {
      console.error('广告播放失败:', error);
      this.emit('item:ad_failed', { itemType, error });
    }
    
    return false;
  }
  
  /**
   * 社交分享获得道具
   * @param {string} itemType - 道具类型
   * @returns {boolean} 是否成功
   */
  shareForItem(itemType) {
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const shareConfig = config?.acquisitionMethods[ACQUISITION_METHODS.SOCIAL_SHARE];
    
    if (!shareConfig) return false;
    
    // 检查每日限制
    const today = new Date().toDateString();
    const limitKey = `share_${itemType}_${today}`;
    const currentCount = this.dailyLimits.get(limitKey) || 0;
    
    if (currentCount >= shareConfig.dailyLimit) {
      this.emit('item:share_limit_reached', { itemType, dailyLimit: shareConfig.dailyLimit });
      return false;
    }
    
    // 这里应该调用微信小游戏的分享API
    // wx.shareAppMessage({ ... });
    
    this.dailyLimits.set(limitKey, currentCount + 1);
    return this.acquireItem(itemType, shareConfig.baseAmount, ACQUISITION_METHODS.SOCIAL_SHARE);
  }
  
  /**
   * 记录道具使用
   * @param {string} itemType - 道具类型
   */
  recordItemUsage(itemType) {
    this.itemUsageStats[itemType] = (this.itemUsageStats[itemType] || 0) + 1;
    
    // 检查是否解锁新道具
    this._checkUnlockConditions();
    
    this._saveData();
  }
  
  /**
   * 获得技能点
   * @param {number} amount - 技能点数量
   * @param {string} source - 来源
   */
  gainSkillPoints(amount, source = 'unknown') {
    this.skillPoints += amount;
    
    this.emit('skill_points:gained', { amount, source, total: this.skillPoints });
    this._saveData();
  }
  
  /**
   * 检查获取限制
   * @param {string} itemType - 道具类型
   * @param {string} method - 获取方式
   * @returns {boolean} 是否可以获取
   * @private
   */
  _checkAcquisitionLimits(itemType, method) {
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const methodConfig = config?.acquisitionMethods[method];
    
    if (!methodConfig) return false;
    
    // 检查冷却时间
    if (methodConfig.cooldown) {
      const cooldownKey = `${method}_${itemType}`;
      const lastTime = this.acquisitionCooldowns.get(cooldownKey) || 0;
      const now = Date.now();
      
      if (now - lastTime < methodConfig.cooldown * 1000) {
        return false;
      }
    }
    
    // 检查每日限制
    if (methodConfig.dailyLimit) {
      const today = new Date().toDateString();
      const limitKey = `${method}_${itemType}_${today}`;
      const currentCount = this.dailyLimits.get(limitKey) || 0;
      
      if (currentCount >= methodConfig.dailyLimit) {
        return false;
      }
    }
    
    return true;
  }
  
  /**
   * 更新获取限制
   * @param {string} itemType - 道具类型
   * @param {string} method - 获取方式
   * @private
   */
  _updateAcquisitionLimits(itemType, method) {
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const methodConfig = config?.acquisitionMethods[method];
    
    if (!methodConfig) return;
    
    const now = Date.now();
    
    // 更新冷却时间
    if (methodConfig.cooldown) {
      const cooldownKey = `${method}_${itemType}`;
      this.acquisitionCooldowns.set(cooldownKey, now);
    }
    
    // 更新每日限制
    if (methodConfig.dailyLimit) {
      const today = new Date().toDateString();
      const limitKey = `${method}_${itemType}_${today}`;
      const currentCount = this.dailyLimits.get(limitKey) || 0;
      this.dailyLimits.set(limitKey, currentCount + 1);
    }
  }
  
  /**
   * 初始化技能树
   * @returns {Object} 技能树结构
   * @private
   */
  _initializeSkillTree() {
    // 简化的技能树结构，可以根据需要扩展
    return {
      [ITEM_TYPES.FIREBALL]: {
        damage: { level: 0, maxLevel: 5, cost: [5, 10, 15, 20, 25] },
        range: { level: 0, maxLevel: 3, cost: [8, 16, 24] },
        cooldown: { level: 0, maxLevel: 3, cost: [6, 12, 18] }
      },
      [ITEM_TYPES.LIGHTNING]: {
        chainCount: { level: 0, maxLevel: 5, cost: [7, 14, 21, 28, 35] },
        range: { level: 0, maxLevel: 3, cost: [10, 20, 30] },
        efficiency: { level: 0, maxLevel: 3, cost: [8, 16, 24] }
      },
      [ITEM_TYPES.WATERFLOW]: {
        rowCount: { level: 0, maxLevel: 3, cost: [12, 24, 36] },
        waveStrength: { level: 0, maxLevel: 5, cost: [8, 16, 24, 32, 40] },
        cooldown: { level: 0, maxLevel: 3, cost: [10, 20, 30] }
      }
    };
  }
  
  /**
   * 获取道具信息
   * @param {string} itemType - 道具类型
   * @returns {Object} 道具信息
   */
  getItemInfo(itemType) {
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const currentLevel = this.itemManager?.itemLevels[itemType] || 1;
    const inventory = this.progressionManager?.itemInventory[itemType] || 0;
    
    return {
      ...config,
      unlocked: this.unlockedItems.has(itemType),
      currentLevel,
      inventory,
      usageCount: this.itemUsageStats[itemType] || 0,
      canUpgrade: this._canUpgrade(itemType),
      nextUpgradeRequirement: this._getNextUpgradeRequirement(itemType)
    };
  }
  
  /**
   * 检查是否可以升级
   * @param {string} itemType - 道具类型
   * @returns {boolean} 是否可以升级
   * @private
   */
  _canUpgrade(itemType) {
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const currentLevel = this.itemManager?.itemLevels[itemType] || 1;
    
    if (currentLevel >= config.maxLevel) return false;
    
    const requirement = config.upgradeRequirements.find(req => req.level === currentLevel + 1);
    if (!requirement) return false;
    
    return this.skillPoints >= requirement.skillPoints && 
           this.progressionManager.playerData.coins >= requirement.coins;
  }
  
  /**
   * 获取下一级升级需求
   * @param {string} itemType - 道具类型
   * @returns {Object|null} 升级需求
   * @private
   */
  _getNextUpgradeRequirement(itemType) {
    const config = ITEM_PROGRESSION_CONFIG[itemType];
    const currentLevel = this.itemManager?.itemLevels[itemType] || 1;
    
    return config.upgradeRequirements.find(req => req.level === currentLevel + 1) || null;
  }
  
  /**
   * 保存数据
   * @private
   */
  _saveData() {
    try {
      const data = {
        unlockedItems: Array.from(this.unlockedItems),
        skillPoints: this.skillPoints,
        itemUsageStats: this.itemUsageStats,
        skillTree: this.skillTree,
        acquisitionCooldowns: Array.from(this.acquisitionCooldowns.entries()),
        dailyLimits: Array.from(this.dailyLimits.entries())
      };

      // 验证数据是否可以序列化
      const jsonString = this._safeStringify(data);
      if (jsonString === null) {
        console.error('道具进度数据序列化失败，跳过保存');
        return;
      }

      wx.setStorageSync('itemProgressionData', jsonString);
      console.log('道具进度数据保存成功');
    } catch (e) {
      console.error('保存道具进度数据失败:', e);
    }
  }

  /**
   * 安全的JSON序列化
   * @param {Object} obj - 要序列化的对象
   * @returns {string|null} 序列化后的字符串，失败返回null
   * @private
   */
  _safeStringify(obj) {
    try {
      return JSON.stringify(obj, (key, value) => {
        // 过滤掉函数、Symbol、undefined等不可序列化的值
        if (typeof value === 'function' || typeof value === 'symbol' || value === undefined) {
          return null;
        }
        return value;
      });
    } catch (e) {
      console.error('JSON序列化失败:', e);
      return null;
    }
  }
  
  /**
   * 加载数据
   * @private
   */
  _loadData() {
    try {
      const data = wx.getStorageSync('itemProgressionData');
      
      if (data) {
        const parsed = JSON.parse(data);
        
        this.unlockedItems = new Set(parsed.unlockedItems || [ITEM_TYPES.FIREBALL]);
        this.skillPoints = parsed.skillPoints || 0;
        this.itemUsageStats = { ...this.itemUsageStats, ...parsed.itemUsageStats };
        this.skillTree = { ...this.skillTree, ...parsed.skillTree };
        this.acquisitionCooldowns = new Map(parsed.acquisitionCooldowns || []);
        this.dailyLimits = new Map(parsed.dailyLimits || []);
      }
    } catch (e) {
      console.error('加载道具进度数据失败:', e);
    }
  }
}
