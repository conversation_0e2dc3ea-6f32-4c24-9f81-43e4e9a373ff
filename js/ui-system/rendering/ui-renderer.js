/**
 * UI渲染引擎
 * 高性能分层渲染系统，支持脏区域检测和批量渲染
 */
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../../render.js';

export default class UIRenderer {
  constructor() {
    // 渲染层配置
    this.layers = {
      BACKGROUND: { index: 0, name: 'background', needsRedraw: true },
      CONTENT: { index: 1, name: 'content', needsRedraw: true },
      UI: { index: 2, name: 'ui', needsRedraw: true },
      OVERLAY: { index: 3, name: 'overlay', needsRedraw: true },
      DEBUG: { index: 4, name: 'debug', needsRedraw: true }
    };
    
    // 渲染缓存
    this.canvasCache = new Map();
    this.layerContexts = new Map();
    
    // 脏区域检测
    this.dirtyRegions = [];
    this.fullRedrawRequested = false;
    
    // 渲染统计
    this.renderStats = {
      frameCount: 0,
      averageFPS: 60,
      renderTime: 0,
      lastFrameTime: 0,
      dirtyRegionCount: 0,
      cachedElementsCount: 0
    };
    
    // 字体缓存
    this.fontCache = new Map();
    this.currentFont = null;
    
    // 渲染配置
    this.config = {
      enableDirtyRegions: true,
      enableLayerCaching: true,
      maxDirtyRegions: 50,
      debugMode: false,
      antialias: true,
      pixelRatio: (typeof window !== 'undefined' ? window.devicePixelRatio : null) || 1
    };
    
    // 初始化渲染层
    this._initializeLayers();
    
    // 性能监控
    this.lastFrameStart = performance.now();
    this.frameTimes = [];
    this.maxFrameTimes = 60;
  }

  /**
   * 初始化渲染层
   * @private
   */
  _initializeLayers() {
    for (const layerName in this.layers) {
      const layer = this.layers[layerName];
      
      // 创建离屏canvas
      let canvas, ctx;
      
      if (typeof document !== 'undefined') {
        // 浏览器环境
        canvas = document.createElement('canvas');
        canvas.width = SCREEN_WIDTH * this.config.pixelRatio;
        canvas.height = SCREEN_HEIGHT * this.config.pixelRatio;
        
        ctx = canvas.getContext('2d', {
          alpha: true,
          antialias: this.config.antialias
        });
        
        // 设置缩放
        ctx.scale(this.config.pixelRatio, this.config.pixelRatio);
      } else {
        // Node.js测试环境 - 创建虚拟canvas和context
        canvas = {
          width: SCREEN_WIDTH * this.config.pixelRatio,
          height: SCREEN_HEIGHT * this.config.pixelRatio
        };
        
        ctx = {
          clearRect: () => {},
          fillRect: () => {},
          drawImage: () => {},
          fillText: () => {},
          save: () => {},
          restore: () => {},
          translate: () => {},
          scale: () => {},
          rotate: () => {},
          beginPath: () => {},
          closePath: () => {},
          moveTo: () => {},
          lineTo: () => {},
          quadraticCurveTo: () => {},
          bezierCurveTo: () => {},
          arc: () => {},
          arcTo: () => {},
          rect: () => {},
          fill: () => {},
          stroke: () => {},
          clip: () => {},
          measureText: (text) => ({ width: text.length * 10 }),
          canvas: canvas
        };
      }
      
      this.canvasCache.set(layer.name, canvas);
      this.layerContexts.set(layer.name, ctx);
    }
  }

  /**
   * 标记区域为脏区域
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {string} layer - 图层名称
   */
  markDirty(x, y, width, height, layer = 'content') {
    if (!this.config.enableDirtyRegions) {
      this.requestFullRedraw();
      return;
    }
    
    const dirtyRegion = {
      x: Math.max(0, Math.floor(x)),
      y: Math.max(0, Math.floor(y)),
      width: Math.min(SCREEN_WIDTH, Math.ceil(width)),
      height: Math.min(SCREEN_HEIGHT, Math.ceil(height)),
      layer: layer,
      timestamp: performance.now()
    };
    
    // 合并重叠的脏区域
    this._mergeDirtyRegions(dirtyRegion);
    
    // 标记图层需要重绘
    if (this.layers[layer.toUpperCase()]) {
      this.layers[layer.toUpperCase()].needsRedraw = true;
    }
  }

  /**
   * 合并脏区域
   * @param {Object} newRegion - 新的脏区域
   * @private
   */
  _mergeDirtyRegions(newRegion) {
    let merged = false;
    
    for (let i = 0; i < this.dirtyRegions.length; i++) {
      const existing = this.dirtyRegions[i];
      
      if (existing.layer === newRegion.layer && this._regionsOverlap(existing, newRegion)) {
        // 合并区域
        const left = Math.min(existing.x, newRegion.x);
        const top = Math.min(existing.y, newRegion.y);
        const right = Math.max(existing.x + existing.width, newRegion.x + newRegion.width);
        const bottom = Math.max(existing.y + existing.height, newRegion.y + newRegion.height);
        
        existing.x = left;
        existing.y = top;
        existing.width = right - left;
        existing.height = bottom - top;
        existing.timestamp = Math.max(existing.timestamp, newRegion.timestamp);
        
        merged = true;
        break;
      }
    }
    
    if (!merged) {
      this.dirtyRegions.push(newRegion);
    }
    
    // 限制脏区域数量
    if (this.dirtyRegions.length > this.config.maxDirtyRegions) {
      this.requestFullRedraw();
    }
  }

  /**
   * 检查两个区域是否重叠
   * @param {Object} rect1 - 区域1
   * @param {Object} rect2 - 区域2
   * @returns {boolean} 是否重叠
   * @private
   */
  _regionsOverlap(rect1, rect2) {
    return !(rect1.x + rect1.width < rect2.x ||
             rect2.x + rect2.width < rect1.x ||
             rect1.y + rect1.height < rect2.y ||
             rect2.y + rect2.height < rect1.y);
  }

  /**
   * 请求完整重绘
   */
  requestFullRedraw() {
    this.fullRedrawRequested = true;
    this.dirtyRegions = [];
    
    // 标记所有图层需要重绘
    for (const layerName in this.layers) {
      this.layers[layerName].needsRedraw = true;
    }
  }

  /**
   * 开始渲染帧
   * @param {CanvasRenderingContext2D} mainCtx - 主渲染上下文
   */
  beginFrame(mainCtx) {
    this.frameStartTime = performance.now();
    this.mainContext = mainCtx;
    
    // 更新渲染统计
    this._updateRenderStats();
    
    // 清除主画布（如果需要完整重绘）
    if (this.fullRedrawRequested) {
      mainCtx.clearRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    }
  }

  /**
   * 渲染图层
   * @param {string} layerName - 图层名称
   * @param {Function} renderCallback - 渲染回调函数
   */
  renderLayer(layerName, renderCallback) {
    const layer = this.layers[layerName.toUpperCase()];
    if (!layer) {
      console.warn(`Layer ${layerName} not found`);
      return;
    }
    
    const ctx = this.layerContexts.get(layer.name);
    const canvas = this.canvasCache.get(layer.name);
    
    if (!ctx || !canvas) return;
    
    // 检查是否需要重绘
    if (!layer.needsRedraw && !this.fullRedrawRequested) {
      return;
    }
    
    // 清除图层
    if (this.fullRedrawRequested) {
      ctx.clearRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    } else {
      // 只清除脏区域
      this._clearDirtyRegions(ctx, layer.name);
    }
    
    // 设置渲染状态
    this._setupRenderingState(ctx);
    
    // 执行渲染回调
    try {
      if (renderCallback) {
        renderCallback(ctx, this.fullRedrawRequested ? null : this._getDirtyRegionsForLayer(layer.name));
      }
    } catch (error) {
      console.error(`Error rendering layer ${layerName}:`, error);
    }
    
    // 标记图层已更新
    layer.needsRedraw = false;
  }

  /**
   * 清除脏区域
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {string} layerName - 图层名称
   * @private
   */
  _clearDirtyRegions(ctx, layerName) {
    const regions = this._getDirtyRegionsForLayer(layerName);
    
    for (const region of regions) {
      ctx.clearRect(region.x, region.y, region.width, region.height);
    }
  }

  /**
   * 获取图层的脏区域
   * @param {string} layerName - 图层名称
   * @returns {Array} 脏区域列表
   * @private
   */
  _getDirtyRegionsForLayer(layerName) {
    return this.dirtyRegions.filter(region => region.layer === layerName);
  }

  /**
   * 设置渲染状态
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _setupRenderingState(ctx) {
    // 重置状态
    ctx.save();
    
    // 设置默认样式
    ctx.fillStyle = '#FFFFFF';
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    
    // 设置抗锯齿
    ctx.imageSmoothingEnabled = this.config.antialias;
  }

  /**
   * 结束渲染帧
   */
  endFrame() {
    // 合成所有图层到主画布
    this._compositeLayers();
    
    // 清理脏区域
    this.dirtyRegions = [];
    this.fullRedrawRequested = false;
    
    // 更新性能统计
    this._updatePerformanceStats();
    
    // 绘制调试信息
    if (this.config.debugMode) {
      this._renderDebugInfo();
    }
  }

  /**
   * 合成图层
   * @private
   */
  _compositeLayers() {
    if (!this.mainContext) return;
    
    // 按图层顺序合成
    const sortedLayers = Object.values(this.layers).sort((a, b) => a.index - b.index);
    
    for (const layer of sortedLayers) {
      const canvas = this.canvasCache.get(layer.name);
      if (canvas) {
        this.mainContext.drawImage(canvas, 0, 0);
      }
    }
  }

  /**
   * 更新渲染统计
   * @private
   */
  _updateRenderStats() {
    this.renderStats.frameCount++;
    this.renderStats.dirtyRegionCount = this.dirtyRegions.length;
    this.renderStats.cachedElementsCount = this.canvasCache.size;
  }

  /**
   * 更新性能统计
   * @private
   */
  _updatePerformanceStats() {
    const frameTime = performance.now() - this.frameStartTime;
    
    this.frameTimes.push(frameTime);
    if (this.frameTimes.length > this.maxFrameTimes) {
      this.frameTimes.shift();
    }
    
    // 计算平均帧时间和FPS
    const avgFrameTime = this.frameTimes.reduce((sum, time) => sum + time, 0) / this.frameTimes.length;
    this.renderStats.averageFPS = Math.round(1000 / avgFrameTime);
    this.renderStats.renderTime = Math.round(frameTime * 100) / 100;
    this.renderStats.lastFrameTime = frameTime;
  }

  /**
   * 渲染调试信息
   * @private
   */
  _renderDebugInfo() {
    if (!this.mainContext) return;
    
    const ctx = this.mainContext;
    const stats = this.renderStats;
    
    ctx.save();
    
    // 背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(10, 10, 200, 120);
    
    // 文本
    ctx.fillStyle = '#00FF00';
    ctx.font = '12px monospace';
    
    const debugText = [
      `FPS: ${stats.averageFPS}`,
      `Frame Time: ${stats.renderTime}ms`,
      `Frame Count: ${stats.frameCount}`,
      `Dirty Regions: ${stats.dirtyRegionCount}`,
      `Cached Elements: ${stats.cachedElementsCount}`,
      `Full Redraw: ${this.fullRedrawRequested ? 'Yes' : 'No'}`
    ];
    
    debugText.forEach((text, index) => {
      ctx.fillText(text, 15, 25 + index * 15);
    });
    
    // 渲染脏区域轮廓
    ctx.strokeStyle = '#FF0000';
    ctx.lineWidth = 1;
    
    for (const region of this.dirtyRegions) {
      ctx.strokeRect(region.x, region.y, region.width, region.height);
    }
    
    ctx.restore();
  }

  // =================== 字体管理 ===================

  /**
   * 设置字体
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} size - 字体大小
   * @param {string} color - 字体颜色
   * @param {string} family - 字体族
   * @param {string} weight - 字体粗细
   */
  setFont(ctx, size = 20, color = '#ffffff', family = 'Arial', weight = 'normal') {
    const fontKey = `${weight} ${size}px ${family}`;
    
    // 缓存字体设置
    if (this.currentFont !== fontKey) {
      ctx.font = fontKey;
      this.currentFont = fontKey;
    }
    
    ctx.fillStyle = color;
  }

  /**
   * 测量文本尺寸
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {string} text - 文本
   * @returns {Object} 文本尺寸
   */
  measureText(ctx, text) {
    const metrics = ctx.measureText(text);
    return {
      width: metrics.width,
      height: metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent
    };
  }

  /**
   * 绘制带边框的文本
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {string} text - 文本
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {string} fillColor - 填充颜色
   * @param {string} strokeColor - 边框颜色
   * @param {number} strokeWidth - 边框宽度
   */
  drawTextWithStroke(ctx, text, x, y, fillColor = '#FFFFFF', strokeColor = '#000000', strokeWidth = 2) {
    ctx.lineWidth = strokeWidth;
    ctx.strokeStyle = strokeColor;
    ctx.fillStyle = fillColor;
    
    // 先绘制边框
    ctx.strokeText(text, x, y);
    // 再绘制填充
    ctx.fillText(text, x, y);
  }

  // =================== 形状绘制工具 ===================

  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.arcTo(x + width, y, x + width, y + height, radius);
    ctx.arcTo(x + width, y + height, x, y + height, radius);
    ctx.arcTo(x, y + height, x, y, radius);
    ctx.arcTo(x, y, x + width, y, radius);
    ctx.closePath();
  }

  /**
   * 绘制带阴影的矩形
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {Object} shadow - 阴影配置
   */
  drawShadowRect(ctx, x, y, width, height, shadow = {}) {
    const {
      offsetX = 2,
      offsetY = 2,
      blur = 4,
      color = 'rgba(0, 0, 0, 0.3)'
    } = shadow;
    
    ctx.save();
    
    // 设置阴影
    ctx.shadowOffsetX = offsetX;
    ctx.shadowOffsetY = offsetY;
    ctx.shadowBlur = blur;
    ctx.shadowColor = color;
    
    // 绘制矩形
    ctx.fillRect(x, y, width, height);
    
    ctx.restore();
  }

  // =================== 公共方法 ===================

  /**
   * 获取渲染统计
   * @returns {Object} 渲染统计
   */
  getRenderStats() {
    return { ...this.renderStats };
  }

  /**
   * 设置配置
   * @param {Object} config - 配置对象
   */
  setConfig(config) {
    this.config = { ...this.config, ...config };
    
    // 如果更改了像素比率，重新初始化图层
    if (config.pixelRatio !== undefined) {
      this._initializeLayers();
    }
  }

  /**
   * 获取图层画布
   * @param {string} layerName - 图层名称
   * @returns {HTMLCanvasElement} 图层画布
   */
  getLayerCanvas(layerName) {
    return this.canvasCache.get(layerName.toLowerCase());
  }

  /**
   * 获取图层上下文
   * @param {string} layerName - 图层名称
   * @returns {CanvasRenderingContext2D} 图层上下文
   */
  getLayerContext(layerName) {
    return this.layerContexts.get(layerName.toLowerCase());
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    for (const ctx of this.layerContexts.values()) {
      ctx.clearRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    }
    
    this.fontCache.clear();
    this.currentFont = null;
    this.requestFullRedraw();
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.canvasCache.clear();
    this.layerContexts.clear();
    this.fontCache.clear();
    this.dirtyRegions = [];
    this.frameTimes = [];
  }
} 