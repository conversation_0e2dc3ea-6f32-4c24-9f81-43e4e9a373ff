/**
 * 触摸事件管理器
 * 统一处理所有触摸和手势事件
 */
import Emitter from '../../libs/tinyemitter.js';

export default class TouchEventManager extends Emitter {
  constructor() {
    super();
    
    // 触摸状态
    this.isProcessing = false;
    this.lastTouchTime = 0;
    this.debounceTime = 200;
    
    // 手势识别
    this.gestureState = {
      isTracking: false,
      startX: 0,
      startY: 0,
      startTime: 0,
      currentX: 0,
      currentY: 0,
      threshold: 30,        // 最小手势距离
      timeThreshold: 500    // 最大手势时间
    };
    
    // 拖拽状态
    this.dragState = {
      isDragging: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
      deltaX: 0,
      deltaY: 0,
      target: null
    };
    
    // 轻击检测
    this.tapState = {
      startX: 0,
      startY: 0,
      startTime: 0,
      maxDistance: 15,      // 最大轻击移动距离
      maxDuration: 300      // 最大轻击持续时间
    };
    
    // 长按检测
    this.longPressState = {
      isPressed: false,
      timer: null,
      delay: 500,           // 长按延迟
      target: null
    };
    
    // 快速连击检测
    this.multiTapState = {
      tapCount: 0,
      lastTapTime: 0,
      maxInterval: 300,     // 连击最大间隔
      lastTapX: 0,
      lastTapY: 0,
      tolerance: 20         // 连击位置容差
    };
    
    // 注册的组件列表
    this.registeredComponents = [];
    
    // 当前活跃的触摸点
    this.activeTouches = new Map();
  }

  /**
   * 注册UI组件
   * @param {Object} component - UI组件
   * @param {number} priority - 优先级 (数字越大优先级越高)
   */
  registerComponent(component, priority = 0) {
    this.registeredComponents.push({
      component: component,
      priority: priority
    });
    
    // 按优先级排序
    this.registeredComponents.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 注销UI组件
   * @param {Object} component - UI组件
   */
  unregisterComponent(component) {
    this.registeredComponents = this.registeredComponents.filter(
      item => item.component !== component
    );
  }

  /**
   * 处理触摸开始事件
   * @param {TouchEvent} event - 触摸事件
   */
  handleTouchStart(event) {
    if (this._shouldDebounce()) return;
    
    const touch = this._extractTouchInfo(event);
    if (!touch) return;
    
    // 记录触摸信息
    this.activeTouches.set(touch.id, touch);
    
    // 更新各种状态
    this._updateGestureState(touch, 'start');
    this._updateDragState(touch, 'start');
    this._updateTapState(touch, 'start');
    this._updateLongPressState(touch, 'start');
    
    // 分发到组件
    this._dispatchToComponents('onPointerDown', touch.x, touch.y, touch);
    
    // 发出事件
    this.emit('touchStart', {
      x: touch.x,
      y: touch.y,
      touch: touch,
      gesture: this.gestureState,
      drag: this.dragState
    });
  }

  /**
   * 处理触摸移动事件
   * @param {TouchEvent} event - 触摸事件
   */
  handleTouchMove(event) {
    const touch = this._extractTouchInfo(event);
    if (!touch) return;
    
    const previousTouch = this.activeTouches.get(touch.id);
    if (!previousTouch) return;
    
    // 更新触摸信息
    this.activeTouches.set(touch.id, touch);
    
    // 更新各种状态
    this._updateGestureState(touch, 'move');
    this._updateDragState(touch, 'move');
    this._cancelLongPressIfMoved(touch, previousTouch);
    
    // 分发到组件
    this._dispatchToComponents('onPointerMove', touch.x, touch.y, touch);
    
    // 检查手势
    this._checkGestures(touch, previousTouch);
    
    // 发出事件
    this.emit('touchMove', {
      x: touch.x,
      y: touch.y,
      touch: touch,
      previousTouch: previousTouch,
      gesture: this.gestureState,
      drag: this.dragState
    });
  }

  /**
   * 处理触摸结束事件
   * @param {TouchEvent} event - 触摸事件
   */
  handleTouchEnd(event) {
    const touch = this._extractTouchInfo(event, true);
    if (!touch) return;
    
    const startTouch = this.activeTouches.get(touch.id);
    this.activeTouches.delete(touch.id);
    
    if (!startTouch) return;
    
    // 计算触摸时长和距离
    const duration = touch.timestamp - startTouch.timestamp;
    const distance = this._calculateDistance(startTouch.x, startTouch.y, touch.x, touch.y);
    
    // 检查各种手势
    this._checkTap(startTouch, touch, duration, distance);
    this._checkSwipe(startTouch, touch, duration, distance);
    
    // 清理状态
    this._updateGestureState(touch, 'end');
    this._updateDragState(touch, 'end');
    this._updateLongPressState(touch, 'end');
    
    // 分发到组件
    this._dispatchToComponents('onPointerUp', touch.x, touch.y, touch);
    
    // 发出事件
    this.emit('touchEnd', {
      x: touch.x,
      y: touch.y,
      touch: touch,
      startTouch: startTouch,
      duration: duration,
      distance: distance
    });
  }

  /**
   * 提取触摸信息
   * @param {TouchEvent} event - 触摸事件
   * @param {boolean} isEnd - 是否是结束事件
   * @returns {Object} 触摸信息
   * @private
   */
  _extractTouchInfo(event, isEnd = false) {
    let touch;
    
    if (isEnd && event.changedTouches && event.changedTouches.length > 0) {
      touch = event.changedTouches[0];
    } else if (event.touches && event.touches.length > 0) {
      touch = event.touches[0];
    } else {
      return null;
    }
    
    return {
      id: touch.identifier || 0,
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now(),
      force: touch.force || 0
    };
  }

  /**
   * 检查是否应该防抖
   * @returns {boolean} 是否应该防抖
   * @private
   */
  _shouldDebounce() {
    const now = Date.now();
    if (this.isProcessing || now - this.lastTouchTime < this.debounceTime) {
      return true;
    }
    
    this.isProcessing = true;
    this.lastTouchTime = now;
    
    // 延时清除处理标志
    setTimeout(() => {
      this.isProcessing = false;
    }, 50);
    
    return false;
  }

  /**
   * 更新手势状态
   * @param {Object} touch - 触摸信息
   * @param {string} phase - 阶段 ('start', 'move', 'end')
   * @private
   */
  _updateGestureState(touch, phase) {
    switch (phase) {
      case 'start':
        this.gestureState.isTracking = true;
        this.gestureState.startX = touch.x;
        this.gestureState.startY = touch.y;
        this.gestureState.startTime = touch.timestamp;
        this.gestureState.currentX = touch.x;
        this.gestureState.currentY = touch.y;
        break;
        
      case 'move':
        this.gestureState.currentX = touch.x;
        this.gestureState.currentY = touch.y;
        break;
        
      case 'end':
        this.gestureState.isTracking = false;
        break;
    }
  }

  /**
   * 更新拖拽状态
   * @param {Object} touch - 触摸信息
   * @param {string} phase - 阶段
   * @private
   */
  _updateDragState(touch, phase) {
    switch (phase) {
      case 'start':
        this.dragState.startX = touch.x;
        this.dragState.startY = touch.y;
        this.dragState.currentX = touch.x;
        this.dragState.currentY = touch.y;
        this.dragState.deltaX = 0;
        this.dragState.deltaY = 0;
        break;
        
      case 'move':
        this.dragState.currentX = touch.x;
        this.dragState.currentY = touch.y;
        this.dragState.deltaX = touch.x - this.dragState.startX;
        this.dragState.deltaY = touch.y - this.dragState.startY;
        
        // 检查是否开始拖拽
        if (!this.dragState.isDragging) {
          const distance = Math.abs(this.dragState.deltaX) + Math.abs(this.dragState.deltaY);
          if (distance > 10) {
            this.dragState.isDragging = true;
            this.emit('dragStart', { ...this.dragState });
          }
        }
        
        if (this.dragState.isDragging) {
          this.emit('dragMove', { ...this.dragState });
        }
        break;
        
      case 'end':
        if (this.dragState.isDragging) {
          this.emit('dragEnd', { ...this.dragState });
        }
        this.dragState.isDragging = false;
        this.dragState.target = null;
        break;
    }
  }

  /**
   * 更新轻击状态
   * @param {Object} touch - 触摸信息
   * @param {string} phase - 阶段
   * @private
   */
  _updateTapState(touch, phase) {
    if (phase === 'start') {
      this.tapState.startX = touch.x;
      this.tapState.startY = touch.y;
      this.tapState.startTime = touch.timestamp;
    }
  }

  /**
   * 更新长按状态
   * @param {Object} touch - 触摸信息
   * @param {string} phase - 阶段
   * @private
   */
  _updateLongPressState(touch, phase) {
    switch (phase) {
      case 'start':
        this.longPressState.isPressed = true;
        this.longPressState.timer = setTimeout(() => {
          if (this.longPressState.isPressed) {
            this.emit('longPress', {
              x: touch.x,
              y: touch.y,
              touch: touch
            });
          }
        }, this.longPressState.delay);
        break;
        
      case 'end':
        this.longPressState.isPressed = false;
        if (this.longPressState.timer) {
          clearTimeout(this.longPressState.timer);
          this.longPressState.timer = null;
        }
        break;
    }
  }

  /**
   * 取消长按（如果移动距离过大）
   * @param {Object} currentTouch - 当前触摸
   * @param {Object} startTouch - 开始触摸
   * @private
   */
  _cancelLongPressIfMoved(currentTouch, startTouch) {
    const distance = this._calculateDistance(
      startTouch.x, startTouch.y,
      currentTouch.x, currentTouch.y
    );
    
    if (distance > 10 && this.longPressState.isPressed) {
      this.longPressState.isPressed = false;
      if (this.longPressState.timer) {
        clearTimeout(this.longPressState.timer);
        this.longPressState.timer = null;
      }
    }
  }

  /**
   * 检查轻击
   * @param {Object} startTouch - 开始触摸
   * @param {Object} endTouch - 结束触摸
   * @param {number} duration - 持续时间
   * @param {number} distance - 移动距离
   * @private
   */
  _checkTap(startTouch, endTouch, duration, distance) {
    if (duration <= this.tapState.maxDuration && distance <= this.tapState.maxDistance) {
      // 检查多次点击
      this._checkMultiTap(endTouch);
      
      this.emit('tap', {
        x: endTouch.x,
        y: endTouch.y,
        duration: duration,
        tapCount: this.multiTapState.tapCount
      });
    }
  }

  /**
   * 检查多次点击
   * @param {Object} touch - 触摸信息
   * @private
   */
  _checkMultiTap(touch) {
    const now = touch.timestamp;
    const timeSinceLastTap = now - this.multiTapState.lastTapTime;
    const distance = this._calculateDistance(
      this.multiTapState.lastTapX, this.multiTapState.lastTapY,
      touch.x, touch.y
    );
    
    if (timeSinceLastTap <= this.multiTapState.maxInterval && 
        distance <= this.multiTapState.tolerance) {
      this.multiTapState.tapCount++;
    } else {
      this.multiTapState.tapCount = 1;
    }
    
    this.multiTapState.lastTapTime = now;
    this.multiTapState.lastTapX = touch.x;
    this.multiTapState.lastTapY = touch.y;
    
    if (this.multiTapState.tapCount >= 2) {
      this.emit('multiTap', {
        x: touch.x,
        y: touch.y,
        tapCount: this.multiTapState.tapCount
      });
    }
  }

  /**
   * 检查滑动手势
   * @param {Object} startTouch - 开始触摸
   * @param {Object} endTouch - 结束触摸
   * @param {number} duration - 持续时间
   * @param {number} distance - 移动距离
   * @private
   */
  _checkSwipe(startTouch, endTouch, duration, distance) {
    if (distance < this.gestureState.threshold || 
        duration > this.gestureState.timeThreshold) {
      return;
    }
    
    const deltaX = endTouch.x - startTouch.x;
    const deltaY = endTouch.y - startTouch.y;
    const velocity = distance / duration;
    
    let direction;
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      direction = deltaX > 0 ? 'right' : 'left';
    } else {
      direction = deltaY > 0 ? 'down' : 'up';
    }
    
    this.emit('swipe', {
      direction: direction,
      distance: distance,
      duration: duration,
      velocity: velocity,
      deltaX: deltaX,
      deltaY: deltaY,
      startX: startTouch.x,
      startY: startTouch.y,
      endX: endTouch.x,
      endY: endTouch.y
    });
  }

  /**
   * 检查手势
   * @param {Object} currentTouch - 当前触摸
   * @param {Object} previousTouch - 上一次触摸
   * @private
   */
  _checkGestures(currentTouch, previousTouch) {
    // 实时手势检测可以在这里添加
    // 例如：捏合手势、旋转手势等
  }

  /**
   * 分发事件到组件
   * @param {string} method - 方法名
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {Object} touch - 触摸信息
   * @private
   */
  _dispatchToComponents(method, x, y, touch) {
    for (const item of this.registeredComponents) {
      const component = item.component;
      
      if (component && typeof component[method] === 'function') {
        try {
          const handled = component[method](x, y, touch);
          if (handled) {
            break; // 高优先级组件处理了事件，停止传播
          }
        } catch (error) {
          console.error(`Error in component ${method}:`, error);
        }
      }
    }
  }

  /**
   * 计算两点间距离
   * @param {number} x1 - 第一个点X坐标
   * @param {number} y1 - 第一个点Y坐标
   * @param {number} x2 - 第二个点X坐标
   * @param {number} y2 - 第二个点Y坐标
   * @returns {number} 距离
   * @private
   */
  _calculateDistance(x1, y1, x2, y2) {
    const dx = x2 - x1;
    const dy = y2 - y1;
    return Math.sqrt(dx * dx + dy * dy);
  }

  // =================== 公共方法 ===================

  /**
   * 设置防抖时间
   * @param {number} time - 防抖时间(毫秒)
   */
  setDebounceTime(time) {
    this.debounceTime = Math.max(0, time);
  }

  /**
   * 设置手势参数
   * @param {Object} config - 手势配置
   */
  setGestureConfig(config) {
    if (config.threshold !== undefined) {
      this.gestureState.threshold = config.threshold;
    }
    if (config.timeThreshold !== undefined) {
      this.gestureState.timeThreshold = config.timeThreshold;
    }
    if (config.longPressDelay !== undefined) {
      this.longPressState.delay = config.longPressDelay;
    }
  }

  /**
   * 获取当前触摸状态
   * @returns {Object} 触摸状态
   */
  getTouchState() {
    return {
      isProcessing: this.isProcessing,
      activeTouches: this.activeTouches.size,
      gestureState: { ...this.gestureState },
      dragState: { ...this.dragState },
      longPressState: { ...this.longPressState }
    };
  }

  /**
   * 清除所有状态
   */
  reset() {
    this.activeTouches.clear();
    this.gestureState.isTracking = false;
    this.dragState.isDragging = false;
    this.longPressState.isPressed = false;
    
    if (this.longPressState.timer) {
      clearTimeout(this.longPressState.timer);
      this.longPressState.timer = null;
    }
  }
} 