/**
 * UI状态管理器
 * 统一管理游戏界面状态和切换逻辑
 */
import Emitter from '../../libs/tinyemitter.js';

export default class UIStateManager extends Emitter {
  constructor() {
    super();
    
    // 界面状态枚举
    this.SCREEN_TYPES = {
      LEVEL_SELECTION: 'level',
      LEVEL_START: 'levelstart',
      GAME: 'game',
      GAME_OVER: 'gameover',
      LEVEL_COMPLETE: 'levelcomplete',
      LEVEL_FAIL: 'levelfail',
      PAUSE: 'pause',
      SETTINGS: 'settings'
    };
    
    // 当前状态
    this.currentScreen = this.SCREEN_TYPES.LEVEL_SELECTION;
    this.previousScreen = null;
    this.screenHistory = [];
    
    // 状态数据
    this.stateData = {
      // 关卡数据
      levels: [],
      currentLevel: null,
      targetScore: 0,
      
      // 游戏状态
      score: 0,
      lives: 3,
      timeRemaining: 0,
      gameState: 'ready',
      
      // 关卡完成数据
      completionData: {
        stars: 0,
        score: 0,
        reason: null
      },
      
      // UI显示状态
      showDebugInfo: false,
      pausedAt: 0,
      
      // 滚动状态
      scrollOffset: 0,
      scrollVelocity: 0,
      maxScrollOffset: 0,
      
      // 动画状态
      transitionProgress: 0,
      isTransitioning: false,
      transitionType: 'fade'
    };
    
    // 状态验证规则
    this.stateValidation = {
      [this.SCREEN_TYPES.LEVEL_SELECTION]: ['levels'],
      [this.SCREEN_TYPES.LEVEL_START]: ['currentLevel', 'targetScore'],
      [this.SCREEN_TYPES.GAME]: ['currentLevel', 'score', 'gameState'],
      [this.SCREEN_TYPES.GAME_OVER]: ['score'],
      [this.SCREEN_TYPES.LEVEL_COMPLETE]: ['completionData'],
      [this.SCREEN_TYPES.LEVEL_FAIL]: ['completionData']
    };
    
    // 状态切换动画配置
    this.transitionConfig = {
      duration: 300,
      easing: 'easeInOut',
      types: {
        fade: { from: 0, to: 1 },
        slide: { from: -100, to: 0 },
        scale: { from: 0.8, to: 1.0 }
      }
    };
    
    // 监听器
    this.stateChangeListeners = new Map();
    this.dataChangeListeners = new Map();
  }

  /**
   * 切换到指定界面
   * @param {string} screenType - 界面类型
   * @param {Object} data - 附加数据
   * @param {Object} options - 切换选项
   */
  switchToScreen(screenType, data = {}, options = {}) {
    if (!this.SCREEN_TYPES[screenType.toUpperCase()]) {
      console.error(`Invalid screen type: ${screenType}`);
      return false;
    }
    
    const newScreen = this.SCREEN_TYPES[screenType.toUpperCase()] || screenType;
    
    // 验证状态切换
    if (!this._validateStateTransition(this.currentScreen, newScreen)) {
      console.warn(`Invalid state transition: ${this.currentScreen} -> ${newScreen}`);
      return false;
    }
    
    // 保存历史
    if (options.saveHistory !== false) {
      this.screenHistory.push(this.currentScreen);
      if (this.screenHistory.length > 10) {
        this.screenHistory.shift();
      }
    }
    
    const previousScreen = this.currentScreen;
    
    // 执行状态切换
    this._performStateTransition(previousScreen, newScreen, data, options);
    
    return true;
  }

  /**
   * 执行状态切换
   * @param {string} fromScreen - 源界面
   * @param {string} toScreen - 目标界面
   * @param {Object} data - 数据
   * @param {Object} options - 选项
   * @private
   */
  _performStateTransition(fromScreen, toScreen, data, options) {
    // 准备切换
    this.emit('beforeStateChange', {
      from: fromScreen,
      to: toScreen,
      data: data
    });
    
    // 更新状态数据
    this._updateStateData(data);
    
    // 开始动画
    if (options.animated !== false) {
      this._startTransitionAnimation(fromScreen, toScreen, options);
    }
    
    // 更新当前状态
    this.previousScreen = fromScreen;
    this.currentScreen = toScreen;
    
    // 验证新状态数据
    this._validateStateData(toScreen);
    
    // 发出状态改变事件
    this.emit('stateChanged', {
      from: fromScreen,
      to: toScreen,
      data: this.stateData,
      isValid: this._isStateValid(toScreen)
    });
    
    // 执行状态特定逻辑
    this._executeStateLogic(toScreen);
  }

  /**
   * 开始过渡动画
   * @param {string} fromScreen - 源界面
   * @param {string} toScreen - 目标界面
   * @param {Object} options - 动画选项
   * @private
   */
  _startTransitionAnimation(fromScreen, toScreen, options) {
    this.stateData.isTransitioning = true;
    this.stateData.transitionProgress = 0;
    this.stateData.transitionType = options.transitionType || 'fade';
    
    const duration = options.duration || this.transitionConfig.duration;
    const startTime = Date.now();
    
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 应用缓动函数
      this.stateData.transitionProgress = this._applyEasing(progress, options.easing);
      
      this.emit('transitionUpdate', {
        progress: this.stateData.transitionProgress,
        type: this.stateData.transitionType
      });
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this._completeTransition();
      }
    };
    
    requestAnimationFrame(animate);
  }

  /**
   * 完成过渡动画
   * @private
   */
  _completeTransition() {
    this.stateData.isTransitioning = false;
    this.stateData.transitionProgress = 1;
    
    this.emit('transitionComplete', {
      screen: this.currentScreen
    });
  }

  /**
   * 应用缓动函数
   * @param {number} t - 时间进度 (0-1)
   * @param {string} easing - 缓动类型
   * @returns {number} 缓动后的进度
   * @private
   */
  _applyEasing(t, easing = 'linear') {
    switch (easing) {
      case 'easeIn':
        return t * t;
      case 'easeOut':
        return t * (2 - t);
      case 'easeInOut':
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
      case 'elastic':
        return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
      default:
        return t;
    }
  }

  /**
   * 验证状态切换
   * @param {string} fromScreen - 源界面
   * @param {string} toScreen - 目标界面
   * @returns {boolean} 是否有效
   * @private
   */
  _validateStateTransition(fromScreen, toScreen) {
    // 定义有效的状态切换
    const validTransitions = {
      [this.SCREEN_TYPES.LEVEL_SELECTION]: [
        this.SCREEN_TYPES.LEVEL_START,
        this.SCREEN_TYPES.SETTINGS
      ],
      [this.SCREEN_TYPES.LEVEL_START]: [
        this.SCREEN_TYPES.GAME,
        this.SCREEN_TYPES.LEVEL_SELECTION
      ],
      [this.SCREEN_TYPES.GAME]: [
        this.SCREEN_TYPES.PAUSE,
        this.SCREEN_TYPES.GAME_OVER,
        this.SCREEN_TYPES.LEVEL_COMPLETE,
        this.SCREEN_TYPES.LEVEL_FAIL
      ],
      [this.SCREEN_TYPES.PAUSE]: [
        this.SCREEN_TYPES.GAME,
        this.SCREEN_TYPES.LEVEL_SELECTION
      ],
      [this.SCREEN_TYPES.GAME_OVER]: [
        this.SCREEN_TYPES.LEVEL_SELECTION,
        this.SCREEN_TYPES.GAME
      ],
      [this.SCREEN_TYPES.LEVEL_COMPLETE]: [
        this.SCREEN_TYPES.LEVEL_SELECTION,
        this.SCREEN_TYPES.LEVEL_START
      ],
      [this.SCREEN_TYPES.LEVEL_FAIL]: [
        this.SCREEN_TYPES.LEVEL_SELECTION,
        this.SCREEN_TYPES.GAME
      ]
    };
    
    const allowedTransitions = validTransitions[fromScreen] || [];
    return allowedTransitions.includes(toScreen);
  }

  /**
   * 更新状态数据
   * @param {Object} newData - 新数据
   * @private
   */
  _updateStateData(newData) {
    const oldData = { ...this.stateData };
    
    // 深度合并数据
    this._deepMerge(this.stateData, newData);
    
    // 发出数据变更事件
    this.emit('dataChanged', {
      oldData: oldData,
      newData: this.stateData,
      changes: this._getDataChanges(oldData, this.stateData)
    });
  }

  /**
   * 深度合并对象
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   * @private
   */
  _deepMerge(target, source) {
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          if (!target[key]) target[key] = {};
          this._deepMerge(target[key], source[key]);
        } else {
          target[key] = source[key];
        }
      }
    }
  }

  /**
   * 获取数据变更
   * @param {Object} oldData - 旧数据
   * @param {Object} newData - 新数据
   * @returns {Array} 变更列表
   * @private
   */
  _getDataChanges(oldData, newData) {
    const changes = [];
    
    const compareObjects = (old, updated, path = '') => {
      for (const key in updated) {
        if (updated.hasOwnProperty(key)) {
          const currentPath = path ? `${path}.${key}` : key;
          
          if (old[key] !== updated[key]) {
            changes.push({
              path: currentPath,
              oldValue: old[key],
              newValue: updated[key]
            });
          }
        }
      }
    };
    
    compareObjects(oldData, newData);
    return changes;
  }

  /**
   * 验证状态数据
   * @param {string} screenType - 界面类型
   * @private
   */
  _validateStateData(screenType) {
    const requiredFields = this.stateValidation[screenType] || [];
    
    for (const field of requiredFields) {
      if (this.stateData[field] === undefined || this.stateData[field] === null) {
        console.warn(`Missing required field for ${screenType}: ${field}`);
      }
    }
  }

  /**
   * 检查状态是否有效
   * @param {string} screenType - 界面类型
   * @returns {boolean} 是否有效
   * @private
   */
  _isStateValid(screenType) {
    const requiredFields = this.stateValidation[screenType] || [];
    
    return requiredFields.every(field => {
      return this.stateData[field] !== undefined && this.stateData[field] !== null;
    });
  }

  /**
   * 执行状态特定逻辑
   * @param {string} screenType - 界面类型
   * @private
   */
  _executeStateLogic(screenType) {
    switch (screenType) {
      case this.SCREEN_TYPES.GAME:
        this._initGameState();
        break;
      case this.SCREEN_TYPES.PAUSE:
        this.stateData.pausedAt = Date.now();
        break;
      case this.SCREEN_TYPES.LEVEL_COMPLETE:
        this._handleLevelComplete();
        break;
      case this.SCREEN_TYPES.LEVEL_FAIL:
        this._handleLevelFail();
        break;
    }
  }

  /**
   * 初始化游戏状态
   * @private
   */
  _initGameState() {
    if (!this.stateData.gameState || this.stateData.gameState === 'ready') {
      this.stateData.gameState = 'playing';
      this.stateData.score = this.stateData.score || 0;
    }
  }

  /**
   * 处理关卡完成
   * @private
   */
  _handleLevelComplete() {
    // 计算星级
    if (this.stateData.completionData && this.stateData.targetScore) {
      const score = this.stateData.completionData.score;
      const target = this.stateData.targetScore;
      
      if (score >= target * 3) {
        this.stateData.completionData.stars = 3;
      } else if (score >= target * 2) {
        this.stateData.completionData.stars = 2;
      } else if (score >= target) {
        this.stateData.completionData.stars = 1;
      } else {
        this.stateData.completionData.stars = 0;
      }
    }
  }

  /**
   * 处理关卡失败
   * @private
   */
  _handleLevelFail() {
    this.stateData.gameState = 'failed';
  }

  // =================== 公共方法 ===================

  /**
   * 返回上一个界面
   * @param {Object} options - 选项
   */
  goBack(options = {}) {
    if (this.screenHistory.length > 0) {
      const previousScreen = this.screenHistory.pop();
      this.switchToScreen(previousScreen, {}, { ...options, saveHistory: false });
    }
  }

  /**
   * 获取当前界面
   * @returns {string} 当前界面类型
   */
  getCurrentScreen() {
    return this.currentScreen;
  }

  /**
   * 获取状态数据
   * @param {string} key - 数据键，如果不提供则返回所有数据
   * @returns {*} 状态数据
   */
  getStateData(key) {
    if (key) {
      return this._getNestedValue(this.stateData, key);
    }
    return { ...this.stateData };
  }

  /**
   * 设置状态数据
   * @param {string|Object} key - 数据键或数据对象
   * @param {*} value - 数据值
   */
  setStateData(key, value) {
    if (typeof key === 'object') {
      this._updateStateData(key);
    } else {
      this._setNestedValue(this.stateData, key, value);
      this.emit('dataChanged', {
        changes: [{ path: key, newValue: value }]
      });
    }
  }

  /**
   * 获取嵌套值
   * @param {Object} obj - 对象
   * @param {string} path - 路径
   * @returns {*} 值
   * @private
   */
  _getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  /**
   * 设置嵌套值
   * @param {Object} obj - 对象
   * @param {string} path - 路径
   * @param {*} value - 值
   * @private
   */
  _setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  /**
   * 检查是否在指定界面
   * @param {string} screenType - 界面类型
   * @returns {boolean} 是否在指定界面
   */
  isCurrentScreen(screenType) {
    return this.currentScreen === screenType;
  }

  /**
   * 检查是否正在过渡
   * @returns {boolean} 是否正在过渡
   */
  isTransitioning() {
    return this.stateData.isTransitioning;
  }

  /**
   * 注册状态变更监听器
   * @param {string} screenType - 界面类型，'*' 表示所有界面
   * @param {Function} callback - 回调函数
   */
  onStateChange(screenType, callback) {
    if (!this.stateChangeListeners.has(screenType)) {
      this.stateChangeListeners.set(screenType, []);
    }
    this.stateChangeListeners.get(screenType).push(callback);
  }

  /**
   * 注册数据变更监听器
   * @param {string} dataPath - 数据路径，'*' 表示所有数据
   * @param {Function} callback - 回调函数
   */
  onDataChange(dataPath, callback) {
    if (!this.dataChangeListeners.has(dataPath)) {
      this.dataChangeListeners.set(dataPath, []);
    }
    this.dataChangeListeners.get(dataPath).push(callback);
  }

  /**
   * 重置所有状态
   */
  reset() {
    this.currentScreen = this.SCREEN_TYPES.LEVEL_SELECTION;
    this.previousScreen = null;
    this.screenHistory = [];
    
    // 重置状态数据（保留基本结构）
    const resetData = {
      levels: this.stateData.levels || [],
      currentLevel: null,
      targetScore: 0,
      score: 0,
      lives: 3,
      timeRemaining: 0,
      gameState: 'ready',
      completionData: { stars: 0, score: 0, reason: null },
      showDebugInfo: false,
      pausedAt: 0,
      scrollOffset: 0,
      scrollVelocity: 0,
      maxScrollOffset: 0,
      transitionProgress: 0,
      isTransitioning: false,
      transitionType: 'fade'
    };
    
    this._updateStateData(resetData);
    
    this.emit('reset');
  }

  /**
   * 获取状态历史
   * @returns {Array} 状态历史
   */
  getHistory() {
    return [...this.screenHistory];
  }

  /**
   * 清除历史
   */
  clearHistory() {
    this.screenHistory = [];
  }
} 