/**
 * 星级评分组件
 * 用于显示和管理星级评分
 */
export default class StarRating {
  constructor(config) {
    // 基础配置
    this.x = config.x || 0;
    this.y = config.y || 0;
    this.starCount = config.starCount || 3;
    this.starSize = config.starSize || 30;
    this.starSpacing = config.starSpacing || 5;
    
    // 样式配置
    this.filledColor = config.filledColor || '#FFD700';  // 金色
    this.emptyColor = config.emptyColor || '#CCCCCC';    // 灰色
    this.outlineColor = config.outlineColor || '#FFA500'; // 橙色边框
    this.outlineWidth = config.outlineWidth || 2;
    
    // 状态
    this.rating = config.rating || 0; // 当前评分 (0-starCount)
    this.animatedRating = 0; // 动画中的评分
    this.animationSpeed = config.animationSpeed || 0.1;
    this.showAnimation = config.showAnimation !== false;
    
    // 交互配置
    this.interactive = config.interactive || false;
    this.onRatingChange = config.onRatingChange || null;
    
    // 动画状态
    this.starAnimations = [];
    for (let i = 0; i < this.starCount; i++) {
      this.starAnimations.push({
        scale: 1.0,
        rotation: 0,
        sparkle: 0,
        delay: i * 100 // 延迟动画
      });
    }
    
    // 闪烁效果
    this.sparkleParticles = [];
    this.lastSparkleTime = 0;
    this.sparkleInterval = 200;
  }

  /**
   * 更新星级组件
   * @param {number} deltaTime - 时间增量
   */
  update(deltaTime) {
    // 更新动画评分
    if (this.showAnimation) {
      this.animatedRating += (this.rating - this.animatedRating) * this.animationSpeed;
    } else {
      this.animatedRating = this.rating;
    }
    
    // 更新星星动画
    this._updateStarAnimations(deltaTime);
    
    // 更新闪烁粒子
    this._updateSparkleParticles(deltaTime);
    
    // 生成新的闪烁粒子
    if (this.rating > 0 && Date.now() - this.lastSparkleTime > this.sparkleInterval) {
      this._generateSparkleParticles();
      this.lastSparkleTime = Date.now();
    }
  }

  /**
   * 渲染星级组件
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   */
  render(ctx) {
    ctx.save();
    
    // 渲染每颗星星
    for (let i = 0; i < this.starCount; i++) {
      const starX = this.x + i * (this.starSize + this.starSpacing);
      const starY = this.y;
      
      this._renderStar(ctx, starX, starY, i);
    }
    
    // 渲染闪烁粒子
    this._renderSparkleParticles(ctx);
    
    ctx.restore();
  }

  /**
   * 渲染单颗星星
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} index - 星星索引
   * @private
   */
  _renderStar(ctx, x, y, index) {
    const centerX = x + this.starSize / 2;
    const centerY = y + this.starSize / 2;
    const animation = this.starAnimations[index];
    
    ctx.save();
    
    // 应用变换
    ctx.translate(centerX, centerY);
    ctx.scale(animation.scale, animation.scale);
    ctx.rotate(animation.rotation);
    ctx.translate(-centerX, -centerY);
    
    // 确定星星状态
    const starValue = index + 1;
    const fillAmount = Math.max(0, Math.min(1, this.animatedRating - index));
    
    // 绘制星星
    if (fillAmount > 0) {
      this._drawStarShape(ctx, centerX, centerY, this.starSize / 2, this.filledColor, fillAmount);
    }
    
    // 绘制空星星部分
    if (fillAmount < 1) {
      this._drawStarShape(ctx, centerX, centerY, this.starSize / 2, this.emptyColor, 1 - fillAmount, true);
    }
    
    // 绘制边框
    ctx.strokeStyle = this.outlineColor;
    ctx.lineWidth = this.outlineWidth;
    this._drawStarPath(ctx, centerX, centerY, this.starSize / 2);
    ctx.stroke();
    
    ctx.restore();
  }

  /**
   * 绘制星星形状
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} radius - 半径
   * @param {string} color - 填充颜色
   * @param {number} fillAmount - 填充比例 (0-1)
   * @param {boolean} isPartial - 是否是部分填充
   * @private
   */
  _drawStarShape(ctx, centerX, centerY, radius, color, fillAmount = 1, isPartial = false) {
    ctx.save();
    
    if (isPartial && fillAmount > 0) {
      // 部分填充：创建裁剪区域
      const clipHeight = radius * 2 * (1 - fillAmount);
      ctx.beginPath();
      ctx.rect(centerX - radius, centerY - radius + clipHeight, radius * 2, radius * 2 - clipHeight);
      ctx.clip();
    } else if (!isPartial && fillAmount < 1) {
      // 渐变填充
      const clipHeight = radius * 2 * fillAmount;
      ctx.beginPath();
      ctx.rect(centerX - radius, centerY - radius, radius * 2, clipHeight);
      ctx.clip();
    }
    
    ctx.fillStyle = color;
    this._drawStarPath(ctx, centerX, centerY, radius);
    ctx.fill();
    
    ctx.restore();
  }

  /**
   * 绘制星星路径
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} radius - 外半径
   * @private
   */
  _drawStarPath(ctx, centerX, centerY, radius) {
    const innerRadius = radius * 0.4;
    const points = 5;
    
    ctx.beginPath();
    
    for (let i = 0; i < points * 2; i++) {
      const angle = (i * Math.PI) / points - Math.PI / 2;
      const r = (i % 2 === 0) ? radius : innerRadius;
      const x = centerX + Math.cos(angle) * r;
      const y = centerY + Math.sin(angle) * r;
      
      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    
    ctx.closePath();
  }

  /**
   * 更新星星动画
   * @param {number} deltaTime - 时间增量
   * @private
   */
  _updateStarAnimations(deltaTime) {
    const currentTime = Date.now();
    
    for (let i = 0; i < this.starAnimations.length; i++) {
      const animation = this.starAnimations[i];
      const starValue = i + 1;
      
      // 只有当前评分达到这颗星时才播放动画
      if (this.rating >= starValue) {
        // 缩放动画
        const targetScale = 1.0 + Math.sin(currentTime * 0.005 + i) * 0.1;
        animation.scale += (targetScale - animation.scale) * 0.1;
        
        // 旋转动画
        animation.rotation += 0.02;
        
        // 闪烁效果
        animation.sparkle = Math.sin(currentTime * 0.01 + i) * 0.5 + 0.5;
      } else {
        // 重置动画
        animation.scale += (1.0 - animation.scale) * 0.1;
        animation.rotation = 0;
        animation.sparkle = 0;
      }
    }
  }

  /**
   * 生成闪烁粒子
   * @private
   */
  _generateSparkleParticles() {
    for (let i = 0; i < this.rating; i++) {
      const starX = this.x + i * (this.starSize + this.starSpacing) + this.starSize / 2;
      const starY = this.y + this.starSize / 2;
      
      // 生成3-5个粒子
      const particleCount = Math.floor(Math.random() * 3) + 3;
      
      for (let j = 0; j < particleCount; j++) {
        this.sparkleParticles.push({
          x: starX + (Math.random() - 0.5) * this.starSize,
          y: starY + (Math.random() - 0.5) * this.starSize,
          vx: (Math.random() - 0.5) * 2,
          vy: (Math.random() - 0.5) * 2,
          life: 1.0,
          decay: 0.02 + Math.random() * 0.02,
          size: Math.random() * 3 + 1,
          color: this.filledColor
        });
      }
    }
  }

  /**
   * 更新闪烁粒子
   * @param {number} deltaTime - 时间增量
   * @private
   */
  _updateSparkleParticles(deltaTime) {
    for (let i = this.sparkleParticles.length - 1; i >= 0; i--) {
      const particle = this.sparkleParticles[i];
      
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.life -= particle.decay;
      
      if (particle.life <= 0) {
        this.sparkleParticles.splice(i, 1);
      }
    }
  }

  /**
   * 渲染闪烁粒子
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderSparkleParticles(ctx) {
    for (const particle of this.sparkleParticles) {
      ctx.save();
      
      ctx.globalAlpha = particle.life;
      ctx.fillStyle = particle.color;
      
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.restore();
    }
  }

  // =================== 交互方法 ===================

  /**
   * 检查点是否在星级组件内
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @returns {number} 星级索引，-1表示不在范围内
   */
  getStarAtPoint(x, y) {
    if (!this.interactive) return -1;
    
    if (y < this.y || y > this.y + this.starSize) return -1;
    
    for (let i = 0; i < this.starCount; i++) {
      const starX = this.x + i * (this.starSize + this.starSpacing);
      if (x >= starX && x <= starX + this.starSize) {
        return i;
      }
    }
    
    return -1;
  }

  /**
   * 处理点击事件
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onClick(x, y) {
    const starIndex = this.getStarAtPoint(x, y);
    
    if (starIndex >= 0) {
      const newRating = starIndex + 1;
      this.setRating(newRating);
      
      if (this.onRatingChange) {
        this.onRatingChange(newRating);
      }
      
      return true;
    }
    
    return false;
  }

  // =================== 公共方法 ===================

  /**
   * 设置评分
   * @param {number} rating - 新的评分 (0-starCount)
   */
  setRating(rating) {
    this.rating = Math.max(0, Math.min(this.starCount, rating));
    
    // 触发星星动画
    for (let i = 0; i < this.rating; i++) {
      setTimeout(() => {
        this.starAnimations[i].scale = 1.3;
      }, i * 100);
    }
  }

  /**
   * 获取当前评分
   * @returns {number} 当前评分
   */
  getRating() {
    return this.rating;
  }

  /**
   * 设置位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
  }

  /**
   * 设置星星数量
   * @param {number} count - 星星数量
   */
  setStarCount(count) {
    this.starCount = Math.max(1, count);
    
    // 重新初始化动画数组
    this.starAnimations = [];
    for (let i = 0; i < this.starCount; i++) {
      this.starAnimations.push({
        scale: 1.0,
        rotation: 0,
        sparkle: 0,
        delay: i * 100
      });
    }
    
    // 调整评分
    this.rating = Math.min(this.rating, this.starCount);
  }

  /**
   * 获取组件尺寸
   * @returns {Object} 尺寸信息
   */
  getSize() {
    const width = this.starCount * this.starSize + (this.starCount - 1) * this.starSpacing;
    const height = this.starSize;
    
    return { width, height };
  }

  /**
   * 获取边界框
   * @returns {Object} 边界框信息
   */
  getBounds() {
    const size = this.getSize();
    return {
      x: this.x,
      y: this.y,
      width: size.width,
      height: size.height,
      left: this.x,
      top: this.y,
      right: this.x + size.width,
      bottom: this.y + size.height
    };
  }
} 