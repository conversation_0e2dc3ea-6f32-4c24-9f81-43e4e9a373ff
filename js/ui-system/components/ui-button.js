/**
 * 可复用UI按钮组件
 * 支持多种样式和交互状态
 */
export default class UIButton {
  constructor(config) {
    // 基础配置
    this.id = config.id || 'button';
    this.x = config.x || 0;
    this.y = config.y || 0;
    this.width = config.width || 100;
    this.height = config.height || 40;
    this.text = config.text || '';
    
    // 样式配置
    this.backgroundColor = config.backgroundColor || '#007ACC';
    this.textColor = config.textColor || '#FFFFFF';
    this.fontSize = config.fontSize || 18;
    this.borderRadius = config.borderRadius || 8;
    this.borderColor = config.borderColor || '#0056B3';
    this.borderWidth = config.borderWidth || 2;
    
    // 状态配置
    this.enabled = config.enabled !== false;
    this.visible = config.visible !== false;
    this.pressed = false;
    this.hovered = false;
    
    // 交互配置
    this.clickHandler = config.onClick || null;
    this.longPressHandler = config.onLongPress || null;
    this.longPressDelay = config.longPressDelay || 500;
    
    // 内部状态
    this.pressStartTime = 0;
    this.isLongPressed = false;
    
    // 动画状态
    this.scaleAnimation = 1.0;
    this.alphaAnimation = 1.0;
    this.animationSpeed = 0.15;
  }

  /**
   * 更新按钮状态
   * @param {number} deltaTime - 时间增量
   */
  update(deltaTime) {
    // 动画更新
    this._updateAnimations(deltaTime);
    
    // 长按检测
    if (this.pressed && !this.isLongPressed) {
      const pressDuration = Date.now() - this.pressStartTime;
      if (pressDuration > this.longPressDelay && this.longPressHandler) {
        this.isLongPressed = true;
        this.longPressHandler(this);
      }
    }
  }

  /**
   * 渲染按钮
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   */
  render(ctx) {
    if (!this.visible) return;

    ctx.save();
    
    // 应用变换
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    
    ctx.translate(centerX, centerY);
    ctx.scale(this.scaleAnimation, this.scaleAnimation);
    ctx.globalAlpha = this.alphaAnimation * (this.enabled ? 1.0 : 0.5);
    ctx.translate(-centerX, -centerY);
    
    // 渲染背景
    this._renderBackground(ctx);
    
    // 渲染边框
    this._renderBorder(ctx);
    
    // 渲染文本
    this._renderText(ctx);
    
    // 渲染状态指示器
    if (this.pressed) {
      this._renderPressedOverlay(ctx);
    }
    
    ctx.restore();
  }

  /**
   * 渲染背景
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderBackground(ctx) {
    const bgColor = this._getBackgroundColor();
    
    // 圆角矩形背景
    ctx.fillStyle = bgColor;
    this._drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
    ctx.fill();
  }

  /**
   * 渲染边框
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderBorder(ctx) {
    if (this.borderWidth > 0) {
      ctx.strokeStyle = this.borderColor;
      ctx.lineWidth = this.borderWidth;
      this._drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
      ctx.stroke();
    }
  }

  /**
   * 渲染文本
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderText(ctx) {
    if (!this.text) return;

    ctx.fillStyle = this.textColor;
    ctx.font = `${this.fontSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    const textX = this.x + this.width / 2;
    const textY = this.y + this.height / 2;
    
    ctx.fillText(this.text, textX, textY);
  }

  /**
   * 渲染按压覆盖层
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderPressedOverlay(ctx) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
    this._drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
    ctx.fill();
  }

  /**
   * 绘制圆角矩形路径
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   * @private
   */
  _drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.arcTo(x + width, y, x + width, y + height, radius);
    ctx.arcTo(x + width, y + height, x, y + height, radius);
    ctx.arcTo(x, y + height, x, y, radius);
    ctx.arcTo(x, y, x + width, y, radius);
    ctx.closePath();
  }

  /**
   * 获取背景颜色
   * @returns {string} 背景颜色
   * @private
   */
  _getBackgroundColor() {
    if (!this.enabled) {
      return '#CCCCCC';
    }
    if (this.pressed) {
      return this._darkenColor(this.backgroundColor, 0.2);
    }
    if (this.hovered) {
      return this._lightenColor(this.backgroundColor, 0.1);
    }
    return this.backgroundColor;
  }

  /**
   * 加深颜色
   * @param {string} color - 原始颜色
   * @param {number} amount - 加深程度
   * @returns {string} 加深后的颜色
   * @private
   */
  _darkenColor(color, amount) {
    // 简单的颜色加深算法
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      
      const newR = Math.max(0, Math.floor(r * (1 - amount)));
      const newG = Math.max(0, Math.floor(g * (1 - amount)));
      const newB = Math.max(0, Math.floor(b * (1 - amount)));
      
      return `rgb(${newR}, ${newG}, ${newB})`;
    }
    return color;
  }

  /**
   * 提亮颜色
   * @param {string} color - 原始颜色
   * @param {number} amount - 提亮程度
   * @returns {string} 提亮后的颜色
   * @private
   */
  _lightenColor(color, amount) {
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      
      const newR = Math.min(255, Math.floor(r + (255 - r) * amount));
      const newG = Math.min(255, Math.floor(g + (255 - g) * amount));
      const newB = Math.min(255, Math.floor(b + (255 - b) * amount));
      
      return `rgb(${newR}, ${newG}, ${newB})`;
    }
    return color;
  }

  /**
   * 更新动画状态
   * @param {number} deltaTime - 时间增量
   * @private
   */
  _updateAnimations(deltaTime) {
    // 缩放动画
    const targetScale = this.pressed ? 0.95 : 1.0;
    this.scaleAnimation += (targetScale - this.scaleAnimation) * this.animationSpeed;
    
    // 透明度动画
    const targetAlpha = this.visible ? 1.0 : 0.0;
    this.alphaAnimation += (targetAlpha - this.alphaAnimation) * this.animationSpeed;
  }

  // =================== 交互方法 ===================

  /**
   * 检查点是否在按钮内
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @returns {boolean} 是否在按钮内
   */
  containsPoint(x, y) {
    return x >= this.x && x <= this.x + this.width &&
           y >= this.y && y <= this.y + this.height;
  }

  /**
   * 处理按钮按下
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onPointerDown(x, y) {
    if (!this.enabled || !this.visible || !this.containsPoint(x, y)) {
      return false;
    }

    this.pressed = true;
    this.pressStartTime = Date.now();
    this.isLongPressed = false;
    
    return true;
  }

  /**
   * 处理按钮释放
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onPointerUp(x, y) {
    if (!this.pressed) return false;

    const wasPressed = this.pressed;
    this.pressed = false;
    
    // 如果释放点在按钮内且没有长按，触发点击
    if (this.containsPoint(x, y) && !this.isLongPressed && this.clickHandler) {
      this.clickHandler(this);
    }
    
    this.isLongPressed = false;
    return wasPressed;
  }

  /**
   * 处理鼠标移动/触摸移动
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  onPointerMove(x, y) {
    const wasHovered = this.hovered;
    this.hovered = this.containsPoint(x, y);
    
    // 如果正在按压但移出按钮，取消按压
    if (this.pressed && !this.hovered) {
      this.pressed = false;
      this.isLongPressed = false;
    }
  }

  // =================== 配置方法 ===================

  /**
   * 设置位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
  }

  /**
   * 设置尺寸
   * @param {number} width - 宽度
   * @param {number} height - 高度
   */
  setSize(width, height) {
    this.width = width;
    this.height = height;
  }

  /**
   * 设置文本
   * @param {string} text - 按钮文本
   */
  setText(text) {
    this.text = text;
  }

  /**
   * 设置启用状态
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled;
    if (!enabled) {
      this.pressed = false;
      this.hovered = false;
    }
  }

  /**
   * 设置可见性
   * @param {boolean} visible - 是否可见
   */
  setVisible(visible) {
    this.visible = visible;
    if (!visible) {
      this.pressed = false;
      this.hovered = false;
    }
  }

  /**
   * 获取按钮区域
   * @returns {Object} 按钮区域信息
   */
  getBounds() {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height,
      left: this.x,
      top: this.y,
      right: this.x + this.width,
      bottom: this.y + this.height
    };
  }
} 