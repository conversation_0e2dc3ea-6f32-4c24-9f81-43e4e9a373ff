let instance;

/**
 * 统一的音效管理器
 */
export default class Music {
  constructor() {
    if (instance) return instance;
    instance = this;

    // 检查微信小游戏环境
    this.isWechatGame = typeof wx !== 'undefined' && wx.createInnerAudioContext;

    if (!this.isWechatGame) {
      console.warn('当前环境不支持微信小游戏音频API，音效将被禁用');
      this._initDummyAudio();
      return;
    }

    // 创建音频上下文
    this.bgmAudio = this._createAudioContext('背景音乐');
    this.shootAudio = this._createAudioContext('射击音效');
    this.boomAudio = this._createAudioContext('爆炸音效');
    this.moveAudio = this._createAudioContext('移动音效');
    this.victoryAudio = this._createAudioContext('胜利音效');
    this.gameOverAudio = this._createAudioContext('游戏结束音效');
    this.buzzAudio = this._createAudioContext('提示音效');

    // 道具音效
    this.fireballAudio = this._createAudioContext('火球术音效');
    this.lightningAudio = this._createAudioContext('闪电链音效');
    this.torrentAudio = this._createAudioContext('激流音效');
    this.earthquakeAudio = this._createAudioContext('地震术音效');

    // 新增音效
    this.eliminateAudio = this._createAudioContext('消除音效');
    this.rotateAudio = this._createAudioContext('旋转音效');

    this._initializeAudio();
  }

  /**
   * 创建音频上下文
   * @param {string} name - 音频名称
   * @returns {InnerAudioContext|Object} 音频上下文或虚拟对象
   * @private
   */
  _createAudioContext(name) {
    try {
      const audio = wx.createInnerAudioContext();
      console.log(`${name}音频上下文创建成功`);
      return audio;
    } catch (error) {
      console.error(`${name}音频上下文创建失败:`, error);
      return this._createDummyAudio();
    }
  }

  /**
   * 创建虚拟音频对象（用于不支持音频的环境）
   * @returns {Object} 虚拟音频对象
   * @private
   */
  _createDummyAudio() {
    return {
      src: '',
      currentTime: 0,
      loop: false,
      play: () => {},
      stop: () => {},
      onError: () => {}
    };
  }

  /**
   * 初始化虚拟音频（用于不支持音频的环境）
   * @private
   */
  _initDummyAudio() {
    const dummyAudio = this._createDummyAudio();
    this.bgmAudio = dummyAudio;
    this.shootAudio = dummyAudio;
    this.boomAudio = dummyAudio;
    this.moveAudio = dummyAudio;
    this.victoryAudio = dummyAudio;
    this.gameOverAudio = dummyAudio;
    this.buzzAudio = dummyAudio;
    this.fireballAudio = dummyAudio;
    this.lightningAudio = dummyAudio;
    this.torrentAudio = dummyAudio;
    this.eliminateAudio = dummyAudio;
    this.rotateAudio = dummyAudio;
  }

  /**
   * 初始化音频设置
   * @private
   */
  _initializeAudio() {
    if (!this.isWechatGame) {
      console.log('跳过音频初始化（非微信小游戏环境）');
      return;
    }

    this.bgmAudio.loop = true; // 背景音乐循环播放
    // this.bgmAudio.autoplay = true; // 背景音乐自动播放

    // 安全设置音频文件路径
    this._setAudioSrc(this.bgmAudio, 'audio/bgm.mp3', '背景音乐');
    this._setAudioSrc(this.shootAudio, 'audio/bullet.mp3', '射击音效');
    this._setAudioSrc(this.boomAudio, 'audio/boom1.mp3', '爆炸音效');

    // 使用已有音频替代缺失的音频
    this._setAudioSrc(this.moveAudio, 'audio/移动.mp3', '移动音效');
    this._setAudioSrc(this.victoryAudio, 'audio/bgm.mp3', '胜利音效');
    this._setAudioSrc(this.gameOverAudio, 'audio/boom1.mp3', '游戏结束音效');
    this._setAudioSrc(this.buzzAudio, 'audio/boom1.mp3', '提示音效');

    // 道具音效
    this._setAudioSrc(this.fireballAudio, 'audio/火球术.mp3', '火球术音效');
    this._setAudioSrc(this.lightningAudio, 'audio/闪电链.mp3', '闪电链音效');
    this._setAudioSrc(this.torrentAudio, 'audio/激流.mp3', '激流音效');
    this._setAudioSrc(this.earthquakeAudio, 'audio/地震术.mp3', '地震术音效');

    // 新增音效
    this._setAudioSrc(this.eliminateAudio, 'audio/消除.mp3', '消除音效');
    this._setAudioSrc(this.rotateAudio, 'audio/旋转.mp3', '旋转音效');

    // 设置错误处理
    const handleAudioError = (name, audio) => {
      if (audio && audio.onError) {
        audio.onError((err) => {
          console.error(`播放${name}音效失败:`, err);
        });
      }
    };

    // 为所有音频添加错误处理
    handleAudioError('背景', this.bgmAudio);
    handleAudioError('射击', this.shootAudio);
    handleAudioError('爆炸', this.boomAudio);
    handleAudioError('移动', this.moveAudio);
    handleAudioError('胜利', this.victoryAudio);
    handleAudioError('游戏结束', this.gameOverAudio);
    handleAudioError('提示', this.buzzAudio);
    handleAudioError('火球术', this.fireballAudio);
    handleAudioError('闪电链', this.lightningAudio);
    handleAudioError('激流', this.torrentAudio);
    handleAudioError('地震术', this.earthquakeAudio);
    handleAudioError('消除', this.eliminateAudio);
    handleAudioError('旋转', this.rotateAudio);
  }

  /**
   * 安全设置音频源
   * @param {InnerAudioContext} audio - 音频上下文
   * @param {string} src - 音频文件路径
   * @param {string} name - 音频名称（用于日志）
   * @private
   */
  _setAudioSrc(audio, src, name) {
    if (!this.isWechatGame || !audio) {
      console.log(`跳过${name}音频源设置（非微信小游戏环境或音频对象无效）`);
      return;
    }

    try {
      // 检查音频文件是否存在（在微信小游戏中，这个检查可能不可靠）
      audio.src = src;
      console.log(`${name}音频源设置成功: ${src}`);

      // 添加加载成功回调
      if (audio.onCanplay) {
        audio.onCanplay(() => {
          console.log(`${name}音频加载完成: ${src}`);
        });
      }

    } catch (error) {
      console.error(`${name}音频源设置失败: ${src}`, error);

      // 如果设置失败，尝试使用备用音频源
      if (src !== 'audio/boom1.mp3') {
        try {
          audio.src = 'audio/boom1.mp3';
          console.log(`${name}使用备用音频源: audio/boom1.mp3`);
        } catch (fallbackError) {
          console.error(`${name}备用音频源也设置失败`, fallbackError);
          // 最后的备用方案：设置为空字符串
          audio.src = '';
        }
      }
    }
  }

  /**
   * 安全播放音频的通用方法
   * @param {InnerAudioContext} audio - 音频上下文
   * @param {string} name - 音频名称（用于日志）
   */
  _safePlay(audio, name) {
    try {
      // 检查音频对象是否有效
      if (!audio || !audio.src) {
        console.warn(`${name}音频对象无效或未设置音频源`);
        return;
      }

      // 重置播放位置并播放
      audio.currentTime = 0;
      audio.play();
      // console.log(`${name}音效播放成功`);
    } catch (e) {
      console.error(`播放${name}音效失败:`, e);

      // 失败时尝试使用爆炸音效（我们确定存在的音效）
      if (audio !== this.boomAudio && this.boomAudio && this.boomAudio.src) {
        try {
          this.boomAudio.currentTime = 0;
          this.boomAudio.play();
          // console.log(`${name}使用备用音效播放成功`);
        } catch (e2) {
          console.error('备用音效也失败了:', e2);
        }
      }
    }
  }

  /**
   * 带备用音效的安全播放方法
   * @param {InnerAudioContext} primaryAudio - 主要音频
   * @param {InnerAudioContext} fallbackAudio - 备用音频
   * @param {string} name - 音频名称
   */
  _safePlayWithFallback(primaryAudio, fallbackAudio, name) {
    try {
      primaryAudio.currentTime = 0;
      primaryAudio.play();
    } catch (e) {
      console.warn(`播放${name}音效失败，使用备用音效:`, e);
      this._safePlay(fallbackAudio, '备用');
    }
  }

  /**
   * 检查音频系统状态
   * @returns {Object} 音频系统状态信息
   */
  getAudioStatus() {
    return {
      isWechatGame: this.isWechatGame,
      audioContextsCreated: !!(this.bgmAudio && this.shootAudio && this.boomAudio),
      audioSourcesSet: !!(this.bgmAudio && this.bgmAudio.src),
      supportedFeatures: {
        createInnerAudioContext: typeof wx !== 'undefined' && !!wx.createInnerAudioContext,
        onCanplay: this.bgmAudio && typeof this.bgmAudio.onCanplay === 'function',
        onError: this.bgmAudio && typeof this.bgmAudio.onError === 'function'
      }
    };
  }

  /**
   * 统一的音效播放接口
   * @param {string} effectType - 音效类型
   * @param {Object} options - 播放选项
   */
  playEffect(effectType, options = {}) {
    // 如果不是微信小游戏环境，直接返回
    if (!this.isWechatGame) {
      console.log(`跳过音效播放: ${effectType}（非微信小游戏环境）`);
      return;
    }
    switch (effectType) {
      case 'explosion':
      case 'boom':
        this.playExplosion();
        break;
      case 'eliminate':
      case 'clear':
        this.playEliminate();
        break;
      case 'rotate':
        this.playRotate();
        break;
      case 'shoot':
      case 'bullet':
        this.playShoot();
        break;
      case 'move':
        this.playMove();
        break;
      case 'freeze':
      case 'frozen':
        // 冰冻音效使用移动音效
        this.playMove();
        break;
      case 'fireball':
        this.playFireball();
        break;
      case 'lightning':
        this.playLightning();
        break;
      case 'torrent':
        this.playTorrent();
        break;
      case 'earthquake':
        this.playEarthquake();
        break;
      case 'buzz':
      case 'error':
        this.playBuzz();
        break;
      case 'victory':
        this.playVictory();
        break;
      case 'gameover':
        this.playGameOver();
        break;
      default:
        console.warn(`未知音效类型: ${effectType}`);
        this.playBuzz(); // 默认播放提示音
    }
  }

  playShoot() {
    this._safePlay(this.shootAudio, '射击');
  }

  playExplosion() {
    this._safePlay(this.boomAudio, '爆炸');
  }

  playMove() {
    this._safePlay(this.moveAudio, '移动');
  }

  playVictory() {
    this._safePlay(this.victoryAudio, '胜利');
  }

  playGameOver() {
    this._safePlay(this.gameOverAudio, '游戏结束');
  }

  /**
   * 播放道具不可用或操作失败的提示音
   */
  playBuzz() {
    this._safePlay(this.buzzAudio, '提示');
  }

  /**
   * 播放冰冻音效（使用移动音效代替）
   */
  playFreeze() {
    this.playMove();
  }

  /**
   * 播放火球术音效
   */
  playFireball() {
    this._safePlay(this.fireballAudio, '火球术');
  }

  /**
   * 播放闪电链音效
   */
  playLightning() {
    this._safePlay(this.lightningAudio, '闪电链');
  }

  /**
   * 播放激流音效
   */
  playTorrent() {
    this._safePlay(this.torrentAudio, '激流');
  }

  /**
   * 播放地震术音效
   */
  playEarthquake() {
    this._safePlay(this.earthquakeAudio, '地震术');
  }

  /**
   * 播放消除音效
   */
  playEliminate() {
    this._safePlay(this.eliminateAudio, '消除');
  }

  /**
   * 播放旋转音效
   */
  playRotate() {
    this._safePlay(this.rotateAudio, '旋转');
  }

  playBgm() {
    this._safePlay(this.bgmAudio, '背景');
  }

  stopBgm() {
    try {
      this.bgmAudio.stop();
    } catch (e) {
      console.error('停止背景音乐失败:', e);
    }
  }
}
