/**
 * 进度和奖励系统管理器
 * 负责每日任务、签到奖励、成就系统等
 */
import Emitter from '../libs/tinyemitter.js';
import { ITEM_TYPES } from '../item/refactored-item-manager.js';

// 任务类型
export const TASK_TYPES = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  ACHIEVEMENT: 'achievement'
};

// 奖励类型
export const REWARD_TYPES = {
  COINS: 'coins',
  GEMS: 'gems',
  ITEMS: 'items',
  EXPERIENCE: 'experience'
};

export default class ProgressionManager extends Emitter {
  constructor() {
    super();
    
    // 玩家数据
    this.playerData = {
      level: 1,
      experience: 0,
      coins: 1000,        // 初始金币
      gems: 10,           // 初始宝石
      totalPlayTime: 0,
      gamesPlayed: 0,
      highestScore: 0,
      totalScore: 0
    };
    
    // 每日任务
    this.dailyTasks = [];
    this.weeklyTasks = [];
    this.achievements = [];
    
    // 签到系统
    this.checkInData = {
      consecutiveDays: 0,
      lastCheckIn: null,
      totalCheckIns: 0
    };
    
    // 道具库存
    this.itemInventory = {
      [ITEM_TYPES.FIREBALL]: 5,
      [ITEM_TYPES.LIGHTNING]: 3,
      [ITEM_TYPES.WATERFLOW]: 2,
      [ITEM_TYPES.EARTHQUAKE]: 1  // 地震术初始库存
    };
    
    // 初始化任务和成就
    this._initTasks();
    this._initAchievements();
    
    // 加载数据
    this._loadData();
  }
  
  /**
   * 初始化每日和每周任务
   * @private
   */
  _initTasks() {
    // 每日任务模板
    this.dailyTaskTemplates = [
      {
        id: 'play_games',
        name: '游戏达人',
        description: '完成3局游戏',
        type: TASK_TYPES.DAILY,
        target: 3,
        rewards: [
          { type: REWARD_TYPES.COINS, amount: 100 },
          { type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.FIREBALL, amount: 2 }
        ]
      },
      {
        id: 'score_points',
        name: '得分高手',
        description: '单局得分达到5000分',
        type: TASK_TYPES.DAILY,
        target: 5000,
        rewards: [
          { type: REWARD_TYPES.COINS, amount: 150 },
          { type: REWARD_TYPES.EXPERIENCE, amount: 50 }
        ]
      },
      {
        id: 'use_items',
        name: '道具专家',
        description: '使用道具5次',
        type: TASK_TYPES.DAILY,
        target: 5,
        rewards: [
          { type: REWARD_TYPES.GEMS, amount: 2 },
          { type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.LIGHTNING, amount: 1 }
        ]
      },
      {
        id: 'clear_blocks',
        name: '消除大师',
        description: '消除100个方块',
        type: TASK_TYPES.DAILY,
        target: 100,
        rewards: [
          { type: REWARD_TYPES.COINS, amount: 200 },
          { type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.WATERFLOW, amount: 1 }
        ]
      }
    ];
    
    // 每周任务模板
    this.weeklyTaskTemplates = [
      {
        id: 'weekly_score',
        name: '周度挑战',
        description: '本周累计得分达到50000分',
        type: TASK_TYPES.WEEKLY,
        target: 50000,
        rewards: [
          { type: REWARD_TYPES.COINS, amount: 1000 },
          { type: REWARD_TYPES.GEMS, amount: 10 },
          { type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.FIREBALL, amount: 10 }
        ]
      },
      {
        id: 'weekly_levels',
        name: '关卡征服者',
        description: '本周通过15个关卡',
        type: TASK_TYPES.WEEKLY,
        target: 15,
        rewards: [
          { type: REWARD_TYPES.GEMS, amount: 15 },
          { type: REWARD_TYPES.EXPERIENCE, amount: 500 }
        ]
      }
    ];
  }
  
  /**
   * 初始化成就系统
   * @private
   */
  _initAchievements() {
    this.achievementTemplates = [
      {
        id: 'first_game',
        name: '初次体验',
        description: '完成第一局游戏',
        target: 1,
        rewards: [
          { type: REWARD_TYPES.COINS, amount: 500 },
          { type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.FIREBALL, amount: 5 }
        ]
      },
      {
        id: 'score_master',
        name: '得分大师',
        description: '单局得分达到20000分',
        target: 20000,
        rewards: [
          { type: REWARD_TYPES.GEMS, amount: 20 },
          { type: REWARD_TYPES.EXPERIENCE, amount: 200 }
        ]
      },
      {
        id: 'combo_king',
        name: '连击之王',
        description: '达到10连击',
        target: 10,
        rewards: [
          { type: REWARD_TYPES.COINS, amount: 1000 },
          { type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.LIGHTNING, amount: 5 }
        ]
      },
      {
        id: 'level_conqueror',
        name: '关卡征服者',
        description: '通过50个关卡',
        target: 50,
        rewards: [
          { type: REWARD_TYPES.GEMS, amount: 50 },
          { type: REWARD_TYPES.EXPERIENCE, amount: 1000 }
        ]
      }
    ];
  }
  
  /**
   * 每日签到
   * @returns {Object} 签到奖励
   */
  dailyCheckIn() {
    const today = new Date().toDateString();
    const lastCheckIn = this.checkInData.lastCheckIn;
    
    // 检查是否已经签到
    if (lastCheckIn === today) {
      return { success: false, reason: 'already_checked_in' };
    }
    
    // 检查连续签到
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toDateString();
    
    if (lastCheckIn === yesterdayStr) {
      this.checkInData.consecutiveDays++;
    } else {
      this.checkInData.consecutiveDays = 1;
    }
    
    this.checkInData.lastCheckIn = today;
    this.checkInData.totalCheckIns++;
    
    // 计算签到奖励
    const rewards = this._calculateCheckInRewards(this.checkInData.consecutiveDays);
    
    // 发放奖励
    this._giveRewards(rewards);
    
    // 保存数据
    this._saveData();
    
    this.emit('checkin:complete', {
      consecutiveDays: this.checkInData.consecutiveDays,
      rewards
    });
    
    return { success: true, rewards, consecutiveDays: this.checkInData.consecutiveDays };
  }
  
  /**
   * 计算签到奖励
   * @param {number} consecutiveDays - 连续签到天数
   * @returns {Array} 奖励列表
   * @private
   */
  _calculateCheckInRewards(consecutiveDays) {
    const baseRewards = [
      { type: REWARD_TYPES.COINS, amount: 100 },
      { type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.FIREBALL, amount: 1 }
    ];
    
    // 连续签到奖励
    if (consecutiveDays >= 7) {
      baseRewards.push({ type: REWARD_TYPES.GEMS, amount: 5 });
    }
    
    if (consecutiveDays >= 14) {
      baseRewards.push({ type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.LIGHTNING, amount: 3 });
    }
    
    if (consecutiveDays >= 30) {
      baseRewards.push({ type: REWARD_TYPES.GEMS, amount: 20 });
      baseRewards.push({ type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.WATERFLOW, amount: 5 });
    }
    
    return baseRewards;
  }
  
  /**
   * 更新任务进度
   * @param {string} taskType - 任务类型
   * @param {number} progress - 进度值
   */
  updateTaskProgress(taskType, progress) {
    const tasks = taskType === 'daily' ? this.dailyTasks : this.weeklyTasks;
    
    tasks.forEach(task => {
      if (!task.completed && this._isTaskRelevant(task, taskType)) {
        task.progress = Math.min(task.progress + progress, task.target);
        
        if (task.progress >= task.target) {
          this._completeTask(task);
        }
      }
    });
    
    this._saveData();
  }
  
  /**
   * 更新成就进度
   * @param {string} achievementId - 成就ID
   * @param {number} progress - 进度值
   */
  updateAchievementProgress(achievementId, progress) {
    const achievement = this.achievements.find(a => a.id === achievementId);
    
    if (achievement && !achievement.completed) {
      achievement.progress = Math.max(achievement.progress, progress);
      
      if (achievement.progress >= achievement.target) {
        this._completeAchievement(achievement);
      }
      
      this._saveData();
    }
  }
  
  /**
   * 发放奖励
   * @param {Array} rewards - 奖励列表
   * @private
   */
  _giveRewards(rewards) {
    rewards.forEach(reward => {
      switch (reward.type) {
        case REWARD_TYPES.COINS:
          this.playerData.coins += reward.amount;
          break;
        case REWARD_TYPES.GEMS:
          this.playerData.gems += reward.amount;
          break;
        case REWARD_TYPES.EXPERIENCE:
          this._addExperience(reward.amount);
          break;
        case REWARD_TYPES.ITEMS:
          this.itemInventory[reward.itemType] += reward.amount;
          break;
      }
    });
    
    this.emit('rewards:received', rewards);
  }
  
  /**
   * 添加经验值
   * @param {number} exp - 经验值
   * @private
   */
  _addExperience(exp) {
    this.playerData.experience += exp;
    
    // 检查升级
    const newLevel = Math.floor(this.playerData.experience / 1000) + 1;
    if (newLevel > this.playerData.level) {
      const oldLevel = this.playerData.level;
      this.playerData.level = newLevel;
      
      this.emit('player:level_up', {
        oldLevel,
        newLevel,
        rewards: this._getLevelUpRewards(newLevel)
      });
    }
  }
  
  /**
   * 获取升级奖励
   * @param {number} level - 新等级
   * @returns {Array} 奖励列表
   * @private
   */
  _getLevelUpRewards(level) {
    return [
      { type: REWARD_TYPES.COINS, amount: level * 100 },
      { type: REWARD_TYPES.GEMS, amount: Math.floor(level / 5) + 1 },
      { type: REWARD_TYPES.ITEMS, itemType: ITEM_TYPES.FIREBALL, amount: 3 }
    ];
  }
  
  /**
   * 完成任务
   * @param {Object} task - 任务对象
   * @private
   */
  _completeTask(task) {
    task.completed = true;
    this._giveRewards(task.rewards);
    
    this.emit('task:complete', task);
  }
  
  /**
   * 完成成就
   * @param {Object} achievement - 成就对象
   * @private
   */
  _completeAchievement(achievement) {
    achievement.completed = true;
    this._giveRewards(achievement.rewards);
    
    this.emit('achievement:unlock', achievement);
  }
  
  /**
   * 检查任务是否相关
   * @param {Object} task - 任务对象
   * @param {string} context - 上下文
   * @returns {boolean} 是否相关
   * @private
   */
  _isTaskRelevant(task, context) {
    // 这里可以添加更复杂的逻辑来判断任务是否与当前操作相关
    return true;
  }
  
  /**
   * 生成今日任务
   */
  generateDailyTasks() {
    const today = new Date().toDateString();
    
    // 检查是否已经生成过今日任务
    if (this.dailyTasks.length > 0 && this.dailyTasks[0].date === today) {
      return;
    }
    
    // 随机选择3个每日任务
    const shuffled = [...this.dailyTaskTemplates].sort(() => 0.5 - Math.random());
    this.dailyTasks = shuffled.slice(0, 3).map(template => ({
      ...template,
      progress: 0,
      completed: false,
      date: today
    }));
    
    this._saveData();
    this.emit('tasks:daily_generated', this.dailyTasks);
  }
  
  /**
   * 保存数据
   * @private
   */
  _saveData() {
    try {
      // 创建安全的数据副本，移除不可序列化的属性
      const data = {
        playerData: this._sanitizeObject(this.playerData),
        checkInData: this._sanitizeObject(this.checkInData),
        itemInventory: this._sanitizeObject(this.itemInventory),
        dailyTasks: this._sanitizeArray(this.dailyTasks),
        weeklyTasks: this._sanitizeArray(this.weeklyTasks),
        achievements: this._sanitizeArray(this.achievements)
      };

      // 验证数据是否可以序列化
      const jsonString = this._safeStringify(data);
      if (jsonString === null) {
        console.error('数据序列化失败，跳过保存');
        return;
      }

      wx.setStorageSync('progressionData', jsonString);
      console.log('进度数据保存成功');
    } catch (e) {
      console.error('保存进度数据失败:', e);
      // 尝试保存基础数据
      this._saveBasicData();
    }
  }

  /**
   * 安全的JSON序列化
   * @param {Object} obj - 要序列化的对象
   * @returns {string|null} 序列化后的字符串，失败返回null
   * @private
   */
  _safeStringify(obj) {
    try {
      return JSON.stringify(obj, (key, value) => {
        // 过滤掉函数、Symbol、undefined等不可序列化的值
        if (typeof value === 'function' || typeof value === 'symbol' || value === undefined) {
          return null;
        }
        // 检查循环引用
        if (typeof value === 'object' && value !== null) {
          if (this._seenObjects && this._seenObjects.has(value)) {
            return '[Circular Reference]';
          }
          if (!this._seenObjects) {
            this._seenObjects = new WeakSet();
          }
          this._seenObjects.add(value);
        }
        return value;
      });
    } catch (e) {
      console.error('JSON序列化失败:', e);
      return null;
    } finally {
      // 清理循环引用检测器
      this._seenObjects = null;
    }
  }

  /**
   * 清理对象，移除不可序列化的属性
   * @param {Object} obj - 原始对象
   * @returns {Object} 清理后的对象
   * @private
   */
  _sanitizeObject(obj) {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'function' || typeof value === 'symbol') {
        continue; // 跳过函数和Symbol
      }
      if (typeof value === 'object' && value !== null) {
        sanitized[key] = this._sanitizeObject(value);
      } else {
        sanitized[key] = value;
      }
    }
    return sanitized;
  }

  /**
   * 清理数组，移除不可序列化的元素
   * @param {Array} arr - 原始数组
   * @returns {Array} 清理后的数组
   * @private
   */
  _sanitizeArray(arr) {
    if (!Array.isArray(arr)) {
      return arr;
    }

    return arr.map(item => {
      if (typeof item === 'object' && item !== null) {
        return this._sanitizeObject(item);
      }
      return item;
    }).filter(item =>
      typeof item !== 'function' &&
      typeof item !== 'symbol' &&
      item !== undefined
    );
  }

  /**
   * 保存基础数据（降级处理）
   * @private
   */
  _saveBasicData() {
    try {
      const basicData = {
        playerData: {
          level: this.playerData.level || 1,
          experience: this.playerData.experience || 0,
          coins: this.playerData.coins || 1000,
          gems: this.playerData.gems || 10
        },
        checkInData: {
          consecutiveDays: this.checkInData.consecutiveDays || 0,
          lastCheckIn: this.checkInData.lastCheckIn || null,
          totalCheckIns: this.checkInData.totalCheckIns || 0
        }
      };

      wx.setStorageSync('progressionData', JSON.stringify(basicData));
      console.log('基础进度数据保存成功');
    } catch (e) {
      console.error('保存基础数据也失败:', e);
    }
  }
  
  /**
   * 加载数据
   * @private
   */
  _loadData() {
    try {
      const data = wx.getStorageSync('progressionData');

      if (data) {
        const parsed = this._safeParseJSON(data);
        if (parsed) {
          this._mergeLoadedData(parsed);
          console.log('进度数据加载成功');
        } else {
          console.warn('数据解析失败，使用默认数据');
          this._initializeDefaultData();
        }
      } else {
        console.log('没有找到保存的数据，使用默认数据');
        this._initializeDefaultData();
      }
    } catch (e) {
      console.error('加载进度数据失败:', e);
      this._initializeDefaultData();
    }
  }

  /**
   * 安全的JSON解析
   * @param {string} jsonString - JSON字符串
   * @returns {Object|null} 解析后的对象，失败返回null
   * @private
   */
  _safeParseJSON(jsonString) {
    try {
      if (typeof jsonString !== 'string') {
        console.warn('数据不是字符串类型:', typeof jsonString);
        return null;
      }
      return JSON.parse(jsonString);
    } catch (e) {
      console.error('JSON解析失败:', e);
      return null;
    }
  }

  /**
   * 合并加载的数据
   * @param {Object} parsed - 解析后的数据
   * @private
   */
  _mergeLoadedData(parsed) {
    // 安全地合并玩家数据
    if (parsed.playerData && typeof parsed.playerData === 'object') {
      this.playerData = { ...this.playerData, ...this._validatePlayerData(parsed.playerData) };
    }

    // 安全地合并签到数据
    if (parsed.checkInData && typeof parsed.checkInData === 'object') {
      this.checkInData = { ...this.checkInData, ...this._validateCheckInData(parsed.checkInData) };
    }

    // 安全地合并道具库存
    if (parsed.itemInventory && typeof parsed.itemInventory === 'object') {
      this.itemInventory = { ...this.itemInventory, ...this._validateItemInventory(parsed.itemInventory) };
    }

    // 安全地加载任务数据
    this.dailyTasks = this._validateTaskArray(parsed.dailyTasks) || [];
    this.weeklyTasks = this._validateTaskArray(parsed.weeklyTasks) || [];

    // 安全地加载成就数据
    this.achievements = this._validateAchievements(parsed.achievements);
  }

  /**
   * 验证玩家数据
   * @param {Object} data - 玩家数据
   * @returns {Object} 验证后的数据
   * @private
   */
  _validatePlayerData(data) {
    const validated = {};

    validated.level = typeof data.level === 'number' && data.level > 0 ? data.level : 1;
    validated.experience = typeof data.experience === 'number' && data.experience >= 0 ? data.experience : 0;
    validated.coins = typeof data.coins === 'number' && data.coins >= 0 ? data.coins : 1000;
    validated.gems = typeof data.gems === 'number' && data.gems >= 0 ? data.gems : 10;
    validated.totalPlayTime = typeof data.totalPlayTime === 'number' && data.totalPlayTime >= 0 ? data.totalPlayTime : 0;
    validated.gamesPlayed = typeof data.gamesPlayed === 'number' && data.gamesPlayed >= 0 ? data.gamesPlayed : 0;
    validated.highestScore = typeof data.highestScore === 'number' && data.highestScore >= 0 ? data.highestScore : 0;
    validated.totalScore = typeof data.totalScore === 'number' && data.totalScore >= 0 ? data.totalScore : 0;

    return validated;
  }

  /**
   * 验证签到数据
   * @param {Object} data - 签到数据
   * @returns {Object} 验证后的数据
   * @private
   */
  _validateCheckInData(data) {
    const validated = {};

    validated.consecutiveDays = typeof data.consecutiveDays === 'number' && data.consecutiveDays >= 0 ? data.consecutiveDays : 0;
    validated.lastCheckIn = typeof data.lastCheckIn === 'string' ? data.lastCheckIn : null;
    validated.totalCheckIns = typeof data.totalCheckIns === 'number' && data.totalCheckIns >= 0 ? data.totalCheckIns : 0;

    return validated;
  }

  /**
   * 验证道具库存
   * @param {Object} data - 道具库存数据
   * @returns {Object} 验证后的数据
   * @private
   */
  _validateItemInventory(data) {
    const validated = {};

    for (const [itemType, count] of Object.entries(data)) {
      if (typeof count === 'number' && count >= 0) {
        validated[itemType] = count;
      }
    }

    return validated;
  }

  /**
   * 验证任务数组
   * @param {Array} tasks - 任务数组
   * @returns {Array|null} 验证后的数组
   * @private
   */
  _validateTaskArray(tasks) {
    if (!Array.isArray(tasks)) {
      return null;
    }

    return tasks.filter(task =>
      task &&
      typeof task === 'object' &&
      typeof task.id === 'string' &&
      typeof task.progress === 'number' &&
      typeof task.completed === 'boolean'
    );
  }

  /**
   * 验证成就数据
   * @param {Array} achievements - 成就数组
   * @returns {Array} 验证后的成就数组
   * @private
   */
  _validateAchievements(achievements) {
    if (Array.isArray(achievements) && achievements.length > 0) {
      const validated = achievements.filter(achievement =>
        achievement &&
        typeof achievement === 'object' &&
        typeof achievement.id === 'string'
      );

      if (validated.length > 0) {
        return validated;
      }
    }

    // 如果没有有效的成就数据，初始化默认成就
    return this.achievementTemplates.map(template => ({
      ...template,
      progress: 0,
      completed: false
    }));
  }

  /**
   * 初始化默认数据
   * @private
   */
  _initializeDefaultData() {
    // 重置为默认值
    this.playerData = {
      level: 1,
      experience: 0,
      coins: 1000,
      gems: 10,
      totalPlayTime: 0,
      gamesPlayed: 0,
      highestScore: 0,
      totalScore: 0
    };

    this.checkInData = {
      consecutiveDays: 0,
      lastCheckIn: null,
      totalCheckIns: 0
    };

    this.itemInventory = {
      [ITEM_TYPES.FIREBALL]: 5,
      [ITEM_TYPES.LIGHTNING]: 3,
      [ITEM_TYPES.WATERFLOW]: 2,
      [ITEM_TYPES.EARTHQUAKE]: 1
    };

    this.dailyTasks = [];
    this.weeklyTasks = [];

    // 初始化成就
    this.achievements = this.achievementTemplates.map(template => ({
      ...template,
      progress: 0,
      completed: false
    }));

    console.log('默认数据初始化完成');
  }
}
