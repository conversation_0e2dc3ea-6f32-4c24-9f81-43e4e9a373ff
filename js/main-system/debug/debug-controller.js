import { setDebugMode, isDebugMode, toggleDebugMode } from '../../config/game-config.js';

/**
 * 调试控制器 - 统一调试功能管理
 * 职责：调试模式控制、性能监控、调试命令、测试功能
 */
export class DebugController {
  constructor(eventBus, systemManager) {
    this.eventBus = eventBus;
    this.systemManager = systemManager;
    this.isInitialized = false;
    
    // 调试状态
    this.debugState = {
      showFPS: false,
      showGrid: false,
      showTouchAreas: false,
      showPerformance: false,
      showSystemInfo: false
    };
    
    // 性能数据
    this.performanceData = {
      fps: 0,
      frameTime: 0,
      updateTime: 0,
      renderTime: 0,
      memoryUsage: 0
    };
    
    this.initializeCommands();
  }

  /**
   * 初始化调试控制器
   */
  initialize() {
    if (this.isInitialized) {
      console.warn('⚠️ 调试控制器已初始化');
      return;
    }

    console.log('🐛 初始化调试控制器');
    
    this.setupGlobalDebugFunctions();
    this.registerEventListeners();
    
    this.isInitialized = true;
    console.log('✅ 调试控制器初始化完成');
  }

  /**
   * 设置全局调试函数
   */
  setupGlobalDebugFunctions() {
    const globalScope = typeof window !== 'undefined' ? window : global;
    
    // 基础调试函数
    globalScope.toggleDebugMode = toggleDebugMode;
    globalScope.setDebugMode = setDebugMode;
    globalScope.isDebugMode = isDebugMode;
    
    // 系统调试函数
    globalScope.getSystemInfo = () => this.getSystemInfo();
    globalScope.getPerformanceStats = () => this.getPerformanceStats();
    
    // 关卡解锁测试函数
    globalScope.unlockLevels = (count) => this.unlockLevelsForTesting(count);
    
    console.log('🔧 调试函数已暴露到全局:');
    console.log('- toggleDebugMode(): 切换调试模式');
    console.log('- getSystemInfo(): 获取系统信息');
    console.log('- unlockLevels(count): 解锁关卡');
  }

  /**
   * 注册事件监听
   */
  registerEventListeners() {
    // 监听游戏循环事件
    this.eventBus.on('gameloop:fps-update', (fps) => {
      this.performanceData.fps = fps;
    });
    
    this.eventBus.on('gameloop:render-debug', (data) => {
      Object.assign(this.performanceData, data);
    });
  }

  /**
   * 初始化调试命令
   */
  initializeCommands() {
    // 这里可以初始化调试命令
    console.log('🔧 调试命令已初始化');
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    const systemManager = this.systemManager;
    if (!systemManager) {
      return { error: '系统管理器未初始化' };
    }

    return {
      systems: systemManager.getDebugInfo(),
      performance: this.getPerformanceStats(),
      debugMode: isDebugMode(),
      timestamp: Date.now()
    };
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      ...this.performanceData,
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if (typeof performance !== 'undefined' && performance.memory) {
      return {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      };
    }
    return null;
  }

  /**
   * 关卡解锁测试
   */
  unlockLevelsForTesting(count) {
    const levelManager = this.systemManager?.getSystem('LevelManager');
    if (levelManager && typeof levelManager.unlockLevelsForTesting === 'function') {
      levelManager.unlockLevelsForTesting(count);
      console.log(`🔓 已解锁 ${count} 个关卡`);
      return true;
    } else {
      console.error('❌ 关卡管理器未初始化或方法不存在');
      return false;
    }
  }

  /**
   * 渲染调试信息
   */
  render(ctx) {
    if (!isDebugMode()) return;

    // 渲染FPS
    if (this.debugState.showFPS) {
      this.renderFPS(ctx);
    }
  }

  /**
   * 渲染FPS信息
   */
  renderFPS(ctx) {
    ctx.save();
    ctx.fillStyle = '#00FF00';
    ctx.font = '16px monospace';
    ctx.fillText(`FPS: ${this.performanceData.fps}`, 10, 30);
    ctx.restore();
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      isInitialized: this.isInitialized,
      debugMode: isDebugMode(),
      debugState: { ...this.debugState },
      performanceData: { ...this.performanceData }
    };
  }

  /**
   * 销毁调试控制器
   */
  destroy() {
    console.log('🗑️ 调试控制器已销毁');
  }
} 