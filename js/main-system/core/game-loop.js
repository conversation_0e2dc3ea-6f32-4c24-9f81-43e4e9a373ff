/**
 * 游戏循环管理器 - 统一的帧更新和渲染
 * 职责：游戏循环控制、帧率管理、性能监控
 */
export class GameLoop {
  constructor(eventBus) {
    this.eventBus = eventBus;
    this.isRunning = false;
    this.animationId = null;
    this.lastTime = 0;
    this.deltaTime = 0;
    this.frameCount = 0;
    this.fps = 0;
    this.fpsUpdateTime = 0;
    
    // 性能监控
    this.performanceStats = {
      frameTime: 0,
      updateTime: 0,
      renderTime: 0,
      avgFrameTime: 0,
      maxFrameTime: 0,
      minFrameTime: Infinity
    };
    
    // 帧率限制
    this.targetFPS = 60;
    this.frameInterval = 1000 / this.targetFPS;
    this.useFrameLimit = false;
    
    // 时间管理
    this.timeScale = 1.0;
    this.pausedTime = 0;
    this.isPaused = false;
    
    // 获取Canvas上下文
    if (!GameGlobal || !GameGlobal.canvas) {
      console.warn('⚠️ GameGlobal.canvas 未初始化，创建虚拟canvas');
      // 创建虚拟canvas作为后备
      this.ctx = {
        clearRect: () => {},
        fillRect: () => {},
        drawImage: () => {},
        fillText: () => {},
        save: () => {},
        restore: () => {},
        translate: () => {},
        scale: () => {},
        rotate: () => {},
        canvas: { width: 750, height: 1334 }
      };
    } else {
      this.ctx = GameGlobal.canvas.getContext('2d');
    }
    
    this.bindMethods();
  }

  /**
   * 绑定方法到正确的上下文
   */
  bindMethods() {
    this.loop = this.loop.bind(this);
    this.update = this.update.bind(this);
    this.render = this.render.bind(this);
  }

  /**
   * 开始游戏循环
   */
  start() {
    if (this.isRunning) {
      console.warn('⚠️ 游戏循环已在运行中');
      return;
    }

    console.log('🔄 启动游戏循环');
    this.isRunning = true;
    this.lastTime = performance.now();
    this.animationId = requestAnimationFrame(this.loop);
    
    // 发布循环开始事件
    this.eventBus.emit('gameloop:started');
  }

  /**
   * 停止游戏循环
   */
  stop() {
    if (!this.isRunning) {
      console.warn('⚠️ 游戏循环未在运行');
      return;
    }

    console.log('⏹️ 停止游戏循环');
    this.isRunning = false;
    
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    // 发布循环停止事件
    this.eventBus.emit('gameloop:stopped');
  }

  /**
   * 暂停游戏循环
   */
  pause() {
    if (this.isPaused) return;
    
    console.log('⏸️ 暂停游戏循环');
    this.isPaused = true;
    this.pausedTime = performance.now();
    
    // 发布循环暂停事件
    this.eventBus.emit('gameloop:paused');
  }

  /**
   * 恢复游戏循环
   */
  resume() {
    if (!this.isPaused) return;
    
    console.log('▶️ 恢复游戏循环');
    
    // 调整时间，避免暂停时间影响deltaTime计算
    const now = performance.now();
    const pauseDuration = now - this.pausedTime;
    this.lastTime += pauseDuration;
    
    this.isPaused = false;
    this.pausedTime = 0;
    
    // 发布循环恢复事件
    this.eventBus.emit('gameloop:resumed');
  }

  /**
   * 主游戏循环
   */
  loop(currentTime) {
    if (!this.isRunning) return;

    // 如果暂停，继续下一帧但不更新游戏逻辑
    if (this.isPaused) {
      this.animationId = requestAnimationFrame(this.loop);
      return;
    }

    // 计算时间差
    this.deltaTime = (currentTime - this.lastTime) * this.timeScale;
    
    // 帧率限制
    if (this.useFrameLimit && this.deltaTime < this.frameInterval) {
      this.animationId = requestAnimationFrame(this.loop);
      return;
    }

    // 防止过大的deltaTime（如tab切换回来）
    if (this.deltaTime > 100) {
      this.deltaTime = 16.67; // 约60fps的帧时间
    }

    const frameStartTime = performance.now();

    try {
      // 更新游戏逻辑
      const updateStartTime = performance.now();
      this.update(this.deltaTime);
      this.performanceStats.updateTime = performance.now() - updateStartTime;

      // 渲染游戏
      const renderStartTime = performance.now();
      this.render();
      this.performanceStats.renderTime = performance.now() - renderStartTime;

    } catch (error) {
      console.error('❌ 游戏循环错误:', error);
      this.eventBus.emit('gameloop:error', error);
    }

    // 更新性能统计
    this.updatePerformanceStats(frameStartTime);

    // 更新FPS
    this.updateFPS(currentTime);

    // 记录时间
    this.lastTime = currentTime;
    this.frameCount++;

    // 继续下一帧
    this.animationId = requestAnimationFrame(this.loop);
  }

  /**
   * 更新游戏逻辑
   */
  update(deltaTime) {
    // 发布更新事件，让其他系统处理
    this.eventBus.emit('gameloop:update', deltaTime);
  }

  /**
   * 渲染游戏
   */
  render() {
    // 清空画布
    this.clearCanvas();
    
    // 发布渲染事件，让其他系统处理
    this.eventBus.emit('gameloop:render', this.ctx);
    
    // 渲染性能信息（如果启用）
    this.renderPerformanceInfo();
  }

  /**
   * 清空画布
   */
  clearCanvas() {
    if (this.ctx && this.ctx.canvas) {
      this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
    }
  }

  /**
   * 更新性能统计
   */
  updatePerformanceStats(frameStartTime) {
    const frameTime = performance.now() - frameStartTime;
    
    this.performanceStats.frameTime = frameTime;
    this.performanceStats.maxFrameTime = Math.max(this.performanceStats.maxFrameTime, frameTime);
    this.performanceStats.minFrameTime = Math.min(this.performanceStats.minFrameTime, frameTime);
    
    // 计算平均帧时间（使用滑动窗口）
    const alpha = 0.1; // 平滑因子
    this.performanceStats.avgFrameTime = 
      this.performanceStats.avgFrameTime * (1 - alpha) + frameTime * alpha;
  }

  /**
   * 更新FPS计算
   */
  updateFPS(currentTime) {
    if (currentTime - this.fpsUpdateTime >= 1000) { // 每秒更新一次FPS
      this.fps = Math.round(this.frameCount * 1000 / (currentTime - this.fpsUpdateTime));
      this.frameCount = 0;
      this.fpsUpdateTime = currentTime;
      
      // 发布FPS更新事件
      this.eventBus.emit('gameloop:fps-update', this.fps);
    }
  }

  /**
   * 渲染性能信息
   */
  renderPerformanceInfo() {
    // 这里可以根据需要渲染性能信息
    // 具体实现可以通过事件系统让调试模块处理
    this.eventBus.emit('gameloop:render-debug', {
      fps: this.fps,
      frameTime: this.performanceStats.frameTime,
      avgFrameTime: this.performanceStats.avgFrameTime,
      updateTime: this.performanceStats.updateTime,
      renderTime: this.performanceStats.renderTime
    });
  }

  /**
   * 设置目标帧率
   */
  setTargetFPS(fps) {
    this.targetFPS = Math.max(1, Math.min(120, fps));
    this.frameInterval = 1000 / this.targetFPS;
    console.log(`🎯 设置目标帧率: ${this.targetFPS} FPS`);
  }

  /**
   * 启用/禁用帧率限制
   */
  setFrameLimit(enabled) {
    this.useFrameLimit = enabled;
    console.log(`🔒 帧率限制: ${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 设置时间缩放
   */
  setTimeScale(scale) {
    this.timeScale = Math.max(0, Math.min(5, scale));
    console.log(`⏱️ 时间缩放设置为: ${this.timeScale}x`);
  }

  /**
   * 获取当前FPS
   */
  getFPS() {
    return this.fps;
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return { ...this.performanceStats };
  }

  /**
   * 获取运行状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      fps: this.fps,
      targetFPS: this.targetFPS,
      timeScale: this.timeScale,
      frameCount: this.frameCount,
      useFrameLimit: this.useFrameLimit
    };
  }

  /**
   * 重置性能统计
   */
  resetPerformanceStats() {
    this.performanceStats = {
      frameTime: 0,
      updateTime: 0,
      renderTime: 0,
      avgFrameTime: 0,
      maxFrameTime: 0,
      minFrameTime: Infinity
    };
    this.frameCount = 0;
    this.fpsUpdateTime = performance.now();
    console.log('📊 性能统计已重置');
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      status: this.getStatus(),
      performance: this.getPerformanceStats(),
      animationId: this.animationId,
      deltaTime: this.deltaTime,
      lastTime: this.lastTime
    };
  }

  /**
   * 销毁游戏循环
   */
  destroy() {
    this.stop();
    this.eventBus.removeAllListeners('gameloop:update');
    this.eventBus.removeAllListeners('gameloop:render');
    this.eventBus.removeAllListeners('gameloop:fps-update');
    this.eventBus.removeAllListeners('gameloop:render-debug');
    console.log('🗑️ 游戏循环已销毁');
  }
} 