/**
 * 游戏状态管理器 - 有限状态机实现
 * 管理游戏的所有状态转换和状态数据
 */
export class GameStateManager {
  constructor(eventBus) {
    this.eventBus = eventBus;
    this.currentState = null;
    this.previousState = null;
    this.stateData = new Map();
    this.stateHistory = [];
    this.maxHistorySize = 50;
    
    // 定义所有可能的游戏状态
    this.STATES = {
      INITIALIZING: 'INITIALIZING',
      LEVEL_SELECTION: 'LEVEL_SELECTION',
      GAME_LOADING: 'GAME_LOADING',
      GAME_PLAYING: 'GAME_PLAYING', 
      GAME_PAUSED: 'GAME_PAUSED',
      GAME_OVER: 'GAME_OVER',
      LEVEL_COMPLETE: 'LEVEL_COMPLETE',
      LEVEL_FAIL: 'LEVEL_FAIL',
      MENU: 'MENU',
      SETTINGS: 'SETTINGS',
      TUTORIAL: 'TUTORIAL',
      TRANSITION: 'TRANSITION'
    };

    // 定义状态转换规则
    this.transitions = new Map([
      [this.STATES.INITIALIZING, [
        this.STATES.LEVEL_SELECTION,
        this.STATES.MENU,
        this.STATES.TUTORIAL
      ]],
      [this.STATES.LEVEL_SELECTION, [
        this.STATES.GAME_LOADING,
        this.STATES.GAME_PLAYING,
        this.STATES.MENU,
        this.STATES.SETTINGS,
        this.STATES.TUTORIAL
      ]],
      [this.STATES.GAME_LOADING, [
        this.STATES.GAME_PLAYING,
        this.STATES.LEVEL_SELECTION,
        this.STATES.GAME_OVER
      ]],
      [this.STATES.GAME_PLAYING, [
        this.STATES.GAME_PAUSED,
        this.STATES.GAME_OVER,
        this.STATES.LEVEL_COMPLETE,
        this.STATES.LEVEL_FAIL,
        this.STATES.LEVEL_SELECTION
      ]],
      [this.STATES.GAME_PAUSED, [
        this.STATES.GAME_PLAYING,
        this.STATES.LEVEL_SELECTION,
        this.STATES.MENU,
        this.STATES.GAME_OVER
      ]],
      [this.STATES.GAME_OVER, [
        this.STATES.LEVEL_SELECTION,
        this.STATES.GAME_LOADING,
        this.STATES.MENU
      ]],
      [this.STATES.LEVEL_COMPLETE, [
        this.STATES.LEVEL_SELECTION,
        this.STATES.GAME_LOADING,
        this.STATES.MENU
      ]],
      [this.STATES.LEVEL_FAIL, [
        this.STATES.LEVEL_SELECTION,
        this.STATES.GAME_LOADING,
        this.STATES.MENU
      ]],
      [this.STATES.MENU, [
        this.STATES.LEVEL_SELECTION,
        this.STATES.SETTINGS,
        this.STATES.TUTORIAL
      ]],
      [this.STATES.SETTINGS, [
        this.STATES.MENU,
        this.STATES.LEVEL_SELECTION
      ]],
      [this.STATES.TUTORIAL, [
        this.STATES.LEVEL_SELECTION,
        this.STATES.MENU,
        this.STATES.GAME_LOADING
      ]],
      [this.STATES.TRANSITION, [
        ...Object.values(this.STATES)
      ]]
    ]);

    // 状态处理器映射
    this.stateHandlers = new Map([
      [this.STATES.INITIALIZING, this.handleInitializingState.bind(this)],
      [this.STATES.LEVEL_SELECTION, this.handleLevelSelectionState.bind(this)],
      [this.STATES.GAME_LOADING, this.handleGameLoadingState.bind(this)],
      [this.STATES.GAME_PLAYING, this.handleGamePlayingState.bind(this)],
      [this.STATES.GAME_PAUSED, this.handleGamePausedState.bind(this)],
      [this.STATES.GAME_OVER, this.handleGameOverState.bind(this)],
      [this.STATES.LEVEL_COMPLETE, this.handleLevelCompleteState.bind(this)],
      [this.STATES.LEVEL_FAIL, this.handleLevelFailState.bind(this)],
      [this.STATES.MENU, this.handleMenuState.bind(this)],
      [this.STATES.SETTINGS, this.handleSettingsState.bind(this)],
      [this.STATES.TUTORIAL, this.handleTutorialState.bind(this)],
      [this.STATES.TRANSITION, this.handleTransitionState.bind(this)]
    ]);

    this.isTransitioning = false;
    this.transitionData = null;
  }

  /**
   * 初始化状态管理器
   */
  initialize() {
    console.log('🎯 初始化游戏状态管理器');
    this.setState(this.STATES.INITIALIZING);
  }

  /**
   * 设置状态
   * @param {string} newState - 新状态
   * @param {*} data - 状态数据
   * @param {boolean} force - 是否强制转换
   */
  setState(newState, data = null, force = false) {
    // 验证状态是否存在
    if (!Object.values(this.STATES).includes(newState)) {
      console.error(`❌ 无效状态: ${newState}`);
      return false;
    }

    // 检查状态转换是否合法
    if (!force && !this.canTransitionTo(newState)) {
      console.error(`❌ 无效状态转换: ${this.currentState} -> ${newState}`);
      return false;
    }

    // 防止重复设置相同状态
    if (this.currentState === newState && !force) {
      console.log(`⚠️ 状态已是 ${newState}，忽略重复设置`);
      return true;
    }

    const previousState = this.currentState;
    
    console.log(`🔄 状态转换: ${previousState || 'NONE'} -> ${newState}`);

    // 执行状态退出处理
    if (previousState) {
      this.exitState(previousState);
    }

    // 更新状态
    this.previousState = previousState;
    this.currentState = newState;

    // 存储状态数据
    if (data !== null) {
      this.stateData.set(newState, data);
    }

    // 记录状态历史
    this.recordStateChange(previousState, newState, data);

    // 执行状态进入处理
    this.enterState(newState, data);

    // 发布状态变化事件
    this.eventBus.emit('state:changed', {
      from: previousState,
      to: newState,
      data: data,
      timestamp: Date.now()
    });

    return true;
  }

  /**
   * 检查是否可以转换到指定状态
   * @param {string} targetState - 目标状态
   */
  canTransitionTo(targetState) {
    if (!this.currentState) {
      return true; // 初始状态可以转换到任何状态
    }

    const allowedTransitions = this.transitions.get(this.currentState);
    return allowedTransitions && allowedTransitions.includes(targetState);
  }

  /**
   * 获取当前状态
   */
  getCurrentState() {
    return this.currentState;
  }

  /**
   * 获取上一个状态
   */
  getPreviousState() {
    return this.previousState;
  }

  /**
   * 获取状态数据
   * @param {string} state - 状态名称，为空则返回当前状态数据
   */
  getStateData(state = null) {
    const targetState = state || this.currentState;
    return this.stateData.get(targetState);
  }

  /**
   * 设置状态数据
   * @param {string} state - 状态名称
   * @param {*} data - 数据
   */
  setStateData(state, data) {
    this.stateData.set(state, data);
  }

  /**
   * 更新状态管理器
   * @param {number} deltaTime - 时间增量
   */
  update(deltaTime) {
    if (this.currentState && this.stateHandlers.has(this.currentState)) {
      const handler = this.stateHandlers.get(this.currentState);
      handler(deltaTime, this.getStateData());
    }
  }

  /**
   * 状态进入处理
   */
  enterState(state, data) {
    console.log(`➡️ 进入状态: ${state}`, data);
    
    switch (state) {
      case this.STATES.GAME_PLAYING:
        this.eventBus.emit('game:state-enter-playing', data);
        break;
      case this.STATES.GAME_PAUSED:
        this.eventBus.emit('game:state-enter-paused', data);
        break;
      case this.STATES.LEVEL_SELECTION:
        this.eventBus.emit('ui:state-enter-level-selection', data);
        break;
    }
  }

  /**
   * 状态退出处理
   */
  exitState(state) {
    console.log(`⬅️ 退出状态: ${state}`);
    
    switch (state) {
      case this.STATES.GAME_PLAYING:
        this.eventBus.emit('game:state-exit-playing');
        break;
      case this.STATES.GAME_PAUSED:
        this.eventBus.emit('game:state-exit-paused');
        break;
      case this.STATES.LEVEL_SELECTION:
        this.eventBus.emit('ui:state-exit-level-selection');
        break;
    }
  }

  // 状态处理器方法
  handleInitializingState(deltaTime, data) {
    // 初始化状态处理逻辑
  }

  handleLevelSelectionState(deltaTime, data) {
    // 关卡选择状态处理逻辑
  }

  handleGameLoadingState(deltaTime, data) {
    // 游戏加载状态处理逻辑
  }

  handleGamePlayingState(deltaTime, data) {
    // 游戏进行状态处理逻辑
  }

  handleGamePausedState(deltaTime, data) {
    // 游戏暂停状态处理逻辑
  }

  handleGameOverState(deltaTime, data) {
    // 游戏结束状态处理逻辑
  }

  handleLevelCompleteState(deltaTime, data) {
    // 关卡完成状态处理逻辑
  }

  handleLevelFailState(deltaTime, data) {
    // 关卡失败状态处理逻辑
  }

  handleMenuState(deltaTime, data) {
    // 菜单状态处理逻辑
  }

  handleSettingsState(deltaTime, data) {
    // 设置状态处理逻辑
  }

  handleTutorialState(deltaTime, data) {
    // 教程状态处理逻辑
  }

  handleTransitionState(deltaTime, data) {
    // 过渡状态处理逻辑
  }

  /**
   * 记录状态变化历史
   */
  recordStateChange(from, to, data) {
    this.stateHistory.push({
      from,
      to,
      data,
      timestamp: Date.now()
    });

    // 限制历史记录大小
    if (this.stateHistory.length > this.maxHistorySize) {
      this.stateHistory.shift();
    }
  }

  /**
   * 获取状态历史
   */
  getStateHistory() {
    return [...this.stateHistory];
  }

  /**
   * 获取状态统计信息
   */
  getStateStats() {
    const stats = {};
    
    // 统计每个状态的使用次数
    this.stateHistory.forEach(entry => {
      if (entry.to) {
        stats[entry.to] = (stats[entry.to] || 0) + 1;
      }
    });

    return {
      currentState: this.currentState,
      previousState: this.previousState,
      totalTransitions: this.stateHistory.length,
      stateUsage: stats,
      availableStates: Object.values(this.STATES)
    };
  }

  /**
   * 回到上一个状态
   */
  goToPreviousState(data = null) {
    if (this.previousState) {
      console.log(`🔙 回到上一个状态: ${this.previousState}`);
      return this.setState(this.previousState, data);
    } else {
      console.warn('⚠️ 没有上一个状态可以返回');
      return false;
    }
  }

  /**
   * 重置状态管理器
   */
  reset() {
    console.log('🔄 重置状态管理器');
    this.currentState = null;
    this.previousState = null;
    this.stateData.clear();
    this.stateHistory = [];
    this.isTransitioning = false;
    this.transitionData = null;
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      currentState: this.currentState,
      previousState: this.previousState,
      stateDataCount: this.stateData.size,
      historyLength: this.stateHistory.length,
      isTransitioning: this.isTransitioning,
      availableTransitions: this.transitions.get(this.currentState) || [],
      allStates: Object.values(this.STATES)
    };
  }
} 