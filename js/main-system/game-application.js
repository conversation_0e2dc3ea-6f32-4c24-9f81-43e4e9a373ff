// 新架构 - 使用模拟依赖避免深层依赖问题

// 模拟DataBus
class MockDataBus {
  constructor() {
    this.gameStatus = 'waiting';
    this.enemies = [];
    this.bullets = [];
    this.items = [];
    this.score = 0;
    console.log('🚌 数据总线已创建');
  }
  
  reset() {
    this.gameStatus = 'waiting';
    this.enemies = [];
    this.bullets = [];
    this.items = [];
    this.score = 0;
    console.log('🔄 数据总线已重置');
  }
}

import { EventBus } from './events/event-bus.js';
import { GameStateManager } from './state/game-state-manager.js';
import { SystemManager } from './systems/system-manager.js';
import LevelManager from '../level/level-manager.js';
import GameInfo from '../runtime/gameinfo.js';
import GameController from '../game/controller.js';
import RefactoredItemManager from '../item/refactored-item-manager.js';
import { TouchController } from './input/touch-controller.js';
import { GameLoop } from './core/game-loop.js';
import { DebugController } from './debug/debug-controller.js';
import Music from '../runtime/music.js';
import Tetromino from '../game/tetromino.js';
import ElementIntroduction from '../tutorial/element-introduction.js';
import { preloadBlockImages } from '../game/block.js';

/**
 * 游戏应用主类 - 采用分层架构
 * 职责：应用级别的协调和生命周期管理
 */
export class GameApplication {
  constructor() {
    console.log('🚀 GameApplication 构造函数开始');
    
    // 状态标志
    this.isInitialized = false;
    this.isRunning = false;
    
    // 核心系统组件
    this.eventBus = new EventBus();
    this.stateManager = new GameStateManager(this.eventBus);
    this.systemManager = new SystemManager(this.eventBus);
    this.gameLoop = new GameLoop(this.eventBus);
    this.touchController = new TouchController(this.eventBus);
    this.debugController = new DebugController(this.eventBus);
    
    // 游戏组件
    this.gameInfo = null;
    this.levelManager = null;
    this.gameController = null;
    this.itemManager = null;
    this.elementIntroduction = null;
    
    console.log('✅ GameApplication 构造函数完成');
  }

  /**
   * 初始化应用
   */
  async initialize() {
    if (this.isInitialized) {
      console.warn('⚠️ GameApplication 已经初始化过了');
      return;
    }

    console.log('🚀 开始初始化 GameApplication');

    try {
      // 初始化全局对象
      this.initializeGlobals();
      
      // 初始化微信小游戏兼容性
      this.initializeWeChatCompatibility();
      
      // 预加载方块图片
      console.log('🖼️ 开始预加载方块图片...');
      await preloadBlockImages();
      console.log('✅ 方块图片预加载完成');
      
      // 初始化核心系统
      await this.systemManager.initialize();
      
      // 初始化游戏信息显示系统（原有的UI）
      this.gameInfo = new GameInfo();
      console.log('🎨 游戏信息显示系统已初始化');
      
      // 初始化关卡管理器
      this.levelManager = new LevelManager();
      console.log('🏗️ 关卡管理器已初始化');
      
      // 初始化元素介绍系统
      this.elementIntroduction = new ElementIntroduction();
      console.log('📚 元素介绍系统已初始化');
      
      // 初始化触摸控制器
      this.touchController.initialize();
      this.touchController.setGameInfo(this.gameInfo);
      console.log('👆 触摸控制器已初始化');
      
      // 连接事件
      this.connectGameInfoEvents();
      this.registerEventListeners();
      
      // 设置初始状态
      this.stateManager.setState('LEVEL_SELECTION');
      
      this.isInitialized = true;
      console.log('✅ GameApplication 初始化完成');
      
    } catch (error) {
      console.error('❌ GameApplication 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化全局对象
   */
  initializeGlobals() {
    // 确保GameGlobal对象存在
    if (typeof GameGlobal === 'undefined') {
      globalThis.GameGlobal = {};
    }
    
    GameGlobal.databus = new MockDataBus();
    
    // 使用真正的音效管理器
    GameGlobal.musicManager = new Music();
    console.log('🎵 真实音效管理器已初始化');
    
    // 暴露Tetromino类到全局（ItemManager需要）
    GameGlobal.Tetromino = Tetromino;
    console.log('🧩 Tetromino类已暴露到全局');
    
    // 暴露应用实例到全局（用于调试）
    if (typeof window !== 'undefined') {
      window.gameApp = this;
    }
    if (typeof global !== 'undefined') {
      global.gameApp = this;
    }
  }

  /**
   * 初始化微信小游戏兼容性
   */
  initializeWeChatCompatibility() {
    if (typeof wx === 'undefined') return;

    const unsupportedAPIs = [
      'setNavigationBarColor',
      'setNavigationBarTitle', 
      'showNavigationBarLoading',
      'hideNavigationBarLoading',
      'getSystemInfo',
      'reportRealtimeAction',
      'reportAnalytics',
      'reportUserBehavior',
      'onNetworkStatusChange',
      'getNetworkType',
      'onAccelerometerChange',
      'startAccelerometer',
      'stopAccelerometer',
      'onCompassChange',
      'startCompass',
      'stopCompass',
      'getSystemInfoSync',
      'getWindowInfo'
    ];

    // 为特定API提供模拟实现
    const mockAPIs = {
      getSystemInfo: function(options = {}) {
        const mockSystemInfo = {
          brand: 'devtools',
          model: 'devtools',
          pixelRatio: 2,
          screenWidth: 375,
          screenHeight: 667,
          windowWidth: 375,
          windowHeight: 667,
          statusBarHeight: 20,
          language: 'zh_CN',
          version: '8.0.5',
          system: 'iOS 16.0',
          platform: 'devtools',
          fontSizeSetting: 16,
          SDKVersion: '3.0.0',
          benchmarkLevel: 1,
          albumAuthorized: false,
          cameraAuthorized: false,
          locationAuthorized: false,
          microphoneAuthorized: false,
          notificationAuthorized: false,
          phoneCalendarAuthorized: false,
          bluetoothEnabled: false,
          locationEnabled: false,
          wifiEnabled: true,
          safeArea: {
            left: 0,
            right: 375,
            top: 44,
            bottom: 667,
            width: 375,
            height: 623
          }
        };
        
        console.log('🚫 模拟 getSystemInfo 调用');
        if (options.success) {
          options.success(mockSystemInfo);
        }
        if (options.complete) {
          options.complete(mockSystemInfo);
        }
        return mockSystemInfo;
      },
      
      getSystemInfoSync: function() {
        const mockSystemInfo = {
          brand: 'devtools',
          model: 'devtools',
          pixelRatio: 2,
          screenWidth: 375,
          screenHeight: 667,
          windowWidth: 375,
          windowHeight: 667,
          statusBarHeight: 20,
          language: 'zh_CN',
          version: '8.0.5',
          system: 'iOS 16.0',
          platform: 'devtools',
          fontSizeSetting: 16,
          SDKVersion: '3.0.0',
          benchmarkLevel: 1
        };
        
        console.log('🚫 模拟 getSystemInfoSync 调用');
        return mockSystemInfo;
      },
      
      getWindowInfo: function() {
        const mockWindowInfo = {
          screenWidth: 375,
          screenHeight: 667,
          windowWidth: 375,
          windowHeight: 667,
          statusBarHeight: 20,
          safeArea: {
            left: 0,
            right: 375,
            top: 44,
            bottom: 667,
            width: 375,
            height: 623
          }
        };
        
        console.log('🚫 模拟 getWindowInfo 调用');
        return mockWindowInfo;
      },
      
      reportRealtimeAction: function(options = {}) {
        console.log('🚫 忽略 reportRealtimeAction 调用:', options);
        if (options.success) {
          options.success({ errMsg: 'reportRealtimeAction:ok' });
        }
        if (options.complete) {
          options.complete({ errMsg: 'reportRealtimeAction:ok' });
        }
      }
    };

    // 应用模拟API
    Object.keys(mockAPIs).forEach(apiName => {
      if (!wx[apiName] || typeof wx[apiName] !== 'function') {
        wx[apiName] = mockAPIs[apiName];
      } else {
        // 如果API存在但调用失败，用模拟版本替换
        const originalAPI = wx[apiName];
        wx[apiName] = function(options = {}) {
          try {
            return originalAPI.call(this, options);
          } catch (error) {
            console.warn(`⚠️ ${apiName} 调用失败，使用模拟实现:`, error);
            return mockAPIs[apiName](options);
          }
        };
      }
    });

    // 处理其他不支持的API
    unsupportedAPIs.forEach(apiName => {
      if (mockAPIs[apiName]) return; // 跳过已处理的API
      
      if (!wx[apiName]) {
        wx[apiName] = function(options = {}) {
          console.log(`🚫 忽略不支持的API调用: ${apiName}`, options);
          if (options.success) {
            options.success({ errMsg: `${apiName}:ok` });
          }
          if (options.complete) {
            options.complete({ errMsg: `${apiName}:ok` });
          }
        };
      } else {
        const originalAPI = wx[apiName];
        wx[apiName] = function(options = {}) {
          try {
            return originalAPI.call(this, options);
          } catch (error) {
            console.warn(`⚠️ API调用失败，已忽略: ${apiName}`, error);
            if (options.fail) {
              options.fail({ errMsg: `${apiName}:fail ${error.message}` });
            }
            if (options.complete) {
              options.complete({ errMsg: `${apiName}:fail ${error.message}` });
            }
          }
        };
      }
    });

    console.log('✅ 已设置微信小游戏API兼容性处理');
  }

  /**
   * 连接GameInfo事件
   */
  connectGameInfoEvents() {
    if (!this.gameInfo) return;
    
    // 连接关卡选择事件
    this.gameInfo.on('level:select', (data) => {
      console.log('🎯 关卡选择事件:', data);
      this.onLevelSelect(data);
    });
    
    // 连接开始游戏事件
    this.gameInfo.on('start:game', () => {
      console.log('🎮 开始游戏事件');
      this.onStartGame();
    });
    
    // 连接道具使用事件
    this.gameInfo.on('useItem', (data) => {
      console.log('🛠️ 道具使用事件:', data);
      this.handleUseItem(data);
    });
    
    // 连接暂停/恢复事件
    this.gameInfo.on('pause', () => {
      console.log('⏸️ 暂停事件');
      this.handlePauseResume();
    });
    
    // 🔥 修复：连接移动事件
    this.gameInfo.on('move', (data) => {
      console.log('🎮 移动事件:', data);
      this.handleMove(data);
    });
    
    // 🔥 修复：连接旋转事件
    this.gameInfo.on('rotate', () => {
      this.handleRotate();
    });
    
    // 连接元素介绍事件
    if (this.elementIntroduction) {
      this.elementIntroduction.on('introduction:end', () => {
        console.log('📚 元素介绍结束');
        // 介绍结束后可以继续游戏
      });
    }
    
    console.log('🔗 GameInfo事件连接完成');
  }

  /**
   * 注册事件监听器
   */
  registerEventListeners() {
    // 状态变化事件
    this.eventBus.on('state:changed', (event) => this.onStateChanged(event.from, event.to, event.data));
    
    // 游戏事件
    this.eventBus.on('game:start', (data) => {
      this.onGameStart(data);
    });

    this.eventBus.on('game:over', (data) => {
      this.onGameOver(data);
    });

    this.eventBus.on('game:pause', () => {
      this.onGamePause();
    });

    this.eventBus.on('game:resume', () => {
      this.onGameResume();
    });

    // 关卡事件
    this.eventBus.on('level:select', (data) => {
      this.onLevelSelect(data);
    });

    this.eventBus.on('start:game', () => {
      this.onStartGame();
    });

    this.eventBus.on('level:complete', (data) => {
      this.onLevelComplete(data);
    });

    this.eventBus.on('level:fail', (data) => {
      this.onLevelFail(data);
    });

    // 注册游戏循环事件
    this.eventBus.on('gameloop:update', (deltaTime) => this.update(deltaTime));
    this.eventBus.on('gameloop:render', (ctx) => this.onGameLoopRender(ctx));
  }

  /**
   * 启动应用
   */
  start() {
    if (!this.isInitialized) {
      throw new Error('应用未初始化，无法启动');
    }

    console.log('🎯 启动游戏应用');
    this.isRunning = true;
    this.gameLoop.start();
  }

  /**
   * 停止应用
   */
  stop() {
    console.log('⏹️ 停止游戏应用');
    this.isRunning = false;
    this.gameLoop.stop();
  }

  /**
   * 更新应用状态
   */
  update(deltaTime) {
    if (!this.isRunning) return;
    
    // 更新系统管理器
    this.systemManager.update(deltaTime);
    
    // 更新状态管理器
    this.stateManager.update(deltaTime);
    
    // 更新游戏控制器（不传递deltaTime参数）
    if (this.gameController && typeof this.gameController.update === 'function') {
      this.gameController.update();
    }
    
    // 更新道具管理器
    if (this.itemManager && typeof this.itemManager.update === 'function') {
      this.itemManager.update();
    }
    
    // 更新元素介绍系统
    if (this.elementIntroduction && typeof this.elementIntroduction.update === 'function') {
      this.elementIntroduction.update();
    }
  }

  /**
   * 渲染应用
   */
  render(ctx) {
    if (!this.isRunning) return;
    
    // 清空画布
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    
    // 渲染游戏信息界面
    if (this.gameInfo) {
      // 渲染游戏控制器（方块、网格等）
      if (this.gameController && this.gameInfo.currentScreen === 'game') {
        this.gameController.render(ctx);
      }
      
      // 渲染道具管理器效果
      if (this.itemManager && this.gameInfo.currentScreen === 'game') {
        this.itemManager.render(ctx);
      }
      
      // 渲染GameInfo界面层
      this.gameInfo.render(ctx);
    }
    
    // 渲染元素介绍界面（在最上层）
    if (this.elementIntroduction && typeof this.elementIntroduction.render === 'function') {
      this.elementIntroduction.render(ctx);
    }
  }

  /**
   * 状态变化处理
   */
  onStateChanged(from, to, data) {
    console.log(`🔄 状态变化: ${from} -> ${to}`, data);
    
    switch (to) {
      case 'LEVEL_SELECTION':
        this.onLevelSelectionEnter();
        break;
      case 'GAME_LOADING':
        this.onGameLoadingEnter(data);
        break;
      case 'GAME_PLAYING':
        this.startGame(data);
        break;
      case 'GAME_PAUSED':
        this.pauseGame();
        break;
      case 'GAME_OVER':
        this.handleGameOver(data);
        break;
    }
  }

  /**
   * 显示关卡选择
   */
  onLevelSelectionEnter() {
    console.log('📋 显示关卡选择界面');
    
    // 使用原有的GameInfo系统显示关卡选择，传递正确的关卡数据
    this.gameInfo.currentScreen = 'level';
    const stageData = this.levelManager.getUnlockedLevelsByStage();
    this.gameInfo.showLevelSelection(stageData);
  }

  /**
   * 处理游戏加载状态
   */
  onGameLoadingEnter(data) {
    console.log('⏳ 开始加载关卡:', data);
    
    try {
      // 加载关卡配置
      const levelConfig = this.levelManager.loadLevel(data.levelId);
      
      if (!levelConfig) {
        console.error(`❌ 无法加载关卡配置: ${data.levelId}`);
        return;
      }
      
      console.log('📋 关卡配置已加载:', levelConfig);
      
      // 设置目标信息
      this.gameInfo.setTargetInfo({
        targetScore: levelConfig.targetScore,
        starThresholds: levelConfig.starThresholds,
        levelType: levelConfig.type,
        description: levelConfig.description || `第${data.levelId}关`,
        objectives: levelConfig.objectives || [`达到 ${levelConfig.targetScore} 分`],
        timeLimit: levelConfig.timeLimit || 0
      });
      
      console.log('🎯 目标信息已设置');
      
      // 显示关卡开始界面
      this.gameInfo.currentScreen = 'levelstart';
      this.gameInfo.showLevelStart(data.levelId);
      
      console.log(`✅ 关卡 ${data.levelId} 加载完成，显示开始界面`);
      
    } catch (error) {
      console.error('❌ 关卡加载失败:', error);
      // 回到关卡选择界面
      this.stateManager.setState('LEVEL_SELECTION');
    }
  }

  /**
   * 开始游戏
   */
  startGame(levelData) {
    console.log('🎮 开始游戏', levelData);
    
    // 切换到游戏界面
    this.gameInfo.currentScreen = 'game';
    
    // 确保关卡数据包含正确的level字段
    const gameLevelData = {
      ...levelData,
      level: levelData.levelId || levelData.level || 1 // 确保有level字段
    };
    
    console.log('🎯 传递给游戏控制器的关卡数据:', gameLevelData);
    
    // 创建真正的游戏控制器
    this.createGameController(gameLevelData);
    
    // 通过系统管理器启动游戏系统
    this.systemManager.startGame(gameLevelData);
    
    // 检查是否需要显示道具介绍
    if (this.elementIntroduction) {
      const showedIntro = this.elementIntroduction.checkAndShowIntroduction(gameLevelData.level);
      if (showedIntro) {
        console.log(`📚 显示第${gameLevelData.level}关道具介绍`);
      }
    }
    
    console.log('✅ 游戏界面已切换，游戏系统已启动');
  }

  /**
   * 暂停游戏
   */
  pauseGame() {
    console.log('⏸️ 暂停游戏');
    this.systemManager.pauseGame();
  }

  /**
   * 恢复游戏
   */
  resumeGame() {
    console.log('▶️ 恢复游戏');
    this.systemManager.resumeGame();
  }

  /**
   * 处理游戏结束
   */
  handleGameOver(data) {
    console.log('💀 游戏结束', data);
    this.eventBus.emit('ui:show-game-over', data);
  }

  // 事件处理方法
  onGameStart(data) {
    console.log('🎯 游戏开始事件', data);
  }

  onGameOver(data) {
    console.log('💀 游戏结束事件', data);
  }

  onGamePause() {
    console.log('⏸️ 游戏暂停事件');
  }

  onGameResume() {
    console.log('▶️ 游戏恢复事件');
  }

  onLevelComplete(data) {
    console.log('🎉 关卡完成事件', data);
    this.stateManager.setState('LEVEL_COMPLETE', data);
  }

  onLevelFail(data) {
    console.log('😵 关卡失败事件', data);
    this.stateManager.setState('LEVEL_FAIL', data);
  }

  /**
   * 处理关卡选择事件
   */
  onLevelSelect(data) {
    console.log('🎯 处理关卡选择:', data);
    
    // 切换到游戏加载状态
    this.stateManager.setState('GAME_LOADING', data);
  }

  /**
   * 处理开始游戏事件
   */
  onStartGame() {
    console.log('🎮 用户点击开始游戏');
    
    // 获取当前加载的关卡数据
    const levelData = this.stateManager.getStateData('GAME_LOADING');
    
    if (levelData) {
      // 切换到游戏进行状态
      this.stateManager.setState('GAME_PLAYING', levelData);
    } else {
      console.error('❌ 没有找到关卡数据，无法开始游戏');
    }
  }

  /**
   * 创建游戏控制器
   */
  createGameController(levelData) {
    try {
      console.log('🎮 创建游戏控制器，关卡数据:', levelData);
      
      // 🔧 修复：清理之前的游戏控制器实例，防止重复运行
      if (this.gameController) {
        console.log('🧹 清理之前的游戏控制器实例');
        if (typeof this.gameController.destroy === 'function') {
          this.gameController.destroy();
        }
        if (typeof this.gameController.removeAllListeners === 'function') {
          this.gameController.removeAllListeners();
        }
        this.gameController = null;
      }
      
      // 清理之前的道具管理器实例
      if (this.itemManager) {
        console.log('🧹 清理之前的道具管理器实例');
        if (typeof this.itemManager.destroy === 'function') {
          this.itemManager.destroy();
        }
        this.itemManager = null;
      }
      
      // 创建新的游戏控制器（使用重构版本）
      this.gameController = new GameController({
        level: levelData.level,
        targetScore: levelData.targetScore,
        timeLimit: levelData.timeLimit,
        starThresholds: levelData.starThresholds,
        objectives: levelData.objectives
      });
      
      // 设置初始方块
      this.levelManager.setupInitialBlocks(this.gameController.grid);
      console.log('🧱 初始方块已设置');
      
      // 创建道具管理器
      this.itemManager = new RefactoredItemManager(this.gameController.grid, {
        getGameController: () => this.gameController,
        getMusicManager: () => GameGlobal.musicManager,
        getTetrominoClass: () => GameGlobal.Tetromino || null
      });
      
      // 设置当前关卡ID
      this.itemManager.setCurrentLevel(levelData.level);
      console.log('🛠️ 道具管理器已创建');
      
      // 启动游戏控制器
      this.gameController.start();
      
      // 暴露到全局（兼容原有代码）
      GameGlobal.gameController = this.gameController;
      GameGlobal.itemManager = this.itemManager;
      GameGlobal.main = { 
        gameController: this.gameController,
        itemManager: this.itemManager
      };
      
      console.log('✅ 游戏控制器和道具管理器已创建并启动');
    } catch (error) {
      console.error('❌ 创建游戏控制器失败:', error);
      throw error;
    }
  }

  /**
   * 获取系统管理器
   */
  getSystemManager() {
    return this.systemManager;
  }

  /**
   * 获取状态管理器
   */
  getStateManager() {
    return this.stateManager;
  }

  /**
   * 获取事件总线
   */
  getEventBus() {
    return this.eventBus;
  }

  /**
   * 获取调试控制器
   */
  getDebugController() {
    return this.debugController;
  }

  /**
   * 游戏循环渲染回调
   */
  onGameLoopRender(ctx) {
    // 使用原有的渲染逻辑
    this.render(ctx);
  }

  /**
   * 处理道具使用事件
   */
  handleUseItem(data) {
    if (!this.gameController || !this.itemManager) {
      console.warn('游戏控制器或道具管理器未初始化');
      return;
    }

    // 检查游戏状态
    if (this.gameController.state !== 'playing' || this.gameController.isAnimating) {
      console.log('当前状态不能使用道具');
      if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playBuzz === 'function') {
        GameGlobal.musicManager.playBuzz();
      } else {
        console.warn('音效管理器或playBuzz方法不可用');
      }
      return;
    }

    console.log('准备使用道具:', data.type);
    
    // 移除之前的事件监听器，防止多次绑定
    this.itemManager.off('check:matches');
    
    // 注册道具效果完成后的匹配检查监听器
    this.itemManager.on('check:matches', () => {
      console.log('道具效果完成，准备检查匹配');
      // 切换到检查状态，让游戏控制器处理匹配和下落
      if (this.gameController) {
        this.gameController.state = 'checking';
      }
    });

    // 检查道具是否在冷却中
    if (!this.itemManager.isItemReady(data.type)) {
      console.log(`道具${data.type}正在冷却中，冷却进度:`, this.itemManager.getItemCooldownProgress(data.type));
      if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playBuzz === 'function') {
        GameGlobal.musicManager.playBuzz();
      } else {
        console.warn('音效管理器或playBuzz方法不可用');
      }
      this.itemManager.off('check:matches');
      return;
    }

    // 检查道具剩余使用次数
    if (this.itemManager.getItemUses(data.type) <= 0) {
      console.log(`道具${data.type}已用完`);
      if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playBuzz === 'function') {
        GameGlobal.musicManager.playBuzz();
      } else {
        console.warn('音效管理器或playBuzz方法不可用');
      }
      this.itemManager.off('check:matches');
      return;
    }

    // 使用道具，让道具管理器自动选择目标位置
    const success = this.itemManager.useItem(data.type);
    console.log('道具使用结果:', success ? '成功' : '失败');
    
    if (!success) {
      console.log('道具使用失败，可能没有有效目标');
      this.itemManager.off('check:matches');
      if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playBuzz === 'function') {
        GameGlobal.musicManager.playBuzz();
      } else {
        console.warn('音效管理器或playBuzz方法不可用');
      }
      return;
    }
  }

  /**
   * 处理暂停/恢复事件
   */
  handlePauseResume() {
    if (!this.gameController) {
      console.warn('游戏控制器未初始化');
      return;
    }
    
    // 检查当前状态决定是暂停还是恢复
    if (this.gameController.state === 'paused') {
      console.log('恢复游戏');
      this.gameController.resume();
      this.eventBus.emit('game:resume');
    } else if (this.gameController.state === 'playing') {
      console.log('暂停游戏');
      this.gameController.pause();
      this.eventBus.emit('game:pause');
    } else {
      console.log('当前状态不支持暂停/恢复:', this.gameController.state);
    }
  }

  /**
   * 处理移动事件
   */
  handleMove(data) {
    if (!this.gameController) {
      console.warn('游戏控制器未初始化');
      return;
    }

    // 检查游戏状态
    if (this.gameController.state !== 'playing') {
      console.log('当前状态不能移动:', this.gameController.state);
      return;
    }

    const { direction, pressed } = data;
    console.log(`🎮 处理移动: ${direction}, 按下: ${pressed}`);

    switch (direction) {
      case 'left':
        if (pressed) {
          this.gameController._handleLeft();
        }
        break;
      case 'right':
        if (pressed) {
          this.gameController._handleRight();
        }
        break;
      case 'down':
        // 快速下落处理
        this.gameController._handleSoftDrop(pressed);
        break;
      default:
        console.warn('未知的移动方向:', direction);
    }
  }

  /**
   * 处理旋转事件
   */
  handleRotate() {
    if (!this.gameController) {
      console.warn('游戏控制器未初始化');
      return;
    }

    // 检查游戏状态
    if (this.gameController.state !== 'playing') {
      console.log('当前状态不能旋转:', this.gameController.state);
      return;
    }

    console.log('🔄 处理旋转');
    this.gameController._handleRotate();
  }
} 