// 暂时注释掉所有外部依赖，创建最小可用版本
// import GameController from '../../game/refactored-refactored-controller.js';
// import RefactoredItemManager from '../../item/refactored-item-manager.js';
// import LevelManager from '../../level/level-manager.js';
// import TutorialManager from '../../tutorial/tutorial-manager.js';
// import ProgressionManager from '../../progression/progression-manager.js';
// import AssistanceManager from '../../assistance/assistance-manager.js';
// import EffectBalanceManager from '../../effects/effect-balance-manager.js';
// import ItemProgressionManager from '../../item/item-progression-manager.js';
// import GameBalanceManager from '../../balance/game-balance-manager.js';
// import RetryManager from '../../retry/retry-manager.js';
// import ElementIntroduction from '../../tutorial/element-introduction.js';
// import GarbageWarning from '../../ui/garbage-warning.js';

/**
 * 系统管理器 - 管理所有游戏子系统 (最小可用版本)
 * 职责：系统生命周期管理、依赖注入、系统间通信协调
 */
export class SystemManager {
  constructor(eventBus) {
    this.eventBus = eventBus;
    this.systems = new Map();
    this.isInitialized = false;
    this.isGameActive = false;
  }

  /**
   * 初始化所有系统 (最小版本)
   */
  async initialize() {
    console.log('🎯 正在初始化游戏系统...');
    
    try {
      // 暂时只创建模拟系统
      this.createMockSystems();
      
      // 注册系统间事件监听
      this.registerSystemEvents();
      
      this.isInitialized = true;
      console.log('✅ 游戏系统初始化完成 (最小版本)');
      
    } catch (error) {
      console.error('❌ 游戏系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建模拟系统用于测试
   */
  createMockSystems() {
    console.log('🏗️ 创建模拟游戏系统...');
    
    // 创建模拟的关卡管理器
    const mockLevelManager = {
      name: 'MockLevelManager',
      unlockLevelsForTesting: (count) => {
        console.log(`🔓 模拟解锁 ${count} 个关卡`);
      },
      initialize: () => Promise.resolve(),
      update: () => {},
      render: () => {}
    };
    this.systems.set('LevelManager', mockLevelManager);
    
    // 创建模拟的进度管理器
    const mockProgressionManager = {
      name: 'MockProgressionManager',
      initialize: () => Promise.resolve(),
      update: () => {},
      render: () => {}
    };
    this.systems.set('ProgressionManager', mockProgressionManager);
    
    // 创建模拟的道具管理器
    const mockItemManager = {
      name: 'MockItemManager',
      initialize: () => Promise.resolve(),
      update: () => {},
      render: () => {}
    };
    this.systems.set('ItemManager', mockItemManager);
    
    console.log(`✅ 已创建 ${this.systems.size} 个模拟系统`);
  }

  /**
   * 注册系统间事件监听
   */
  registerSystemEvents() {
    console.log('📡 注册系统间事件监听...');

    // 游戏状态事件
    this.eventBus.on('game:start', (data) => {
      this.onGameStart(data);
    });

    this.eventBus.on('game:over', (data) => {
      this.onGameOver(data);
    });

    this.eventBus.on('game:pause', () => {
      this.onGamePause();
    });

    this.eventBus.on('game:resume', () => {
      this.onGameResume();
    });
  }

  /**
   * 开始游戏 (模拟版本)
   */
  startGame(levelData) {
    console.log('🎮 系统管理器：开始游戏 (模拟版本)', levelData);
    
    try {
      // 模拟游戏开始
      this.isGameActive = true;
      console.log('✅ 游戏已开始 (模拟)');
      
    } catch (error) {
      console.error('❌ 开始游戏失败:', error);
      throw error;
    }
  }

  /**
   * 暂停游戏
   */
  pauseGame() {
    console.log('⏸️ 系统管理器：暂停游戏');
  }

  /**
   * 恢复游戏
   */
  resumeGame() {
    console.log('▶️ 系统管理器：恢复游戏');
  }

  /**
   * 停止游戏
   */
  stopGame() {
    console.log('⏹️ 系统管理器：停止游戏');
    this.isGameActive = false;
    console.log('✅ 游戏已停止');
  }

  /**
   * 更新所有系统
   */
  update(deltaTime) {
    if (!this.isInitialized) return;

    // 更新模拟系统
    this.systems.forEach((system, name) => {
      if (system && typeof system.update === 'function') {
        system.update(deltaTime);
      }
    });
  }

  /**
   * 渲染所有系统
   */
  render(ctx) {
    if (!this.isInitialized) return;

    // 渲染模拟系统
    this.systems.forEach((system, name) => {
      if (system && typeof system.render === 'function') {
        system.render(ctx);
      }
    });
  }

  /**
   * 获取系统
   */
  getSystem(systemName) {
    return this.systems.get(systemName);
  }

  /**
   * 检查系统是否存在
   */
  hasSystem(systemName) {
    return this.systems.has(systemName);
  }

  /**
   * 获取所有系统名称
   */
  getSystemNames() {
    return Array.from(this.systems.keys());
  }

  // 事件处理方法
  onGameStart(data) {
    console.log('🎯 系统管理器：游戏开始事件', data);
  }

  onGameOver(data) {
    console.log('💀 系统管理器：游戏结束事件', data);
    this.stopGame();
  }

  onGamePause() {
    console.log('⏸️ 系统管理器：游戏暂停事件');
  }

  onGameResume() {
    console.log('▶️ 系统管理器：游戏恢复事件');
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats() {
    return {
      totalSystems: this.systems.size,
      isInitialized: this.isInitialized,
      isGameActive: this.isGameActive,
      systemNames: this.getSystemNames(),
      hasGameController: false // 模拟版本无真实游戏控制器
    };
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    const systemInfo = {};
    
    this.systems.forEach((system, name) => {
      systemInfo[name] = {
        exists: true,
        hasUpdate: typeof system.update === 'function',
        hasRender: typeof system.render === 'function',
        hasInitialize: typeof system.initialize === 'function',
        isMock: true
      };
    });

    return {
      ...this.getSystemStats(),
      systemInfo
    };
  }
} 