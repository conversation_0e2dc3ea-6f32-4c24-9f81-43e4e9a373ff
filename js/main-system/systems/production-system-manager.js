// 生产版本 - 使用真实依赖
import GameController from '../../game/controller.js';
import RefactoredItemManager from '../../item/refactored-item-manager.js';
import LevelManager from '../../level/level-manager.js';

/**
 * 系统管理器 - 管理所有游戏子系统 (生产版本)
 * 职责：系统生命周期管理、依赖注入、系统间通信协调
 */
export class ProductionSystemManager {
  constructor(eventBus) {
    this.eventBus = eventBus;
    this.systems = new Map();
    this.isInitialized = false;
    this.isGameActive = false;
  }

  /**
   * 初始化所有系统
   */
  async initialize() {
    console.log('🎯 正在初始化游戏系统...');
    
    try {
      // 创建核心系统
      this.createCoreSystems();
      
      // 注册系统间事件监听
      this.registerSystemEvents();
      
      this.isInitialized = true;
      console.log('✅ 游戏系统初始化完成');
      
    } catch (error) {
      console.error('❌ 游戏系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建核心系统
   */
  createCoreSystems() {
    console.log('🏗️ 创建核心游戏系统...');
    
    try {
      // 创建关卡管理器
      const levelManager = new LevelManager();
      this.systems.set('LevelManager', levelManager);
      console.log('✅ 关卡管理器已创建');
      
      // 创建道具管理器
      const itemManager = new RefactoredItemManager();
      this.systems.set('ItemManager', itemManager);
      console.log('✅ 道具管理器已创建');
      
      console.log(`✅ 已创建 ${this.systems.size} 个核心系统`);
      
    } catch (error) {
      console.error('❌ 创建核心系统失败:', error);
      // 如果真实依赖失败，回退到模拟系统
      this.createFallbackSystems();
    }
  }

  /**
   * 创建回退模拟系统
   */
  createFallbackSystems() {
    console.log('🔄 创建回退模拟系统...');
    
    // 创建模拟的关卡管理器
    const mockLevelManager = {
      name: 'MockLevelManager',
      unlockLevelsForTesting: (count) => {
        console.log(`🔓 模拟解锁 ${count} 个关卡`);
      },
      initialize: () => Promise.resolve(),
      update: () => {},
      render: () => {}
    };
    this.systems.set('LevelManager', mockLevelManager);
    
    // 创建模拟的道具管理器
    const mockItemManager = {
      name: 'MockItemManager',
      initialize: () => Promise.resolve(),
      update: () => {},
      render: () => {}
    };
    this.systems.set('ItemManager', mockItemManager);
    
    console.log(`✅ 已创建 ${this.systems.size} 个回退系统`);
  }

  /**
   * 注册系统间事件监听
   */
  registerSystemEvents() {
    console.log('📡 注册系统间事件监听...');

    // 游戏状态事件
    this.eventBus.on('game:start', (data) => {
      this.onGameStart(data);
    });

    this.eventBus.on('game:over', (data) => {
      this.onGameOver(data);
    });

    this.eventBus.on('game:pause', () => {
      this.onGamePause();
    });

    this.eventBus.on('game:resume', () => {
      this.onGameResume();
    });
  }

  /**
   * 开始游戏
   */
  startGame(levelData) {
    console.log('🎮 系统管理器：开始游戏', levelData);
    
    try {
      // 🔧 修复：不要重复创建GameController，使用GameApplication中已经创建的实例
      if (typeof GameGlobal !== 'undefined' && GameGlobal.gameController) {
        console.log('✅ 使用已存在的GameController实例');
        this.systems.set('GameController', GameGlobal.gameController);
      } else {
        // 如果没有全局GameController，创建模拟版本
        console.log('⚠️ 未找到全局GameController，创建模拟版本');
        this.createMockGameController(levelData);
      }
      
      this.isGameActive = true;
      console.log('✅ 游戏已开始');
      
    } catch (error) {
      console.error('❌ 开始游戏失败:', error);
      throw error;
    }
  }

  /**
   * 创建模拟游戏控制器
   */
  createMockGameController(levelData) {
    const mockGameController = {
      name: 'MockGameController',
      levelData,
      update: () => {},
      render: () => {},
      pause: () => console.log('⏸️ 模拟游戏暂停'),
      resume: () => console.log('▶️ 模拟游戏恢复')
    };
    
    this.systems.set('GameController', mockGameController);
    console.log('✅ 模拟游戏控制器已创建');
  }

  /**
   * 暂停游戏
   */
  pauseGame() {
    console.log('⏸️ 系统管理器：暂停游戏');
    const gameController = this.systems.get('GameController');
    if (gameController && typeof gameController.pause === 'function') {
      gameController.pause();
    }
  }

  /**
   * 恢复游戏
   */
  resumeGame() {
    console.log('▶️ 系统管理器：恢复游戏');
    const gameController = this.systems.get('GameController');
    if (gameController && typeof gameController.resume === 'function') {
      gameController.resume();
    }
  }

  /**
   * 停止游戏
   */
  stopGame() {
    console.log('⏹️ 系统管理器：停止游戏');
    this.systems.delete('GameController');
    this.isGameActive = false;
    console.log('✅ 游戏已停止');
  }

  /**
   * 更新所有系统
   */
  update(deltaTime) {
    if (!this.isInitialized) return;

    // 🔧 修复：不要重复更新GameController，GameApplication已经在处理了
    // 只更新其他系统
    this.systems.forEach((system, name) => {
      if (name !== 'GameController' && system && typeof system.update === 'function') {
        system.update(deltaTime);
      }
    });
  }

  /**
   * 渲染所有系统
   */
  render(ctx) {
    if (!this.isInitialized) return;

    // 🔧 修复：不要重复渲染GameController，GameApplication已经在处理了
    // 只渲染其他系统
    this.systems.forEach((system, name) => {
      if (name !== 'GameController' && system && typeof system.render === 'function') {
        system.render(ctx);
      }
    });
  }

  /**
   * 获取系统
   */
  getSystem(systemName) {
    return this.systems.get(systemName);
  }

  /**
   * 检查系统是否存在
   */
  hasSystem(systemName) {
    return this.systems.has(systemName);
  }

  /**
   * 获取所有系统名称
   */
  getSystemNames() {
    return Array.from(this.systems.keys());
  }

  // 事件处理方法
  onGameStart(data) {
    console.log('🎯 系统管理器：游戏开始事件', data);
  }

  onGameOver(data) {
    console.log('💀 系统管理器：游戏结束事件', data);
    this.stopGame();
  }

  onGamePause() {
    console.log('⏸️ 系统管理器：游戏暂停事件');
  }

  onGameResume() {
    console.log('▶️ 系统管理器：游戏恢复事件');
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats() {
    const realSystems = Array.from(this.systems.values()).filter(
      system => !system.name || !system.name.startsWith('Mock')
    ).length;
    
    return {
      totalSystems: this.systems.size,
      realSystems,
      mockSystems: this.systems.size - realSystems,
      isInitialized: this.isInitialized,
      isGameActive: this.isGameActive,
      systemNames: this.getSystemNames(),
      hasGameController: this.hasSystem('GameController')
    };
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    const systemInfo = {};
    
    this.systems.forEach((system, name) => {
      systemInfo[name] = {
        exists: true,
        hasUpdate: typeof system.update === 'function',
        hasRender: typeof system.render === 'function',
        hasInitialize: typeof system.initialize === 'function',
        isMock: system.name && system.name.startsWith('Mock')
      };
    });

    return {
      ...this.getSystemStats(),
      systemInfo
    };
  }
} 