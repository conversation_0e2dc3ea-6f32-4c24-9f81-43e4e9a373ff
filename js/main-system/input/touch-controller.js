// 暂时内联TouchDebouncer功能
// import TouchDebouncer from '../../utils/touch-debouncer.js';

class InlineTouchDebouncer {
  constructor() {
    this.lastTouchTime = 0;
    this.debounceDelay = 50; // 50ms去抖动
    this.stats = { processed: 0, blocked: 0 };
  }
  
  shouldProcess(touchEvent) {
    const now = Date.now();
    if (now - this.lastTouchTime < this.debounceDelay) {
      this.stats.blocked++;
      return false;
    }
    
    this.lastTouchTime = now;
    this.stats.processed++;
    return true;
  }
  
  reset() {
    this.lastTouchTime = 0;
    console.log('🔄 触摸去抖动器重置');
  }
  
  getStats() {
    return this.stats;
  }
}

/**
 * 触摸控制器 - 统一触摸输入管理
 * 职责：触摸事件处理、手势识别、输入去重、事件分发
 */
export class TouchController {
  constructor(eventBus) {
    this.eventBus = eventBus;
    this.touchDebouncer = new InlineTouchDebouncer();
    this.isEnabled = true;
    this.isInitialized = false;
    this.gameInfo = null; // GameInfo引用
    
    // 触摸状态追踪
    this.touchState = {
      isActive: false,
      startTime: 0,
      startPosition: { x: 0, y: 0 },
      currentPosition: { x: 0, y: 0 },
      lastProcessedTouch: null,
      touchProcessingTimeout: null
    };
    
    // 手势识别配置
    this.gestureConfig = {
      longPressThreshold: 500, // 长按阈值(ms)
      swipeThreshold: 50,      // 滑动阈值(px)
      tapTimeout: 300,         // 点击超时(ms)
      doubleTapInterval: 300   // 双击间隔(ms)
    };
    
    // 手势状态
    this.gestureState = {
      lastTapTime: 0,
      tapCount: 0,
      longPressTimer: null,
      isLongPress: false
    };
    
    // 区域管理
    this.touchRegions = new Map();
    this.activeRegion = null;
    
    this.bindMethods();
  }

  /**
   * 绑定方法
   */
  bindMethods() {
    this.onTouchStart = this.onTouchStart.bind(this);
    this.onTouchEnd = this.onTouchEnd.bind(this);
    this.onTouchMove = this.onTouchMove.bind(this);
    this.onTouchCancel = this.onTouchCancel.bind(this);
  }

  /**
   * 初始化触摸控制器
   */
  initialize() {
    if (this.isInitialized) {
      console.warn('⚠️ 触摸控制器已初始化');
      return;
    }

    console.log('🎮 初始化触摸控制器');
    
    this.registerEventListeners();
    this.registerSystemEvents();
    
    this.isInitialized = true;
    console.log('✅ 触摸控制器初始化完成');
  }

  /**
   * 注册DOM事件监听
   */
  registerEventListeners() {
    if (typeof GameGlobal === 'undefined' || !GameGlobal.canvas) {
      console.warn('⚠️ GameGlobal.canvas 未定义，跳过触摸事件注册');
      return;
    }
    
    const canvas = GameGlobal.canvas;
    
    // 触摸事件
    canvas.addEventListener('touchstart', this.onTouchStart, { passive: false });
    canvas.addEventListener('touchend', this.onTouchEnd, { passive: false });
    canvas.addEventListener('touchmove', this.onTouchMove, { passive: false });
    canvas.addEventListener('touchcancel', this.onTouchCancel, { passive: false });
    
    console.log('✅ 触摸事件监听器已注册');
  }

  /**
   * 注册系统事件监听
   */
  registerSystemEvents() {
    // 监听状态变化
    this.eventBus.on('state:changed', (data) => {
      this.onStateChanged(data);
    });
  }

  /**
   * 设置GameInfo实例
   */
  setGameInfo(gameInfo) {
    this.gameInfo = gameInfo;
    console.log('🎮 TouchController已连接到GameInfo');
  }

  /**
   * 触摸开始处理
   */
  onTouchStart(e) {
    if (!this.isEnabled) return;
    
    e.preventDefault();
    
    const touch = e.touches[0];
    const position = this.getTouchPosition(touch);
    
    console.log('👆 触摸开始:', position);
    
    // 更新触摸状态
    this.touchState.isActive = true;
    this.touchState.startTime = Date.now();
    this.touchState.startPosition = position;
    this.touchState.currentPosition = position;
    
    // 直接调用GameInfo的触摸处理方法
    if (this.gameInfo && typeof this.gameInfo.touchEventHandler === 'function') {
      this.gameInfo.touchEventHandler({
        type: 'touchstart',
        touches: [{ clientX: position.x, clientY: position.y }]
      });
    }
    
    // 移除EventBus事件发布，避免重复处理
    // this.eventBus.emit('touch:start', { position, timestamp: this.touchState.startTime });
  }

  /**
   * 触摸结束处理
   */
  onTouchEnd(e) {
    if (!this.isEnabled) return;
    
    e.preventDefault();
    
    const position = this.touchState.currentPosition;
    const duration = Date.now() - this.touchState.startTime;
    
    console.log('👆 触摸结束:', position, `持续时间: ${duration}ms`);
    
    // 直接调用GameInfo的触摸处理方法
    if (this.gameInfo && typeof this.gameInfo.touchEndHandler === 'function') {
      this.gameInfo.touchEndHandler({
        type: 'touchend',
        changedTouches: [{ clientX: position.x, clientY: position.y }]
      });
    }
    
    // 移除手势处理，避免重复处理关卡选择
    // this.processGesture(position, duration);
    
    // 重置状态
    this.resetTouchState();
    
    // 移除EventBus事件发布，避免重复处理
    // this.eventBus.emit('touch:end', { position, duration, timestamp: Date.now() });
  }

  /**
   * 触摸移动处理
   */
  onTouchMove(e) {
    if (!this.isEnabled || !this.touchState.isActive) return;
    
    e.preventDefault();
    
    const touch = e.touches[0];
    const position = this.getTouchPosition(touch);
    
    // 更新当前位置
    this.touchState.currentPosition = position;
    
    // 直接调用GameInfo的触摸处理方法
    if (this.gameInfo && typeof this.gameInfo.touchMoveHandler === 'function') {
      this.gameInfo.touchMoveHandler({
        type: 'touchmove',
        touches: [{ clientX: position.x, clientY: position.y }]
      });
    }
    
    // 移除EventBus事件发布，避免重复处理
    // this.eventBus.emit('touch:move', { position, timestamp: Date.now() });
  }

  /**
   * 触摸取消处理
   */
  onTouchCancel(e) {
    console.log('❌ 触摸取消');
    this.resetTouchState();
  }

  /**
   * 获取触摸位置
   */
  getTouchPosition(touch) {
    if (typeof GameGlobal === 'undefined' || !GameGlobal.canvas) {
      return { x: 0, y: 0 };
    }
    
    const canvas = GameGlobal.canvas;
    const rect = canvas.getBoundingClientRect();
    
    return {
      x: touch.clientX - rect.left,
      y: touch.clientY - rect.top
    };
  }

  /**
   * 处理手势识别
   */
  processGesture(position, duration) {
    const distance = this.calculateDistance(this.touchState.startPosition, position);
    
    // 简单的点击处理
    if (distance <= this.gestureConfig.swipeThreshold) {
      console.log('👆 点击手势:', position);
      // 移除EventBus事件发布，避免重复处理
      // this.eventBus.emit('gesture:tap', { position, duration });
    }
  }

  /**
   * 重置触摸状态
   */
  resetTouchState() {
    this.touchState.isActive = false;
    this.touchState.startTime = 0;
  }

  /**
   * 计算两点距离
   */
  calculateDistance(pos1, pos2) {
    const deltaX = pos2.x - pos1.x;
    const deltaY = pos2.y - pos1.y;
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }

  /**
   * 启用触摸控制
   */
  enable() {
    this.isEnabled = true;
    console.log('✅ 触摸控制已启用');
  }

  /**
   * 禁用触摸控制
   */
  disable() {
    this.isEnabled = false;
    this.resetTouchState();
    console.log('❌ 触摸控制已禁用');
  }

  /**
   * 状态变化处理
   */
  onStateChanged({ from, to }) {
    console.log(`🎮 触摸控制器响应状态变化: ${from} -> ${to}`);
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      isInitialized: this.isInitialized,
      isEnabled: this.isEnabled,
      touchState: { ...this.touchState }
    };
  }

  /**
   * 销毁触摸控制器
   */
  destroy() {
    if (typeof GameGlobal !== 'undefined' && GameGlobal.canvas) {
      const canvas = GameGlobal.canvas;
      
      // 移除事件监听器
      canvas.removeEventListener('touchstart', this.onTouchStart);
      canvas.removeEventListener('touchend', this.onTouchEnd);
      canvas.removeEventListener('touchmove', this.onTouchMove);
      canvas.removeEventListener('touchcancel', this.onTouchCancel);
    }
    
    // 清理状态
    this.resetTouchState();
    
    console.log('��️ 触摸控制器已销毁');
  }
} 