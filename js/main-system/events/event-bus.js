/**
 * 事件总线 - 解耦系统间通信
 * 采用发布订阅模式，支持事件优先级和错误处理
 */
export class EventBus {
  constructor() {
    this.listeners = new Map();
    this.onceListeners = new Map();
    this.eventHistory = [];
    this.maxHistorySize = 100;
    this.debugMode = false;
  }

  /**
   * 订阅事件
   * @param {string} event - 事件名称
   * @param {Function} listener - 事件监听器
   * @param {number} priority - 优先级(数字越大优先级越高)
   */
  on(event, listener, priority = 0) {
    if (typeof listener !== 'function') {
      throw new Error('Event listener must be a function');
    }

    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }

    const listenerObj = {
      fn: listener,
      priority: priority,
      id: this.generateListenerId()
    };

    const listeners = this.listeners.get(event);
    listeners.push(listenerObj);
    
    // 按优先级排序（优先级高的先执行）
    listeners.sort((a, b) => b.priority - a.priority);

    if (this.debugMode) {
      console.log(`📢 事件监听器已注册: ${event} (优先级: ${priority})`);
    }

    return listenerObj.id;
  }

  /**
   * 订阅一次性事件
   * @param {string} event - 事件名称
   * @param {Function} listener - 事件监听器
   * @param {number} priority - 优先级
   */
  once(event, listener, priority = 0) {
    if (typeof listener !== 'function') {
      throw new Error('Event listener must be a function');
    }

    if (!this.onceListeners.has(event)) {
      this.onceListeners.set(event, []);
    }

    const listenerObj = {
      fn: listener,
      priority: priority,
      id: this.generateListenerId()
    };

    const listeners = this.onceListeners.get(event);
    listeners.push(listenerObj);
    listeners.sort((a, b) => b.priority - a.priority);

    if (this.debugMode) {
      console.log(`📢 一次性事件监听器已注册: ${event} (优先级: ${priority})`);
    }

    return listenerObj.id;
  }

  /**
   * 取消订阅事件
   * @param {string} event - 事件名称
   * @param {Function|string} listener - 事件监听器函数或监听器ID
   */
  off(event, listener) {
    // 移除常规监听器
    if (this.listeners.has(event)) {
      const listeners = this.listeners.get(event);
      const index = typeof listener === 'string' 
        ? listeners.findIndex(l => l.id === listener)
        : listeners.findIndex(l => l.fn === listener);
      
      if (index !== -1) {
        listeners.splice(index, 1);
        if (listeners.length === 0) {
          this.listeners.delete(event);
        }
        if (this.debugMode) {
          console.log(`📢 事件监听器已移除: ${event}`);
        }
        return true;
      }
    }

    // 移除一次性监听器
    if (this.onceListeners.has(event)) {
      const listeners = this.onceListeners.get(event);
      const index = typeof listener === 'string'
        ? listeners.findIndex(l => l.id === listener)
        : listeners.findIndex(l => l.fn === listener);
      
      if (index !== -1) {
        listeners.splice(index, 1);
        if (listeners.length === 0) {
          this.onceListeners.delete(event);
        }
        if (this.debugMode) {
          console.log(`📢 一次性事件监听器已移除: ${event}`);
        }
        return true;
      }
    }

    return false;
  }

  /**
   * 发布事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   * @param {Object} options - 选项
   */
  emit(event, data = null, options = {}) {
    const {
      async = false,
      stopOnError = false,
      timeout = 5000
    } = options;

    // 记录事件历史
    this.recordEvent(event, data);

    if (this.debugMode) {
      console.log(`🚀 发布事件: ${event}`, data);
    }

    let results = [];
    let errors = [];

    // 处理常规监听器
    if (this.listeners.has(event)) {
      const listeners = this.listeners.get(event);
      results = results.concat(
        this.executeListeners(listeners, event, data, { async, stopOnError, timeout })
      );
    }

    // 处理一次性监听器
    if (this.onceListeners.has(event)) {
      const listeners = this.onceListeners.get(event);
      results = results.concat(
        this.executeListeners(listeners, event, data, { async, stopOnError, timeout })
      );
      
      // 清空一次性监听器
      this.onceListeners.delete(event);
    }

    // 如果是异步模式，返回Promise
    if (async) {
      return Promise.all(results).catch(error => {
        console.error(`❌ 事件处理出错: ${event}`, error);
        throw error;
      });
    }

    return results;
  }

  /**
   * 执行监听器
   */
  executeListeners(listeners, event, data, options) {
    const { async, stopOnError, timeout } = options;
    const results = [];

    for (const listenerObj of listeners) {
      try {
        if (async) {
          // 异步执行
          const promise = Promise.resolve(listenerObj.fn(data))
            .then(result => {
              if (this.debugMode) {
                console.log(`✅ 事件处理完成: ${event}`, result);
              }
              return result;
            })
            .catch(error => {
              console.error(`❌ 事件监听器出错: ${event}`, error);
              if (stopOnError) {
                throw error;
              }
              return { error: error.message };
            });

          // 添加超时处理
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error(`Event handler timeout: ${event}`)), timeout);
          });

          results.push(Promise.race([promise, timeoutPromise]));
        } else {
          // 同步执行
          const result = listenerObj.fn(data);
          results.push(result);
          
          if (this.debugMode) {
            console.log(`✅ 事件处理完成: ${event}`, result);
          }
        }
      } catch (error) {
        console.error(`❌ 事件监听器执行出错: ${event}`, error);
        if (stopOnError) {
          throw error;
        }
        results.push({ error: error.message });
      }
    }

    return results;
  }

  /**
   * 移除所有监听器
   * @param {string} event - 可选，指定事件名称
   */
  removeAllListeners(event = null) {
    if (event) {
      this.listeners.delete(event);
      this.onceListeners.delete(event);
      if (this.debugMode) {
        console.log(`📢 已移除所有监听器: ${event}`);
      }
    } else {
      this.listeners.clear();
      this.onceListeners.clear();
      if (this.debugMode) {
        console.log(`📢 已移除所有事件监听器`);
      }
    }
  }

  /**
   * 获取事件监听器数量
   * @param {string} event - 事件名称
   */
  getListenerCount(event) {
    const regularCount = this.listeners.has(event) ? this.listeners.get(event).length : 0;
    const onceCount = this.onceListeners.has(event) ? this.onceListeners.get(event).length : 0;
    return regularCount + onceCount;
  }

  /**
   * 获取所有已注册的事件名称
   */
  getEventNames() {
    const regularEvents = Array.from(this.listeners.keys());
    const onceEvents = Array.from(this.onceListeners.keys());
    return [...new Set([...regularEvents, ...onceEvents])];
  }

  /**
   * 获取事件历史
   */
  getEventHistory() {
    return [...this.eventHistory];
  }

  /**
   * 清空事件历史
   */
  clearEventHistory() {
    this.eventHistory = [];
  }

  /**
   * 启用/禁用调试模式
   */
  setDebugMode(enabled) {
    this.debugMode = enabled;
    console.log(`🐛 事件总线调试模式: ${enabled ? '开启' : '关闭'}`);
  }

  /**
   * 等待特定事件
   * @param {string} event - 事件名称
   * @param {number} timeout - 超时时间(毫秒)
   */
  waitFor(event, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.off(event, onEventReceived);
        reject(new Error(`Timeout waiting for event: ${event}`));
      }, timeout);

      const onEventReceived = (data) => {
        clearTimeout(timeoutId);
        resolve(data);
      };

      this.once(event, onEventReceived);
    });
  }

  /**
   * 生成监听器ID
   */
  generateListenerId() {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * 记录事件历史
   */
  recordEvent(event, data) {
    this.eventHistory.push({
      event,
      data,
      timestamp: Date.now()
    });

    // 限制历史记录大小
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      totalEvents: this.getEventNames().length,
      totalListeners: Array.from(this.listeners.values()).reduce((sum, arr) => sum + arr.length, 0) +
                     Array.from(this.onceListeners.values()).reduce((sum, arr) => sum + arr.length, 0),
      eventNames: this.getEventNames(),
      historySize: this.eventHistory.length,
      debugMode: this.debugMode
    };
  }
} 