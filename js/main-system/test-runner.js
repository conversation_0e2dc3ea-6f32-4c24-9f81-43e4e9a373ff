import { GameApplication } from './game-application.js';

/**
 * 测试新架构是否能正常运行
 */
class ArchitectureTestRunner {
  constructor() {
    // 立即设置模拟环境
    this.setupMockEnvironment();
    
    this.gameApp = null;
    this.testResults = {
      initialization: false,
      eventBus: false,
      stateManager: false,
      systemManager: false,
      gameLoop: false,
      touchController: false,
      debugController: false
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 === 开始架构测试 ===');
    console.log('📊 测试目标：验证分层架构是否能正常运行');
    
    try {
      // 测试1：初始化
      await this.testInitialization();
      
      // 测试2：事件总线
      this.testEventBus();
      
      // 测试3：状态管理
      this.testStateManager();
      
      // 测试4：系统管理
      this.testSystemManager();
      
      // 测试5：游戏循环
      this.testGameLoop();
      
      // 测试6：触摸控制
      this.testTouchController();
      
      // 测试7：调试控制
      this.testDebugController();
      
      // 生成测试报告
      this.generateTestReport();
      
    } catch (error) {
      console.error('❌ 架构测试失败:', error);
      this.logFailureDetails(error);
    }
  }

  /**
   * 测试应用初始化
   */
  async testInitialization() {
    console.log('🔧 测试1：应用初始化...');
    
    try {
      // 创建应用实例
      this.gameApp = new GameApplication();
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // 检查初始化状态
      if (this.gameApp.isInitialized) {
        console.log('✅ 应用初始化成功');
        this.testResults.initialization = true;
      } else {
        throw new Error('应用初始化失败');
      }
      
    } catch (error) {
      console.error('❌ 应用初始化测试失败:', error);
      throw error;
    }
  }

  /**
   * 测试事件总线
   */
  testEventBus() {
    console.log('📡 测试2：事件总线...');
    
    try {
      const eventBus = this.gameApp.getEventBus();
      
      // 测试事件监听和触发
      let eventReceived = false;
      eventBus.on('test:event', () => {
        eventReceived = true;
      });
      
      eventBus.emit('test:event');
      
      if (eventReceived) {
        console.log('✅ 事件总线工作正常');
        this.testResults.eventBus = true;
      } else {
        throw new Error('事件总线测试失败');
      }
      
    } catch (error) {
      console.error('❌ 事件总线测试失败:', error);
    }
  }

  /**
   * 测试状态管理器
   */
  testStateManager() {
    console.log('🎮 测试3：状态管理器...');
    
    try {
      const stateManager = this.gameApp.getStateManager();
      
      // 测试状态切换
      const initialState = stateManager.getCurrentState();
      console.log('📊 当前状态:', initialState);
      
      // 尝试状态切换
      stateManager.setState('GAME_PLAYING', { level: 1 });
      const newState = stateManager.getCurrentState();
      
      if (newState === 'GAME_PLAYING') {
        console.log('✅ 状态管理器工作正常');
        this.testResults.stateManager = true;
      } else {
        throw new Error('状态切换失败');
      }
      
    } catch (error) {
      console.error('❌ 状态管理器测试失败:', error);
    }
  }

  /**
   * 测试系统管理器
   */
  testSystemManager() {
    console.log('⚙️ 测试4：系统管理器...');
    
    try {
      const systemManager = this.gameApp.getSystemManager();
      const stats = systemManager.getSystemStats();
      
      console.log('📊 系统统计:', stats);
      
      if (stats.isInitialized && stats.totalSystems > 0) {
        console.log('✅ 系统管理器工作正常');
        this.testResults.systemManager = true;
      } else {
        throw new Error('系统管理器状态异常');
      }
      
    } catch (error) {
      console.error('❌ 系统管理器测试失败:', error);
    }
  }

  /**
   * 测试游戏循环
   */
  testGameLoop() {
    console.log('🔄 测试5：游戏循环...');
    
    try {
      // 简单验证游戏循环组件存在并可初始化
      const gameLoop = this.gameApp.gameLoop;
      
      if (gameLoop) {
        console.log('✅ 游戏循环组件存在');
        
        // 测试启动和立即停止，不让它持续运行
        gameLoop.start();
        
        // 检查状态
        if (gameLoop.isRunning) {
          console.log('✅ 游戏循环可以启动');
          this.testResults.gameLoop = true;
          
          // 立即停止
          gameLoop.stop();
          console.log('🛑 游戏循环已停止');
        } else {
          throw new Error('游戏循环启动失败');
        }
      } else {
        throw new Error('游戏循环组件不存在');
      }
      
    } catch (error) {
      console.error('❌ 游戏循环测试失败:', error);
    }
  }

  /**
   * 测试触摸控制器
   */
  testTouchController() {
    console.log('👆 测试6：触摸控制器...');
    
    try {
      const debugInfo = this.gameApp.touchController.getDebugInfo();
      console.log('📊 触摸控制器状态:', debugInfo);
      
      if (debugInfo.isInitialized) {
        console.log('✅ 触摸控制器工作正常');
        this.testResults.touchController = true;
      } else {
        throw new Error('触摸控制器初始化失败');
      }
      
    } catch (error) {
      console.error('❌ 触摸控制器测试失败:', error);
    }
  }

  /**
   * 测试调试控制器
   */
  testDebugController() {
    console.log('🔍 测试7：调试控制器...');
    
    try {
      const debugController = this.gameApp.getDebugController();
      const debugInfo = debugController.getDebugInfo();
      
      console.log('📊 调试信息:', debugInfo);
      
      if (debugInfo.isInitialized) {
        console.log('✅ 调试控制器工作正常');
        this.testResults.debugController = true;
      } else {
        throw new Error('调试控制器初始化失败');
      }
      
    } catch (error) {
      console.error('❌ 调试控制器测试失败:', error);
    }
  }

  /**
   * 设置模拟环境
   */
  setupMockEnvironment() {
    // 模拟GameGlobal
    if (typeof global !== 'undefined') {
      global.GameGlobal = {
        canvas: {
          addEventListener: () => {},
          removeEventListener: () => {},
          getBoundingClientRect: () => ({ left: 0, top: 0 }),
          getContext: () => ({
            clearRect: () => {},
            fillRect: () => {},
            drawImage: () => {},
            fillText: () => {},
            save: () => {},
            restore: () => {},
            translate: () => {},
            scale: () => {},
            rotate: () => {}
          })
        }
      };
      
      // 模拟requestAnimationFrame和cancelAnimationFrame
      global.requestAnimationFrame = (callback) => {
        return setTimeout(callback, 16); // 模拟60FPS
      };
      
      global.cancelAnimationFrame = (id) => {
        clearTimeout(id);
      };
      
      // 模拟性能API
      global.performance = {
        now: () => Date.now()
      };
    }
    
    if (typeof window !== 'undefined') {
      window.GameGlobal = global.GameGlobal;
      window.requestAnimationFrame = global.requestAnimationFrame;
      window.cancelAnimationFrame = global.cancelAnimationFrame;
      window.performance = global.performance;
    }
    
    console.log('🏗️ 模拟环境设置完成');
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    console.log('\n📋 === 架构测试报告 ===');
    
    const totalTests = Object.keys(this.testResults).length;
    const passedTests = Object.values(this.testResults).filter(result => result).length;
    const successRate = (passedTests / totalTests * 100).toFixed(1);
    
    console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过 (${successRate}%)`);
    console.log('\n📝 详细结果:');
    
    Object.entries(this.testResults).forEach(([testName, passed], index) => {
      const icon = passed ? '✅' : '❌';
      const status = passed ? '通过' : '失败';
      console.log(`${index + 1}. ${icon} ${testName}: ${status}`);
    });
    
    if (passedTests === totalTests) {
      console.log('\n🎉 恭喜！新架构完全可用！');
      console.log('💡 可以开始使用新的分层架构替换旧的main.js了');
    } else {
      console.log('\n⚠️ 部分测试失败，需要进一步调试');
    }
    
    console.log('\n=== 测试完成 ===\n');
  }

  /**
   * 记录失败详情
   */
  logFailureDetails(error) {
    console.log('\n💥 === 失败分析 ===');
    console.log('🔍 错误信息:', error.message);
    console.log('📚 错误堆栈:', error.stack);
    console.log('🛠️ 建议：检查依赖导入和模块路径');
    console.log('===================\n');
  }
}

// 立即运行测试
console.log('🚀 开始执行架构测试...');
const testRunner = new ArchitectureTestRunner();
testRunner.runAllTests().then(() => {
  console.log('🏁 测试执行完成');
}).catch((error) => {
  console.error('💥 测试执行失败:', error);
});

export { ArchitectureTestRunner }; 