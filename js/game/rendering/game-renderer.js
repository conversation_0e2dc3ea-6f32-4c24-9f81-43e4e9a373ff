/**
 * 游戏渲染器
 * 职责：专门管理游戏中的渲染逻辑、视觉效果、分层渲染和渲染优化
 */
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../../render.js';
import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

export class GameRenderer extends Emitter {
  constructor(canvas, options = {}) {
    super();
    
    this.canvas = canvas;
    this.ctx = canvas?.getContext('2d');
    
    this.options = {
      enableLayeredRendering: true,  // 启用分层渲染
      enableVsync: true,            // 启用垂直同步
      enableDebugRendering: false,  // 启用调试渲染
      targetFPS: 60,                // 目标帧率
      backgroundAlpha: 1.0,         // 背景透明度
      enableAntiAliasing: true,     // 启用抗锯齿
      pixelRatio: window.devicePixelRatio || 1, // 像素比
      enableOptimization: true,     // 启用渲染优化
      ...options
    };
    
    // 渲染状态
    this.renderState = {
      isInitialized: false,
      currentFrame: 0,
      lastFrameTime: 0,
      frameCount: 0,
      renderTime: 0,
      skipFrame: false,
      needsRedraw: true
    };
    
    // 渲染层级系统
    this.layerSystem = {
      layers: new Map([
        ['background', { zIndex: 0, canvas: null, ctx: null, dirty: true }],
        ['grid', { zIndex: 10, canvas: null, ctx: null, dirty: true }],
        ['blocks', { zIndex: 20, canvas: null, ctx: null, dirty: true }],
        ['tetromino', { zIndex: 30, canvas: null, ctx: null, dirty: true }],
        ['effects', { zIndex: 40, canvas: null, ctx: null, dirty: true }],
        ['ui', { zIndex: 50, canvas: null, ctx: null, dirty: true }],
        ['debug', { zIndex: 100, canvas: null, ctx: null, dirty: true }]
      ]),
      mainLayer: 'blocks',
      enableDirtyTracking: true
    };
    
    // 渲染组件
    this.renderComponents = {
      gridRenderer: null,
      blockRenderer: null,
      tetrominoRenderer: null,
      effectRenderer: null,
      uiRenderer: null,
      debugRenderer: null
    };
    
    // 渲染缓存
    this.renderCache = {
      enabled: true,
      canvasCache: new Map(),
      imageCache: new Map(),
      pathCache: new Map(),
      maxCacheSize: 100,
      cacheHits: 0,
      cacheMisses: 0
    };
    
    // 渲染统计
    this.renderStats = {
      totalFramesRendered: 0,
      averageFPS: 0,
      renderCalls: 0,
      averageRenderTime: 0,
      droppedFrames: 0,
      layerRenderCounts: new Map(),
      cacheHitRate: 0
    };
    
    // 性能监控
    this.performanceMonitor = {
      enabled: true,
      frameTimings: [],
      maxFrameTimings: 60,
      renderTimings: [],
      maxRenderTimings: 60,
      memoryUsage: 0,
      lastGCTime: Date.now()
    };
    
    console.log('🎨 游戏渲染器已初始化', this.options);
    
    // 初始化渲染系统
    this._initializeRenderer();
  }

  /**
   * 初始化渲染器
   * @private
   */
  _initializeRenderer() {
    if (!this.canvas || !this.ctx) {
      console.warn('⚠️ 渲染器初始化失败：缺少canvas或context');
      return;
    }
    
    // 设置画布属性
    this._setupCanvas();
    
    // 初始化分层渲染
    if (this.options.enableLayeredRendering) {
      this._initializeLayers();
    }
    
    // 初始化渲染组件
    this._initializeRenderComponents();
    
    // 设置渲染循环
    this._setupRenderLoop();
    
    this.renderState.isInitialized = true;
    console.log('✅ 渲染器初始化完成');
    
    this.emit('renderer:initialized');
  }

  /**
   * 渲染游戏帧
   * @param {Object} gameState - 游戏状态
   * @param {number} deltaTime - 时间间隔
   */
  render(gameState, deltaTime = 0) {
    if (!this.renderState.isInitialized) return;
    
    const startTime = performance.now();
    
    // 检查是否需要重绘
    if (!this._needsRender(gameState)) {
      return;
    }
    
    try {
      // 开始渲染帧
      this._beginFrame();
      
      // 清除画布
      this._clearCanvas();
      
      // 分层渲染
      if (this.options.enableLayeredRendering) {
        this._renderLayers(gameState);
      } else {
        this._renderDirect(gameState);
      }
      
      // 渲染调试信息
      if (this.options.enableDebugRendering) {
        this._renderDebugInfo(gameState);
      }
      
      // 结束渲染帧
      this._endFrame(startTime);
      
    } catch (error) {
      console.error('❌ 渲染错误:', error);
      this.renderStats.droppedFrames++;
    }
  }

  /**
   * 渲染网格
   * @param {Object} grid - 网格对象
   * @param {Object} options - 渲染选项
   */
  renderGrid(grid, options = {}) {
    if (!grid || !this.renderComponents.gridRenderer) return;
    
    const layer = this._getLayer('grid');
    if (layer && layer.dirty) {
      this.renderComponents.gridRenderer.render(layer.ctx, grid, options);
      layer.dirty = false;
    }
  }

  /**
   * 渲染方块
   * @param {Array} blocks - 方块数组
   * @param {Object} options - 渲染选项
   */
  renderBlocks(blocks, options = {}) {
    if (!blocks || !this.renderComponents.blockRenderer) return;
    
    const layer = this._getLayer('blocks');
    if (layer) {
      this.renderComponents.blockRenderer.render(layer.ctx, blocks, options);
      layer.dirty = false;
    }
  }

  /**
   * 渲染当前方块
   * @param {Object} tetromino - 当前方块
   * @param {Object} options - 渲染选项
   */
  renderTetromino(tetromino, options = {}) {
    if (!tetromino || !this.renderComponents.tetrominoRenderer) return;
    
    const layer = this._getLayer('tetromino');
    if (layer) {
      this.renderComponents.tetrominoRenderer.render(layer.ctx, tetromino, options);
      layer.dirty = false;
    }
  }

  /**
   * 渲染特效
   * @param {Array} effects - 特效数组
   * @param {Object} options - 渲染选项
   */
  renderEffects(effects, options = {}) {
    if (!effects || !this.renderComponents.effectRenderer) return;
    
    const layer = this._getLayer('effects');
    if (layer) {
      this.renderComponents.effectRenderer.render(layer.ctx, effects, options);
      layer.dirty = false;
    }
  }

  /**
   * 渲染UI元素
   * @param {Object} uiData - UI数据
   * @param {Object} options - 渲染选项
   */
  renderUI(uiData, options = {}) {
    if (!uiData || !this.renderComponents.uiRenderer) return;
    
    const layer = this._getLayer('ui');
    if (layer) {
      this.renderComponents.uiRenderer.render(layer.ctx, uiData, options);
      layer.dirty = false;
    }
  }

  /**
   * 标记层需要重绘
   * @param {string} layerName - 层名称
   */
  markLayerDirty(layerName) {
    const layer = this._getLayer(layerName);
    if (layer) {
      layer.dirty = true;
      this.renderState.needsRedraw = true;
    }
  }

  /**
   * 标记所有层需要重绘
   */
  markAllLayersDirty() {
    for (const layer of this.layerSystem.layers.values()) {
      layer.dirty = true;
    }
    this.renderState.needsRedraw = true;
  }

  /**
   * 获取渲染统计
   * @returns {Object} 渲染统计信息
   */
  getRenderStats() {
    return {
      state: { ...this.renderState },
      stats: { ...this.renderStats },
      performance: {
        averageFPS: this._calculateAverageFPS(),
        averageRenderTime: this.renderStats.averageRenderTime,
        droppedFrames: this.renderStats.droppedFrames,
        cacheHitRate: this._calculateCacheHitRate()
      },
      layers: {
        total: this.layerSystem.layers.size,
        dirty: Array.from(this.layerSystem.layers.values()).filter(l => l.dirty).length
      },
      cache: {
        enabled: this.renderCache.enabled,
        size: this.renderCache.canvasCache.size,
        hitRate: this._calculateCacheHitRate()
      }
    };
  }

  /**
   * 调整画布大小
   * @param {number} width - 新宽度
   * @param {number} height - 新高度
   */
  resize(width, height) {
    if (!this.canvas) return;
    
    this.canvas.width = width * this.options.pixelRatio;
    this.canvas.height = height * this.options.pixelRatio;
    this.canvas.style.width = width + 'px';
    this.canvas.style.height = height + 'px';
    
    // 重新设置context属性
    this._setupCanvasContext();
    
    // 调整所有层的大小
    this._resizeLayers(width, height);
    
    // 标记所有层需要重绘
    this.markAllLayersDirty();
    
    console.log(`🖼️ 画布大小调整: ${width}x${height}`);
    
    this.emit('renderer:resized', { width, height });
  }

  /**
   * 设置渲染选项
   * @param {Object} options - 新选项
   */
  setOptions(options) {
    Object.assign(this.options, options);
    
    // 应用新选项
    if (options.enableAntiAliasing !== undefined) {
      this._setupCanvasContext();
    }
    
    if (options.enableLayeredRendering !== undefined) {
      if (options.enableLayeredRendering && !this.layerSystem.layers.size) {
        this._initializeLayers();
      }
    }
    
    console.log('⚙️ 渲染选项已更新', options);
  }

  /**
   * 重置渲染器
   */
  reset() {
    console.log('🔄 重置渲染器');
    
    // 重置渲染状态
    this.renderState = {
      isInitialized: this.renderState.isInitialized,
      currentFrame: 0,
      lastFrameTime: 0,
      frameCount: 0,
      renderTime: 0,
      skipFrame: false,
      needsRedraw: true
    };
    
    // 重置统计
    this.renderStats = {
      totalFramesRendered: 0,
      averageFPS: 0,
      renderCalls: 0,
      averageRenderTime: 0,
      droppedFrames: 0,
      layerRenderCounts: new Map(),
      cacheHitRate: 0
    };
    
    // 清除缓存
    this._clearCache();
    
    // 重置性能监控
    this.performanceMonitor.frameTimings = [];
    this.performanceMonitor.renderTimings = [];
    
    // 标记所有层需要重绘
    this.markAllLayersDirty();
    
    this.emit('renderer:reset');
  }

  // =================== 私有方法 ===================

  /**
   * 设置画布
   * @private
   */
  _setupCanvas() {
    this.canvas.width = this.canvas.clientWidth * this.options.pixelRatio;
    this.canvas.height = this.canvas.clientHeight * this.options.pixelRatio;
    
    this._setupCanvasContext();
  }

  /**
   * 设置画布上下文
   * @private
   */
  _setupCanvasContext() {
    if (!this.ctx) return;
    
    this.ctx.scale(this.options.pixelRatio, this.options.pixelRatio);
    
    if (this.options.enableAntiAliasing) {
      this.ctx.imageSmoothingEnabled = true;
      this.ctx.imageSmoothingQuality = 'high';
    } else {
      this.ctx.imageSmoothingEnabled = false;
    }
  }

  /**
   * 初始化渲染层
   * @private
   */
  _initializeLayers() {
    for (const [layerName, layer] of this.layerSystem.layers) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      canvas.width = this.canvas.width;
      canvas.height = this.canvas.height;
      
      layer.canvas = canvas;
      layer.ctx = ctx;
      
      // 设置上下文属性
      ctx.scale(this.options.pixelRatio, this.options.pixelRatio);
      if (this.options.enableAntiAliasing) {
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
      }
    }
    
    console.log('🎨 渲染层初始化完成');
  }

  /**
   * 初始化渲染组件
   * @private
   */
  _initializeRenderComponents() {
    // 这里可以初始化具体的渲染组件
    this.renderComponents = {
      gridRenderer: {
        render: (ctx, grid, options) => {
          // 简化的网格渲染逻辑
          if (grid && typeof grid.render === 'function') {
            grid.render(ctx);
          }
        }
      },
      blockRenderer: {
        render: (ctx, blocks, options) => {
          // 简化的方块渲染逻辑
          if (Array.isArray(blocks)) {
            blocks.forEach(block => {
              if (block && typeof block.render === 'function') {
                block.render(ctx);
              }
            });
          }
        }
      },
      tetrominoRenderer: {
        render: (ctx, tetromino, options) => {
          // 简化的方块渲染逻辑
          if (tetromino && typeof tetromino.render === 'function') {
            tetromino.render(ctx, options.blockSize || 25);
          }
        }
      },
      effectRenderer: {
        render: (ctx, effects, options) => {
          // 简化的特效渲染逻辑
          if (Array.isArray(effects)) {
            effects.forEach(effect => {
              if (effect && typeof effect.render === 'function') {
                effect.render(ctx);
              }
            });
          }
        }
      },
      uiRenderer: {
        render: (ctx, uiData, options) => {
          // 简化的UI渲染逻辑
          if (uiData && Array.isArray(uiData.components)) {
            uiData.components.forEach(component => {
              if (component && typeof component.render === 'function') {
                component.render(ctx);
              }
            });
          }
        }
      },
      debugRenderer: {
        render: (ctx, debugData, options) => {
          // 调试信息渲染
          this._renderDebugInfo(debugData);
        }
      }
    };
  }

  /**
   * 设置渲染循环
   * @private
   */
  _setupRenderLoop() {
    // 这里可以设置渲染循环的优化逻辑
    // 比如帧率控制、垂直同步等
  }

  /**
   * 检查是否需要渲染
   * @param {Object} gameState - 游戏状态
   * @returns {boolean} 是否需要渲染
   * @private
   */
  _needsRender(gameState) {
    // 简单的重绘检查
    return this.renderState.needsRedraw || 
           this._hasAnimations(gameState) ||
           this._hasMovingElements(gameState);
  }

  /**
   * 检查是否有动画
   * @param {Object} gameState - 游戏状态
   * @returns {boolean} 是否有动画
   * @private
   */
  _hasAnimations(gameState) {
    return gameState?.hasAnimations || false;
  }

  /**
   * 检查是否有移动元素
   * @param {Object} gameState - 游戏状态
   * @returns {boolean} 是否有移动元素
   * @private
   */
  _hasMovingElements(gameState) {
    return gameState?.currentTetromino !== null;
  }

  /**
   * 开始渲染帧
   * @private
   */
  _beginFrame() {
    this.renderState.currentFrame++;
    this.renderState.frameCount++;
    this.renderStats.renderCalls++;
  }

  /**
   * 清除画布
   * @private
   */
  _clearCanvas() {
    if (this.options.enableLayeredRendering) {
      // 清除脏层
      for (const layer of this.layerSystem.layers.values()) {
        if (layer.dirty && layer.ctx) {
          layer.ctx.clearRect(0, 0, layer.canvas.width, layer.canvas.height);
        }
      }
    } else {
      // 清除主画布
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
  }

  /**
   * 分层渲染
   * @param {Object} gameState - 游戏状态
   * @private
   */
  _renderLayers(gameState) {
    // 按z-index顺序渲染层
    const sortedLayers = Array.from(this.layerSystem.layers.entries())
      .sort(([,a], [,b]) => a.zIndex - b.zIndex);
    
    for (const [layerName, layer] of sortedLayers) {
      if (layer.dirty) {
        this._renderLayer(layerName, gameState);
        this.renderStats.layerRenderCounts.set(
          layerName, 
          (this.renderStats.layerRenderCounts.get(layerName) || 0) + 1
        );
      }
      
      // 合成到主画布
      if (layer.canvas) {
        this.ctx.drawImage(layer.canvas, 0, 0);
      }
    }
  }

  /**
   * 渲染单个层
   * @param {string} layerName - 层名称
   * @param {Object} gameState - 游戏状态
   * @private
   */
  _renderLayer(layerName, gameState) {
    switch (layerName) {
      case 'grid':
        this.renderGrid(gameState.grid);
        break;
      case 'blocks':
        this.renderBlocks(gameState.blocks);
        break;
      case 'tetromino':
        this.renderTetromino(gameState.currentTetromino);
        break;
      case 'effects':
        this.renderEffects(gameState.effects);
        break;
      case 'ui':
        this.renderUI(gameState.ui);
        break;
    }
  }

  /**
   * 直接渲染（非分层）
   * @param {Object} gameState - 游戏状态
   * @private
   */
  _renderDirect(gameState) {
    // 直接在主画布上渲染所有元素
    if (gameState.grid) {
      this.renderGrid(gameState.grid);
    }
    if (gameState.blocks) {
      this.renderBlocks(gameState.blocks);
    }
    if (gameState.currentTetromino) {
      this.renderTetromino(gameState.currentTetromino);
    }
    if (gameState.effects) {
      this.renderEffects(gameState.effects);
    }
    if (gameState.ui) {
      this.renderUI(gameState.ui);
    }
  }

  /**
   * 渲染调试信息
   * @param {Object} gameState - 游戏状态
   * @private
   */
  _renderDebugInfo(gameState) {
    const debugLayer = this._getLayer('debug');
    const ctx = debugLayer?.ctx || this.ctx;
    
    if (!ctx) return;
    
    ctx.save();
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(10, 10, 200, 100);
    
    ctx.fillStyle = 'white';
    ctx.font = '12px monospace';
    
    const stats = this.getRenderStats();
    const debugText = [
      `FPS: ${stats.performance.averageFPS.toFixed(1)}`,
      `Render: ${stats.performance.averageRenderTime.toFixed(1)}ms`,
      `Dropped: ${stats.performance.droppedFrames}`,
      `Cache: ${(stats.cache.hitRate * 100).toFixed(1)}%`,
      `Layers: ${stats.layers.dirty}/${stats.layers.total} dirty`
    ];
    
    debugText.forEach((text, index) => {
      ctx.fillText(text, 15, 30 + index * 15);
    });
    
    ctx.restore();
  }

  /**
   * 结束渲染帧
   * @param {number} startTime - 开始时间
   * @private
   */
  _endFrame(startTime) {
    const renderTime = performance.now() - startTime;
    
    // 更新统计
    this.renderState.renderTime = renderTime;
    this.renderStats.totalFramesRendered++;
    this.renderStats.averageRenderTime = 
      (this.renderStats.averageRenderTime + renderTime) / 2;
    
    // 更新性能监控
    this._updatePerformanceStats(renderTime);
    
    // 重置重绘标志
    this.renderState.needsRedraw = false;
    
    this.emit('frame:rendered', {
      frameNumber: this.renderState.currentFrame,
      renderTime
    });
  }

  /**
   * 获取渲染层
   * @param {string} layerName - 层名称
   * @returns {Object} 渲染层
   * @private
   */
  _getLayer(layerName) {
    return this.layerSystem.layers.get(layerName);
  }

  /**
   * 调整层大小
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @private
   */
  _resizeLayers(width, height) {
    for (const layer of this.layerSystem.layers.values()) {
      if (layer.canvas) {
        layer.canvas.width = width * this.options.pixelRatio;
        layer.canvas.height = height * this.options.pixelRatio;
        layer.canvas.style.width = width + 'px';
        layer.canvas.style.height = height + 'px';
        
        if (layer.ctx) {
          layer.ctx.scale(this.options.pixelRatio, this.options.pixelRatio);
        }
      }
    }
  }

  /**
   * 计算平均FPS
   * @returns {number} 平均FPS
   * @private
   */
  _calculateAverageFPS() {
    if (this.performanceMonitor.frameTimings.length < 2) return 0;
    
    const timings = this.performanceMonitor.frameTimings;
    const totalTime = timings[timings.length - 1] - timings[0];
    const frameCount = timings.length - 1;
    
    return frameCount > 0 ? (frameCount / totalTime) * 1000 : 0;
  }

  /**
   * 计算缓存命中率
   * @returns {number} 缓存命中率
   * @private
   */
  _calculateCacheHitRate() {
    const total = this.renderCache.cacheHits + this.renderCache.cacheMisses;
    return total > 0 ? this.renderCache.cacheHits / total : 0;
  }

  /**
   * 更新性能统计
   * @param {number} renderTime - 渲染时间
   * @private
   */
  _updatePerformanceStats(renderTime) {
    const now = performance.now();
    
    // 更新帧时间
    this.performanceMonitor.frameTimings.push(now);
    if (this.performanceMonitor.frameTimings.length > this.performanceMonitor.maxFrameTimings) {
      this.performanceMonitor.frameTimings.shift();
    }
    
    // 更新渲染时间
    this.performanceMonitor.renderTimings.push(renderTime);
    if (this.performanceMonitor.renderTimings.length > this.performanceMonitor.maxRenderTimings) {
      this.performanceMonitor.renderTimings.shift();
    }
  }

  /**
   * 清除缓存
   * @private
   */
  _clearCache() {
    this.renderCache.canvasCache.clear();
    this.renderCache.imageCache.clear();
    this.renderCache.pathCache.clear();
    this.renderCache.cacheHits = 0;
    this.renderCache.cacheMisses = 0;
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    // 清除所有层
    for (const layer of this.layerSystem.layers.values()) {
      if (layer.canvas) {
        layer.canvas = null;
        layer.ctx = null;
      }
    }
    
    // 清除缓存
    this._clearCache();
    
    // 移除事件监听器
    this.removeAllListeners();
    
    this.renderState.isInitialized = false;
    
    console.log('🎨 游戏渲染器已销毁');
  }
} 
