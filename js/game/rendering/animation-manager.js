import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

/**
 * 动画管理器
 * 职责：专门管理游戏中的动画系统、过渡效果、时间轴管理和动画队列
 */
export class AnimationManager extends Emitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      enableAnimations: true,       // 启用动画
      defaultDuration: 300,         // 默认动画持续时间(ms)
      defaultEasing: 'easeOutQuad', // 默认缓动函数
      maxConcurrentAnimations: 50,  // 最大并发动画数
      enablePerformanceMode: false, // 启用性能模式
      globalTimeScale: 1.0,         // 全局时间缩放
      enableDebugMode: false,       // 启用调试模式
      autoCleanup: true,            // 自动清理完成的动画
      ...options
    };
    
    // 动画状态
    this.animationState = {
      isInitialized: false,
      isPlaying: true,
      isPaused: false,
      currentFrame: 0,
      totalAnimations: 0,
      activeAnimations: 0,
      lastUpdateTime: 0
    };
    
    // 动画队列
    this.animationQueue = {
      active: new Map(),      // 活跃动画
      pending: new Map(),     // 等待执行的动画
      finished: new Map(),    // 已完成的动画
      paused: new Map()       // 暂停的动画
    };
    
    // 动画时间轴
    this.timeline = {
      globalTime: 0,
      timeScale: 1.0,
      sequences: new Map(),   // 动画序列
      groups: new Map(),      // 动画组
      markers: new Map()      // 时间标记
    };
    
    // 缓动函数库
    this.easingFunctions = {
      linear: t => t,
      easeInQuad: t => t * t,
      easeOutQuad: t => t * (2 - t),
      easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeInCubic: t => t * t * t,
      easeOutCubic: t => (--t) * t * t + 1,
      easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
      easeInQuart: t => t * t * t * t,
      easeOutQuart: t => 1 - (--t) * t * t * t,
      easeInOutQuart: t => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t,
      easeInSine: t => 1 - Math.cos(t * Math.PI / 2),
      easeOutSine: t => Math.sin(t * Math.PI / 2),
      easeInOutSine: t => -(Math.cos(Math.PI * t) - 1) / 2,
      bounce: t => {
        if (t < 1 / 2.75) return 7.5625 * t * t;
        if (t < 2 / 2.75) return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        if (t < 2.5 / 2.75) return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
      },
      elastic: t => t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI)
    };
    
    // 动画类型处理器
    this.animationTypes = {
      fade: this._createFadeAnimation.bind(this),
      slide: this._createSlideAnimation.bind(this),
      scale: this._createScaleAnimation.bind(this),
      rotate: this._createRotateAnimation.bind(this),
      color: this._createColorAnimation.bind(this),
      position: this._createPositionAnimation.bind(this),
      shake: this._createShakeAnimation.bind(this),
      bounce: this._createBounceAnimation.bind(this),
      flash: this._createFlashAnimation.bind(this),
      custom: this._createCustomAnimation.bind(this)
    };
    
    // 动画统计
    this.animationStats = {
      totalCreated: 0,
      totalCompleted: 0,
      totalCancelled: 0,
      averageDuration: 0,
      peakConcurrent: 0,
      performanceMetrics: {
        averageUpdateTime: 0,
        frameDrops: 0,
        memoryUsage: 0
      }
    };
    
    // 性能监控
    this.performanceMonitor = {
      enabled: true,
      updateTimes: [],
      maxUpdateTimes: 60,
      memorySnapshots: [],
      maxMemorySnapshots: 10,
      lastCleanupTime: Date.now()
    };
    
    console.log('🎬 动画管理器已初始化', this.options);
    
    // 初始化动画系统
    this._initializeAnimationSystem();
  }

  /**
   * 初始化动画系统
   * @private
   */
  _initializeAnimationSystem() {
    this.animationState.isInitialized = true;
    this.animationState.lastUpdateTime = performance.now();
    
    console.log('✅ 动画系统初始化完成');
    
    this.emit('animation:system:initialized');
  }

  /**
   * 创建动画
   * @param {Object} config - 动画配置
   * @returns {string} 动画ID
   */
  createAnimation(config) {
    if (!this.options.enableAnimations) {
      console.warn('⚠️ 动画已禁用');
      return null;
    }
    
    const animationId = this._generateAnimationId();
    
    const animation = {
      id: animationId,
      type: config.type || 'custom',
      target: config.target,
      
      // 时间配置
      duration: config.duration || this.options.defaultDuration,
      delay: config.delay || 0,
      startTime: null,
      endTime: null,
      currentTime: 0,
      
      // 缓动配置
      easing: config.easing || this.options.defaultEasing,
      easingFunction: this._getEasingFunction(config.easing || this.options.defaultEasing),
      
      // 属性配置
      from: config.from || {},
      to: config.to || {},
      current: {},
      
      // 状态配置
      state: 'pending',
      progress: 0,
      direction: config.direction || 'normal', // normal, reverse, alternate
      iterations: config.iterations || 1,      // 迭代次数，Infinity为无限
      currentIteration: 0,
      
      // 回调配置
      onStart: config.onStart || null,
      onUpdate: config.onUpdate || null,
      onComplete: config.onComplete || null,
      onCancel: config.onCancel || null,
      
      // 附加配置
      group: config.group || null,
      priority: config.priority || 0,
      metadata: config.metadata || {}
    };
    
    // 初始化当前值
    animation.current = { ...animation.from };
    
    // 创建特定类型的动画处理器
    if (this.animationTypes[animation.type]) {
      animation.typeHandler = this.animationTypes[animation.type](animation);
    }
    
    // 添加到队列
    if (animation.delay > 0) {
      this.animationQueue.pending.set(animationId, animation);
    } else {
      this._startAnimation(animation);
    }
    
    this.animationStats.totalCreated++;
    this.animationState.totalAnimations++;
    
    console.log(`🎬 动画已创建: ${animationId} (${animation.type})`);
    
    this.emit('animation:created', { animationId, animation });
    
    return animationId;
  }

  /**
   * 更新动画系统
   * @param {number} deltaTime - 时间间隔
   */
  update(deltaTime = 0) {
    if (!this.animationState.isInitialized || 
        !this.animationState.isPlaying || 
        this.animationState.isPaused) {
      return;
    }
    
    const startTime = performance.now();
    
    // 更新全局时间
    this.timeline.globalTime += deltaTime * this.options.globalTimeScale;
    this.animationState.currentFrame++;
    
    // 处理等待中的动画
    this._processPendingAnimations(deltaTime);
    
    // 更新活跃动画
    this._updateActiveAnimations(deltaTime);
    
    // 清理完成的动画
    if (this.options.autoCleanup) {
      this._cleanupFinishedAnimations();
    }
    
    // 更新性能统计
    this._updatePerformanceStats(startTime);
    
    this.emit('animation:system:updated', {
      activeCount: this.animationQueue.active.size,
      deltaTime
    });
  }

  /**
   * 播放动画
   * @param {string} animationId - 动画ID
   */
  play(animationId) {
    const animation = this._getAnimation(animationId);
    if (!animation) return false;
    
    if (animation.state === 'paused') {
      this.animationQueue.paused.delete(animationId);
      this.animationQueue.active.set(animationId, animation);
      animation.state = 'playing';
      
      console.log(`▶️ 动画恢复播放: ${animationId}`);
      this.emit('animation:resumed', { animationId, animation });
    } else if (animation.state === 'pending') {
      this._startAnimation(animation);
    }
    
    return true;
  }

  /**
   * 暂停动画
   * @param {string} animationId - 动画ID
   */
  pause(animationId) {
    const animation = this.animationQueue.active.get(animationId);
    if (!animation) return false;
    
    this.animationQueue.active.delete(animationId);
    this.animationQueue.paused.set(animationId, animation);
    animation.state = 'paused';
    
    console.log(`⏸️ 动画已暂停: ${animationId}`);
    this.emit('animation:paused', { animationId, animation });
    
    return true;
  }

  /**
   * 停止动画
   * @param {string} animationId - 动画ID
   * @param {boolean} complete - 是否完成到最终状态
   */
  stop(animationId, complete = false) {
    const animation = this._getAnimation(animationId);
    if (!animation) return false;
    
    if (complete) {
      // 设置到最终状态
      this._applyAnimationValues(animation, 1.0);
      if (animation.onComplete) {
        animation.onComplete(animation);
      }
    } else {
      // 取消动画
      if (animation.onCancel) {
        animation.onCancel(animation);
      }
      this.animationStats.totalCancelled++;
    }
    
    // 从所有队列中移除
    this._removeAnimationFromQueues(animationId);
    
    console.log(`⏹️ 动画已停止: ${animationId} (完成: ${complete})`);
    this.emit('animation:stopped', { animationId, animation, completed: complete });
    
    return true;
  }

  /**
   * 创建动画序列
   * @param {Array} animations - 动画配置数组
   * @param {Object} options - 序列选项
   * @returns {string} 序列ID
   */
  createSequence(animations, options = {}) {
    const sequenceId = this._generateSequenceId();
    
    const sequence = {
      id: sequenceId,
      animations: [],
      currentIndex: 0,
      state: 'pending',
      options: {
        loop: options.loop || false,
        autoStart: options.autoStart !== false,
        onComplete: options.onComplete || null,
        onSequenceComplete: options.onSequenceComplete || null
      }
    };
    
    // 设置动画序列的时间延迟
    let totalDelay = 0;
    animations.forEach((config, index) => {
      const animConfig = {
        ...config,
        delay: totalDelay,
        onComplete: (animation) => {
          if (config.onComplete) config.onComplete(animation);
          this._advanceSequence(sequenceId);
        }
      };
      
      sequence.animations.push(animConfig);
      totalDelay += (config.duration || this.options.defaultDuration);
      
      if (options.overlap) {
        totalDelay -= options.overlap;
      }
    });
    
    this.timeline.sequences.set(sequenceId, sequence);
    
    if (sequence.options.autoStart) {
      this.playSequence(sequenceId);
    }
    
    console.log(`🎬 动画序列已创建: ${sequenceId} (${animations.length}个动画)`);
    
    return sequenceId;
  }

  /**
   * 播放动画序列
   * @param {string} sequenceId - 序列ID
   */
  playSequence(sequenceId) {
    const sequence = this.timeline.sequences.get(sequenceId);
    if (!sequence) return false;
    
    sequence.state = 'playing';
    sequence.currentIndex = 0;
    
    if (sequence.animations.length > 0) {
      const firstAnimation = sequence.animations[0];
      const animationId = this.createAnimation(firstAnimation);
      sequence.currentAnimationId = animationId;
    }
    
    console.log(`▶️ 序列开始播放: ${sequenceId}`);
    this.emit('sequence:started', { sequenceId, sequence });
    
    return true;
  }

  /**
   * 创建动画组
   * @param {Array} animations - 动画配置数组
   * @param {Object} options - 组选项
   * @returns {string} 组ID
   */
  createGroup(animations, options = {}) {
    const groupId = this._generateGroupId();
    
    const group = {
      id: groupId,
      animationIds: [],
      state: 'pending',
      options: {
        sync: options.sync !== false,    // 同步播放
        waitForAll: options.waitForAll !== false, // 等待所有动画完成
        onGroupComplete: options.onGroupComplete || null
      }
    };
    
    // 创建组内的所有动画
    animations.forEach(config => {
      const animationId = this.createAnimation({
        ...config,
        group: groupId,
        onComplete: (animation) => {
          if (config.onComplete) config.onComplete(animation);
          this._checkGroupCompletion(groupId);
        }
      });
      group.animationIds.push(animationId);
    });
    
    this.timeline.groups.set(groupId, group);
    
    console.log(`🎬 动画组已创建: ${groupId} (${animations.length}个动画)`);
    
    return groupId;
  }

  /**
   * 暂停所有动画
   */
  pauseAll() {
    this.animationState.isPaused = true;
    
    console.log('⏸️ 所有动画已暂停');
    this.emit('animation:system:paused');
  }

  /**
   * 恢复所有动画
   */
  resumeAll() {
    this.animationState.isPaused = false;
    
    console.log('▶️ 所有动画已恢复');
    this.emit('animation:system:resumed');
  }

  /**
   * 停止所有动画
   * @param {boolean} complete - 是否完成到最终状态
   */
  stopAll(complete = false) {
    const allAnimationIds = [
      ...this.animationQueue.active.keys(),
      ...this.animationQueue.pending.keys(),
      ...this.animationQueue.paused.keys()
    ];
    
    allAnimationIds.forEach(id => this.stop(id, complete));
    
    console.log(`⏹️ 所有动画已停止 (${allAnimationIds.length}个)`);
    this.emit('animation:system:stopped', { count: allAnimationIds.length });
  }

  /**
   * 获取动画统计
   * @returns {Object} 动画统计信息
   */
  getAnimationStats() {
    return {
      state: { ...this.animationState },
      stats: { ...this.animationStats },
      queues: {
        active: this.animationQueue.active.size,
        pending: this.animationQueue.pending.size,
        finished: this.animationQueue.finished.size,
        paused: this.animationQueue.paused.size
      },
      timeline: {
        globalTime: this.timeline.globalTime,
        sequences: this.timeline.sequences.size,
        groups: this.timeline.groups.size,
        markers: this.timeline.markers.size
      },
      performance: {
        averageUpdateTime: this.animationStats.performanceMetrics.averageUpdateTime,
        frameDrops: this.animationStats.performanceMetrics.frameDrops,
        memoryUsage: this.animationStats.performanceMetrics.memoryUsage
      }
    };
  }

  /**
   * 设置全局时间缩放
   * @param {number} scale - 时间缩放比例
   */
  setTimeScale(scale) {
    this.options.globalTimeScale = Math.max(0, scale);
    this.timeline.timeScale = this.options.globalTimeScale;
    
    console.log(`⏱️ 全局时间缩放设置为: ${scale}`);
    this.emit('animation:timescale:changed', { scale });
  }

  /**
   * 重置动画系统
   */
  reset() {
    console.log('🔄 重置动画系统');
    
    // 停止所有动画
    this.stopAll(false);
    
    // 重置状态
    this.animationState = {
      isInitialized: this.animationState.isInitialized,
      isPlaying: true,
      isPaused: false,
      currentFrame: 0,
      totalAnimations: 0,
      activeAnimations: 0,
      lastUpdateTime: performance.now()
    };
    
    // 清空队列
    this.animationQueue.active.clear();
    this.animationQueue.pending.clear();
    this.animationQueue.finished.clear();
    this.animationQueue.paused.clear();
    
    // 重置时间轴
    this.timeline.globalTime = 0;
    this.timeline.sequences.clear();
    this.timeline.groups.clear();
    this.timeline.markers.clear();
    
    // 重置统计
    this.animationStats = {
      totalCreated: 0,
      totalCompleted: 0,
      totalCancelled: 0,
      averageDuration: 0,
      peakConcurrent: 0,
      performanceMetrics: {
        averageUpdateTime: 0,
        frameDrops: 0,
        memoryUsage: 0
      }
    };
    
    this.emit('animation:system:reset');
  }

  // =================== 私有方法 ===================

  /**
   * 生成动画ID
   * @returns {string} 动画ID
   * @private
   */
  _generateAnimationId() {
    return `anim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成序列ID
   * @returns {string} 序列ID
   * @private
   */
  _generateSequenceId() {
    return `seq_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成组ID
   * @returns {string} 组ID
   * @private
   */
  _generateGroupId() {
    return `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取缓动函数
   * @param {string} easingName - 缓动函数名
   * @returns {Function} 缓动函数
   * @private
   */
  _getEasingFunction(easingName) {
    return this.easingFunctions[easingName] || this.easingFunctions.easeOutQuad;
  }

  /**
   * 获取动画对象
   * @param {string} animationId - 动画ID
   * @returns {Object} 动画对象
   * @private
   */
  _getAnimation(animationId) {
    return this.animationQueue.active.get(animationId) ||
           this.animationQueue.pending.get(animationId) ||
           this.animationQueue.paused.get(animationId) ||
           this.animationQueue.finished.get(animationId);
  }

  /**
   * 开始动画
   * @param {Object} animation - 动画对象
   * @private
   */
  _startAnimation(animation) {
    animation.state = 'playing';
    animation.startTime = this.timeline.globalTime;
    animation.endTime = animation.startTime + animation.duration;
    
    this.animationQueue.pending.delete(animation.id);
    this.animationQueue.active.set(animation.id, animation);
    
    if (animation.onStart) {
      animation.onStart(animation);
    }
    
    this.animationState.activeAnimations = this.animationQueue.active.size;
    this.animationStats.peakConcurrent = Math.max(
      this.animationStats.peakConcurrent, 
      this.animationState.activeAnimations
    );
    
    this.emit('animation:started', { animationId: animation.id, animation });
  }

  /**
   * 处理等待中的动画
   * @param {number} deltaTime - 时间间隔
   * @private
   */
  _processPendingAnimations(deltaTime) {
    for (const [id, animation] of this.animationQueue.pending) {
      animation.delay -= deltaTime;
      
      if (animation.delay <= 0) {
        this._startAnimation(animation);
      }
    }
  }

  /**
   * 更新活跃动画
   * @param {number} deltaTime - 时间间隔
   * @private
   */
  _updateActiveAnimations(deltaTime) {
    const completedAnimations = [];
    
    for (const [id, animation] of this.animationQueue.active) {
      animation.currentTime += deltaTime;
      
      // 计算进度
      const rawProgress = Math.min(animation.currentTime / animation.duration, 1);
      animation.progress = animation.easingFunction(rawProgress);
      
      // 应用动画值
      this._applyAnimationValues(animation, animation.progress);
      
      // 调用更新回调
      if (animation.onUpdate) {
        animation.onUpdate(animation);
      }
      
      // 检查是否完成
      if (rawProgress >= 1) {
        this._completeAnimation(animation);
        completedAnimations.push(id);
      }
    }
    
    // 移除完成的动画
    completedAnimations.forEach(id => {
      const animation = this.animationQueue.active.get(id);
      this.animationQueue.active.delete(id);
      this.animationQueue.finished.set(id, animation);
    });
    
    this.animationState.activeAnimations = this.animationQueue.active.size;
  }

  /**
   * 应用动画值
   * @param {Object} animation - 动画对象
   * @param {number} progress - 进度(0-1)
   * @private
   */
  _applyAnimationValues(animation, progress) {
    for (const prop in animation.from) {
      const fromValue = animation.from[prop];
      const toValue = animation.to[prop];
      
      if (typeof fromValue === 'number' && typeof toValue === 'number') {
        animation.current[prop] = fromValue + (toValue - fromValue) * progress;
      } else if (typeof fromValue === 'object' && typeof toValue === 'object') {
        // 处理对象属性（如颜色、位置等）
        animation.current[prop] = this._interpolateObject(fromValue, toValue, progress);
      } else {
        // 非数值属性，在进度达到阈值时切换
        animation.current[prop] = progress < 0.5 ? fromValue : toValue;
      }
    }
    
    // 应用到目标对象
    if (animation.target && typeof animation.target === 'object') {
      Object.assign(animation.target, animation.current);
    }
    
    // 调用类型特定的处理器
    if (animation.typeHandler && animation.typeHandler.update) {
      animation.typeHandler.update(animation, progress);
    }
  }

  /**
   * 对象插值
   * @param {Object} from - 起始值
   * @param {Object} to - 结束值
   * @param {number} progress - 进度
   * @returns {Object} 插值结果
   * @private
   */
  _interpolateObject(from, to, progress) {
    const result = {};
    
    for (const key in from) {
      if (from.hasOwnProperty(key) && to.hasOwnProperty(key)) {
        if (typeof from[key] === 'number' && typeof to[key] === 'number') {
          result[key] = from[key] + (to[key] - from[key]) * progress;
        } else {
          result[key] = progress < 0.5 ? from[key] : to[key];
        }
      }
    }
    
    return result;
  }

  /**
   * 完成动画
   * @param {Object} animation - 动画对象
   * @private
   */
  _completeAnimation(animation) {
    animation.state = 'completed';
    animation.progress = 1;
    
    // 检查迭代
    animation.currentIteration++;
    
    if (animation.iterations === Infinity || animation.currentIteration < animation.iterations) {
      // 重新开始动画
      animation.currentTime = 0;
      animation.progress = 0;
      animation.startTime = this.timeline.globalTime;
      animation.endTime = animation.startTime + animation.duration;
      
      // 处理方向
      if (animation.direction === 'alternate') {
        const temp = animation.from;
        animation.from = animation.to;
        animation.to = temp;
      } else if (animation.direction === 'reverse') {
        // 保持反向
      } else {
        // normal 方向，重置到初始值
        animation.current = { ...animation.from };
      }
      
      return; // 继续循环，不完成
    }
    
    // 真正完成
    if (animation.onComplete) {
      animation.onComplete(animation);
    }
    
    this.animationStats.totalCompleted++;
    
    this.emit('animation:completed', { animationId: animation.id, animation });
  }

  /**
   * 推进序列
   * @param {string} sequenceId - 序列ID
   * @private
   */
  _advanceSequence(sequenceId) {
    const sequence = this.timeline.sequences.get(sequenceId);
    if (!sequence) return;
    
    sequence.currentIndex++;
    
    if (sequence.currentIndex < sequence.animations.length) {
      // 播放下一个动画
      const nextAnimation = sequence.animations[sequence.currentIndex];
      const animationId = this.createAnimation(nextAnimation);
      sequence.currentAnimationId = animationId;
    } else {
      // 序列完成
      sequence.state = 'completed';
      
      if (sequence.options.loop) {
        // 重新开始序列
        sequence.currentIndex = 0;
        if (sequence.animations.length > 0) {
          const firstAnimation = sequence.animations[0];
          const animationId = this.createAnimation(firstAnimation);
          sequence.currentAnimationId = animationId;
        }
      } else {
        // 完成序列
        if (sequence.options.onSequenceComplete) {
          sequence.options.onSequenceComplete(sequence);
        }
        
        this.emit('sequence:completed', { sequenceId, sequence });
      }
    }
  }

  /**
   * 检查组完成状态
   * @param {string} groupId - 组ID
   * @private
   */
  _checkGroupCompletion(groupId) {
    const group = this.timeline.groups.get(groupId);
    if (!group) return;
    
    // 检查所有动画是否完成
    const allCompleted = group.animationIds.every(id => {
      const animation = this._getAnimation(id);
      return animation && (animation.state === 'completed' || animation.state === 'cancelled');
    });
    
    if (allCompleted) {
      group.state = 'completed';
      
      if (group.options.onGroupComplete) {
        group.options.onGroupComplete(group);
      }
      
      this.emit('group:completed', { groupId, group });
    }
  }

  /**
   * 从所有队列中移除动画
   * @param {string} animationId - 动画ID
   * @private
   */
  _removeAnimationFromQueues(animationId) {
    this.animationQueue.active.delete(animationId);
    this.animationQueue.pending.delete(animationId);
    this.animationQueue.paused.delete(animationId);
    this.animationQueue.finished.delete(animationId);
    
    this.animationState.activeAnimations = this.animationQueue.active.size;
  }

  /**
   * 清理完成的动画
   * @private
   */
  _cleanupFinishedAnimations() {
    // 保留最近的一些完成动画用于调试
    if (this.animationQueue.finished.size > 100) {
      const finishedArray = Array.from(this.animationQueue.finished.entries());
      finishedArray.slice(0, 50).forEach(([id]) => {
        this.animationQueue.finished.delete(id);
      });
    }
  }

  /**
   * 更新性能统计
   * @param {number} startTime - 开始时间
   * @private
   */
  _updatePerformanceStats(startTime) {
    const updateTime = performance.now() - startTime;
    
    // 更新时间记录
    this.performanceMonitor.updateTimes.push(updateTime);
    if (this.performanceMonitor.updateTimes.length > this.performanceMonitor.maxUpdateTimes) {
      this.performanceMonitor.updateTimes.shift();
    }
    
    // 计算平均更新时间
    const avgUpdateTime = this.performanceMonitor.updateTimes.reduce((a, b) => a + b, 0) / 
                         this.performanceMonitor.updateTimes.length;
    this.animationStats.performanceMetrics.averageUpdateTime = avgUpdateTime;
    
    // 检测帧掉落（更新时间过长）
    if (updateTime > 16.67) { // 60fps阈值
      this.animationStats.performanceMetrics.frameDrops++;
    }
  }

  // =================== 动画类型创建器 ===================

  /**
   * 创建淡入淡出动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createFadeAnimation(animation) {
    if (!animation.from.hasOwnProperty('alpha')) {
      animation.from.alpha = animation.target?.alpha || 1;
    }
    if (!animation.to.hasOwnProperty('alpha')) {
      animation.to.alpha = 0;
    }
    
    return {
      update: (anim, progress) => {
        if (anim.target && anim.target.hasOwnProperty('alpha')) {
          anim.target.alpha = anim.current.alpha;
        }
      }
    };
  }

  /**
   * 创建滑动动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createSlideAnimation(animation) {
    if (!animation.from.x) animation.from.x = animation.target?.x || 0;
    if (!animation.from.y) animation.from.y = animation.target?.y || 0;
    
    return {
      update: (anim, progress) => {
        if (anim.target) {
          if (anim.current.x !== undefined) anim.target.x = anim.current.x;
          if (anim.current.y !== undefined) anim.target.y = anim.current.y;
        }
      }
    };
  }

  /**
   * 创建缩放动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createScaleAnimation(animation) {
    if (!animation.from.scaleX) animation.from.scaleX = animation.target?.scaleX || 1;
    if (!animation.from.scaleY) animation.from.scaleY = animation.target?.scaleY || 1;
    
    return {
      update: (anim, progress) => {
        if (anim.target) {
          if (anim.current.scaleX !== undefined) anim.target.scaleX = anim.current.scaleX;
          if (anim.current.scaleY !== undefined) anim.target.scaleY = anim.current.scaleY;
        }
      }
    };
  }

  /**
   * 创建旋转动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createRotateAnimation(animation) {
    if (!animation.from.rotation) animation.from.rotation = animation.target?.rotation || 0;
    
    return {
      update: (anim, progress) => {
        if (anim.target && anim.current.rotation !== undefined) {
          anim.target.rotation = anim.current.rotation;
        }
      }
    };
  }

  /**
   * 创建颜色动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createColorAnimation(animation) {
    // 将颜色字符串转换为RGB对象
    if (typeof animation.from.color === 'string') {
      animation.from.color = this._parseColor(animation.from.color);
    }
    if (typeof animation.to.color === 'string') {
      animation.to.color = this._parseColor(animation.to.color);
    }
    
    return {
      update: (anim, progress) => {
        if (anim.target && anim.current.color) {
          const color = anim.current.color;
          anim.target.color = `rgb(${Math.round(color.r)}, ${Math.round(color.g)}, ${Math.round(color.b)})`;
        }
      }
    };
  }

  /**
   * 创建位置动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createPositionAnimation(animation) {
    return this._createSlideAnimation(animation);
  }

  /**
   * 创建震动动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createShakeAnimation(animation) {
    const originalX = animation.target?.x || 0;
    const originalY = animation.target?.y || 0;
    const intensity = animation.metadata.intensity || 10;
    
    return {
      update: (anim, progress) => {
        if (anim.target) {
          const shakeAmount = intensity * (1 - progress);
          anim.target.x = originalX + (Math.random() - 0.5) * shakeAmount;
          anim.target.y = originalY + (Math.random() - 0.5) * shakeAmount;
          
          if (progress >= 1) {
            anim.target.x = originalX;
            anim.target.y = originalY;
          }
        }
      }
    };
  }

  /**
   * 创建弹跳动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createBounceAnimation(animation) {
    if (!animation.from.y) animation.from.y = animation.target?.y || 0;
    if (!animation.to.y) animation.to.y = animation.from.y - (animation.metadata.height || 50);
    
    // 使用弹跳缓动函数
    animation.easingFunction = this.easingFunctions.bounce;
    
    return this._createSlideAnimation(animation);
  }

  /**
   * 创建闪烁动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createFlashAnimation(animation) {
    const flashCount = animation.metadata.flashCount || 3;
    
    return {
      update: (anim, progress) => {
        if (anim.target) {
          const cycle = Math.floor(progress * flashCount * 2);
          anim.target.visible = cycle % 2 === 0;
          
          if (progress >= 1) {
            anim.target.visible = true;
          }
        }
      }
    };
  }

  /**
   * 创建自定义动画
   * @param {Object} animation - 动画对象
   * @returns {Object} 类型处理器
   * @private
   */
  _createCustomAnimation(animation) {
    return {
      update: (anim, progress) => {
        // 自定义动画由用户的onUpdate回调处理
      }
    };
  }

  /**
   * 解析颜色字符串
   * @param {string} colorStr - 颜色字符串
   * @returns {Object} RGB对象
   * @private
   */
  _parseColor(colorStr) {
    // 简单的颜色解析，支持hex和rgb
    if (colorStr.startsWith('#')) {
      const hex = colorStr.slice(1);
      return {
        r: parseInt(hex.slice(0, 2), 16),
        g: parseInt(hex.slice(2, 4), 16),
        b: parseInt(hex.slice(4, 6), 16)
      };
    } else if (colorStr.startsWith('rgb')) {
      const matches = colorStr.match(/\d+/g);
      return {
        r: parseInt(matches[0]),
        g: parseInt(matches[1]),
        b: parseInt(matches[2])
      };
    }
    
    // 默认返回白色
    return { r: 255, g: 255, b: 255 };
  }

  /**
   * 销毁动画管理器
   */
  destroy() {
    console.log('🎬 销毁动画管理器');
    
    // 停止所有动画
    this.stopAll(false);
    
    // 清空所有队列和时间轴
    this.animationQueue.active.clear();
    this.animationQueue.pending.clear();
    this.animationQueue.finished.clear();
    this.animationQueue.paused.clear();
    
    this.timeline.sequences.clear();
    this.timeline.groups.clear();
    this.timeline.markers.clear();
    
    // 移除事件监听器
    this.removeAllListeners();
    
    this.animationState.isInitialized = false;
    
    console.log('🎬 动画管理器已销毁');
  }
} 