/**
 * 游戏状态管理器
 * 负责管理游戏的状态转换和生命周期
 */

import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

// 游戏状态
export const GAME_STATE = {
  READY: 'ready',         // 准备开始
  PLAYING: 'playing',     // 游戏中
  CHECKING: 'checking',   // 检查匹配
  ANIMATING: 'animating', // 动画中
  PAUSED: 'paused',       // 暂停
  GAME_OVER: 'gameover',  // 游戏结束
  LEVEL_CLEAR: 'levelclear' // 关卡通关
};

// 状态转换规则
const STATE_TRANSITIONS = {
  [GAME_STATE.READY]: [GAME_STATE.PLAYING],
  [GAME_STATE.PLAYING]: [GAME_STATE.CHECKING, GAME_STATE.PAUSED, GAME_STATE.GAME_OVER],
  [GAME_STATE.CHECKING]: [GAME_STATE.PLAYING, GAME_STATE.ANIMATING, GAME_STATE.GAME_OVER],
  [GAME_STATE.ANIMATING]: [GAME_STATE.CHECKING, GAME_STATE.PLAYING, GAME_STATE.GAME_OVER],
  [GAME_STATE.PAUSED]: [GAME_STATE.PLAYING, GAME_STATE.GAME_OVER],
  [GAME_STATE.GAME_OVER]: [GAME_STATE.READY],
  [GAME_STATE.LEVEL_CLEAR]: [GAME_STATE.READY]
};

/**
 * 游戏状态管理器
 * 职责：统一管理游戏状态转换，确保状态转换的合法性和一致性
 */
export class GameStateManager extends Emitter {
  constructor(gameController) {
    super();
    this.gameController = gameController;
    this.currentState = GAME_STATE.READY;
    this.previousState = null;
    this.stateHistory = [{ state: this.state, timestamp: Date.now(), data: null }];
    this.stateData = new Map();
    this.stateListeners = new Map();
    
    // 计时器
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.animationTimer = 0;
    this.effectsTimer = 0;
    
    // 锁定重置计数
    this.lockResetCount = 0;
    this.maxLockResets = 15;
    
    // 游戏配置
    this.baseSpeed = 30;       // 基础下落间隔帧数
    this.lockDelay = 30;       // 锁定延迟帧数
    this.effectsDelay = 15;    // 特效延迟帧数
    this.softDropSpeed = 20;   // 软下落的速度倍数
    
    console.log('�� 游戏状态管理器已初始化');
  }

  /**
   * 获取当前状态
   * @returns {string} 当前游戏状态
   */
  getCurrentState() {
    return this.currentState;
  }

  /**
   * 获取前一个状态
   * @returns {string|null} 前一个游戏状态
   */
  getPreviousState() {
    return this.previousState;
  }

  /**
   * 设置游戏状态
   * @param {string} newState - 新状态
   * @param {Object} data - 状态数据
   * @param {boolean} force - 是否强制转换（跳过验证）
   * @returns {boolean} 是否转换成功
   */
  setState(newState, data = null, force = false) {
    // 如果状态相同，跳过
    if (this.currentState === newState) {
      console.log(`🎮 状态已经是 ${newState}，跳过转换`);
      return true;
    }

    // 验证状态转换是否合法
    if (!force && !this.canTransition(this.currentState, newState)) {
      console.warn(`🚫 非法状态转换: ${this.currentState} → ${newState}`);
      return false;
    }

    const oldState = this.currentState;
    
    // 记录前一个状态
    this.previousState = oldState;
    
    // 更新当前状态
    this.currentState = newState;
    
    // 存储状态数据
    if (data) {
      this.stateData.set(newState, data);
    }
    
    // 记录状态历史
    this.stateHistory.push({
      from: oldState,
      to: newState,
      timestamp: Date.now(),
      data: data
    });
    
    // 保持历史记录在合理长度
    if (this.stateHistory.length > 50) {
      this.stateHistory.shift();
    }
    
    console.log(`🎮 状态转换: ${oldState} → ${newState}`, data);
    
    // 发出状态变化事件
    this.emit('stateChanged', {
      from: oldState,
      to: newState,
      data: data
    });
    
    // 发出具体状态事件
    this.emit(`state:${newState}`, { from: oldState, data: data });
    
    // 调用状态监听器
    this._notifyStateListeners(oldState, newState, data);
    
    return true;
  }

  /**
   * 检查状态转换是否合法
   * @param {string} fromState - 起始状态
   * @param {string} toState - 目标状态
   * @returns {boolean} 是否可以转换
   */
  canTransition(fromState, toState) {
    const allowedTransitions = STATE_TRANSITIONS[fromState];
    return allowedTransitions && allowedTransitions.includes(toState);
  }

  /**
   * 获取状态数据
   * @param {string} state - 状态名称，如果不提供则返回当前状态的数据
   * @returns {*} 状态数据
   */
  getStateData(state = null) {
    const targetState = state || this.currentState;
    return this.stateData.get(targetState);
  }

  /**
   * 设置状态数据
   * @param {string} state - 状态名称
   * @param {*} data - 状态数据
   */
  setStateData(state, data) {
    this.stateData.set(state, data);
  }

  /**
   * 清除状态数据
   * @param {string} state - 状态名称，如果不提供则清除所有数据
   */
  clearStateData(state = null) {
    if (state) {
      this.stateData.delete(state);
    } else {
      this.stateData.clear();
    }
  }

  /**
   * 是否处于指定状态
   * @param {...string} states - 要检查的状态列表
   * @returns {boolean} 是否处于指定状态之一
   */
  isState(...states) {
    return states.includes(this.currentState);
  }

  /**
   * 是否处于游戏进行状态（可以进行游戏操作）
   * @returns {boolean} 是否可以进行游戏操作
   */
  isPlayable() {
    return this.isState(GAME_STATE.PLAYING, GAME_STATE.CHECKING, GAME_STATE.ANIMATING);
  }

  /**
   * 是否处于暂停状态
   * @returns {boolean} 是否暂停
   */
  isPaused() {
    return this.currentState === GAME_STATE.PAUSED;
  }

  /**
   * 是否游戏结束
   * @returns {boolean} 是否游戏结束
   */
  isGameOver() {
    return this.currentState === GAME_STATE.GAME_OVER;
  }

  /**
   * 注册状态监听器
   * @param {string} state - 要监听的状态
   * @param {Function} callback - 回调函数
   */
  onStateEnter(state, callback) {
    if (!this.stateListeners.has(state)) {
      this.stateListeners.set(state, []);
    }
    this.stateListeners.get(state).push(callback);
  }

  /**
   * 移除状态监听器
   * @param {string} state - 状态名称
   * @param {Function} callback - 要移除的回调函数
   */
  offStateEnter(state, callback) {
    const listeners = this.stateListeners.get(state);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 获取状态历史
   * @param {number} limit - 返回的最大条目数
   * @returns {Array} 状态历史记录
   */
  getStateHistory(limit = 10) {
    return this.stateHistory.slice(-limit);
  }

  /**
   * 重置状态管理器
   */
  reset() {
    this.currentState = GAME_STATE.READY;
    this.previousState = null;
    this.stateHistory = [];
    this.stateData.clear();
    
    console.log('🎮 游戏状态管理器已重置');
    
    // 发出重置事件
    this.emit('reset');
  }

  /**
   * 获取调试信息
   * @returns {Object} 调试信息
   */
  getDebugInfo() {
    return {
      currentState: this.currentState,
      previousState: this.previousState,
      stateHistory: this.getStateHistory(5),
      stateData: Object.fromEntries(this.stateData),
      listeners: Object.fromEntries(
        Array.from(this.stateListeners.entries()).map(([state, listeners]) => [
          state,
          listeners.length
        ])
      )
    };
  }

  /**
   * 通知状态监听器
   * @param {string} fromState - 起始状态
   * @param {string} toState - 目标状态
   * @param {*} data - 状态数据
   * @private
   */
  _notifyStateListeners(fromState, toState, data) {
    const listeners = this.stateListeners.get(toState);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback({ from: fromState, to: toState, data: data });
        } catch (error) {
          console.error(`状态监听器执行失败 (${toState}):`, error);
        }
      });
    }
  }

  /**
   * 销毁状态管理器
   */
  destroy() {
    this.removeAllListeners();
    this.stateListeners.clear();
    this.stateData.clear();
    this.stateHistory = [];
    
    console.log('�� 游戏状态管理器已销毁');
  }

  /**
   * 开始游戏
   */
  start() {
    console.log(`🎮 [${Date.now()}] GameStateManager.start() 开始`);
    
    if (this.currentState === GAME_STATE.PLAYING) {
      console.log('游戏已经在进行中');
      return;
    }

    this.setState(GAME_STATE.PLAYING);
    
    // 重置计时器
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.animationTimer = 0;
    this.effectsTimer = 0;
    this.lockResetCount = 0;

    console.log('游戏开始');
  }

  /**
   * 暂停游戏
   */
  pause() {
    if (!this.isPlayable()) {
      console.log('只有在游戏进行中才能暂停');
      return;
    }

    this.setState(GAME_STATE.PAUSED);
    console.log('游戏暂停');
  }

  /**
   * 恢复游戏
   */
  resume() {
    if (!this.isPaused()) {
      console.log('只有在暂停状态才能恢复');
      return;
    }

    this.setState(GAME_STATE.PLAYING);
    console.log('游戏恢复');
  }

  /**
   * 重置游戏
   */
  resetGame() {
    console.log('重置游戏状态');
    
    this.setState(GAME_STATE.READY);
    
    // 重置所有计时器
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.animationTimer = 0;
    this.effectsTimer = 0;
    this.lockResetCount = 0;
  }

  /**
   * 游戏结束
   */
  gameOver() {
    console.log('游戏结束');
    
    this.setState(GAME_STATE.GAME_OVER);
    
    // 通知游戏控制器
    if (this.gameController.emit) {
      this.gameController.emit('game-over', {
        score: this.gameController.score,
        combo: this.gameController.combo
      });
    }
  }

  /**
   * 关卡完成
   */
  levelClear() {
    console.log('关卡完成');
    
    this.setState(GAME_STATE.LEVEL_CLEAR);
    
    // 通知游戏控制器
    if (this.gameController.emit) {
      this.gameController.emit('level-clear', {
        score: this.gameController.score,
        combo: this.gameController.combo
      });
    }
  }

  /**
   * 切换到检查状态
   */
  startChecking() {
    this.setState(GAME_STATE.CHECKING);
    this.animationTimer = 0;
    console.log('开始检查匹配');
  }

  /**
   * 切换到动画状态
   */
  startAnimating() {
    this.setState(GAME_STATE.ANIMATING);
    this.animationTimer = 0;
    console.log('开始播放动画');
  }

  /**
   * 从动画状态回到游戏状态
   */
  finishAnimating() {
    if (this.currentState === GAME_STATE.ANIMATING) {
      this.setState(GAME_STATE.PLAYING);
      console.log('动画结束，回到游戏状态');
    }
  }

  /**
   * 重置锁定计时器（如果可能）
   */
  resetLockTimerIfPossible() {
    if (this.lockResetCount < this.maxLockResets) {
      this.lockTimer = 0;
      this.lockResetCount++;
      console.log(`重置锁定计时器 (${this.lockResetCount}/${this.maxLockResets})`);
      return true;
    }
    return false;
  }

  /**
   * 更新计时器
   */
  updateTimers() {
    if (this.isPaused()) return;

    // 更新下落计时器
    if (this.currentState === GAME_STATE.PLAYING) {
      this.fallTimer++;
      
      // 如果当前方块存在且可以下落，检查锁定计时器
      const { currentTetromino, grid } = this.gameController;
      if (currentTetromino && !currentTetromino.canMoveTo(currentTetromino.col, currentTetromino.row + 1, grid)) {
        this.lockTimer++;
      }
    }

    // 更新动画计时器
    if (this.currentState === GAME_STATE.ANIMATING || this.currentState === GAME_STATE.CHECKING) {
      this.animationTimer++;
      this.effectsTimer++;
    }
  }

  /**
   * 检查是否应该下落
   */
  shouldFall() {
    const speed = this.gameController.isSoftDropping ? this.softDropSpeed : this.baseSpeed;
    return this.fallTimer >= speed;
  }

  /**
   * 检查是否应该锁定
   */
  shouldLock() {
    return this.lockTimer >= this.lockDelay;
  }

  /**
   * 重置下落计时器
   */
  resetFallTimer() {
    this.fallTimer = 0;
  }

  /**
   * 重置锁定计时器
   */
  resetLockTimer() {
    this.lockTimer = 0;
    this.lockResetCount = 0;
  }

  /**
   * 检查游戏是否在进行中
   */
  isPlaying() {
    return this.currentState === GAME_STATE.PLAYING;
  }

  /**
   * 检查游戏是否暂停
   */
  isPausedState() {
    return this.currentState === GAME_STATE.PAUSED;
  }

  /**
   * 检查游戏是否结束
   */
  isGameOver() {
    return this.currentState === GAME_STATE.GAME_OVER;
  }

  /**
   * 检查是否在动画状态
   */
  isAnimating() {
    return this.currentState === GAME_STATE.ANIMATING;
  }

  /**
   * 检查是否在检查状态
   */
  isChecking() {
    return this.currentState === GAME_STATE.CHECKING;
  }

  /**
   * 获取当前状态
   */
  getState() {
    return this.currentState;
  }

  /**
   * 获取状态信息
   */
  getStateInfo() {
    return {
      state: this.currentState,
      isPaused: this.isPaused(),
      fallTimer: this.fallTimer,
      lockTimer: this.lockTimer,
      animationTimer: this.animationTimer,
      lockResetCount: this.lockResetCount,
      maxLockResets: this.maxLockResets
    };
  }
} 