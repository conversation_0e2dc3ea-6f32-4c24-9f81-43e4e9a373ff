import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;
import { GAME_STATE } from './game-state-manager.js';

/**
 * 游戏流程管理器
 * 职责：管理游戏的生命周期流程，包括开始、暂停、恢复、结束、重置等操作
 */
export class GameFlowManager extends Emitter {
  constructor(gameController, stateManager) {
    super();
    
    this.gameController = gameController;
    this.stateManager = stateManager;
    
    // 保存的游戏状态（用于暂停恢复）
    this.savedGameState = null;
    
    // 流程配置
    this.config = {
      autoStart: false,
      pauseOnWindowBlur: true,
      maxRetries: 3
    };
    
    // 重试计数
    this.retryCount = 0;
    
    console.log('🎮 游戏流程管理器已初始化');
    
    // 监听状态变化
    this._setupStateListeners();
  }

  /**
   * 开始游戏
   * @param {Object} options - 开始选项
   * @returns {boolean} 是否成功开始
   */
  start(options = {}) {
    console.log('🚀 开始游戏', options);
    
    try {
      // 检查是否可以开始
      if (!this._canStart()) {
        console.warn('当前状态不允许开始游戏');
        return false;
      }
      
      // 重置重试计数
      this.retryCount = 0;
      
      // 设置状态为准备中
      this.stateManager.setState(GAME_STATE.READY, { 
        startTime: Date.now(),
        options: options 
      });
      
      // 初始化游戏
      this._initializeGame(options);
      
      // 设置状态为游戏中
      this.stateManager.setState(GAME_STATE.PLAYING, {
        gameStartTime: Date.now()
      });
      
      // 发出游戏开始事件
      this.emit('gameStart', { options });
      
      console.log('✅ 游戏成功开始');
      return true;
      
    } catch (error) {
      console.error('❌ 游戏开始失败:', error);
      this.emit('gameStartError', { error });
      return false;
    }
  }

  /**
   * 暂停游戏
   * @param {Object} options - 暂停选项
   * @returns {boolean} 是否成功暂停
   */
  pause(options = {}) {
    console.log('⏸️ 暂停游戏', options);
    
    try {
      // 检查是否可以暂停
      if (!this._canPause()) {
        console.warn('当前状态不允许暂停游戏');
        return false;
      }
      
      // 保存当前游戏状态
      this._saveGameState();
      
      // 设置状态为暂停
      this.stateManager.setState(GAME_STATE.PAUSED, {
        pauseTime: Date.now(),
        reason: options.reason || 'manual',
        savedState: this.savedGameState
      });
      
      // 暂停游戏逻辑（如计时器等）
      this._pauseGameLogic();
      
      // 发出游戏暂停事件
      this.emit('gamePause', { options, savedState: this.savedGameState });
      
      console.log('✅ 游戏成功暂停');
      return true;
      
    } catch (error) {
      console.error('❌ 游戏暂停失败:', error);
      this.emit('gamePauseError', { error });
      return false;
    }
  }

  /**
   * 恢复游戏
   * @param {Object} options - 恢复选项
   * @returns {boolean} 是否成功恢复
   */
  resume(options = {}) {
    console.log('▶️ 恢复游戏', options);
    
    try {
      // 检查是否可以恢复
      if (!this._canResume()) {
        console.warn('当前状态不允许恢复游戏');
        return false;
      }
      
      // 恢复游戏状态
      const previousState = this._restoreGameState();
      
      // 设置状态为之前的状态或游戏中
      this.stateManager.setState(previousState || GAME_STATE.PLAYING, {
        resumeTime: Date.now(),
        previousState: GAME_STATE.PAUSED
      });
      
      // 恢复游戏逻辑
      this._resumeGameLogic();
      
      // 发出游戏恢复事件
      this.emit('gameResume', { options, previousState });
      
      console.log('✅ 游戏成功恢复');
      return true;
      
    } catch (error) {
      console.error('❌ 游戏恢复失败:', error);
      this.emit('gameResumeError', { error });
      return false;
    }
  }

  /**
   * 结束游戏
   * @param {Object} options - 结束选项
   * @returns {boolean} 是否成功结束
   */
  gameOver(options = {}) {
    console.log('🏁 游戏结束', options);
    
    try {
      // 计算游戏时间
      const gameData = this.stateManager.getStateData(GAME_STATE.PLAYING);
      const gameTime = gameData ? Date.now() - gameData.gameStartTime : 0;
      
      // 设置状态为游戏结束
      this.stateManager.setState(GAME_STATE.GAME_OVER, {
        endTime: Date.now(),
        gameTime: gameTime,
        reason: options.reason || 'normal',
        score: options.score || 0,
        level: options.level || 1
      });
      
      // 执行游戏结束逻辑
      this._executeGameOverLogic(options);
      
      // 发出游戏结束事件
      this.emit('gameOver', { 
        options, 
        gameTime, 
        gameData: this.stateManager.getStateData(GAME_STATE.GAME_OVER)
      });
      
      console.log('✅ 游戏结束处理完成');
      return true;
      
    } catch (error) {
      console.error('❌ 游戏结束处理失败:', error);
      this.emit('gameOverError', { error });
      return false;
    }
  }

  /**
   * 重置游戏
   * @param {Object} options - 重置选项
   * @returns {boolean} 是否成功重置
   */
  reset(options = {}) {
    console.log('🔄 重置游戏', options);
    
    try {
      // 清理当前状态
      this._cleanup();
      
      // 重置状态管理器
      this.stateManager.reset();
      
      // 重置游戏逻辑
      this._resetGameLogic();
      
      // 重置保存的状态
      this.savedGameState = null;
      this.retryCount = 0;
      
      // 发出游戏重置事件
      this.emit('gameReset', { options });
      
      console.log('✅ 游戏成功重置');
      return true;
      
    } catch (error) {
      console.error('❌ 游戏重置失败:', error);
      this.emit('gameResetError', { error });
      return false;
    }
  }

  /**
   * 重新开始游戏
   * @param {Object} options - 重新开始选项
   * @returns {boolean} 是否成功重新开始
   */
  restart(options = {}) {
    console.log('🔄 重新开始游戏', options);
    
    // 先重置，再开始
    if (this.reset(options)) {
      return this.start(options);
    }
    
    return false;
  }

  /**
   * 重试游戏（在失败后重试）
   * @param {Object} options - 重试选项
   * @returns {boolean} 是否成功重试
   */
  retry(options = {}) {
    console.log('🔁 重试游戏', options);
    
    // 检查重试次数
    if (this.retryCount >= this.config.maxRetries) {
      console.warn('已达到最大重试次数');
      this.emit('maxRetriesReached', { retryCount: this.retryCount });
      return false;
    }
    
    this.retryCount++;
    
    // 发出重试事件
    this.emit('gameRetry', { 
      retryCount: this.retryCount, 
      maxRetries: this.config.maxRetries,
      options 
    });
    
    // 重新开始游戏
    return this.restart(options);
  }

  /**
   * 获取当前流程状态
   * @returns {Object} 流程状态信息
   */
  getFlowState() {
    return {
      currentState: this.stateManager.getCurrentState(),
      previousState: this.stateManager.getPreviousState(),
      hasSavedState: !!this.savedGameState,
      retryCount: this.retryCount,
      maxRetries: this.config.maxRetries,
      config: { ...this.config }
    };
  }

  /**
   * 设置流程配置
   * @param {Object} config - 配置选项
   */
  setConfig(config) {
    this.config = { ...this.config, ...config };
    console.log('🎮 游戏流程配置已更新:', this.config);
  }

  /**
   * 检查是否可以开始游戏
   * @returns {boolean} 是否可以开始
   * @private
   */
  _canStart() {
    const currentState = this.stateManager.getCurrentState();
    return currentState === GAME_STATE.READY || currentState === GAME_STATE.GAME_OVER;
  }

  /**
   * 检查是否可以暂停游戏
   * @returns {boolean} 是否可以暂停
   * @private
   */
  _canPause() {
    return this.stateManager.isPlayable();
  }

  /**
   * 检查是否可以恢复游戏
   * @returns {boolean} 是否可以恢复
   * @private
   */
  _canResume() {
    return this.stateManager.isPaused();
  }

  /**
   * 初始化游戏
   * @param {Object} options - 初始化选项
   * @private
   */
  _initializeGame(options) {
    // 重置游戏控制器
    if (this.gameController.reset) {
      this.gameController.reset();
    }
    
    // 应用选项
    if (options.level && this.gameController.updateLevel) {
      this.gameController.updateLevel(options.level);
    }
    
    // 启动游戏控制器
    if (this.gameController.start) {
      this.gameController.start();
    }
  }

  /**
   * 保存游戏状态
   * @private
   */
  _saveGameState() {
    const currentState = this.stateManager.getCurrentState();
    const stateData = this.stateManager.getStateData();
    
    this.savedGameState = {
      state: currentState,
      data: stateData,
      timestamp: Date.now(),
      // 保存游戏控制器状态
      controllerState: this._getControllerState()
    };
    
    console.log('💾 游戏状态已保存', this.savedGameState);
  }

  /**
   * 恢复游戏状态
   * @returns {string|null} 恢复的状态
   * @private
   */
  _restoreGameState() {
    if (!this.savedGameState) {
      console.warn('没有保存的游戏状态可恢复');
      return null;
    }
    
    // 恢复游戏控制器状态
    this._restoreControllerState(this.savedGameState.controllerState);
    
    const restoredState = this.savedGameState.state;
    console.log('🔄 游戏状态已恢复', this.savedGameState);
    
    // 清除保存的状态
    this.savedGameState = null;
    
    return restoredState;
  }

  /**
   * 暂停游戏逻辑
   * @private
   */
  _pauseGameLogic() {
    // 暂停游戏控制器
    if (this.gameController.pause) {
      this.gameController.pause();
    }
    
    // 暂停其他系统（如音乐、动画等）
    if ((typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager && (typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager.pause) {
      (typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager.pause();
    }
  }

  /**
   * 恢复游戏逻辑
   * @private
   */
  _resumeGameLogic() {
    // 恢复游戏控制器
    if (this.gameController.resume) {
      this.gameController.resume();
    }
    
    // 恢复其他系统
    if ((typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager && (typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager.resume) {
      (typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager.resume();
    }
  }

  /**
   * 执行游戏结束逻辑
   * @param {Object} options - 结束选项
   * @private
   */
  _executeGameOverLogic(options) {
    // 停止游戏控制器
    if (this.gameController.gameOver) {
      this.gameController.gameOver();
    }
    
    // 清理保存的状态
    this.savedGameState = null;
    
    // 停止音乐
    if ((typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager && (typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager.stopBgm) {
      (typeof GameGlobal !== "undefined" && GameGlobal ? GameGlobal : {}).musicManager.stopBgm();
    }
  }

  /**
   * 重置游戏逻辑
   * @private
   */
  _resetGameLogic() {
    // 重置游戏控制器
    if (this.gameController.reset) {
      this.gameController.reset();
    }
  }

  /**
   * 获取游戏控制器状态
   * @returns {Object} 控制器状态
   * @private
   */
  _getControllerState() {
    if (!this.gameController) return null;
    
    return {
      score: this.gameController.score || 0,
      combo: this.gameController.combo || 0,
      fallTimer: this.gameController.fallTimer || 0,
      lockTimer: this.gameController.lockTimer || 0,
      // 其他需要保存的状态...
    };
  }

  /**
   * 恢复游戏控制器状态
   * @param {Object} controllerState - 控制器状态
   * @private
   */
  _restoreControllerState(controllerState) {
    if (!controllerState || !this.gameController) return;
    
    // 恢复分数等状态
    if (typeof controllerState.score === 'number') {
      this.gameController.score = controllerState.score;
    }
    if (typeof controllerState.combo === 'number') {
      this.gameController.combo = controllerState.combo;
    }
    if (typeof controllerState.fallTimer === 'number') {
      this.gameController.fallTimer = controllerState.fallTimer;
    }
    if (typeof controllerState.lockTimer === 'number') {
      this.gameController.lockTimer = controllerState.lockTimer;
    }
  }

  /**
   * 设置状态监听器
   * @private
   */
  _setupStateListeners() {
    // 监听状态变化
    this.stateManager.on('stateChanged', (event) => {
      this.emit('stateChanged', event);
    });
    
    // 监听特定状态
    this.stateManager.on('state:playing', () => {
      this.emit('flowStateChanged', { flow: 'playing' });
    });
    
    this.stateManager.on('state:paused', () => {
      this.emit('flowStateChanged', { flow: 'paused' });
    });
    
    this.stateManager.on('state:gameover', () => {
      this.emit('flowStateChanged', { flow: 'gameover' });
    });
  }

  /**
   * 清理资源
   * @private
   */
  _cleanup() {
    // 清理保存的状态
    this.savedGameState = null;
    
    // 停止所有计时器
    // 清理事件监听器等...
  }

  /**
   * 销毁流程管理器
   */
  destroy() {
    this._cleanup();
    this.removeAllListeners();
    console.log('🎮 游戏流程管理器已销毁');
  }
} 