export const GRID_COLS = 10;
export const GRID_ROWS = 20;

// 导入屏幕尺寸用于正确计算坐标
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render.js';

// 计算正确的网格参数，添加NaN检查
const SAFE_SCREEN_WIDTH = SCREEN_WIDTH || 750; // 默认宽度
const SAFE_SCREEN_HEIGHT = SCREEN_HEIGHT || 1334; // 默认高度

const BLOCK_SIZE = Math.floor(SAFE_SCREEN_WIDTH * 0.8 / GRID_COLS); // 方块大小，屏幕宽度的80%
const GRID_OFFSET_X = (SAFE_SCREEN_WIDTH - BLOCK_SIZE * GRID_COLS) / 2 - SAFE_SCREEN_WIDTH * 0.09; // 网格X轴偏移量，使网格水平居中
const GRID_OFFSET_Y = SAFE_SCREEN_HEIGHT * 0.1; // 网格Y轴偏移量，留出顶部空间

// 检查计算结果是否有效
if (isNaN(BLOCK_SIZE) || isNaN(GRID_OFFSET_X) || isNaN(GRID_OFFSET_Y)) {
  console.error('❌ 网格参数计算出现NaN:', {
    SCREEN_WIDTH, SCREEN_HEIGHT,
    SAFE_SCREEN_WIDTH, SAFE_SCREEN_HEIGHT,
    BLOCK_SIZE, GRID_OFFSET_X, GRID_OFFSET_Y
  });
}

export default class Grid {
  constructor(rows = 20, cols = 10) {
    this.rows = rows;
    this.cols = cols;
    this.blocks = Array(rows).fill().map(() => Array(cols).fill(null));
    
    // 动画系统
    this.animations = [];
    this.animationIdCounter = 0;
    
    // 使用正确的渲染参数
    this.blockSize = BLOCK_SIZE;
    this.offsetX = GRID_OFFSET_X;
    this.offsetY = GRID_OFFSET_Y;
    
    console.log(`🎮 网格系统已初始化: ${rows}x${cols}`);
    console.log(`📏 网格参数: blockSize=${this.blockSize}, offset=(${this.offsetX.toFixed(1)}, ${this.offsetY.toFixed(1)})`);
    console.log(`📱 屏幕尺寸: ${SCREEN_WIDTH}x${SCREEN_HEIGHT}`);
  }
  
  /**
   * 检查指定位置是否有效（在边界内且没有方块）
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {boolean} 位置是否有效
   */
  isValidPosition(row, col) {
    return row >= 0 && row < this.rows && col >= 0 && col < this.cols && !this.getBlock(row, col);
  }
  
  addTetromino(tetromino) {
    if (!tetromino) return false;
    console.log('🔲 添加方块到网格');
    return true;
  }
  
  removeFullRows() {
    const removedRows = [];
    for (let row = 0; row < this.rows; row++) {
      let isFullLine = true;
      for (let col = 0; col < this.cols; col++) {
        if (!this.blocks[row][col]) {
          isFullLine = false;
          break;
        }
      }
      if (isFullLine) {
        removedRows.push(row);
      }
    }
    return removedRows;
  }
  
  clear() {
    this.blocks = Array(this.rows).fill().map(() => Array(this.cols).fill(null));
    this.animations = [];
    console.log('🧹 网格已清空');
  }
  
  /**
   * 获取指定位置的方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {Block|null} 方块对象或null
   */
  getBlock(row, col) {
    if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
      return null;
    }
    return this.blocks[row][col];
  }
  
  /**
   * 设置指定位置的方块
   * @param {number} row - 行索引  
   * @param {number} col - 列索引
   * @param {Block} block - 方块对象
   * @returns {boolean} 是否成功设置
   */
  setBlock(row, col, block) {
    if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
      return false;
    }
    
    // 🔥 修复：如果方块存在，给它设置正确的位置信息
    if (block) {
      block.row = row;
      block.col = col;
    }
    
    this.blocks[row][col] = block;
    return true;
  }

  /**
   * 移除指定位置的方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {Block|null} 被移除的方块
   */
  removeBlock(row, col) {
    if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
      return null;
    }
    
    const block = this.blocks[row][col];
    this.blocks[row][col] = null;
    return block;
  }

  /**
   * 放置方块到指定位置
   * @param {Block} block - 要放置的方块
   * @param {number} row - 行索引  
   * @param {number} col - 列索引
   * @returns {boolean} 是否成功放置
   */
  placeBlock(block, row, col) {
    if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
      console.warn(`❌ 无效位置: (${row}, ${col})`);
      return false;
    }
    
    if (this.blocks[row][col] !== null) {
      console.warn(`❌ 位置已被占用: (${row}, ${col})`);
      return false;
    }
    
    // 🔥 修复：给方块设置正确的位置信息
    block.row = row;
    block.col = col;
    
    this.blocks[row][col] = block;
    return true;
  }

  /**
   * 检查指定位置是否有方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
* @returns {boolean} 是否有方块
   */
  hasBlock(row, col) {
    if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
      return false;
    }
    return this.blocks[row][col] !== null;
  }

  /**
   * 获取所有非空方块的位置和信息
   * @returns {Array} 方块信息数组
   */
  getAllBlocks() {
    const blocks = [];
    for (let row = 0; row < this.rows; row++) {
      for (let col = 0; col < this.cols; col++) {
        if (this.blocks[row][col]) {
          blocks.push({
            row,
            col,
            block: this.blocks[row][col]
          });
        }
      }
    }
    return blocks;
  }

  /**
   * 检查网格是否已满
   * @returns {boolean} 网格是否已满
   */
  isFull() {
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[0][col] !== null) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取指定行的所有方块
   * @param {number} row - 行索引
   * @returns {Array} 该行的方块数组
   */
  getRow(row) {
    if (row < 0 || row >= this.rows) {
      return [];
    }
    return [...this.blocks[row]];
  }

  /**
   * 检查指定行是否为满行
   * @param {number} row - 行索引
   * @returns {boolean} 是否为满行
   */
  isFullRow(row) {
    if (row < 0 || row >= this.rows) {
      return false;
    }
    
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[row][col] === null) {
        return false;
      }
    }
    return true;
  }

  /**
   * 检查指定行是否为满行 (controller.js兼容性方法)
   * @param {number} row - 行索引
   * @returns {boolean} 是否为满行
   */
  isRowFull(row) {
    return this.isFullRow(row);
  }

  /**
   * 检查指定行是否为空行
   * @param {number} row - 行索引
   * @returns {boolean} 是否为空行
   */
  isRowEmpty(row) {
    if (row < 0 || row >= this.rows) {
      return false;
    }
    
    for (let col = 0; col < this.cols; col++) {
      if (this.blocks[row][col] !== null) {
        return false;
      }
    }
    return true;
  }

  /**
   * 设置渲染相关属性
   * @param {Object} options - 渲染选项
   */
  setRenderOptions(options = {}) {
    if (options.blockSize) this.blockSize = options.blockSize;
    if (options.offsetX !== undefined) this.offsetX = options.offsetX;
    if (options.offsetY !== undefined) this.offsetY = options.offsetY;
    console.log(`🎨 更新渲染选项: blockSize=${this.blockSize}, offset=(${this.offsetX}, ${this.offsetY})`);
  }

  /**
   * 将网格坐标转换为屏幕坐标
   * @param {number} row - 网格行索引
   * @param {number} col - 网格列索引
   * @returns {Object} 屏幕坐标 {x, y}
   */
  gridToScreen(row, col) {
    // 检查输入参数
    if (isNaN(row) || isNaN(col)) {
      console.warn(`❌ gridToScreen收到无效参数: row=${row}, col=${col}`);
      return { x: 0, y: 0 };
    }

    // 检查网格参数
    if (isNaN(this.offsetX) || isNaN(this.offsetY) || isNaN(this.blockSize)) {
      console.error(`❌ 网格参数包含NaN: offsetX=${this.offsetX}, offsetY=${this.offsetY}, blockSize=${this.blockSize}`);
      return { x: 0, y: 0 };
    }

    const x = this.offsetX + col * this.blockSize;
    const y = this.offsetY + row * this.blockSize;

    // 检查计算结果
    if (isNaN(x) || isNaN(y)) {
      console.error(`❌ gridToScreen计算结果为NaN: x=${x}, y=${y}`);
      return { x: 0, y: 0 };
    }

    return { x, y };
  }

  /**
   * 将屏幕坐标转换为网格坐标
   * @param {number} x - 屏幕X坐标
   * @param {number} y - 屏幕Y坐标
   * @returns {Object} 网格坐标 {row, col}
   */
  screenToGrid(x, y) {
    const col = Math.floor((x - this.offsetX) / this.blockSize);
    const row = Math.floor((y - this.offsetY) / this.blockSize);
    
    return {
      row: Math.max(0, Math.min(row, this.rows - 1)),
      col: Math.max(0, Math.min(col, this.cols - 1))
    };
  }

  /**
   * 获取网格中心点的屏幕坐标
   * @param {number} row - 网格行索引
   * @param {number} col - 网格列索引
   * @returns {Object} 中心点屏幕坐标 {x, y}
   */
  gridCenterToScreen(row, col) {
    const pos = this.gridToScreen(row, col);
    return {
      x: pos.x + this.blockSize / 2,
      y: pos.y + this.blockSize / 2
    };
  }

  /**
   * 检查屏幕坐标是否在网格范围内
   * @param {number} x - 屏幕X坐标
   * @param {number} y - 屏幕Y坐标
   * @returns {boolean} 是否在网格范围内
   */
  isInGridBounds(x, y) {
    return x >= this.offsetX &&
           x < this.offsetX + this.cols * this.blockSize &&
           y >= this.offsetY &&
           y < this.offsetY + this.rows * this.blockSize;
  }

  /**
   * 添加下落动画
   * @param {Block} block - 方块对象
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {number} endRow - 结束行
   * @param {number} endCol - 结束列
   * @returns {number} 动画ID
   */
  addFallingAnimation(block, startRow, startCol, endRow, endCol) {
    if (!block || startRow === endRow) {
      console.log('⚠️ 跳过无效的下落动画');
      return -1; // 没有方块或没有移动，不需要动画
    }

    const startPos = this.gridToScreen(startRow, startCol);
    const endPos = this.gridToScreen(endRow, endCol);
    const distance = Math.abs(endRow - startRow);
    
    // 计算动画持续时间：距离越远，时间越长
    const duration = Math.max(200, distance * 80); // 最少200ms，每行80ms
    
    const animationId = this.animationIdCounter++;
    const animation = {
      id: animationId,
      type: 'falling',
      block: block,
      startPos: startPos,
      endPos: endPos,
      currentPos: { ...startPos },
      startTime: Date.now(),
      duration: duration,
      progress: 0,
      completed: false,
      startRow: startRow,
      startCol: startCol,
      endRow: endRow,
      endCol: endCol
    };

    this.animations.push(animation);
    
    console.log(`🎭 添加下落动画: (${startRow}, ${startCol}) → (${endRow}, ${endCol}), 持续时间: ${duration}ms`);
    console.log(`📍 动画坐标: (${startPos.x.toFixed(1)}, ${startPos.y.toFixed(1)}) → (${endPos.x.toFixed(1)}, ${endPos.y.toFixed(1)})`);
    return animationId;
  }

  /**
   * 添加满行清除后的下落动画（带延迟和特效）
   * @param {Block} block - 方块对象
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {number} endRow - 结束行
   * @param {number} endCol - 结束列
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {number} 动画ID
   */
  addRowClearFallingAnimation(block, startRow, startCol, endRow, endCol, delay = 0) {
    if (!block || startRow === endRow) {
      console.log('⚠️ 跳过无效的满行清除下落动画');
      return -1;
    }

    const startPos = this.gridToScreen(startRow, startCol);
    const endPos = this.gridToScreen(endRow, endCol);
    const distance = Math.abs(endRow - startRow);

    // 满行清除动画更慢一些，更有视觉冲击力
    const duration = Math.max(200, distance * 120); // 每行120ms，比普通下落慢

    const animationId = this.animationIdCounter++;
    const animation = {
      id: animationId,
      type: 'rowClearFalling',
      block: block,
      startPos: startPos,
      endPos: endPos,
      currentPos: { ...startPos },
      startTime: Date.now() + delay, // 添加延迟
      duration: duration,
      progress: 0,
      completed: false,
      startRow: startRow,
      startCol: startCol,
      endRow: endRow,
      endCol: endCol,
      delay: delay,
      easingType: 'easeOutBounce' // 使用弹跳效果
    };

    this.animations.push(animation);

    console.log(`🎬 添加满行清除下落动画: (${startRow}, ${startCol}) → (${endRow}, ${endCol}), 持续时间: ${duration}ms, 延迟: ${delay}ms`);
    return animationId;
  }

  /**
   * 创建爆炸动画
   * @param {number} row - 爆炸中心行
   * @param {number} col - 爆炸中心列
   * @param {number} range - 爆炸范围
   * @returns {number} 动画ID
   */
  createExplosionAnimation(row, col, range = 1) {
    const centerPos = this.gridToScreen(row, col);
    const animationId = this.animationIdCounter++;
    
    const animation = {
      id: animationId,
      type: 'explosion',
      centerPos: centerPos,
      range: range,
      startTime: Date.now(),
      duration: 500, // 500ms动画
      progress: 0,
      completed: false,
      particles: this._generateExplosionParticles(centerPos, range)
    };

    this.animations.push(animation);
    console.log(`💥 创建爆炸动画: 中心(${row}, ${col}), 位置(${centerPos.x.toFixed(1)}, ${centerPos.y.toFixed(1)}), 范围: ${range}`);
    return animationId;
  }

  /**
   * 生成爆炸粒子
   * @param {Object} centerPos - 中心位置
   * @param {number} range - 爆炸范围
   * @returns {Array} 粒子数组
   * @private
   */
  _generateExplosionParticles(centerPos, range) {
    const particles = [];
    const particleCount = range * 8; // 每个范围8个粒子
    
    for (let i = 0; i < particleCount; i++) {
      const angle = (i / particleCount) * Math.PI * 2;
      const speed = Math.random() * 100 + 50; // 随机速度
      const size = Math.random() * 6 + 2; // 随机大小
      
      particles.push({
        x: centerPos.x,
        y: centerPos.y,
        vx: Math.cos(angle) * speed,
        vy: Math.sin(angle) * speed,
        size: size,
        life: 1.0,
        decay: Math.random() * 0.02 + 0.01
      });
    }
    
    return particles;
  }

  /**
   * 渲染网格
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!ctx) {
      console.warn('⚠️ 无效的画布上下文');
      return;
    }

    // 保存画布状态
    ctx.save();

    try {
      // 渲染网格背景
      this._renderGridBackground(ctx);
      
      // 渲染网格线
      this._renderGridLines(ctx);
      
      // 渲染所有方块
      this._renderBlocks(ctx);
      
      // 渲染动画
      this._renderAnimations(ctx);
      
    } catch (error) {
      console.error('❌ 网格渲染出错:', error);
    } finally {
      // 恢复画布状态
      ctx.restore();
    }
  }

  /**
   * 渲染网格背景
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderGridBackground(ctx) {
    // 绘制深色背景
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(
      this.offsetX - 2, 
      this.offsetY - 2, 
      this.cols * this.blockSize + 4, 
      this.rows * this.blockSize + 4
    );
  }

  /**
   * 渲染网格线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderGridLines(ctx) {
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 0.5;
    ctx.globalAlpha = 0.3;

    // 绘制垂直线
    for (let col = 0; col <= this.cols; col++) {
      const x = this.offsetX + col * this.blockSize;
      ctx.beginPath();
      ctx.moveTo(x, this.offsetY);
      ctx.lineTo(x, this.offsetY + this.rows * this.blockSize);
      ctx.stroke();
    }

    // 绘制水平线
    for (let row = 0; row <= this.rows; row++) {
      const y = this.offsetY + row * this.blockSize;
      ctx.beginPath();
      ctx.moveTo(this.offsetX, y);
      ctx.lineTo(this.offsetX + this.cols * this.blockSize, y);
      ctx.stroke();
    }

    ctx.globalAlpha = 1.0;
  }

  /**
   * 渲染所有方块
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderBlocks(ctx) {
    for (let row = 0; row < this.rows; row++) {
      for (let col = 0; col < this.cols; col++) {
        const block = this.blocks[row][col];
        if (block) {
          const { x, y } = this.gridToScreen(row, col);
          
          // 检查方块是否正在动画中
          const isAnimating = this.animations.some(anim => 
            anim.type === 'falling' && 
            anim.block === block && 
            !anim.completed
          );
          
          // 只渲染非动画方块
          if (!isAnimating && block.render) {
            block.render(ctx, x, y, this.blockSize);
          }
        }
      }
    }
  }

  /**
   * 渲染所有动画
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderAnimations(ctx) {
    for (const animation of this.animations) {
      if (!animation.completed) {
        this._renderAnimation(ctx, animation);
      }
    }
  }

  /**
   * 渲染单个动画
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} animation - 动画对象
   * @private
   */
  _renderAnimation(ctx, animation) {
    switch (animation.type) {
      case 'falling':
        this._renderFallingAnimation(ctx, animation);
        break;
      case 'rowClearFalling':
        this._renderRowClearFallingAnimation(ctx, animation);
        break;
      case 'explosion':
        this._renderExplosionAnimation(ctx, animation);
        break;
    }
  }

  /**
   * 渲染下落动画
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} animation - 下落动画对象
   * @private
   */
  _renderFallingAnimation(ctx, animation) {
    if (animation.block && animation.block.render && animation.currentPos) {
      // 添加轻微的透明度效果
      ctx.save();
      ctx.globalAlpha = 0.9;

      animation.block.render(
        ctx,
        animation.currentPos.x,
        animation.currentPos.y,
        this.blockSize
      );

      ctx.restore();
    }
  }

  /**
   * 渲染满行清除下落动画
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} animation - 满行清除下落动画对象
   * @private
   */
  _renderRowClearFallingAnimation(ctx, animation) {
    if (!animation.block || !animation.block.render || !animation.currentPos) {
      return;
    }

    // 检查动画是否已经开始（处理延迟）
    const currentTime = Date.now();
    if (currentTime < animation.startTime) {
      return; // 动画还没开始
    }

    ctx.save();

    // 添加更明显的视觉效果
    const progress = animation.progress;

    // 透明度变化：开始时更透明，结束时更不透明
    const alpha = 0.7 + (progress * 0.3);
    ctx.globalAlpha = alpha;

    // 添加轻微的缩放效果
    const scale = 0.95 + (progress * 0.05);
    const centerX = animation.currentPos.x + this.blockSize / 2;
    const centerY = animation.currentPos.y + this.blockSize / 2;

    ctx.translate(centerX, centerY);
    ctx.scale(scale, scale);
    ctx.translate(-centerX, -centerY);

    // 添加轻微的阴影效果
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 4;
    ctx.shadowOffsetY = 2;

    animation.block.render(
      ctx,
      animation.currentPos.x,
      animation.currentPos.y,
      this.blockSize
    );

    ctx.restore();
  }

  /**
   * 渲染爆炸动画
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} animation - 爆炸动画对象
   * @private
   */
  _renderExplosionAnimation(ctx, animation) {
    if (!animation.particles) return;

    ctx.save();
    
    for (const particle of animation.particles) {
      if (particle.life > 0) {
        ctx.globalAlpha = particle.life;
        ctx.fillStyle = `hsl(${Math.random() * 60 + 15}, 100%, 50%)`; // 橙红色
        
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
      }
    }
    
    ctx.restore();
  }

  /**
   * 获取网格边界信息
   * @returns {Object} 边界信息
   */
  getGridBounds() {
    return {
      left: this.offsetX,
      top: this.offsetY,
      right: this.offsetX + this.cols * this.blockSize,
      bottom: this.offsetY + this.rows * this.blockSize,
      width: this.cols * this.blockSize,
      height: this.rows * this.blockSize
    };
  }

  /**
   * 添加动画
   * @param {Object} animation - 动画对象
   * @returns {number} 动画ID
   */
  addAnimation(animation) {
    if (!animation || typeof animation !== 'object') {
      console.warn('⚠️ 无效的动画对象');
      return -1;
    }
    
    const animationId = this.animationIdCounter++;
    const animationWithId = {
      id: animationId,
      duration: animation.duration || 300,
      startTime: Date.now(),
      progress: 0,
      completed: false,
      ...animation
    };
    
    this.animations.push(animationWithId);
    console.log(`🎬 添加动画: ID=${animationId}, 类型=${animation.type || '未知'}`);
    return animationId;
  }

  /**
   * 移除动画
   * @param {number} animationId - 动画ID
   */
  removeAnimation(animationId) {
    const index = this.animations.findIndex(anim => anim.id === animationId);
    if (index !== -1) {
      this.animations.splice(index, 1);
      console.log(`🗑️ 移除动画: ID=${animationId}`);
    }
  }

  /**
   * 更新所有动画
   * @returns {boolean} 是否还有动画在进行
   */
  updateAnimations() {
    if (this.animations.length === 0) {
      return false;
    }

    const currentTime = Date.now();
    const completedAnimations = [];

    for (let i = 0; i < this.animations.length; i++) {
      const animation = this.animations[i];
      const elapsed = currentTime - animation.startTime;

      // 处理延迟动画
      if (elapsed < 0) {
        continue; // 动画还没开始
      }

      animation.progress = Math.min(elapsed / animation.duration, 1.0);

      // 处理普通下落动画的位置更新
      if (animation.type === 'falling' && animation.currentPos) {
        const easedProgress = this._applyEasing(animation.progress, 'easeInQuad');

        // 更新当前位置
        animation.currentPos.x = animation.startPos.x +
          (animation.endPos.x - animation.startPos.x) * easedProgress;
        animation.currentPos.y = animation.startPos.y +
          (animation.endPos.y - animation.startPos.y) * easedProgress;
      }

      // 处理满行清除下落动画的位置更新
      if (animation.type === 'rowClearFalling' && animation.currentPos) {
        const easingType = animation.easingType || 'easeOutBounce';
        const easedProgress = this._applyEasing(animation.progress, easingType);

        // 更新当前位置
        animation.currentPos.x = animation.startPos.x +
          (animation.endPos.x - animation.startPos.x) * easedProgress;
        animation.currentPos.y = animation.startPos.y +
          (animation.endPos.y - animation.startPos.y) * easedProgress;
      }
      
      // 处理爆炸动画的粒子更新
      if (animation.type === 'explosion' && animation.particles) {
        for (const particle of animation.particles) {
          particle.x += particle.vx * 0.016; // 60fps
          particle.y += particle.vy * 0.016;
          particle.life -= particle.decay;
        }
      }

      // 执行动画更新回调
      if (animation.onUpdate && typeof animation.onUpdate === 'function') {
        animation.onUpdate(animation.progress, animation);
      }

      // 检查动画是否完成
      if (animation.progress >= 1.0) {
        animation.completed = true;
        completedAnimations.push(i);

        // 执行完成回调
        if (animation.onComplete && typeof animation.onComplete === 'function') {
          animation.onComplete(animation);
        }
      }
    }

    // 移除已完成的动画（从后往前移除，避免索引错乱）
    for (let i = completedAnimations.length - 1; i >= 0; i--) {
      const index = completedAnimations[i];
      this.animations.splice(index, 1);
    }

    // 返回是否还有未完成的动画
    return this.animations.length > 0;
  }

  /**
   * 清除所有动画
   */
  clearAnimations() {
    this.animations = [];
    console.log('🧹 清除所有动画');
  }

  /**
   * 获取动画数量
   * @returns {number} 当前动画数量
   */
  getAnimationCount() {
    return this.animations.length;
  }

  /**
   * 检查是否有指定类型的动画
   * @param {string} type - 动画类型
   * @returns {boolean} 是否存在该类型的动画
   */
  hasAnimationType(type) {
    return this.animations.some(anim => anim.type === type);
  }

  /**
   * 获取指定类型的所有动画
   * @param {string} type - 动画类型
   * @returns {Array} 指定类型的动画数组
   */
  getAnimationsByType(type) {
    return this.animations.filter(anim => anim.type === type);
  }

  /**
   * 暂停所有动画
   */
  pauseAnimations() {
    const currentTime = Date.now();
    this.animations.forEach(animation => {
      if (!animation.paused) {
        animation.pausedAt = currentTime;
        animation.paused = true;
      }
    });
    console.log('⏸️ 暂停所有动画');
  }

  /**
   * 恢复所有动画
   */
  resumeAnimations() {
    const currentTime = Date.now();
    this.animations.forEach(animation => {
      if (animation.paused) {
        const pauseDuration = currentTime - animation.pausedAt;
        animation.startTime += pauseDuration;
        animation.paused = false;
        delete animation.pausedAt;
      }
    });
    console.log('▶️ 恢复所有动画');
  }

  /**
   * 应用缓动函数
   * @param {number} t - 进度 (0-1)
   * @param {string} type - 缓动类型
   * @returns {number} 缓动后的进度
   * @private
   */
  _applyEasing(t, type = 'linear') {
    switch (type) {
      case 'ease-out':
        return 1 - Math.pow(1 - t, 3);
      case 'ease-in':
        return t * t * t;
      case 'ease-in-out':
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
      case 'easeInQuad':
        return t * t;
      case 'easeOutQuad':
        return 1 - (1 - t) * (1 - t);
      case 'easeOutBounce':
        const n1 = 7.5625;
        const d1 = 2.75;
        if (t < 1 / d1) {
          return n1 * t * t;
        } else if (t < 2 / d1) {
          return n1 * (t -= 1.5 / d1) * t + 0.75;
        } else if (t < 2.5 / d1) {
          return n1 * (t -= 2.25 / d1) * t + 0.9375;
        } else {
          return n1 * (t -= 2.625 / d1) * t + 0.984375;
        }
      case 'easeInBounce':
        return 1 - this._applyEasing(1 - t, 'easeOutBounce');
      default:
        return t;
    }
  }

  /**
   * 应用重力效果到指定列和方块
   * @param {Array} columnsToCheck - 需要检查的列数组
   * @param {Set|Array} blocksToCheck - 需要检查的方块集合
   * @param {Array} removedPositions - 已移除的位置数组
   * @returns {boolean} 是否有方块下落
   */
  applyGravity(columnsToCheck = null, blocksToCheck = null, removedPositions = []) {
    console.log('🌊 Grid.applyGravity 调用', {
      columnsToCheck,
      blocksToCheck: blocksToCheck ? blocksToCheck : null,
      removedPositions: removedPositions.length
    });

    // 🧩 修复：如果有 blocksToCheck，使用新的重力系统
    if (blocksToCheck && blocksToCheck.size > 0) {
      console.log('🌊 使用新重力系统处理解体方块');

      // 委托给新的重力系统
      if (this.gravitySystem) {
        return this.gravitySystem.applyGravity(columnsToCheck, blocksToCheck, removedPositions);
      } else {
        // 如果没有新重力系统，使用简化的解体方块处理
        return this._handleDisintegratedBlocks(blocksToCheck, columnsToCheck);
      }
    }

    if (!columnsToCheck && !blocksToCheck) {
      // 如果没有指定参数，应用全网格重力
      return this.applyFullGridGravity();
    }

    let hasFallen = false;

    // 确定要检查的列
    const targetColumns = columnsToCheck || Array.from(Array(this.cols).keys());

    // 对每列应用重力
    for (const col of targetColumns) {
      const columnHadMovement = this._applyGravityToColumn(col);
      if (columnHadMovement) {
        hasFallen = true;
      }
    }

    return hasFallen;
  }

  /**
   * 对单列应用重力
   * @param {number} col - 列索引
   * @returns {boolean} 该列是否有方块移动
   * @private
   */
  _applyGravityToColumn(col) {
    let hasMovement = false;
    
    // 从底部向上检查每一行
    for (let row = this.rows - 2; row >= 0; row--) {
      const block = this.getBlock(row, col);
      if (block) {
        // 找到这个方块应该下落到的位置
        const targetRow = this._findLowestAvailableRow(row, col);
        
        if (targetRow > row) {
          // 方块需要下落
          this.removeBlock(row, col);
          this.setBlock(targetRow, col, block);
          
          // 创建下落动画
          this.addFallingAnimation(block, row, col, targetRow, col);
          
          hasMovement = true;
          console.log(`📉 方块下落: (${row}, ${col}) → (${targetRow}, ${col})`);
        }
      }
    }
    
    return hasMovement;
  }

  /**
   * 找到指定位置方块能够下落到的最低行
   * @param {number} startRow - 起始行
   * @param {number} col - 列索引
   * @returns {number} 目标行
   * @private
   */
  _findLowestAvailableRow(startRow, col) {
    let targetRow = startRow;

    // 从当前位置向下寻找第一个被占用的位置
    for (let row = startRow + 1; row < this.rows; row++) {
      if (this.getBlock(row, col) === null) {
        targetRow = row;
      } else {
        break; // 遇到方块就停止
      }
    }

    return targetRow;
  }

  /**
   * 处理解体方块的重力
   * @param {Set} blocksToCheck - 需要处理的解体方块
   * @param {Set} columnsToCheck - 需要检查的列
   * @returns {boolean} 是否有方块下落
   * @private
   */
  _handleDisintegratedBlocks(blocksToCheck, columnsToCheck) {
    console.log('🧩 处理解体方块重力');
    let hasFallen = false;

    // 将解体方块按列分组
    const blocksByColumn = {};

    for (const blockInfo of blocksToCheck) {
      let row, col, block;

      // 解析方块信息
      if (blockInfo.block && blockInfo.row !== undefined && blockInfo.col !== undefined) {
        // 新格式：{block, row, col}
        block = blockInfo.block;
        row = blockInfo.row;
        col = blockInfo.col;
      } else if (blockInfo.row !== undefined && blockInfo.col !== undefined) {
        // 旧格式：方块对象直接有 row, col 属性
        block = blockInfo;
        row = blockInfo.row;
        col = blockInfo.col;
      } else {
        console.warn('🧩 无效的解体方块格式:', blockInfo);
        continue;
      }

      // 验证方块是否在网格中
      const gridBlock = this.getBlock(row, col);
      if (gridBlock !== block) {
        console.warn(`🧩 解体方块位置不匹配: [${row}, ${col}]`);
        continue;
      }

      if (!blocksByColumn[col]) {
        blocksByColumn[col] = [];
      }
      blocksByColumn[col].push({ block, row, col });
    }

    // 对每列的解体方块应用重力
    for (const col in blocksByColumn) {
      const columnBlocks = blocksByColumn[col];
      console.log(`🧩 处理列 ${col} 的 ${columnBlocks.length} 个解体方块`);

      // 按行排序（从下到上），确保下面的方块先处理
      columnBlocks.sort((a, b) => b.row - a.row);

      // 🧩 修复：每个方块独立计算下落位置，不穿透固定方块
      for (const blockInfo of columnBlocks) {
        const { block, row } = blockInfo;

        // 为当前方块找到能下落到的最低位置
        const targetRow = this._findLowestAvailableRow(row, col);

        if (row !== targetRow) {
          console.log(`🧩 解体方块下落: [${row}, ${col}] → [${targetRow}, ${col}]`);

          // 移动方块
          this.removeBlock(row, col);
          this.setBlock(targetRow, col, block);

          // 创建下落动画
          this.addFallingAnimation(block, row, col, targetRow, col);

          hasFallen = true;
        } else {
          console.log(`🧩 解体方块 [${row}, ${col}] 已在最低位置，无需下落`);
        }
      }
    }

    console.log(`🧩 解体方块重力处理完成，${hasFallen ? '有' : '无'}方块下落`);
    return hasFallen;
  }

  /**
   * 应用全网格重力
   * @returns {boolean} 是否有方块下落
   */
  applyFullGridGravity() {
    console.log('🌊 应用全网格重力');
    let hasFallen = false;
    
    // 对所有列应用重力
    for (let col = 0; col < this.cols; col++) {
      const columnHadMovement = this._applyGravityToColumn(col);
      if (columnHadMovement) {
        hasFallen = true;
      }
    }
    
    console.log(`🌊 全网格重力完成，${hasFallen ? '有' : '无'}方块下落`);
    return hasFallen;
  }

  /**
   * 静态方法：比较两个网格状态
   * @param {Object} beforeState - 之前的状态
   * @param {Object} afterState - 之后的状态
   * @param {string} operation - 操作名称
   */
  static compareGridStates(beforeState, afterState, operation = '状态对比') {
    if (!beforeState || !afterState) {
      console.log(`🔍 ${operation}: 状态对比失败 - 缺少状态数据`);
      return;
    }
    
    console.log(`🔍 ${operation} - 状态对比:`);
    console.log(`📊 方块数量: ${beforeState.totalBlocks} → ${afterState.totalBlocks} (${afterState.totalBlocks - beforeState.totalBlocks >= 0 ? '+' : ''}${afterState.totalBlocks - beforeState.totalBlocks})`);
    
    // 比较颜色分布
    const beforeColors = Object.keys(beforeState.blocksByColor || {});
    const afterColors = Object.keys(afterState.blocksByColor || {});
    const allColors = [...new Set([...beforeColors, ...afterColors])];
    
    if (allColors.length > 0) {
      console.log(`🎨 颜色变化:`);
      allColors.forEach(color => {
        const before = beforeState.blocksByColor?.[color] || 0;
        const after = afterState.blocksByColor?.[color] || 0;
        const change = after - before;
        if (change !== 0) {
          console.log(`  ${color}: ${before} → ${after} (${change >= 0 ? '+' : ''}${change})`);
        }
      });
    }
    
    // 比较动画数量
    const animationChange = afterState.animations - beforeState.animations;
    if (animationChange !== 0) {
      console.log(`🎬 动画数量: ${beforeState.animations} → ${afterState.animations} (${animationChange >= 0 ? '+' : ''}${animationChange})`);
    }
    
    console.log(`⏰ 时间差: ${afterState.timestamp - beforeState.timestamp}ms`);
  }

  /**
   * 调试网格状态
   * @param {string} label - 调试标签
   * @param {boolean} detailed - 是否显示详细信息
   * @returns {Object} 网格状态信息
   */
  debugGridState(label = '网格状态', detailed = true) {
    console.log(`🔍 ${label}`);
    
    // 统计信息
    let totalBlocks = 0;
    let blocksByColor = {};
    let blocksByEffect = {};
    
    // 收集网格数据
    const gridData = [];
    for (let row = 0; row < this.rows; row++) {
      const rowData = [];
      for (let col = 0; col < this.cols; col++) {
        const block = this.blocks[row][col];
        if (block) {
          totalBlocks++;
          
          // 统计颜色
          const color = block.color || 'unknown';
          blocksByColor[color] = (blocksByColor[color] || 0) + 1;
          
          // 统计特效
          const effect = block.effect || 'none';
          blocksByEffect[effect] = (blocksByEffect[effect] || 0) + 1;
          
          rowData.push(detailed ? `${color.charAt(0)}${effect !== 'none' ? '*' : ''}` : '█');
        } else {
          rowData.push('.');
        }
      }
      gridData.push(rowData);
    }
    
    // 输出基本统计
    console.log(`📊 总方块数: ${totalBlocks}`);
    if (Object.keys(blocksByColor).length > 0) {
      // console.log(`🎨 颜色分布:`, blocksByColor);
    }
    if (Object.keys(blocksByEffect).length > 1 || !blocksByEffect.none) {
      // console.log(`✨ 特效分布:`, blocksByEffect);
    }
    
    // 输出网格可视化
    if (detailed && totalBlocks > 0) {
      console.log('🗂️ 网格布局:');
      gridData.forEach((row, index) => {
        console.log(`${index.toString().padStart(2)}: ${row.join(' ')}`);
      });
    }
    
    // 返回状态对象
    return {
      label,
      timestamp: Date.now(),
      totalBlocks,
      blocksByColor,
      blocksByEffect,
      gridData: detailed ? gridData : null,
      animations: this.animations.length
    };
  }
}