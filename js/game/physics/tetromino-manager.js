import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;
import Tetromino, { TETROMINO_SHAPES } from '../tetromino.js';

// 方块颜色分布配置
const COLOR_DISTRIBUTIONS = {
  RANDOM: 'random',
  SINGLE: 'single',
  GRADIENT: 'gradient'
};

/**
 * 方块管理器
 * 职责：专门管理Tetromino的整个生命周期，包括生成、移动、旋转、锁定等操作
 */
export class TetrominoManager extends Emitter {
  constructor(grid, options = {}) {
    super();
    
    this.grid = grid;
    this.options = {
      colorDistribution: COLOR_DISTRIBUTIONS.RANDOM,
      colorCount: 4,
      effectProbability: 0.1,
      allowedEffects: ['frozen', 'mine'],
      previewCount: 1, // 预览方块数量
      ...options
    };
    
    // 当前活动方块
    this.currentTetromino = null;
    
    // 下一个方块队列
    this.nextTetrominoQueue = [];
    
    // 保存的方块（用于Hold功能，未来扩展）
    this.heldTetromino = null;
    this.canHold = true;
    
    // 方块生成历史（用于随机化算法）
    this.generationHistory = [];
    this.historyLimit = 4; // 避免连续相同方块
    
    // 移动状态
    this.movementState = {
      canMove: true,
      lastMoveTime: 0,
      moveDelay: 100, // 移动延迟（毫秒）
      autoRepeat: false,
      autoRepeatDelay: 150,
      autoRepeatRate: 50
    };
    
    // 旋转状态
    this.rotationState = {
      canRotate: true,
      lastRotateTime: 0,
      rotateDelay: 200, // 旋转延迟（毫秒）
      wallKickAttempts: 0,
      maxWallKickAttempts: 4
    };
    
    // 锁定状态
    this.lockState = {
      timer: 0,
      delay: 30, // 锁定延迟帧数
      resetCount: 0,
      maxResets: 15, // 最大锁定重置次数
      isLocking: false,
      lastResetTime: 0
    };
    
    // 统计信息
    this.stats = {
      totalGenerated: 0,
      totalLocked: 0,
      shapeCount: {},
      moveCount: 0,
      rotateCount: 0
    };
    
    console.log('🧩 方块管理器已初始化', this.options);
    
    // 初始化方块队列
    this._initializeQueue();
  }

  /**
   * 获取当前活动方块
   * @returns {Tetromino|null} 当前方块
   */
  getCurrentTetromino() {
    return this.currentTetromino;
  }

  /**
   * 获取下一个方块
   * @param {number} index - 预览索引，默认0（下一个）
   * @returns {Tetromino|null} 下一个方块
   */
  getNextTetromino(index = 0) {
    return this.nextTetrominoQueue[index] || null;
  }

  /**
   * 获取下一个方块队列
   * @returns {Array<Tetromino>} 下一个方块队列
   */
  getNextTetrominoQueue() {
    return [...this.nextTetrominoQueue];
  }

  /**
   * 生成新的活动方块
   * @returns {boolean} 是否成功生成
   */
  generateNewTetromino() {
    console.log('🎲 生成新的活动方块');
    
    try {
      // 从队列中取出下一个方块
      this.currentTetromino = this.nextTetrominoQueue.shift();
      
      if (!this.currentTetromino) {
        console.error('方块队列为空，无法生成新方块');
        return false;
      }
      
      // 设置初始位置
      this._setInitialPosition(this.currentTetromino);
      
      // 补充队列
      this._fillQueue();
      
      // 重置锁定状态
      this._resetLockState();
      
      // 重置移动状态
      this._resetMovementState();
      
      // 检查是否可以放置
      if (!this._canPlaceTetromino(this.currentTetromino)) {
        console.warn('新方块无法放置，可能需要游戏结束');
        this.emit('tetromino:cannotPlace', { tetromino: this.currentTetromino });
        return false;
      }
      
      // 更新统计
      this.stats.totalGenerated++;
      const shape = this.currentTetromino.shape;
      this.stats.shapeCount[shape] = (this.stats.shapeCount[shape] || 0) + 1;
      
      // 发出事件
      this.emit('tetromino:generated', { 
        tetromino: this.currentTetromino,
        nextQueue: this.getNextTetrominoQueue()
      });
      
      console.log('✅ 新方块生成成功:', shape);
      return true;
      
    } catch (error) {
      console.error('生成新方块失败:', error);
      this.emit('tetromino:generateError', { error });
      return false;
    }
  }

  /**
   * 移动当前方块
   * @param {string} direction - 移动方向：'left', 'right', 'down'
   * @param {boolean} force - 是否强制移动（跳过延迟检查）
   * @returns {boolean} 是否移动成功
   */
  moveTetromino(direction, force = false) {
    if (!this.currentTetromino) {
      console.warn('没有活动方块，无法移动');
      return false;
    }
    
    if (!force && !this._canMove()) {
      return false;
    }
    
    console.log(`🎮 移动方块: ${direction}`);
    
    const originalPosition = { ...this.currentTetromino.position };
    let moveSuccess = false;
    
    try {
      // 执行移动
      switch (direction) {
        case 'left':
          this.currentTetromino.moveLeft();
          break;
        case 'right':
          this.currentTetromino.moveRight();
          break;
        case 'down':
          this.currentTetromino.moveDown();
          break;
        default:
          console.warn('无效的移动方向:', direction);
          return false;
      }
      
      // 检查新位置是否有效
      if (this._canPlaceTetromino(this.currentTetromino)) {
        moveSuccess = true;
        this._updateMovementState();
        
        // 如果是向下移动且成功，可能需要重置锁定计时器
        if (direction === 'down') {
          this._tryResetLockTimer('movement');
        } else {
          // 水平移动也可以重置锁定计时器
          this._tryResetLockTimer('movement');
        }
        
        // 更新统计
        this.stats.moveCount++;
        
        // 发出事件
        this.emit('tetromino:moved', {
          tetromino: this.currentTetromino,
          direction,
          from: originalPosition,
          to: { ...this.currentTetromino.position }
        });
        
        // 播放移动音效
        this._playMoveSound(direction);
        
      } else {
        // 位置无效，恢复原位置
        this.currentTetromino.position = originalPosition;
        // 注意：不需要调用updateBlocks()，因为getBlockPositions()是动态计算的

        console.log(`移动失败，位置无效: ${direction}`);

        // 如果是向下移动失败，可能需要开始锁定倒计时
        if (direction === 'down') {
          this._startLocking();
        }

        // 发出移动失败事件
        this.emit('tetromino:moveBlocked', {
          tetromino: this.currentTetromino,
          direction,
          position: originalPosition
        });
      }
      
    } catch (error) {
      console.error('移动方块时出错:', error);
      // 恢复原位置
      this.currentTetromino.position = originalPosition;
      // 注意：不需要调用updateBlocks()，因为getBlockPositions()是动态计算的
      moveSuccess = false;
    }
    
    return moveSuccess;
  }

  /**
   * 旋转当前方块
   * @param {string} direction - 旋转方向：'cw'(顺时针), 'ccw'(逆时针)
   * @param {boolean} force - 是否强制旋转（跳过延迟检查）
   * @returns {boolean} 是否旋转成功
   */
  rotateTetromino(direction = 'cw', force = false) {
    if (!this.currentTetromino) {
      console.warn('没有活动方块，无法旋转');
      return false;
    }
    
    if (!force && !this._canRotate()) {
      return false;
    }
    
    console.log(`🔄 旋转方块: ${direction}`);
    
    const originalRotation = this.currentTetromino.rotation;
    const originalPosition = { ...this.currentTetromino.position };
    
    try {
      // 执行旋转
      if (direction === 'ccw') {
        this.currentTetromino.rotateCounterClockwise();
      } else {
        this.currentTetromino.rotate(); // 默认顺时针
      }
      
      // 检查旋转后是否需要踢墙
      const kickSuccess = this._attemptWallKick(originalPosition, originalRotation);
      
      if (kickSuccess) {
        this._updateRotationState();
        this._tryResetLockTimer('rotation');
        
        // 更新统计
        this.stats.rotateCount++;
        
        // 发出事件
        this.emit('tetromino:rotated', {
          tetromino: this.currentTetromino,
          direction,
          from: { rotation: originalRotation, position: originalPosition },
          to: { rotation: this.currentTetromino.rotation, position: { ...this.currentTetromino.position } }
        });
        
        // 播放旋转音效
        this._playRotateSound();
        
        return true;
        
      } else {
        // 旋转失败，恢复原状态
        this.currentTetromino.rotation = originalRotation;
        this.currentTetromino.position = originalPosition;
        // 注意：不需要调用updateBlocks()，因为getBlockPositions()是动态计算的

        console.log('❌ 旋转失败，无法找到合适位置');

        // 发出旋转失败事件
        this.emit('tetromino:rotateBlocked', {
          tetromino: this.currentTetromino,
          direction,
          originalRotation,
          originalPosition
        });

        return false;
      }
      
    } catch (error) {
      console.error('旋转方块时出错:', error);
      // 恢复原状态
      this.currentTetromino.rotation = originalRotation;
      this.currentTetromino.position = originalPosition;
      // 注意：不需要调用updateBlocks()，因为getBlockPositions()是动态计算的
      return false;
    }
  }

  /**
   * 硬降（瞬间落到底部）
   * @returns {number} 下降的行数
   */
  hardDrop() {
    if (!this.currentTetromino) {
      console.warn('没有活动方块，无法硬降');
      return 0;
    }
    
    console.log('⬇️ 硬降方块');
    
    const originalRow = this.currentTetromino.position.row;
    let dropDistance = 0;
    
    // 找到最底部的有效位置
    while (this.moveTetromino('down', true)) {
      dropDistance++;
    }
    
    // 立即锁定
    this.lockState.timer = this.lockState.delay;
    this._startLocking();
    
    // 发出事件
    this.emit('tetromino:hardDropped', {
      tetromino: this.currentTetromino,
      dropDistance,
      from: originalRow,
      to: this.currentTetromino.position.row
    });
    
    console.log(`硬降完成，下降了${dropDistance}行`);
    return dropDistance;
  }

  /**
   * 锁定当前方块
   * @returns {boolean} 是否锁定成功
   */
  lockTetromino() {
    if (!this.currentTetromino) {
      console.warn('没有活动方块，无法锁定');
      return false;
    }
    
    console.log('🔒 锁定方块');
    
    try {
      // 将方块放置到网格中
      const blockPositions = this.currentTetromino.getBlockPositions();
      const placedBlocks = blockPositions.map(({ row, col, block }) => ({
        row: row,
        col: col,
        color: block.color,
        effect: block.effect
      }));
      
      // 实际放置到网格
      for (const block of placedBlocks) {
        this.grid.setBlock(block.row, block.col, {
          color: block.color,
          effect: block.effect,
          isEmpty: false
        });
      }
      
      // 保存锁定信息
      const lockedTetromino = {
        shape: this.currentTetromino.shape,
        rotation: this.currentTetromino.rotation,
        position: { ...this.currentTetromino.position },
        blocks: placedBlocks
      };
      
      // 更新统计
      this.stats.totalLocked++;
      
      // 发出锁定事件
      this.emit('tetromino:locked', {
        tetromino: lockedTetromino,
        placedBlocks
      });
      
      // 清除当前方块
      this.currentTetromino = null;
      
      // 重置锁定状态
      this._resetLockState();
      
      console.log('✅ 方块锁定成功');
      return true;
      
    } catch (error) {
      console.error('锁定方块时出错:', error);
      this.emit('tetromino:lockError', { error });
      return false;
    }
  }

  /**
   * 更新锁定计时器
   * @param {number} deltaTime - 时间增量（帧数）
   * @returns {boolean} 是否应该锁定
   */
  updateLockTimer(deltaTime = 1) {
    if (!this.currentTetromino || !this.lockState.isLocking) {
      return false;
    }
    
    this.lockState.timer += deltaTime;
    
    // 检查是否到达锁定时间
    if (this.lockState.timer >= this.lockState.delay) {
      return true; // 应该锁定了
    }
    
    return false;
  }

  /**
   * 检查是否应该开始锁定倒计时
   * @returns {boolean} 是否开始锁定
   */
  shouldStartLocking() {
    if (!this.currentTetromino) return false;
    
    // 检查方块是否无法继续下降
    return !this.moveTetromino('down', true);
  }

  /**
   * 获取管理器状态
   * @returns {Object} 状态信息
   */
  getState() {
    return {
      currentTetromino: this.currentTetromino ? {
        shape: this.currentTetromino.shape,
        rotation: this.currentTetromino.rotation,
        position: { ...this.currentTetromino.position }
      } : null,
      nextQueue: this.nextTetrominoQueue.map(t => ({
        shape: t.shape,
        rotation: t.rotation
      })),
      lockState: { ...this.lockState },
      movementState: { ...this.movementState },
      rotationState: { ...this.rotationState },
      stats: { ...this.stats }
    };
  }

  /**
   * 重置管理器
   */
  reset() {
    console.log('🔄 重置方块管理器');
    
    // 清除当前方块
    this.currentTetromino = null;
    this.heldTetromino = null;
    this.canHold = true;
    
    // 清空队列
    this.nextTetrominoQueue = [];
    this.generationHistory = [];
    
    // 重置状态
    this._resetLockState();
    this._resetMovementState();
    this._resetRotationState();
    
    // 重置统计
    this.stats = {
      totalGenerated: 0,
      totalLocked: 0,
      shapeCount: {},
      moveCount: 0,
      rotateCount: 0
    };
    
    // 重新初始化队列
    this._initializeQueue();
    
    this.emit('manager:reset');
  }

  // =================== 私有方法 ===================

  /**
   * 初始化方块队列
   * @private
   */
  _initializeQueue() {
    console.log('🎲 初始化方块队列');
    
    // 生成初始队列
    for (let i = 0; i < this.options.previewCount + 1; i++) {
      const tetromino = this._generateRandomTetromino();
      this.nextTetrominoQueue.push(tetromino);
    }
    
    console.log(`队列初始化完成，包含${this.nextTetrominoQueue.length}个方块`);
  }

  /**
   * 补充队列
   * @private
   */
  _fillQueue() {
    while (this.nextTetrominoQueue.length < this.options.previewCount + 1) {
      const tetromino = this._generateRandomTetromino();
      this.nextTetrominoQueue.push(tetromino);
    }
  }

  /**
   * 生成随机方块
   * @returns {Tetromino} 新的方块
   * @private
   */
  _generateRandomTetromino() {
    const shapes = Object.keys(TETROMINO_SHAPES);
    
    // 避免连续相同方块的算法
    let availableShapes = shapes.filter(shape => {
      const recentCount = this.generationHistory.slice(-this.historyLimit)
        .filter(h => h === shape).length;
      return recentCount < 2; // 最近不超过2个相同方块
    });
    
    // 如果过滤后没有可用方块，使用全部方块
    if (availableShapes.length === 0) {
      availableShapes = shapes;
    }
    
    // 随机选择
    const randomShape = availableShapes[Math.floor(Math.random() * availableShapes.length)];
    
    // 创建方块
    const tetromino = new Tetromino(randomShape, {
      colorDistribution: this.options.colorDistribution,
      effectProbability: this.options.effectProbability,
      colorCount: this.options.colorCount,
      allowedEffects: this.options.allowedEffects
    });
    
    // 更新历史
    this.generationHistory.push(randomShape);
    if (this.generationHistory.length > this.historyLimit * 2) {
      this.generationHistory.shift();
    }
    
    return tetromino;
  }

  /**
   * 设置方块初始位置
   * @param {Tetromino} tetromino - 方块
   * @private
   */
  _setInitialPosition(tetromino) {
    // 安全的网格列数
    const safeGridCols = this.grid && this.grid.cols ? this.grid.cols : 10;

    // 安全的方块宽度（Tetromino没有width属性，使用默认值）
    const safeTetrominoWidth = 4; // 大多数方块的最大宽度

    // 计算初始列位置
    const initialCol = Math.floor((safeGridCols - safeTetrominoWidth) / 2);

    // 检查计算结果
    if (isNaN(initialCol)) {
      console.error(`❌ _setInitialPosition计算出现NaN: gridCols=${safeGridCols}, tetrominoWidth=${safeTetrominoWidth}, initialCol=${initialCol}`);
      tetromino.position = { row: 0, col: 3 }; // 安全默认值
    } else {
      tetromino.position = { row: 0, col: initialCol };
    }

  }

  /**
   * 检查方块是否可以放置在指定位置
   * @param {Tetromino} tetromino - 方块
   * @returns {boolean} 是否可以放置
   * @private
   */
  _canPlaceTetromino(tetromino) {
    return tetromino.isValidPosition(this.grid);
  }

  /**
   * 检查是否可以移动
   * @returns {boolean} 是否可以移动
   * @private
   */
  _canMove() {
    if (!this.movementState.canMove) return false;
    
    const now = Date.now();
    return now - this.movementState.lastMoveTime >= this.movementState.moveDelay;
  }

  /**
   * 检查是否可以旋转
   * @returns {boolean} 是否可以旋转
   * @private
   */
  _canRotate() {
    if (!this.rotationState.canRotate) return false;
    
    const now = Date.now();
    return now - this.rotationState.lastRotateTime >= this.rotationState.rotateDelay;
  }

  /**
   * 尝试踢墙
   * @param {Object} originalPosition - 原始位置
   * @param {number} originalRotation - 原始旋转
   * @returns {boolean} 是否踢墙成功
   * @private
   */
  _attemptWallKick(originalPosition, originalRotation) {
    // 首先检查当前位置是否有效
    if (this._canPlaceTetromino(this.currentTetromino)) {
      return true;
    }
    
    // 标准踢墙偏移表（简化版本）
    const kickOffsets = [
      { row: 0, col: -1 }, // 左移1格
      { row: 0, col: 1 },  // 右移1格
      { row: -1, col: 0 }, // 上移1格
      { row: 0, col: -2 }, // 左移2格
      { row: 0, col: 2 },  // 右移2格
    ];
    
    const originalPos = { ...this.currentTetromino.position };
    
    // 尝试每个偏移
    for (const offset of kickOffsets) {
      this.currentTetromino.position.row = originalPos.row + offset.row;
      this.currentTetromino.position.col = originalPos.col + offset.col;
      // 注意：不需要调用updateBlocks()，因为getBlockPositions()是动态计算的

      if (this._canPlaceTetromino(this.currentTetromino)) {
        console.log('🦵 踢墙成功:', offset);
        return true;
      }
    }

    // 所有偏移都失败，恢复原位置
    this.currentTetromino.position = originalPos;
    // 注意：不需要调用updateBlocks()，因为getBlockPositions()是动态计算的
    return false;
  }

  /**
   * 更新移动状态
   * @private
   */
  _updateMovementState() {
    this.movementState.lastMoveTime = Date.now();
  }

  /**
   * 更新旋转状态
   * @private
   */
  _updateRotationState() {
    this.rotationState.lastRotateTime = Date.now();
    this.rotationState.wallKickAttempts = 0;
  }

  /**
   * 尝试重置锁定计时器
   * @param {string} reason - 重置原因
   * @private
   */
  _tryResetLockTimer(reason) {
    if (this.lockState.resetCount < this.lockState.maxResets) {
      this.lockState.timer = 0;
      this.lockState.resetCount++;
      this.lockState.lastResetTime = Date.now();
      
      this.emit('lock:timerReset', { reason, resetCount: this.lockState.resetCount });
    }
  }

  /**
   * 开始锁定倒计时
   * @private
   */
  _startLocking() {
    if (!this.lockState.isLocking) {
      this.lockState.isLocking = true;
      this.lockState.timer = 0;
      
      console.log('🔒 开始锁定倒计时');
      this.emit('lock:started');
    }
  }

  /**
   * 重置锁定状态
   * @private
   */
  _resetLockState() {
    this.lockState.timer = 0;
    this.lockState.resetCount = 0;
    this.lockState.isLocking = false;
    this.lockState.lastResetTime = 0;
  }

  /**
   * 重置移动状态
   * @private
   */
  _resetMovementState() {
    this.movementState.lastMoveTime = 0;
    this.movementState.canMove = true;
  }

  /**
   * 重置旋转状态
   * @private
   */
  _resetRotationState() {
    this.rotationState.lastRotateTime = 0;
    this.rotationState.canRotate = true;
    this.rotationState.wallKickAttempts = 0;
  }

  /**
   * 播放移动音效
   * @param {string} direction - 移动方向
   * @private
   */
  _playMoveSound(direction) {
    if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playMove === 'function') {
      GameGlobal.musicManager.playMove();
    }
  }

  /**
   * 播放旋转音效
   * @private
   */
  _playRotateSound() {
    if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playRotate === 'function') {
      GameGlobal.musicManager.playRotate();
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.removeAllListeners();
    this.currentTetromino = null;
    this.nextTetrominoQueue = [];
    this.heldTetromino = null;
    this.generationHistory = [];
    
    console.log('🧩 方块管理器已销毁');
  }
} 