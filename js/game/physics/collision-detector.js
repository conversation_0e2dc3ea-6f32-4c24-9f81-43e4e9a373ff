import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

/**
 * 碰撞检测器
 * 职责：专门处理游戏中的各种碰撞检测，包括方块与网格、方块与方块、边界检测等
 */
export class CollisionDetector extends Emitter {
  constructor(grid, options = {}) {
    super();
    
    this.grid = grid;
    this.options = {
      enableDetailedLogging: false,
      enablePerformanceTracking: true,
      cacheCollisionResults: true,
      maxCacheSize: 1000,
      ...options
    };
    
    // 碰撞缓存（优化性能）
    this.collisionCache = new Map();
    this.cacheHits = 0;
    this.cacheMisses = 0;
    
    // 性能统计
    this.performanceStats = {
      totalChecks: 0,
      averageCheckTime: 0,
      maxCheckTime: 0,
      cacheHitRate: 0
    };
    
    console.log('🎯 碰撞检测器已初始化', this.options);
  }

  /**
   * 检查方块是否可以放置在指定位置
   * @param {Tetromino} tetromino - 方块对象
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @param {number} rotation - 旋转状态
   * @returns {boolean} 是否可以放置
   */
  canPlaceAt(tetromino, row = null, col = null, rotation = null) {
    const startTime = this.options.enablePerformanceTracking ? performance.now() : 0;
    
    // 使用当前位置如果没有指定
    const targetRow = row !== null ? row : tetromino.position.row;
    const targetCol = col !== null ? col : tetromino.position.col;
    const targetRotation = rotation !== null ? rotation : tetromino.rotation;
    
    // 生成缓存键
    const cacheKey = this._generateCacheKey(tetromino.shape, targetRow, targetCol, targetRotation);
    
    // 检查缓存
    if (this.options.cacheCollisionResults && this.collisionCache.has(cacheKey)) {
      this.cacheHits++;
      const result = this.collisionCache.get(cacheKey);
      this._updatePerformanceStats(startTime, true);
      return result;
    }
    
    this.cacheMisses++;
    
    try {
      // 获取方块的形状数据
      const shapeData = tetromino.getCurrentShape(targetRotation);
      const canPlace = this._checkShapeCollision(shapeData, targetRow, targetCol);
      
      // 缓存结果
      if (this.options.cacheCollisionResults) {
        this._cacheResult(cacheKey, canPlace);
      }
      
      // 发出检测事件
      this.emit('collision:checked', {
        tetromino: tetromino.shape,
        position: { row: targetRow, col: targetCol },
        rotation: targetRotation,
        canPlace,
        cached: false
      });
      
      this._updatePerformanceStats(startTime, false);
      
      if (this.options.enableDetailedLogging) {
        console.log(`🎯 碰撞检测: ${tetromino.shape} at (${targetRow},${targetCol}) rot:${targetRotation} = ${canPlace}`);
      }
      
      return canPlace;
      
    } catch (error) {
      console.error('碰撞检测时出错:', error);
      this.emit('collision:error', { error, tetromino, position: { row: targetRow, col: targetCol } });
      return false;
    }
  }

  /**
   * 检查方块是否与网格中的其他方块碰撞
   * @param {Array} blocks - 方块列表
   * @returns {Array} 碰撞的方块列表
   */
  checkBlockCollisions(blocks) {
    const collisions = [];
    
    for (const block of blocks) {
      if (this._isPositionOccupied(block.row, block.col)) {
        collisions.push({
          block,
          occupiedBy: this.grid.getBlock(block.row, block.col)
        });
      }
    }
    
    if (collisions.length > 0) {
      this.emit('collision:blockCollision', { collisions });
    }
    
    return collisions;
  }

  /**
   * 检查边界碰撞
   * @param {Array} blocks - 方块列表
   * @returns {Object} 边界碰撞信息
   */
  checkBoundaryCollisions(blocks) {
    const boundaryCollisions = {
      left: false,
      right: false,
      top: false,
      bottom: false,
      blocks: []
    };
    
    for (const block of blocks) {
      const collision = this._checkBlockBoundary(block);
      
      if (collision.hasCollision) {
        boundaryCollisions.blocks.push({
          block,
          collisionTypes: collision.types
        });
        
        // 更新总体碰撞状态
        collision.types.forEach(type => {
          boundaryCollisions[type] = true;
        });
      }
    }
    
    if (boundaryCollisions.blocks.length > 0) {
      this.emit('collision:boundary', boundaryCollisions);
    }
    
    return boundaryCollisions;
  }

  /**
   * 检查特定方向的移动是否可行
   * @param {Tetromino} tetromino - 方块对象
   * @param {string} direction - 移动方向 ('left', 'right', 'down')
   * @returns {boolean} 是否可以移动
   */
  canMoveInDirection(tetromino, direction) {
    const currentPos = tetromino.position;
    let newRow = currentPos.row;
    let newCol = currentPos.col;
    
    switch (direction) {
      case 'left':
        newCol = currentPos.col - 1;
        break;
      case 'right':
        newCol = currentPos.col + 1;
        break;
      case 'down':
        newRow = currentPos.row + 1;
        break;
      default:
        console.warn('无效的移动方向:', direction);
        return false;
    }
    
    return this.canPlaceAt(tetromino, newRow, newCol);
  }

  /**
   * 检查旋转是否可行
   * @param {Tetromino} tetromino - 方块对象
   * @param {string} rotationDirection - 旋转方向 ('cw', 'ccw')
   * @returns {Object} 旋转检测结果
   */
  checkRotation(tetromino, rotationDirection = 'cw') {
    const currentRotation = tetromino.rotation;
    let newRotation;
    
    if (rotationDirection === 'ccw') {
      newRotation = (currentRotation - 1 + 4) % 4;
    } else {
      newRotation = (currentRotation + 1) % 4;
    }
    
    const result = {
      canRotate: false,
      newRotation,
      kickRequired: false,
      kickOffset: null,
      originalPosition: { ...tetromino.position }
    };
    
    // 首先检查当前位置是否可以旋转
    if (this.canPlaceAt(tetromino, null, null, newRotation)) {
      result.canRotate = true;
      return result;
    }
    
    // 尝试踢墙
    const kickResult = this._tryWallKick(tetromino, newRotation);
    if (kickResult.success) {
      result.canRotate = true;
      result.kickRequired = true;
      result.kickOffset = kickResult.offset;
    }
    
    this.emit('collision:rotationChecked', result);
    
    return result;
  }

  /**
   * 寻找最近的有效位置
   * @param {Tetromino} tetromino - 方块对象
   * @param {number} maxDistance - 最大搜索距离
   * @returns {Object|null} 最近的有效位置
   */
  findNearestValidPosition(tetromino, maxDistance = 3) {
    const currentPos = tetromino.position;
    
    // 搜索周围的位置
    for (let distance = 1; distance <= maxDistance; distance++) {
      for (let rowOffset = -distance; rowOffset <= distance; rowOffset++) {
        for (let colOffset = -distance; colOffset <= distance; colOffset++) {
          // 跳过不在当前距离边界上的位置
          if (Math.abs(rowOffset) !== distance && Math.abs(colOffset) !== distance) {
            continue;
          }
          
          const newRow = currentPos.row + rowOffset;
          const newCol = currentPos.col + colOffset;
          
          if (this.canPlaceAt(tetromino, newRow, newCol)) {
            const foundPosition = {
              row: newRow,
              col: newCol,
              distance,
              offset: { row: rowOffset, col: colOffset }
            };
            
            this.emit('collision:validPositionFound', {
              tetromino: tetromino.shape,
              original: currentPos,
              found: foundPosition
            });
            
            return foundPosition;
          }
        }
      }
    }
    
    return null;
  }

  /**
   * 批量检查多个位置
   * @param {Tetromino} tetromino - 方块对象
   * @param {Array} positions - 位置列表
   * @returns {Array} 检测结果列表
   */
  batchCheckPositions(tetromino, positions) {
    const results = [];
    
    for (const position of positions) {
      const canPlace = this.canPlaceAt(
        tetromino,
        position.row,
        position.col,
        position.rotation
      );
      
      results.push({
        position,
        canPlace,
        timestamp: Date.now()
      });
    }
    
    this.emit('collision:batchChecked', {
      tetromino: tetromino.shape,
      totalChecked: positions.length,
      validPositions: results.filter(r => r.canPlace).length
    });
    
    return results;
  }

  /**
   * 清理碰撞缓存
   * @param {boolean} force - 是否强制清理所有缓存
   */
  clearCache(force = false) {
    if (force || this.collisionCache.size > this.options.maxCacheSize) {
      const oldSize = this.collisionCache.size;
      this.collisionCache.clear();
      
      console.log(`🧹 碰撞缓存已清理: ${oldSize} → 0`);
      this.emit('collision:cacheCleared', { oldSize, forced: force });
    }
  }

  /**
   * 获取碰撞检测统计
   * @returns {Object} 统计信息
   */
  getStats() {
    const totalCacheAccess = this.cacheHits + this.cacheMisses;
    
    return {
      performance: { ...this.performanceStats },
      cache: {
        size: this.collisionCache.size,
        hits: this.cacheHits,
        misses: this.cacheMisses,
        hitRate: totalCacheAccess > 0 ? (this.cacheHits / totalCacheAccess * 100).toFixed(2) + '%' : '0%'
      },
      totalChecks: this.performanceStats.totalChecks
    };
  }

  /**
   * 重置碰撞检测器
   */
  reset() {
    console.log('🔄 重置碰撞检测器');
    
    // 清理缓存
    this.collisionCache.clear();
    this.cacheHits = 0;
    this.cacheMisses = 0;
    
    // 重置统计
    this.performanceStats = {
      totalChecks: 0,
      averageCheckTime: 0,
      maxCheckTime: 0,
      cacheHitRate: 0
    };
    
    this.emit('collision:reset');
  }

  // =================== 私有方法 ===================

  /**
   * 检查形状碰撞
   * @param {Array} shapeData - 形状数据
   * @param {number} row - 行位置
   * @param {number} col - 列位置
   * @returns {boolean} 是否可以放置
   * @private
   */
  _checkShapeCollision(shapeData, row, col) {
    for (let shapeRow = 0; shapeRow < shapeData.length; shapeRow++) {
      for (let shapeCol = 0; shapeCol < shapeData[shapeRow].length; shapeCol++) {
        if (shapeData[shapeRow][shapeCol]) {
          const gridRow = row + shapeRow;
          const gridCol = col + shapeCol;
          
          // 检查边界
          if (gridRow < 0 || gridRow >= this.grid.rows || 
              gridCol < 0 || gridCol >= this.grid.cols) {
            return false;
          }
          
          // 检查位置是否被占用
          if (this._isPositionOccupied(gridRow, gridCol)) {
            return false;
          }
        }
      }
    }
    
    return true;
  }

  /**
   * 检查位置是否被占用
   * @param {number} row - 行
   * @param {number} col - 列
   * @returns {boolean} 是否被占用
   * @private
   */
  _isPositionOccupied(row, col) {
    const block = this.grid.getBlock(row, col);
    return block && !block.isEmpty;
  }

  /**
   * 检查方块的边界碰撞
   * @param {Object} block - 方块
   * @returns {Object} 碰撞信息
   * @private
   */
  _checkBlockBoundary(block) {
    const collisionTypes = [];
    
    if (block.row < 0) collisionTypes.push('top');
    if (block.row >= this.grid.rows) collisionTypes.push('bottom');
    if (block.col < 0) collisionTypes.push('left');
    if (block.col >= this.grid.cols) collisionTypes.push('right');
    
    return {
      hasCollision: collisionTypes.length > 0,
      types: collisionTypes
    };
  }

  /**
   * 尝试踢墙
   * @param {Tetromino} tetromino - 方块对象
   * @param {number} newRotation - 新旋转状态
   * @returns {Object} 踢墙结果
   * @private
   */
  _tryWallKick(tetromino, newRotation) {
    const kickTests = this._getWallKickTests(tetromino.shape, tetromino.rotation, newRotation);
    
    for (const testOffset of kickTests) {
      const testRow = tetromino.position.row + testOffset.row;
      const testCol = tetromino.position.col + testOffset.col;
      
      if (this.canPlaceAt(tetromino, testRow, testCol, newRotation)) {
        return {
          success: true,
          offset: testOffset,
          newPosition: { row: testRow, col: testCol }
        };
      }
    }
    
    return { success: false };
  }

  /**
   * 获取踢墙测试偏移
   * @param {string} shape - 方块形状
   * @param {number} fromRotation - 原旋转状态
   * @param {number} toRotation - 目标旋转状态
   * @returns {Array} 测试偏移列表
   * @private
   */
  _getWallKickTests(shape, fromRotation, toRotation) {
    // 标准踢墙表（简化版本）
    const standardKicks = [
      { row: 0, col: -1 }, // 左移1格
      { row: 0, col: 1 },  // 右移1格
      { row: -1, col: 0 }, // 上移1格
      { row: 0, col: -2 }, // 左移2格
      { row: 0, col: 2 },  // 右移2格
    ];
    
    // I型方块有特殊的踢墙规则
    if (shape === 'I') {
      return [
        { row: 0, col: -2 },
        { row: 0, col: 1 },
        { row: -1, col: -2 },
        { row: 2, col: 1 }
      ];
    }
    
    return standardKicks;
  }

  /**
   * 生成缓存键
   * @param {string} shape - 方块形状
   * @param {number} row - 行
   * @param {number} col - 列
   * @param {number} rotation - 旋转
   * @returns {string} 缓存键
   * @private
   */
  _generateCacheKey(shape, row, col, rotation) {
    return `${shape}:${row}:${col}:${rotation}`;
  }

  /**
   * 缓存结果
   * @param {string} key - 缓存键
   * @param {boolean} result - 结果
   * @private
   */
  _cacheResult(key, result) {
    if (this.collisionCache.size >= this.options.maxCacheSize) {
      // 移除最老的缓存项
      const firstKey = this.collisionCache.keys().next().value;
      this.collisionCache.delete(firstKey);
    }
    
    this.collisionCache.set(key, result);
  }

  /**
   * 更新性能统计
   * @param {number} startTime - 开始时间
   * @param {boolean} wasCached - 是否来自缓存
   * @private
   */
  _updatePerformanceStats(startTime, wasCached) {
    if (!this.options.enablePerformanceTracking) return;
    
    const checkTime = performance.now() - startTime;
    
    this.performanceStats.totalChecks++;
    
    if (!wasCached) {
      if (checkTime > this.performanceStats.maxCheckTime) {
        this.performanceStats.maxCheckTime = checkTime;
      }
      
      // 更新平均时间
      const currentAvg = this.performanceStats.averageCheckTime;
      const totalNonCachedChecks = this.cacheMisses;
      this.performanceStats.averageCheckTime = 
        (currentAvg * (totalNonCachedChecks - 1) + checkTime) / totalNonCachedChecks;
    }
    
    // 更新缓存命中率
    const totalAccess = this.cacheHits + this.cacheMisses;
    this.performanceStats.cacheHitRate = 
      totalAccess > 0 ? (this.cacheHits / totalAccess) * 100 : 0;
  }

  /**
   * 销毁碰撞检测器
   */
  destroy() {
    this.removeAllListeners();
    this.collisionCache.clear();
    
    console.log('🎯 碰撞检测器已销毁');
  }


  /**
   * 检查碰撞
   */
  checkCollision(tetromino, offsetX = 0, offsetY = 0) {
    if (!tetromino || !this.grid) {
      return false;
    }
    
    const x = tetromino.x + offsetX;
    const y = tetromino.y + offsetY;
    
    // 边界检查
    if (x < 0 || x + tetromino.width > this.grid.cols || 
        y < 0 || y + tetromino.height > this.grid.rows) {
      console.log(`🚧 边界碰撞检测: (${x}, ${y})`);
      return true;
    }
    
    // 方块碰撞检查（简化版本）
    for (let row = 0; row < tetromino.height; row++) {
      for (let col = 0; col < tetromino.width; col++) {
        if (tetromino.shape && tetromino.shape[row] && tetromino.shape[row][col] &&
            this.grid.blocks && this.grid.blocks[y + row] && this.grid.blocks[y + row][x + col]) {
          console.log(`💥 方块碰撞检测: (${x + col}, ${y + row})`);
          return true;
        }
      }
    }
    
    return false;
  }
} 