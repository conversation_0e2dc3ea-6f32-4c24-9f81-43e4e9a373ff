import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

/**
 * 物理引擎
 * 职责：专门处理游戏中的物理逻辑，包括重力、方块下落、浮动检测、碰撞响应等
 */
export class PhysicsEngine extends Emitter {
  constructor(grid, options = {}) {
    super();
    
    this.grid = grid;
    this.options = {
      gravity: 1, // 重力强度
      fallSpeed: 30, // 基础下落速度（帧数）
      softDropMultiplier: 20, // 软下降速度倍数
      lockDelay: 30, // 锁定延迟（帧数）
      maxFloatingChecks: 50, // 最大浮动检测次数
      cascadeDelay: 5, // 连锁延迟（帧数）
      ...options
    };
    
    // 重力状态
    this.gravityState = {
      timer: 0,
      currentSpeed: this.options.fallSpeed,
      isSoftDropping: false,
      isEnabled: true
    };
    
    // 下落状态跟踪
    this.fallState = {
      activeFalls: new Map(), // 正在下落的方块
      fallQueue: [], // 下落队列
      isProcessing: false,
      cascadeTimer: 0
    };
    
    // 浮动检测状态
    this.floatingState = {
      isChecking: false,
      checkQueue: new Set(),
      foundFloating: new Set(),
      checkCount: 0,
      maxChecks: this.options.maxFloatingChecks
    };
    
    // 物理统计
    this.stats = {
      totalFalls: 0,
      totalFloatingFixed: 0,
      maxFallDistance: 0,
      averageFallDistance: 0,
      cascadeCount: 0
    };
    
    console.log('⚡ 物理引擎已初始化', this.options);
  }

  /**
   * 更新物理引擎
   * @param {number} deltaTime - 时间增量（帧数）
   */
  update(deltaTime = 1) {
    if (!this.gravityState.isEnabled) return;
    
    // 更新重力计时器
    this._updateGravity(deltaTime);
    
    // 处理下落队列
    this._processFallQueue();
    
    // 处理连锁反应
    this._processCascade(deltaTime);
    
    // 处理浮动检测
    this._processFloatingDetection();
  }

  /**
   * 设置重力参数
   * @param {Object} gravityConfig - 重力配置
   */
  setGravity(gravityConfig) {
    this.options = { ...this.options, ...gravityConfig };
    this.gravityState.currentSpeed = this.options.fallSpeed;
    
    console.log('⚙️ 重力参数已更新:', gravityConfig);
    this.emit('gravity:configChanged', gravityConfig);
  }

  /**
   * 启用/禁用重力
   * @param {boolean} enabled - 是否启用重力
   */
  setGravityEnabled(enabled) {
    this.gravityState.isEnabled = enabled;
    console.log(`⚡ 重力${enabled ? '启用' : '禁用'}`);
    this.emit('gravity:toggled', { enabled });
  }

  /**
   * 设置软下降状态
   * @param {boolean} isSoftDropping - 是否软下降
   */
  setSoftDropping(isSoftDropping) {
    this.gravityState.isSoftDropping = isSoftDropping;
    
    if (isSoftDropping) {
      this.gravityState.currentSpeed = Math.floor(this.options.fallSpeed / this.options.softDropMultiplier);
    } else {
      this.gravityState.currentSpeed = this.options.fallSpeed;
    }
    
    console.log(`📉 软下降${isSoftDropping ? '启用' : '禁用'}，当前速度: ${this.gravityState.currentSpeed}`);
    this.emit('gravity:softDropChanged', { isSoftDropping, speed: this.gravityState.currentSpeed });
  }

  /**
   * 应用重力到指定方块或区域
   * @param {Object} target - 目标（方块或区域）
   * @param {boolean} immediate - 是否立即应用
   * @returns {boolean} 是否有方块下落
   */
  applyGravity(target = null, immediate = false) {
    if (target) {
      return this._applyGravityToTarget(target, immediate);
    } else {
      return this._applyGravityToAll(immediate);
    }
  }

  /**
   * 处理方块下落
   * @param {boolean} isSpecialEffect - 是否是特效引起的下落
   * @returns {boolean} 是否有方块下落
   */
  handleBlocksDrop(isSpecialEffect = false) {
    console.log('📉 处理方块下落', { isSpecialEffect });
    
    let hasDropped = false;
    const affectedColumns = new Set();
    
    try {
      // 从底部向上扫描每一列
      for (let col = 0; col < this.grid.cols; col++) {
        const columnDropped = this._processColumnDrop(col);
        if (columnDropped) {
          hasDropped = true;
          affectedColumns.add(col);
        }
      }
      
      if (hasDropped) {
        this.stats.totalFalls++;
        
        this.emit('physics:blocksDropped', {
          affectedColumns: Array.from(affectedColumns),
          isSpecialEffect
        });
        
        console.log(`✅ 方块下落完成，影响列: ${Array.from(affectedColumns).join(', ')}`);
      }
      
    } catch (error) {
      console.error('处理方块下落时出错:', error);
      this.emit('physics:dropError', { error });
    }
    
    return hasDropped;
  }

  /**
   * 检测并修复浮动方块
   * @returns {boolean} 是否有浮动方块被修复
   */
  detectAndFixFloatingBlocks() {
    console.log('🔍 检测浮动方块');
    
    this.floatingState.isChecking = true;
    this.floatingState.foundFloating.clear();
    this.floatingState.checkCount = 0;
    
    // 扫描整个网格寻找浮动方块
    for (let row = this.grid.rows - 2; row >= 0; row--) { // 从倒数第二行开始
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        
        if (block && !block.isEmpty && this._isBlockFloating(row, col)) {
          this.floatingState.foundFloating.add(`${row},${col}`);
        }
      }
    }
    
    // 修复找到的浮动方块
    const fixedCount = this._fixFloatingBlocks();
    
    this.floatingState.isChecking = false;
    
    if (fixedCount > 0) {
      this.stats.totalFloatingFixed += fixedCount;
      
      this.emit('physics:floatingFixed', {
        fixedCount,
        totalFixed: this.stats.totalFloatingFixed
      });
      
      console.log(`✅ 修复了${fixedCount}个浮动方块`);
      return true;
    }
    
    console.log('ℹ️ 没有发现浮动方块');
    return false;
  }

  /**
   * 智能浮动检测（优化版本）
   * @returns {boolean} 是否有浮动方块被修复
   */
  smartFloatingCheck() {
    console.log('🧠 智能浮动检测');
    
    const startTime = Date.now();
    let totalFixed = 0;
    let iterationCount = 0;
    const maxIterations = 10;
    
    // 多次迭代，直到没有浮动方块
    while (iterationCount < maxIterations) {
      const fixedThisIteration = this.detectAndFixFloatingBlocks();
      
      if (!fixedThisIteration) {
        break; // 没有更多浮动方块
      }
      
      totalFixed += this.floatingState.foundFloating.size;
      iterationCount++;
      
      // 每次修复后，可能产生新的下落，需要处理
      this.handleBlocksDrop(true);
    }
    
    const endTime = Date.now();
    
    if (totalFixed > 0) {
      console.log(`🎯 智能浮动检测完成: 修复${totalFixed}个方块，用时${endTime - startTime}ms，迭代${iterationCount}次`);
      
      this.emit('physics:smartFloatingComplete', {
        totalFixed,
        iterations: iterationCount,
        duration: endTime - startTime
      });
    }
    
    return totalFixed > 0;
  }

  /**
   * 计算最优下落位置
   * @param {Array} blocksToMove - 需要移动的方块
   * @returns {Map} 方块移动计划
   */
  calculateOptimalDropPositions(blocksToMove) {
    console.log('🎯 计算最优下落位置', { count: blocksToMove.length });
    
    const movePlan = new Map();
    
    for (const blockPos of blocksToMove) {
      const [row, col] = blockPos.split(',').map(Number);
      const block = this.grid.getBlock(row, col);
      
      if (!block || block.isEmpty) continue;
      
      // 找到该列中该方块能到达的最低位置
      const targetRow = this._findLowestPositionInColumn(col, row);
      
      if (targetRow > row) {
        movePlan.set(blockPos, {
          from: { row, col },
          to: { row: targetRow, col },
          block: block,
          distance: targetRow - row
        });
      }
    }
    
    console.log(`计算完成，${movePlan.size}个方块需要移动`);
    return movePlan;
  }

  /**
   * 执行方块移动计划
   * @param {Map} movePlan - 移动计划
   * @returns {boolean} 是否执行成功
   */
  executeMoveplan(movePlan) {
    if (movePlan.size === 0) return false;
    
    console.log('⚡ 执行方块移动计划', { count: movePlan.size });
    
    try {
      // 先清除原位置的方块
      for (const [, moveData] of movePlan) {
        this.grid.setBlock(moveData.from.row, moveData.from.col, null);
      }
      
      // 再设置新位置的方块
      for (const [, moveData] of movePlan) {
        this.grid.setBlock(moveData.to.row, moveData.to.col, moveData.block);
        
        // 更新统计
        if (moveData.distance > this.stats.maxFallDistance) {
          this.stats.maxFallDistance = moveData.distance;
        }
      }
      
      // 计算平均下落距离
      const totalDistance = Array.from(movePlan.values()).reduce((sum, move) => sum + move.distance, 0);
      this.stats.averageFallDistance = totalDistance / movePlan.size;
      
      this.emit('physics:movePlanExecuted', {
        moveCount: movePlan.size,
        totalDistance,
        averageDistance: this.stats.averageFallDistance
      });
      
      console.log('✅ 移动计划执行完成');
      return true;
      
    } catch (error) {
      console.error('执行移动计划时出错:', error);
      this.emit('physics:movePlanError', { error });
      return false;
    }
  }

  /**
   * 重置物理引擎
   */
  reset() {
    console.log('🔄 重置物理引擎');
    
    // 重置重力状态
    this.gravityState.timer = 0;
    this.gravityState.currentSpeed = this.options.fallSpeed;
    this.gravityState.isSoftDropping = false;
    this.gravityState.isEnabled = true;
    
    // 重置下落状态
    this.fallState.activeFalls.clear();
    this.fallState.fallQueue = [];
    this.fallState.isProcessing = false;
    this.fallState.cascadeTimer = 0;
    
    // 重置浮动状态
    this.floatingState.isChecking = false;
    this.floatingState.checkQueue.clear();
    this.floatingState.foundFloating.clear();
    this.floatingState.checkCount = 0;
    
    // 重置统计
    this.stats = {
      totalFalls: 0,
      totalFloatingFixed: 0,
      maxFallDistance: 0,
      averageFallDistance: 0,
      cascadeCount: 0
    };
    
    this.emit('physics:reset');
  }

  /**
   * 获取物理引擎状态
   * @returns {Object} 状态信息
   */
  getState() {
    return {
      gravity: { ...this.gravityState },
      fall: {
        activeFalls: this.fallState.activeFalls.size,
        fallQueue: this.fallState.fallQueue.length,
        isProcessing: this.fallState.isProcessing
      },
      floating: {
        isChecking: this.floatingState.isChecking,
        foundFloating: this.floatingState.foundFloating.size,
        checkCount: this.floatingState.checkCount
      },
      stats: { ...this.stats }
    };
  }

  // =================== 私有方法 ===================

  /**
   * 更新重力
   * @param {number} deltaTime - 时间增量
   * @private
   */
  _updateGravity(deltaTime) {
    this.gravityState.timer += deltaTime;
    
    // 检查是否到达下落时间
    if (this.gravityState.timer >= this.gravityState.currentSpeed) {
      this.gravityState.timer = 0;
      this.emit('gravity:tick');
    }
  }

  /**
   * 应用重力到指定目标
   * @param {Object} target - 目标
   * @param {boolean} immediate - 是否立即应用
   * @private
   */
  _applyGravityToTarget(target, immediate) {
    // 实现针对特定目标的重力应用
    console.log('🎯 应用重力到目标:', target);
    return false; // 临时返回
  }

  /**
   * 应用重力到所有方块
   * @param {boolean} immediate - 是否立即应用
   * @private
   */
  _applyGravityToAll(immediate) {
    // 实现全局重力应用
    return this.handleBlocksDrop();
  }

  /**
   * 处理单列的方块下落
   * @param {number} col - 列索引
   * @returns {boolean} 该列是否有方块下落
   * @private
   */
  _processColumnDrop(col) {
    console.log(`[Debug] Processing column ${col}`);
    let hasDropped = false;
    
    // 从底部向上扫描
    let writeRow = this.grid.rows - 1;
    
    for (let readRow = this.grid.rows - 1; readRow >= 0; readRow--) {
      const block = this.grid.getBlock(readRow, col);
      
      if (block && !block.isEmpty) {
        console.log(`[Debug] Found block at (${readRow}, ${col}). Color: ${block.color}, Effect: ${block.effect}. writeRow is ${writeRow}`);
        if (writeRow !== readRow) {
          // 需要移动方块
          console.log(`[Debug] Moving block from (${readRow}, ${col}) to (${writeRow}, ${col})`);
          this.grid.setBlock(writeRow, col, block);
          this.grid.setBlock(readRow, col, null);
          hasDropped = true;
        } else {
          console.log(`[Debug] Block at (${readRow}, ${col}) is already in place.`);
        }
        writeRow--;
      } else {
        // console.log(`[Debug] Empty space at (${readRow}, ${col})`);
      }
    }
    
    // 清理 writeRow 以上的剩余部分
    for (let row = writeRow; row >= 0; row--) {
      if (this.grid.getBlock(row, col)) {
        console.log(`[Debug] Clearing leftover block at (${row}, ${col})`);
        this.grid.setBlock(row, col, null);
      }
    }

    return hasDropped;
  }

  /**
   * 检查方块是否浮动
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {boolean} 是否浮动
   * @private
   */
  _isBlockFloating(row, col) {
    // 检查下方是否有支撑
    if (row === this.grid.rows - 1) {
      return false; // 底行不会浮动
    }
    
    const belowBlock = this.grid.getBlock(row + 1, col);
    return !belowBlock || belowBlock.isEmpty;
  }

  /**
   * 修复浮动方块
   * @returns {number} 修复的方块数量
   * @private
   */
  _fixFloatingBlocks() {
    let fixedCount = 0;
    
    for (const posKey of this.floatingState.foundFloating) {
      const [row, col] = posKey.split(',').map(Number);
      const block = this.grid.getBlock(row, col);
      
      if (block && !block.isEmpty) {
        const targetRow = this._findLowestPositionInColumn(col, row);
        
        if (targetRow > row) {
          // 移动方块到正确位置
          this.grid.setBlock(targetRow, col, block);
          this.grid.setBlock(row, col, null);
          fixedCount++;
        }
      }
    }
    
    return fixedCount;
  }

  /**
   * 在指定列中找到最低可用位置
   * @param {number} col - 列索引
   * @param {number} startRow - 开始行
   * @returns {number} 最低可用行
   * @private
   */
  _findLowestPositionInColumn(col, startRow) {
    for (let row = this.grid.rows - 1; row > startRow; row--) {
      const block = this.grid.getBlock(row, col);
      if (!block || block.isEmpty) {
        return row;
      }
    }
    return startRow;
  }

  /**
   * 处理下落队列
   * @private
   */
  _processFallQueue() {
    if (this.fallState.fallQueue.length === 0) return;
    
    // 处理队列中的下落请求
    while (this.fallState.fallQueue.length > 0) {
      const fallRequest = this.fallState.fallQueue.shift();
      this._processFallRequest(fallRequest);
    }
  }

  /**
   * 处理下落请求
   * @param {Object} fallRequest - 下落请求
   * @private
   */
  _processFallRequest(fallRequest) {
    // 实现具体的下落请求处理
    console.log('⬇️ 处理下落请求:', fallRequest);
  }

  /**
   * 处理连锁反应
   * @param {number} deltaTime - 时间增量
   * @private
   */
  _processCascade(deltaTime) {
    if (this.fallState.cascadeTimer > 0) {
      this.fallState.cascadeTimer -= deltaTime;
      
      if (this.fallState.cascadeTimer <= 0) {
        this.stats.cascadeCount++;
        this.emit('physics:cascadeTriggered', { count: this.stats.cascadeCount });
      }
    }
  }

  /**
   * 处理浮动检测
   * @private
   */
  _processFloatingDetection() {
    if (!this.floatingState.isChecking || this.floatingState.checkQueue.size === 0) {
      return;
    }
    
    // 批量处理浮动检测
    const batchSize = 5;
    let processed = 0;
    
    for (const posKey of this.floatingState.checkQueue) {
      if (processed >= batchSize) break;
      
      const [row, col] = posKey.split(',').map(Number);
      if (this._isBlockFloating(row, col)) {
        this.floatingState.foundFloating.add(posKey);
      }
      
      this.floatingState.checkQueue.delete(posKey);
      processed++;
    }
  }

  /**
   * 销毁物理引擎
   */
  destroy() {
    this.removeAllListeners();
    
    // 清理状态
    this.fallState.activeFalls.clear();
    this.fallState.fallQueue = [];
    this.floatingState.checkQueue.clear();
    this.floatingState.foundFloating.clear();
    
    console.log('⚡ 物理引擎已销毁');
  }
} 