/**
 * 垃圾方块生成器
 * 负责从底部生成新的方块行，作为主要难度机制
 */

import { BLOCK_COLORS, BLOCK_EFFECTS } from './block.js';
import Block from './block.js';

export default class GarbageGenerator {
  /**
   * 创建垃圾生成器
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    // 基础配置
    this.enabled = options.enabled !== false;
    this.level = options.level || 1;
    this.colorCount = options.colorCount || 4;
    this.allowedEffects = options.allowedEffects || ['frozen'];
    
    // 生成频率配置（帧数）
    this.baseInterval = options.baseInterval || 1800; // 30秒基础间隔
    this.minInterval = options.minInterval || 300;    // 5秒最小间隔
    this.intervalDecrease = options.intervalDecrease || 60; // 每级减少1秒
    
    // 生成数量配置
    this.baseRowCount = options.baseRowCount || 1;    // 基础生成行数
    this.maxRowCount = options.maxRowCount || 3;      // 最大生成行数
    this.rowIncreaseLevel = options.rowIncreaseLevel || 10; // 每10级增加1行
    
    // 方块密度配置（每行有多少个方块）
    this.baseDensity = options.baseDensity || 0.6;    // 60%密度
    this.maxDensity = options.maxDensity || 0.9;      // 90%最大密度
    this.densityIncrease = options.densityIncrease || 0.02; // 每级增加2%
    
    // 特效概率配置
    this.baseEffectProbability = options.baseEffectProbability || 0.05; // 5%基础特效概率
    this.maxEffectProbability = options.maxEffectProbability || 0.25;   // 25%最大特效概率
    this.effectIncrease = options.effectIncrease || 0.01; // 每级增加1%
    
    // 运行时状态
    this.timer = 0;
    this.currentInterval = this.baseInterval;
    this.isActive = false;
    this.grid = null;
    
    // 预警系统
    this.warningTime = 300; // 5秒预警时间
    this.isWarning = false;
    this.warningTimer = 0;
    
    // 智能生成配置
    this.smartGeneration = options.smartGeneration !== false; // 默认启用智能生成
    this.avoidanceStrength = options.avoidanceStrength || 0.3; // 避免相邻相同颜色的概率
    this.maxGenerationRetries = options.maxGenerationRetries || 3; // 最大重试次数
    
    console.log('垃圾生成器初始化:', {
      level: this.level,
      interval: this.currentInterval,
      density: this.getCurrentDensity(),
      rowCount: this.getCurrentRowCount()
    });
  }
  
  /**
   * 设置游戏网格引用
   * @param {Grid} grid - 游戏网格
   */
  setGrid(grid) {
    this.grid = grid;
  }
  
  /**
   * 启动垃圾生成器
   */
  start() {
    if (!this.enabled) return;
    
    this.isActive = true;
    this.timer = 0;
    this.updateInterval();
    console.log('垃圾生成器启动，间隔:', this.currentInterval);
  }
  
  /**
   * 停止垃圾生成器
   */
  stop() {
    this.isActive = false;
    this.isWarning = false;
    this.warningTimer = 0;
    this.timer = 0;
  }
  
  /**
   * 暂停垃圾生成器
   */
  pause() {
    this.isActive = false;
  }
  
  /**
   * 恢复垃圾生成器
   */
  resume() {
    if (this.enabled) {
      this.isActive = true;
    }
  }
  
  /**
   * 更新关卡
   * @param {number} level - 新关卡
   */
  updateLevel(level) {
    this.level = level;
    this.updateInterval();
    console.log(`垃圾生成器更新到关卡${level}:`, {
      interval: this.currentInterval,
      density: this.getCurrentDensity(),
      rowCount: this.getCurrentRowCount()
    });
  }
  
  /**
   * 更新生成间隔
   * @private
   */
  updateInterval() {
    // 根据关卡计算新的生成间隔
    const decrease = Math.floor(this.level / 2) * this.intervalDecrease;
    this.currentInterval = Math.max(this.minInterval, this.baseInterval - decrease);
  }
  
  /**
   * 获取当前密度
   * @returns {number} 当前方块密度
   */
  getCurrentDensity() {
    const increase = this.level * this.densityIncrease;
    return Math.min(this.maxDensity, this.baseDensity + increase);
  }
  
  /**
   * 获取当前生成行数
   * @returns {number} 当前生成行数
   */
  getCurrentRowCount() {
    const additionalRows = Math.floor(this.level / this.rowIncreaseLevel);
    return Math.min(this.maxRowCount, this.baseRowCount + additionalRows);
  }
  
  /**
   * 获取当前特效概率
   * @returns {number} 当前特效概率
   */
  getCurrentEffectProbability() {
    const increase = this.level * this.effectIncrease;
    return Math.min(this.maxEffectProbability, this.baseEffectProbability + increase);
  }
  
  /**
   * 更新垃圾生成器
   * @param {number} deltaTime - 时间增量（帧数）
   * @returns {Object|null} 生成事件信息
   */
  update(deltaTime = 1) {
    if (!this.isActive || !this.grid) return null;
    
    this.timer += deltaTime;
    
    // 检查是否进入预警阶段
    if (!this.isWarning && this.timer >= this.currentInterval - this.warningTime) {
      this.isWarning = true;
      this.warningTimer = 0;
      return { type: 'warning', timeLeft: this.warningTime };
    }
    
    // 更新预警计时器
    if (this.isWarning) {
      this.warningTimer += deltaTime;
    }
    
    // 检查是否到达生成时间
    if (this.timer >= this.currentInterval) {
      return this.generateGarbageRows();
    }
    
    return null;
  }
  
  /**
   * 生成垃圾行
   * @returns {Object} 生成结果
   * @private
   */
  generateGarbageRows() {
    if (!this.grid) return null;
    
    const rowCount = this.getCurrentRowCount();
    const density = this.getCurrentDensity();
    const effectProbability = this.getCurrentEffectProbability();
    
    console.log(`生成${rowCount}行垃圾方块，密度${density.toFixed(2)}`);
    
    // 检查是否有足够空间生成
    if (!this.canGenerateRows(rowCount)) {
      console.log('空间不足，无法生成垃圾行');
      return { type: 'failed', reason: 'no_space' };
    }
    
    // 向上推移现有方块
    this.pushExistingBlocksUp(rowCount);
    
    // 生成新的垃圾行（现在带有智能重试机制）
    const generatedBlocks = this.createGarbageRows(rowCount, density, effectProbability);
    
    // 可选的验证步骤（主要用于统计和调试）
    const startRow = this.grid.rows - rowCount;
    if (this._validateGarbageRows(startRow, rowCount)) {
      console.warn('⚠️ 垃圾行仍包含立即可消除的组合（重试机制未完全成功）');
    } else {
      console.log('✅ 垃圾行生成成功，无立即可消除组合');
    }
    
    // 重置计时器
    this.timer = 0;
    this.isWarning = false;
    this.warningTimer = 0;
    
    return {
      type: 'generated',
      rowCount: rowCount,
      blockCount: generatedBlocks.length,
      blocks: generatedBlocks,
      needsActiveTetrominoAdjustment: true // 标记需要调整活动方块
    };
  }
  
  /**
   * 检查是否可以生成指定行数
   * @param {number} rowCount - 要生成的行数
   * @returns {boolean} 是否可以生成
   * @private
   */
  canGenerateRows(rowCount) {
    // 检查顶部是否有足够空间
    for (let row = 0; row < rowCount; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.getBlock(row, col)) {
          return false; // 顶部有方块，无法生成
        }
      }
    }
    return true;
  }
  
  /**
   * 向上推移现有方块
   * @param {number} rowCount - 推移行数
   * @private
   */
  pushExistingBlocksUp(rowCount) {
    // 从上到下遍历，将方块向上移动
    for (let row = 0; row < this.grid.rows - rowCount; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row + rowCount, col);
        if (block) {
          // 移除原位置
          this.grid.removeBlock(row + rowCount, col);
          // 更新方块位置
          block.row = row;
          // 放置到新位置
          this.grid.placeBlock(block, row, col);
        }
      }
    }
  }
  
  /**
   * 创建垃圾行
   * @param {number} rowCount - 行数
   * @param {number} density - 密度
   * @param {number} effectProbability - 特效概率
   * @returns {Array} 生成的方块列表
   * @private
   */
  createGarbageRows(rowCount, density, effectProbability) {
    const colors = Object.keys(BLOCK_COLORS).slice(0, this.colorCount);
    let attempts = 0;
    const maxAttempts = this.maxGenerationRetries;
    
    while (attempts < maxAttempts) {
      attempts++;
      
      // 保存当前网格状态
      const gridSnapshot = this._saveGridSnapshot();
      
      try {
        const generatedBlocks = [];
        
        if (this.smartGeneration) {
          // 使用智能生成算法
          for (let i = 0; i < rowCount; i++) {
            const row = this.grid.rows - rowCount + i;
            const rowBlocks = this._createSingleRowSafe(row, density, effectProbability, colors);
            generatedBlocks.push(...rowBlocks);
          }
        } else {
          // 使用简单随机生成算法（原始方法）
          for (let i = 0; i < rowCount; i++) {
            const row = this.grid.rows - rowCount + i;
            const rowBlocks = this._createSimpleRow(row, density, effectProbability, colors);
            generatedBlocks.push(...rowBlocks);
          }
        }
        
        // 验证生成的垃圾行是否包含立即可消除的组合
        const startRow = this.grid.rows - rowCount;
        if (!this._validateGarbageRows(startRow, rowCount)) {
          // 生成成功，无立即可消除组合
          console.log(`✅ 第${attempts}次尝试成功生成垃圾行，无立即可消除组合`);
          return generatedBlocks;
        } else {
          // 包含立即可消除组合，需要重试
          console.log(`⚠️ 第${attempts}次尝试生成的垃圾行包含立即可消除组合，准备重试...`);
          
          // 恢复网格状态并重试
          this._restoreGridSnapshot(gridSnapshot, rowCount);
          
          if (attempts < maxAttempts) {
            continue; // 重试
          } else {
            // 最后一次尝试失败，使用当前结果
            console.warn(`❌ 经过${maxAttempts}次尝试后仍包含立即可消除组合，使用当前结果`);
            return generatedBlocks;
          }
        }
      } catch (error) {
        console.error('生成垃圾行时发生错误:', error);
        // 恢复网格状态
        this._restoreGridSnapshot(gridSnapshot, rowCount);
        
        if (attempts >= maxAttempts) {
          // 如果是最后一次尝试，返回空数组避免游戏崩溃
          return [];
        }
      }
    }
    
    // 理论上不应该到达这里
    console.warn('垃圾行生成失败，返回空数组');
    return [];
  }

  /**
   * 保存当前网格状态的快照
   * @returns {Object} 网格状态快照
   * @private
   */
  _saveGridSnapshot() {
    const snapshot = {
      blocks: new Map()
    };
    
    // 保存当前网格中所有方块的状态
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          const key = `${row}-${col}`;
          snapshot.blocks.set(key, {
            color: block.color,
            effect: block.effect,
            row: block.row,
            col: block.col
          });
        }
      }
    }
    
    return snapshot;
  }

  /**
   * 恢复网格状态快照
   * @param {Object} snapshot - 网格状态快照
   * @param {number} rowCount - 需要清除的垃圾行数
   * @private
   */
  _restoreGridSnapshot(snapshot, rowCount) {
    // 清除生成的垃圾行区域
    const startRow = this.grid.rows - rowCount;
    for (let row = startRow; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        this.grid.removeBlock(row, col);
      }
    }
    
    // 恢复所有方块到原始位置
    snapshot.blocks.forEach((blockData, key) => {
      const block = new Block(blockData.color, blockData.effect);
      block.row = blockData.row;
      block.col = blockData.col;
      this.grid.placeBlock(block, blockData.row, blockData.col);
    });
  }

  /**
   * 安全版本的智能创建单行垃圾方块，使用更强的约束避免立即可消除的组合
   * @param {number} row - 行号
   * @param {number} density - 密度
   * @param {number} effectProbability - 特效概率
   * @param {Array} colors - 可用颜色列表
   * @returns {Array} 生成的方块列表
   * @private
   */
  _createSingleRowSafe(row, density, effectProbability, colors) {
    const generatedBlocks = [];
    const rowPattern = new Array(this.grid.cols).fill(null);
    
    // 第一步：确定哪些位置需要放置方块
    const positions = [];
    for (let col = 0; col < this.grid.cols; col++) {
      if (Math.random() < density) {
        positions.push(col);
      }
    }
    
    // 第二步：为每个位置分配颜色，使用更强的约束避免创建3连
    for (const col of positions) {
      const availableColors = this._getSafeAvailableColors(rowPattern, row, col, colors);
      const selectedColor = availableColors[Math.floor(Math.random() * availableColors.length)];
      rowPattern[col] = selectedColor;
    }
    
    // 第三步：最终验证和调整
    this._finalAdjustmentPass(rowPattern, row);
    
    // 第四步：创建实际的方块对象
    for (let col = 0; col < this.grid.cols; col++) {
      if (rowPattern[col]) {
        const colorKey = rowPattern[col];
        const color = BLOCK_COLORS[colorKey];
        
        // 随机选择特效
        let effect = BLOCK_EFFECTS.NONE;
        if (Math.random() < effectProbability && this.allowedEffects.length > 0) {
          const effectName = this.allowedEffects[Math.floor(Math.random() * this.allowedEffects.length)];
          effect = BLOCK_EFFECTS[effectName.toUpperCase()] || BLOCK_EFFECTS.NONE;
        }
        
        // 创建方块
        const block = new Block(color, effect);
        
        // 放置到网格
        this.grid.placeBlock(block, row, col);
        
        generatedBlocks.push({
          row: row,
          col: col,
          color: colorKey,
          effect: effect
        });
      }
    }
    
    return generatedBlocks;
  }

  /**
   * 获取指定位置可用的颜色，使用更强的约束避免各种可消除组合
   * @param {Array} rowPattern - 当前行的颜色模式
   * @param {number} row - 行号
   * @param {number} col - 列号
   * @param {Array} allColors - 所有可用颜色
   * @returns {Array} 可用颜色列表
   * @private
   */
  _getSafeAvailableColors(rowPattern, row, col, allColors) {
    const forbiddenColors = new Set();
    
    // 1. 检查水平3连 - 左侧连续颜色
    if (col >= 2) {
      const leftColor1 = rowPattern[col - 1];
      const leftColor2 = rowPattern[col - 2];
      if (leftColor1 && leftColor2 && leftColor1 === leftColor2) {
        forbiddenColors.add(leftColor1);
      }
    }
    
    // 2. 检查水平3连 - 左侧单个相邻
    if (col >= 1) {
      const leftColor = rowPattern[col - 1];
      if (leftColor && Math.random() < this.avoidanceStrength) {
        forbiddenColors.add(leftColor);
      }
    }
    
    // 3. 检查垂直3连 - 上方连续颜色
    if (row >= 2) {
      const upperBlock1 = this.grid.getBlock(row - 1, col);
      const upperBlock2 = this.grid.getBlock(row - 2, col);
      
      if (upperBlock1 && upperBlock2) {
        const upperColor1 = this._getBlockColorKey(upperBlock1);
        const upperColor2 = this._getBlockColorKey(upperBlock2);
        
        if (upperColor1 === upperColor2) {
          forbiddenColors.add(upperColor1);
        }
      }
    }
    
    // 4. 检查垂直3连 - 上方单个相邻
    if (row >= 1) {
      const upperBlock = this.grid.getBlock(row - 1, col);
      if (upperBlock) {
        const upperColor = this._getBlockColorKey(upperBlock);
        if (upperColor && Math.random() < this.avoidanceStrength) {
          forbiddenColors.add(upperColor);
        }
      }
    }
    
    // 5. 检查对角线组合（可选，更严格的约束）
    if (this.avoidanceStrength > 0.5) { // 只在高约束强度下启用
      // 左上角
      if (row >= 1 && col >= 1) {
        const diagonalBlock = this.grid.getBlock(row - 1, col - 1);
        if (diagonalBlock) {
          const diagonalColor = this._getBlockColorKey(diagonalBlock);
          if (diagonalColor && Math.random() < (this.avoidanceStrength - 0.3)) {
            forbiddenColors.add(diagonalColor);
          }
        }
      }
      
      // 右上角
      if (row >= 1 && col < this.grid.cols - 1) {
        const diagonalBlock = this.grid.getBlock(row - 1, col + 1);
        if (diagonalBlock) {
          const diagonalColor = this._getBlockColorKey(diagonalBlock);
          if (diagonalColor && Math.random() < (this.avoidanceStrength - 0.3)) {
            forbiddenColors.add(diagonalColor);
          }
        }
      }
    }
    
    // 返回可用颜色
    const availableColors = allColors.filter(color => !forbiddenColors.has(color));
    
    // 如果没有可用颜色，至少保证有一种颜色可选（避免无限循环）
    if (availableColors.length === 0) {
      // 选择使用频率最低的颜色
      const colorUsage = new Map();
      allColors.forEach(color => colorUsage.set(color, 0));
      
      // 统计当前行已使用的颜色
      for (let c = 0; c < col; c++) {
        if (rowPattern[c]) {
          colorUsage.set(rowPattern[c], colorUsage.get(rowPattern[c]) + 1);
        }
      }
      
      // 返回使用最少的颜色
      const sortedColors = allColors.sort((a, b) => colorUsage.get(a) - colorUsage.get(b));
      return [sortedColors[0]];
    }
    
    return availableColors;
  }

  /**
   * 最终调整阶段，进行全局检查和优化
   * @param {Array} rowPattern - 当前行的颜色模式
   * @param {number} row - 行号
   * @private
   */
  _finalAdjustmentPass(rowPattern, row) {
    // 检查是否会形成满行
    let blockCount = 0;
    for (let col = 0; col < this.grid.cols; col++) {
      if (rowPattern[col]) {
        blockCount++;
      }
    }
    
    // 如果接近满行，随机移除一些方块
    if (blockCount >= this.grid.cols * 0.95) {
      const removeCount = Math.ceil((blockCount - this.grid.cols * 0.8));
      for (let i = 0; i < removeCount; i++) {
        const randomCol = Math.floor(Math.random() * this.grid.cols);
        if (rowPattern[randomCol]) {
          rowPattern[randomCol] = null;
          console.log(`防止满行：移除位置 [${row}, ${randomCol}] 的方块`);
        }
      }
    }
    
    // 二次验证垂直匹配
    for (let col = 0; col < this.grid.cols; col++) {
      if (!rowPattern[col]) continue;
      
      if (row >= 2) {
        const upperBlock1 = this.grid.getBlock(row - 1, col);
        const upperBlock2 = this.grid.getBlock(row - 2, col);
        
        if (upperBlock1 && upperBlock2) {
          const upperColor1 = this._getBlockColorKey(upperBlock1);
          const upperColor2 = this._getBlockColorKey(upperBlock2);
          
          if (upperColor1 === upperColor2 && rowPattern[col] === upperColor1) {
            // 强制更换颜色
            const colors = Object.keys(BLOCK_COLORS).slice(0, this.colorCount);
            const alternativeColors = colors.filter(color => color !== upperColor1);
            
            if (alternativeColors.length > 0) {
              const newColor = alternativeColors[Math.floor(Math.random() * alternativeColors.length)];
              console.log(`最终调整：垂直3连 [${row}, ${col}] ${rowPattern[col]} -> ${newColor}`);
              rowPattern[col] = newColor;
            }
          }
        }
      }
    }
  }

  /**
   * 获取方块的颜色键
   * @param {Block} block - 方块对象
   * @returns {string} 颜色键
   * @private
   */
  _getBlockColorKey(block) {
    if (!block) return null;
    
    // 通过颜色值反查颜色键
    for (const [key, value] of Object.entries(BLOCK_COLORS)) {
      if (value === block.color) {
        return key;
      }
    }
    
    return null;
  }

  /**
   * 验证生成的垃圾行是否包含立即可消除的组合
   * @param {number} startRow - 开始行
   * @param {number} rowCount - 行数
   * @returns {boolean} 如果包含立即可消除的组合返回true
   * @private
   */
  _validateGarbageRows(startRow, rowCount) {
    // 检查水平3连
    for (let row = startRow; row < startRow + rowCount; row++) {
      for (let col = 0; col < this.grid.cols - 2; col++) {
        const block1 = this.grid.getBlock(row, col);
        const block2 = this.grid.getBlock(row, col + 1);
        const block3 = this.grid.getBlock(row, col + 2);
        
        if (block1 && block2 && block3 && 
            block1.color === block2.color && block2.color === block3.color) {
          console.warn(`发现水平3连: [${row}, ${col}-${col+2}]`);
          return true;
        }
      }
    }
    
    // 检查垂直3连（包括与现有方块的组合）
    // 如果添加的行数少于3行，不可能在新添加的行内部形成3连，但仍需检查与现有方块的组合
    if (rowCount < 3) {
      // 只需检查可能跨越边界的垂直3连
      for (let row = Math.max(0, startRow - 2); row < startRow + rowCount; row++) {
        for (let col = 0; col < this.grid.cols; col++) {
          // 确保检查的三个位置都是有效的
          if (row + 2 < this.grid.rows) {
            const block1 = this.grid.getBlock(row, col);
            const block2 = this.grid.getBlock(row + 1, col);
            const block3 = this.grid.getBlock(row + 2, col);
            
            if (block1 && block2 && block3 && 
                block1.color === block2.color && block2.color === block3.color) {
              console.warn(`发现垂直3连: [${row}-${row+2}, ${col}]`);
              return true;
            }
          }
        }
      }
    } else {
      // 当 rowCount >= 3 时，检查所有可能的垂直3连
      for (let row = Math.max(0, startRow - 2); row <= startRow + rowCount - 3; row++) {
        for (let col = 0; col < this.grid.cols; col++) {
          const block1 = this.grid.getBlock(row, col);
          const block2 = this.grid.getBlock(row + 1, col);
          const block3 = this.grid.getBlock(row + 2, col);
          
          if (block1 && block2 && block3 && 
              block1.color === block2.color && block2.color === block3.color) {
            console.warn(`发现垂直3连: [${row}-${row+2}, ${col}]`);
            return true;
          }
        }
      }
    }
    
    // 检查满行
    for (let row = startRow; row < startRow + rowCount; row++) {
      let blockCount = 0;
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.getBlock(row, col)) {
          blockCount++;
        }
      }
      if (blockCount === this.grid.cols) {
        console.warn(`发现满行: ${row}`);
        return true;
      }
    }
    
    return false;
  }

  /**
   * 简单创建单行垃圾方块（原始随机方法）
   * @param {number} row - 行号
   * @param {number} density - 密度
   * @param {number} effectProbability - 特效概率
   * @param {Array} colors - 可用颜色列表
   * @returns {Array} 生成的方块列表
   * @private
   */
  _createSimpleRow(row, density, effectProbability, colors) {
    const generatedBlocks = [];
    
    // 为每列随机生成方块
    for (let col = 0; col < this.grid.cols; col++) {
      if (Math.random() < density) {
        // 随机选择颜色
        const colorKey = colors[Math.floor(Math.random() * colors.length)];
        const color = BLOCK_COLORS[colorKey];
        
        // 随机选择特效
        let effect = BLOCK_EFFECTS.NONE;
        if (Math.random() < effectProbability && this.allowedEffects.length > 0) {
          const effectName = this.allowedEffects[Math.floor(Math.random() * this.allowedEffects.length)];
          effect = BLOCK_EFFECTS[effectName.toUpperCase()] || BLOCK_EFFECTS.NONE;
        }
        
        // 创建方块
        const block = new Block(color, effect);
        
        // 放置到网格
        this.grid.placeBlock(block, row, col);
        
        generatedBlocks.push({
          row: row,
          col: col,
          color: colorKey,
          effect: effect
        });
      }
    }
    
    return generatedBlocks;
  }
  
  /**
   * 获取下次生成的剩余时间
   * @returns {number} 剩余帧数
   */
  getTimeUntilNext() {
    return Math.max(0, this.currentInterval - this.timer);
  }
  
  /**
   * 获取预警状态
   * @returns {Object} 预警信息
   */
  getWarningStatus() {
    return {
      isWarning: this.isWarning,
      timeLeft: this.isWarning ? Math.max(0, this.warningTime - this.warningTimer) : 0
    };
  }
  
  /**
   * 手动触发生成（用于测试或特殊情况）
   */
  forceGenerate() {
    if (!this.isActive || !this.grid) return null;

    this.timer = this.currentInterval;
    return this.generateGarbageRows();
  }

  /**
   * 移除所有事件监听器
   * 垃圾生成器目前没有事件系统，此方法为空实现
   * 用于与其他组件保持一致的清理接口
   */
  removeAllListeners() {
    // 垃圾生成器目前没有事件监听器，无需清理
  }

  /**
   * 智能创建单行垃圾方块，避免立即可消除的组合（原版本，保持向后兼容）
   * @param {number} row - 行号
   * @param {number} density - 密度
   * @param {number} effectProbability - 特效概率
   * @param {Array} colors - 可用颜色列表
   * @returns {Array} 生成的方块列表
   * @private
   */
  _createSingleRow(row, density, effectProbability, colors) {
    const generatedBlocks = [];
    const rowPattern = new Array(this.grid.cols).fill(null);
    
    // 第一步：确定哪些位置需要放置方块
    const positions = [];
    for (let col = 0; col < this.grid.cols; col++) {
      if (Math.random() < density) {
        positions.push(col);
      }
    }
    
    // 第二步：为每个位置分配颜色，避免创建3连
    for (const col of positions) {
      const availableColors = this._getAvailableColors(rowPattern, col, colors);
      const selectedColor = availableColors[Math.floor(Math.random() * availableColors.length)];
      rowPattern[col] = selectedColor;
    }
    
    // 第三步：检查并调整垂直匹配
    this._adjustVerticalMatches(rowPattern, row);
    
    // 第四步：创建实际的方块对象
    for (let col = 0; col < this.grid.cols; col++) {
      if (rowPattern[col]) {
        const colorKey = rowPattern[col];
        const color = BLOCK_COLORS[colorKey];
        
        // 随机选择特效
        let effect = BLOCK_EFFECTS.NONE;
        if (Math.random() < effectProbability && this.allowedEffects.length > 0) {
          const effectName = this.allowedEffects[Math.floor(Math.random() * this.allowedEffects.length)];
          effect = BLOCK_EFFECTS[effectName.toUpperCase()] || BLOCK_EFFECTS.NONE;
        }
        
        // 创建方块
        const block = new Block(color, effect);
        
        // 放置到网格
        this.grid.placeBlock(block, row, col);
        
        generatedBlocks.push({
          row: row,
          col: col,
          color: colorKey,
          effect: effect
        });
      }
    }
    
    return generatedBlocks;
  }

  /**
   * 获取指定位置可用的颜色，避免水平3连（原版本，保持向后兼容）
   * @param {Array} rowPattern - 当前行的颜色模式
   * @param {number} col - 列号
   * @param {Array} allColors - 所有可用颜色
   * @returns {Array} 可用颜色列表
   * @private
   */
  _getAvailableColors(rowPattern, col, allColors) {
    const forbiddenColors = new Set();
    
    // 检查左侧连续颜色
    if (col >= 2) {
      const leftColor1 = rowPattern[col - 1];
      const leftColor2 = rowPattern[col - 2];
      if (leftColor1 && leftColor2 && leftColor1 === leftColor2) {
        // 左侧已有2个相同颜色，当前位置不能使用相同颜色
        forbiddenColors.add(leftColor1);
      }
    }
    
    // 检查形成3连的情况
    if (col >= 1 && col < this.grid.cols - 1) {
      const leftColor = rowPattern[col - 1];
      // 检查是否会与左右形成3连（暂时无法检查右侧，但可以避免与左侧相同）
      if (leftColor) {
        // 如果左侧有颜色，检查下一个位置的可能性
        // 使用配置的避免强度：避免与相邻位置相同
        if (Math.random() < this.avoidanceStrength) {
          forbiddenColors.add(leftColor);
        }
      }
    }
    
    // 返回可用颜色
    const availableColors = allColors.filter(color => !forbiddenColors.has(color));
    
    // 如果没有可用颜色，返回所有颜色（保证总能生成）
    return availableColors.length > 0 ? availableColors : allColors;
  }

  /**
   * 调整垂直匹配，避免与上方形成3连（原版本，保持向后兼容）
   * @param {Array} rowPattern - 当前行的颜色模式
   * @param {number} row - 行号
   * @private
   */
  _adjustVerticalMatches(rowPattern, row) {
    if (row < 2) return; // 顶部两行无需检查垂直匹配
    
    for (let col = 0; col < this.grid.cols; col++) {
      if (!rowPattern[col]) continue;
      
      // 检查上方两个位置的方块颜色
      const upperBlock1 = this.grid.getBlock(row - 1, col);
      const upperBlock2 = this.grid.getBlock(row - 2, col);
      
      if (upperBlock1 && upperBlock2) {
        const upperColor1 = this._getBlockColorKey(upperBlock1);
        const upperColor2 = this._getBlockColorKey(upperBlock2);
        
        // 如果上方两个方块颜色相同，且当前方块也是相同颜色，调整当前方块颜色
        if (upperColor1 === upperColor2 && rowPattern[col] === upperColor1) {
          const colors = Object.keys(BLOCK_COLORS).slice(0, this.colorCount);
          const alternativeColors = colors.filter(color => color !== upperColor1);
          
          if (alternativeColors.length > 0) {
            rowPattern[col] = alternativeColors[Math.floor(Math.random() * alternativeColors.length)];
            console.log(`调整垃圾方块颜色避免垂直3连: [${row}, ${col}] ${upperColor1} -> ${rowPattern[col]}`);
          }
        }
      }
    }
  }
}
