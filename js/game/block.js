/**
 * 单个方块类
 * 管理方块的颜色、效果和渲染
 */
// 方块颜色定义
export const BLOCK_COLORS = {
  RED: 'red',
  BLUE: 'blue',
  GREEN: 'green',
  YELLOW: 'yellow',
  ORANGE: 'orange',
  GRAY: 'gray',
  BLACK: 'black'
};

// 方块特殊效果
export const BLOCK_EFFECTS = {
  NONE: 'none',         // 无特殊效果
  MINE: 'mine',         // 地雷效果（正面）
  FROZEN: 'frozen',     // 冰冻效果（负面）
  SHIELD: 'shield',     // 护盾效果（负面）
  MAGNET: 'magnet',     // 磁力效果（正面）
  RAINBOW: 'rainbow',   // 彩虹效果（正面）
  VIRUS: 'virus',       // 病毒效果（负面）
  CRYSTAL: 'crystal',   // 水晶效果（正面）
  ANCHOR: 'anchor'      // 锚点效果（负面）
};

// 特殊效果分类
export const EFFECT_CATEGORIES = {
  POSITIVE: 'positive',  // 正面效果（帮助玩家）
  NEGATIVE: 'negative',  // 负面效果（增加难度）
  NEUTRAL: 'neutral'     // 中性效果
};

// 效果分类映射
export const EFFECT_TYPE_MAP = {
  [BLOCK_EFFECTS.MINE]: EFFECT_CATEGORIES.POSITIVE,     // 地雷帮助清理
  [BLOCK_EFFECTS.MAGNET]: EFFECT_CATEGORIES.POSITIVE,   // 磁力吸引相同颜色
  [BLOCK_EFFECTS.RAINBOW]: EFFECT_CATEGORIES.POSITIVE,  // 彩虹可匹配任意颜色
  [BLOCK_EFFECTS.CRYSTAL]: EFFECT_CATEGORIES.POSITIVE,  // 水晶提供额外分数
  [BLOCK_EFFECTS.FROZEN]: EFFECT_CATEGORIES.NEGATIVE,   // 冰冻需要两次消除
  [BLOCK_EFFECTS.SHIELD]: EFFECT_CATEGORIES.NEGATIVE,   // 护盾免疫一次道具攻击
  [BLOCK_EFFECTS.VIRUS]: EFFECT_CATEGORIES.NEGATIVE,    // 病毒传播到相邻方块
  [BLOCK_EFFECTS.ANCHOR]: EFFECT_CATEGORIES.NEGATIVE    // 锚点阻止下落
};

// 颜色到填充色和边框色的映射
const COLOR_MAP = {
  [BLOCK_COLORS.RED]: { fill: '#FF4136', stroke: '#85144b' },
  [BLOCK_COLORS.BLUE]: { fill: '#0074D9', stroke: '#001f3f' },
  [BLOCK_COLORS.GREEN]: { fill: '#2ECC40', stroke: '#3D9970' },
  [BLOCK_COLORS.YELLOW]: { fill: '#FFDC00', stroke: '#FF851B' },
  [BLOCK_COLORS.ORANGE]: { fill: '#FF851B', stroke: '#FF4136' },
  [BLOCK_COLORS.GRAY]: { fill: '#AAAAAA', stroke: '#666666' },
  [BLOCK_COLORS.BLACK]: { fill: '#333333', stroke: '#111111' }
};

// 方块图片资源映射
const BLOCK_IMAGES = {
  [BLOCK_COLORS.RED]: 'images/blocks/tileRed.png',
  [BLOCK_COLORS.BLUE]: 'images/blocks/tileBlue.png',
  [BLOCK_COLORS.GREEN]: 'images/blocks/tileGreen.png',
  [BLOCK_COLORS.YELLOW]: 'images/blocks/tileYellow.png',
  [BLOCK_COLORS.ORANGE]: 'images/blocks/tileOrange.png',
  [BLOCK_COLORS.GRAY]: 'images/blocks/tileGray.png',
  [BLOCK_COLORS.BLACK]: 'images/blocks/tileBlack.png'
};

// 图片加载缓存
const imageCache = new Map();

/**
 * 加载方块图片
 * @param {string} color - 方块颜色
 * @returns {Promise<HTMLImageElement>} 图片对象
 */
function loadBlockImage(color) {
  const imagePath = BLOCK_IMAGES[color];
  if (!imagePath) {
    return Promise.resolve(null);
  }

  // 检查缓存
  if (imageCache.has(color)) {
    return Promise.resolve(imageCache.get(color));
  }

  return new Promise((resolve, reject) => {
    // 微信小游戏环境使用 wx.createImage()
    let img;
    if (typeof wx !== 'undefined' && wx.createImage) {
      img = wx.createImage();
    } else if (typeof Image !== 'undefined') {
      img = new Image();
    } else {
      console.warn(`当前环境不支持图片加载，颜色: ${color}`);
      resolve(null);
      return;
    }

    img.onload = () => {
      imageCache.set(color, img);
      console.log(`方块图片加载成功: ${imagePath}`);
      resolve(img);
    };
    img.onerror = (error) => {
      console.warn(`方块图片加载失败: ${imagePath}`, error);
      resolve(null); // 返回null而不是reject，这样可以回退到颜色渲染
    };
    img.src = imagePath;
  });
}

/**
 * 预加载所有方块图片
 * @returns {Promise<void>}
 */
export function preloadBlockImages() {
  const loadPromises = Object.keys(BLOCK_IMAGES).map(color => loadBlockImage(color));
  return Promise.all(loadPromises).then(() => {
    console.log('所有方块图片预加载完成');
  });
}

export default class Block {
  /**
   * 创建一个新的方块
   * @param {string} color - 方块颜色
   * @param {string} effect - 方块特殊效果
   */
  constructor(color = BLOCK_COLORS.RED, effect = BLOCK_EFFECTS.NONE) {
    this.color = color;
    this.effect = effect;
    this.row = -1;  // 初始化为-1，表示尚未放置在网格中
    this.col = -1;
    this.isAnimating = false;
    this.isDestroying = false;
    this.destroyProgress = 0;
    this.destroyDuration = 20; // 增加动画持续时间到20帧
    this.rotationAngle = 0;    // 旋转角度
    this.sparkles = [];        // 闪光粒子
    this.isFrozen = effect === BLOCK_EFFECTS.FROZEN;
    this.originalColor = null; // 保存原始颜色，用于冰冻方块
    
    // 特效动画参数
    this.effectAnimationFrame = 0;
    this.effectAnimationDuration = 30; // 特效动画持续时间
    
    // 粒子效果（用于地雷爆炸）
    this.particles = [];
    
    // 如果创建时就是冰冻方块，保存原始颜色
    if (effect === BLOCK_EFFECTS.FROZEN) {
      this.originalColor = color;
    }

    // 动画相关属性
    this.renderPosition = null; // 自定义渲染位置，用于动画
    this.animationState = null; // 动画状态
  }
  
  /**
   * 设置方块特效
   * @param {string} effect - 特效类型
   */
  setEffect(effect) {
    // 如果是从普通状态变为冰冻状态，保存原始颜色
    if (effect === BLOCK_EFFECTS.FROZEN && this.effect !== BLOCK_EFFECTS.FROZEN) {
      this.originalColor = this.color;
    }
    
    this.effect = effect;
    this.isFrozen = effect === BLOCK_EFFECTS.FROZEN;
    this.effectAnimationFrame = 0;
    
    if (effect === BLOCK_EFFECTS.MINE) {
      // 初始化爆炸粒子
      this.initExplosionParticles();
    }
  }
  
  /**
   * 初始化爆炸粒子
   */
  initExplosionParticles() {
    // 增加粒子数量和多样性
    this.particles = Array(16).fill().map(() => ({
      x: 0.5,
      y: 0.5,
      angle: Math.random() * Math.PI * 2,
      speed: 3 + Math.random() * 3,
      size: 2 + Math.random() * 3,
      life: 1,
      color: Math.random() < 0.5 ? '#FF4500' : '#FFD700' // 随机橙色或金色
    }));
  }
  
  /**
   * 更新特效动画
   */
  updateEffectAnimation() {
    if (this.effect === BLOCK_EFFECTS.NONE) return;
    
    this.effectAnimationFrame++;
    if (this.effectAnimationFrame >= this.effectAnimationDuration) {
      this.effectAnimationFrame = 0;
    }
    
    // 更新爆炸粒子
    if (this.effect === BLOCK_EFFECTS.MINE && this.particles.length > 0) {
      this.particles.forEach(particle => {
        // 增加粒子运动的随机性
        particle.x += Math.cos(particle.angle) * particle.speed * 0.15;
        particle.y += Math.sin(particle.angle) * particle.speed * 0.15;
        particle.angle += (Math.random() - 0.5) * 0.2; // 轻微改变方向
        particle.life -= 0.03; // 降低消失速度
        particle.size *= 0.95; // 逐渐缩小
      });
      
      // 移除消失的粒子
      this.particles = this.particles.filter(p => p.life > 0);
    }
  }
  
  /**
   * 应用特殊效果
   * @returns {Object} 效果的结果信息
   */
  applyEffect() {
    switch (this.effect) {
      case BLOCK_EFFECTS.MINE:
        // 地雷效果，返回需要影响的周围区域
        return {
          type: BLOCK_EFFECTS.MINE,
          range: 1  // 影响范围，1表示3x3的区域
        };

      case BLOCK_EFFECTS.FROZEN:
        // 冰冻效果，消除时变为普通方块
        this.effect = BLOCK_EFFECTS.NONE;
        // 保留originalColor，以便在需要时仍然可以引用
        return {
          type: BLOCK_EFFECTS.FROZEN,
          consumed: true
        };

      case BLOCK_EFFECTS.SHIELD:
        // 护盾效果，消耗护盾但不消除方块
        this.effect = BLOCK_EFFECTS.NONE;
        return {
          type: BLOCK_EFFECTS.SHIELD,
          consumed: true,
          blocked: true  // 表示攻击被阻挡
        };

      case BLOCK_EFFECTS.CRYSTAL:
        // 水晶效果，提供额外分数
        const multiplier = 2 + Math.floor(Math.random() * 4); // 2-5倍
        return {
          type: BLOCK_EFFECTS.CRYSTAL,
          scoreMultiplier: multiplier
        };

      default:
        return {
          type: BLOCK_EFFECTS.NONE
        };
    }
  }
  
  /**
   * 开始消除动画
   */
  startDestroyAnimation() {
    // 如果是冰冻状态，不开始消除动画
    if (this.isFrozen) {
      return false;
    }
    
    // 重置动画进度
    this.isDestroying = true;
    this.destroyAnimationFrame = 0;
    this.destroyProgress = 0;
    
    return true;
  }
  
  /**
   * 更新消除动画
   * @returns {boolean} 动画是否完成
   */
  updateDestroyAnimation() {
    if (!this.isDestroying) return false;
    
    this.destroyAnimationFrame++;
    this.destroyProgress++;
    
    // 动画持续时间与destroyDuration一致(20帧)
    if (this.destroyAnimationFrame >= this.destroyDuration) {
      this.isDestroying = false;
      this.destroyAnimationFrame = 0; // 确保动画帧被重置
      return true;
    }
    
    return false;
  }
  
  /**
   * 设置自定义渲染位置（用于动画）
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setRenderPosition(x, y) {
    this.renderPosition = { x, y };
  }

  /**
   * 清除自定义渲染位置
   */
  clearRenderPosition() {
    this.renderPosition = null;
  }

  /**
   * 设置动画状态
   * @param {string} state - 动画状态
   * @param {Object} data - 动画数据
   */
  setAnimationState(state, data = {}) {
    this.animationState = { state, ...data };
    this.isAnimating = true;
  }

  /**
   * 清除动画状态
   */
  clearAnimationState() {
    this.animationState = null;
    this.isAnimating = false;
    this.renderPosition = null;
  }

  /**
   * 渲染方块
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - 屏幕X坐标
   * @param {number} y - 屏幕Y坐标
   * @param {number} size - 方块大小
   * @param {boolean} isFalling - 是否为下落中的方块
   */
  render(ctx, x, y, size, isFalling = false) {
    // 如果有自定义渲染位置，使用它
    if (this.renderPosition) {
      x = this.renderPosition.x;
      y = this.renderPosition.y;
    }

    // 确保所有参数都是有限数值
    if (!isFinite(x) || !isFinite(y) || !isFinite(size) || size <= 0) {
      console.error('Block.render: 收到无效参数', { x, y, size });
      return; // 早期返回，避免使用无效参数
    }

    ctx.save();

    // 如果正在消除，应用动画效果
    if (this.isDestroying) {
      const progress = this.destroyAnimationFrame / 10;
      const scale = 1 - progress;
      ctx.translate(x + size / 2, y + size / 2);
      ctx.scale(scale, scale);
      ctx.translate(-(x + size / 2), -(y + size / 2));
      ctx.globalAlpha = 1 - progress;
    } else if (this.destroyAnimationFrame > 0) {
      // 如果动画帧大于0但不在消除状态，重置动画帧
      this.destroyAnimationFrame = 0;
    }

    // 尝试使用图片渲染
    const blockImage = imageCache.get(this.color);
    if (blockImage && blockImage.complete) {
      this._renderWithImage(ctx, x, y, size, blockImage, isFalling);
    } else {
      // 回退到颜色渲染
      this._renderWithColor(ctx, x, y, size, isFalling);
    }

    // 渲染特效
    if (this.effect !== BLOCK_EFFECTS.NONE) {
      this._renderEffect(ctx, x, y, size);
    }

    ctx.restore();
  }

  /**
   * 使用图片渲染方块
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @param {HTMLImageElement} image - 图片对象
   * @param {boolean} isFalling - 是否为下落中的方块
   * @private
   */
  _renderWithImage(ctx, x, y, size, image, isFalling) {
    // 冰冻方块保持原始颜色，不应用任何颜色滤镜

    // 绘制图片
    ctx.drawImage(image, x, y, size, size);
    // 如果是下落中的方块，添加发光效果
    if (isFalling) {
      const glowGradient = ctx.createRadialGradient(
        x + size/2, y + size/2, 0,
        x + size/2, y + size/2, size * 0.8
      );
      glowGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
      glowGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

      ctx.fillStyle = glowGradient;
      ctx.fillRect(x, y, size, size);
    }

    // 添加轻微的高光效果
    const highlightGradient = ctx.createLinearGradient(x, y, x + size, y + size);
    highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.2)');
    highlightGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)');
    highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

    ctx.fillStyle = highlightGradient;
    ctx.fillRect(x, y, size, size);
  }

  /**
   * 使用颜色渲染方块（回退方案）
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @param {boolean} isFalling - 是否为下落中的方块
   * @private
   */
  _renderWithColor(ctx, x, y, size, isFalling) {
    // 获取颜色信息
    const colorInfo = COLOR_MAP[this.color];

    // 保持原始颜色，不因冰冻效果而改变
    let fillStyle = colorInfo.fill;
    let strokeStyle = colorInfo.stroke;

    // 绘制方块主体
    ctx.fillStyle = fillStyle;
    ctx.strokeStyle = strokeStyle;
    ctx.lineWidth = isFalling ? 3 : 2; // 下落中的方块边框更粗

    // 绘制圆角方块
    const radius = size * (isFalling ? 0.25 : 0.2); // 下落中的方块圆角更大
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + size - radius, y);
    ctx.quadraticCurveTo(x + size, y, x + size, y + radius);
    ctx.lineTo(x + size, y + size - radius);
    ctx.quadraticCurveTo(x + size, y + size, x + size - radius, y + size);
    ctx.lineTo(x + radius, y + size);
    ctx.quadraticCurveTo(x, y + size, x, y + size - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();

    // 下落中的方块使用渐变填充
    if (isFalling) {
      const gradient = ctx.createLinearGradient(
        x + radius,
        y + radius,
        x + size - radius,
        y + size - radius
      );
      gradient.addColorStop(0, colorInfo.fill);
      gradient.addColorStop(1, colorInfo.stroke);
      ctx.fillStyle = gradient;
    }

    ctx.fill();
    ctx.stroke();

    // 添加高光效果
    const gradientSize = size - radius * 2;
    const gradient = ctx.createLinearGradient(
      x + radius,
      y + radius,
      x + radius + gradientSize,
      y + radius + gradientSize
    );

    if (isFalling) {
      // 下落中的方块高光更明显
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
      gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)');
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    } else {
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    }

    ctx.fillStyle = gradient;
    ctx.fill();
  }
  
  /**
   * 渲染特效
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderEffect(ctx, x, y, size) {
    switch (this.effect) {
      case BLOCK_EFFECTS.MINE:
        this._renderMineEffect(ctx, x, y, size);
        break;
      case BLOCK_EFFECTS.FROZEN:
        this._renderFrozenEffect(ctx, x, y, size);
        break;
      case BLOCK_EFFECTS.SHIELD:
        this._renderShieldEffect(ctx, x, y, size);
        break;
      case BLOCK_EFFECTS.RAINBOW:
        this._renderRainbowEffect(ctx, x, y, size);
        break;
      case BLOCK_EFFECTS.CRYSTAL:
        this._renderCrystalEffect(ctx, x, y, size);
        break;
    }
  }
  
  /**
   * 渲染地雷特效
   * @private
   */
  _renderMineEffect(ctx, x, y, size) {
    // 绘制地雷图标
    const centerX = x + size/2;
    const centerY = y + size/2;
    
    // 绘制地雷主体
    ctx.fillStyle = '#333333';
    ctx.beginPath();
    ctx.arc(centerX, centerY, size * 0.25, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制引信
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - size * 0.25);
    ctx.lineTo(centerX, centerY - size * 0.35);
    ctx.stroke();
    
    // 绘制闪光效果
    const animProgress = (this.effectAnimationFrame % 20) / 20;
    const glowOpacity = 0.5 + Math.sin(animProgress * Math.PI * 2) * 0.3;
    
    // 绘制外圈光晕
    const gradient = ctx.createRadialGradient(
      centerX, centerY, size * 0.2,
      centerX, centerY, size * 0.4
    );
    gradient.addColorStop(0, `rgba(255, 69, 0, ${glowOpacity})`);
    gradient.addColorStop(1, 'rgba(255, 69, 0, 0)');
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(centerX, centerY, size * 0.4, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制粒子
    this.particles.forEach(particle => {
      ctx.fillStyle = particle.color.replace('1)', `${particle.life})`);
      ctx.beginPath();
      ctx.arc(
        x + size * particle.x,
        y + size * particle.y,
        particle.size,
        0,
        Math.PI * 2
      );
      ctx.fill();
      
      // 绘制粒子拖尾
      const tailGradient = ctx.createLinearGradient(
        x + size * particle.x,
        y + size * particle.y,
        x + size * (particle.x - Math.cos(particle.angle) * 0.1),
        y + size * (particle.y - Math.sin(particle.angle) * 0.1)
      );
      tailGradient.addColorStop(0, particle.color.replace('1)', `${particle.life * 0.5})`));
      tailGradient.addColorStop(1, particle.color.replace('1)', '0)'));
      
      ctx.fillStyle = tailGradient;
      ctx.beginPath();
      ctx.arc(
        x + size * (particle.x - Math.cos(particle.angle) * 0.05),
        y + size * (particle.y - Math.sin(particle.angle) * 0.05),
        particle.size * 0.8,
        0,
        Math.PI * 2
      );
      ctx.fill();
    });
  }
  
  /**
   * 渲染冰冻特效
   * @private
   */
  _renderFrozenEffect(ctx, x, y, size) {
    // 保存上下文
    ctx.save();

    // 动画进度
    const animProgress = (this.effectAnimationFrame % 60) / 60;
    const pulseIntensity = 0.8 + Math.sin(animProgress * Math.PI * 2) * 0.2;

    // 1. 绘制简洁的冰层边框（不改变方块颜色）
    ctx.strokeStyle = `rgba(200, 220, 255, ${0.8 * pulseIntensity})`;
    ctx.lineWidth = 2;
    ctx.strokeRect(x + 1, y + 1, size - 2, size - 2);

    // 2. 绘制简化的冰晶图案 - 只保留核心十字
    const centerX = x + size/2;
    const centerY = y + size/2;
    const crossSize = size * 0.3;

    ctx.strokeStyle = `rgba(255, 255, 255, ${0.9 * pulseIntensity})`;
    ctx.lineWidth = 2;

    // 垂直线
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - crossSize);
    ctx.lineTo(centerX, centerY + crossSize);
    ctx.stroke();

    // 水平线
    ctx.beginPath();
    ctx.moveTo(centerX - crossSize, centerY);
    ctx.lineTo(centerX + crossSize, centerY);
    ctx.stroke();

    // 3. 绘制对角线（形成星形）
    const diagonalSize = size * 0.2;
    ctx.strokeStyle = `rgba(255, 255, 255, ${0.7 * pulseIntensity})`;
    ctx.lineWidth = 1;

    // 左上到右下
    ctx.beginPath();
    ctx.moveTo(centerX - diagonalSize, centerY - diagonalSize);
    ctx.lineTo(centerX + diagonalSize, centerY + diagonalSize);
    ctx.stroke();

    // 右上到左下
    ctx.beginPath();
    ctx.moveTo(centerX + diagonalSize, centerY - diagonalSize);
    ctx.lineTo(centerX - diagonalSize, centerY + diagonalSize);
    ctx.stroke();

    // 4. 绘制中心点
    ctx.fillStyle = `rgba(255, 255, 255, ${0.9 * pulseIntensity})`;
    ctx.beginPath();
    ctx.arc(centerX, centerY, 2, 0, Math.PI * 2);
    ctx.fill();

    // 5. 绘制四个角落的小冰晶
    const cornerOffset = size * 0.15;
    const cornerSize = size * 0.08;
    ctx.strokeStyle = `rgba(255, 255, 255, ${0.5 * pulseIntensity})`;
    ctx.lineWidth = 1;

    const corners = [
      { x: x + cornerOffset, y: y + cornerOffset },           // 左上
      { x: x + size - cornerOffset, y: y + cornerOffset },    // 右上
      { x: x + cornerOffset, y: y + size - cornerOffset },    // 左下
      { x: x + size - cornerOffset, y: y + size - cornerOffset } // 右下
    ];

    corners.forEach(corner => {
      // 小十字
      ctx.beginPath();
      ctx.moveTo(corner.x - cornerSize, corner.y);
      ctx.lineTo(corner.x + cornerSize, corner.y);
      ctx.moveTo(corner.x, corner.y - cornerSize);
      ctx.lineTo(corner.x, corner.y + cornerSize);
      ctx.stroke();
    });

    // 恢复上下文
    ctx.restore();
  }

  /**
   * 渲染护盾特效
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderShieldEffect(ctx, x, y, size) {
    ctx.save();

    const time = this.effectAnimationFrame * 0.15;
    const centerX = x + size / 2;
    const centerY = y + size / 2;
    const radius = size * 0.4;

    // 护盾光环效果
    const alpha = 0.7 + Math.sin(time) * 0.3;
    ctx.globalAlpha = alpha;

    // 创建护盾渐变
    const gradient = ctx.createRadialGradient(
      centerX, centerY, radius * 0.5,
      centerX, centerY, radius
    );
    gradient.addColorStop(0, 'rgba(0, 150, 255, 0.8)');
    gradient.addColorStop(1, 'rgba(0, 100, 200, 0.2)');

    // 绘制护盾圆环
    ctx.strokeStyle = gradient;
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    ctx.stroke();

    // 绘制护盾符号
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.font = `${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('🛡️', centerX, centerY);

    ctx.restore();
  }

  /**
   * 渲染彩虹特效
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderRainbowEffect(ctx, x, y, size) {
    ctx.save();

    const time = this.effectAnimationFrame * 0.2;

    // 创建彩虹渐变
    const gradient = ctx.createLinearGradient(x, y, x + size, y + size);
    const hue1 = (time * 60) % 360;
    const hue2 = (time * 60 + 120) % 360;
    const hue3 = (time * 60 + 240) % 360;

    gradient.addColorStop(0, `hsl(${hue1}, 80%, 60%)`);
    gradient.addColorStop(0.5, `hsl(${hue2}, 80%, 60%)`);
    gradient.addColorStop(1, `hsl(${hue3}, 80%, 60%)`);

    // 绘制彩虹覆盖层
    ctx.globalAlpha = 0.7;
    ctx.fillStyle = gradient;
    ctx.fillRect(x + 1, y + 1, size - 2, size - 2);

    // 绘制彩虹符号
    ctx.globalAlpha = 1;
    ctx.fillStyle = 'white';
    ctx.font = `${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('🌈', x + size / 2, y + size / 2);

    ctx.restore();
  }

  /**
   * 渲染水晶特效
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderCrystalEffect(ctx, x, y, size) {
    ctx.save();

    const time = this.effectAnimationFrame * 0.1;
    const centerX = x + size / 2;
    const centerY = y + size / 2;

    // 闪亮效果
    const sparkleAlpha = 0.5 + Math.sin(time * 2) * 0.5;
    ctx.globalAlpha = sparkleAlpha;

    // 绘制水晶光芒
    const gradient = ctx.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, size * 0.5
    );
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
    gradient.addColorStop(0.5, 'rgba(200, 200, 255, 0.6)');
    gradient.addColorStop(1, 'rgba(150, 150, 255, 0.1)');

    ctx.fillStyle = gradient;
    ctx.fillRect(x, y, size, size);

    // 绘制水晶符号
    ctx.globalAlpha = 1;
    ctx.fillStyle = 'white';
    ctx.font = `${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('💎', centerX, centerY);

    ctx.restore();
  }

  /**
   * 绘制闪光效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - 方块X坐标
   * @param {number} y - 方块Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _drawSparkles(ctx, x, y, size) {
    const progress = this.destroyProgress / this.destroyDuration;
    
    this.sparkles.forEach(sparkle => {
      const sparkleX = x + sparkle.x * size;
      const sparkleY = y + sparkle.y * size;
      
      // 绘制闪光
      ctx.beginPath();
      ctx.fillStyle = '#FFFFFF';
      const sparkleAlpha = Math.max(0, 1 - progress * 2);
      ctx.globalAlpha = sparkleAlpha;
      
      // 绘制星形闪光
      const spikes = 4;
      const outerRadius = sparkle.size * (1 - progress * 0.5);
      const innerRadius = outerRadius * 0.5;
      
      for (let i = 0; i < spikes * 2; i++) {
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const angle = (i * Math.PI) / spikes + sparkle.angle;
        const pointX = sparkleX + Math.cos(angle) * radius;
        const pointY = sparkleY + Math.sin(angle) * radius;
        
        if (i === 0) {
          ctx.moveTo(pointX, pointY);
        } else {
          ctx.lineTo(pointX, pointY);
        }
      }
      
      ctx.closePath();
      ctx.fill();
    });
    
    ctx.globalAlpha = 1;
  }
  
  /**
   * 克隆方块
   * @returns {Block} 方块的克隆
   */
  clone() {
    const clonedBlock = new Block(this.color, this.effect);
    clonedBlock.row = this.row;
    clonedBlock.col = this.col;
    return clonedBlock;
  }
}