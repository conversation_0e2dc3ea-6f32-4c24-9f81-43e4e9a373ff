/**
 * 输入处理器
 * 负责处理游戏输入事件（键盘、触摸）
 */
export default class InputHandler {
  constructor(gameController) {
    this.gameController = gameController;
    this.touchStartX = 0;
    this.touchStartY = 0;
    this.isTouchHold = false;
    this.touchHoldTimer = 0;
    
    this._initEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  _initEventHandlers() {
    // 保存事件处理函数的引用，以便后续移除
    this._keyDownHandler = (res) => {
      console.log('key', res);
      const { key } = res;
      if (!this._isGamePlayable()) return;
      
      switch (key) {
        case 'ArrowLeft':
          this.handleLeft();
          break;
        case 'ArrowRight':
          this.handleRight();
          break;
        case 'ArrowDown':
          this.handleSoftDrop(true);
          break;
        case 'ArrowUp':
        case 'w':
          this.handleRotate();
          break;
        case ' ': // 空格键
          this.handleHardDrop();
          break;
      }
    };
    
    this._keyUpHandler = (res) => {
      const { key } = res;
      
      if (key === 'ArrowDown') {
        this.handleSoftDrop(false);
      }
    };
    
    // 注册键盘事件
    if (typeof wx !== 'undefined') {
      wx.onKeyDown(this._keyDownHandler);
      wx.onKeyUp(this._keyUpHandler);
    }
  }

  /**
   * 处理左移
   */
  handleLeft() {
    const { currentTetromino, grid } = this.gameController;
    if (!currentTetromino) return;

    // 尝试左移
    const newCol = currentTetromino.col - 1;
    if (currentTetromino.canMoveTo(newCol, currentTetromino.row, grid)) {
      currentTetromino.col = newCol;
      this.gameController._resetLockTimerIfPossible();
      
      console.log(`方块左移到列 ${newCol}`);
    }
  }

  /**
   * 处理右移
   */
  handleRight() {
    const { currentTetromino, grid } = this.gameController;
    if (!currentTetromino) return;

    // 尝试右移
    const newCol = currentTetromino.col + 1;
    if (currentTetromino.canMoveTo(newCol, currentTetromino.row, grid)) {
      currentTetromino.col = newCol;
      this.gameController._resetLockTimerIfPossible();
      
      console.log(`方块右移到列 ${newCol}`);
    }
  }

  /**
   * 处理旋转
   */
  handleRotate() {
    const { currentTetromino, grid } = this.gameController;
    if (!currentTetromino) return;

    console.log('尝试旋转方块');
    
    // 保存当前状态
    const originalRotation = currentTetromino.rotation;
    const originalCol = currentTetromino.col;
    const originalRow = currentTetromino.row;
    
    // 尝试旋转
    currentTetromino.rotation = (currentTetromino.rotation + 1) % 4;
    
    // 如果旋转后位置冲突，尝试wall kick
    if (!currentTetromino.canMoveTo(currentTetromino.col, currentTetromino.row, grid)) {
      console.log('旋转位置冲突，尝试wall kick');
      
      let kickSuccess = false;
      
      // Wall kick尝试序列：左1、右1、左2、右2、上1
      const kickOffsets = [
        [-1, 0], [1, 0], [-2, 0], [2, 0], [0, -1]
      ];
      
      for (const [colOffset, rowOffset] of kickOffsets) {
        const testCol = originalCol + colOffset;
        const testRow = originalRow + rowOffset;
        
        if (currentTetromino.canMoveTo(testCol, testRow, grid)) {
          currentTetromino.col = testCol;
          currentTetromino.row = testRow;
          kickSuccess = true;
          console.log(`Wall kick成功: 偏移(${colOffset}, ${rowOffset})`);
          break;
        }
      }
      
      // 如果wall kick失败，恢复原状态
      if (!kickSuccess) {
        currentTetromino.rotation = originalRotation;
        currentTetromino.col = originalCol;
        currentTetromino.row = originalRow;
        console.log('旋转失败，恢复原状态');
        return;
      }
    }
    
    this.gameController._resetLockTimerIfPossible();
    console.log('方块旋转成功');
  }

  /**
   * 处理软下落
   */
  handleSoftDrop(isDown) {
    this.gameController.isSoftDropping = isDown;
    console.log(`软下落 ${isDown ? '开始' : '结束'}`);
  }

  /**
   * 处理硬下落
   */
  handleHardDrop() {
    const { currentTetromino, grid } = this.gameController;
    if (!currentTetromino) return;

    console.log('执行硬下落');
    
    // 计算下落距离
    let dropDistance = 0;
    let testRow = currentTetromino.row;
    
    while (currentTetromino.canMoveTo(currentTetromino.col, testRow + 1, grid)) {
      testRow++;
      dropDistance++;
    }
    
    if (dropDistance > 0) {
      currentTetromino.row = testRow;
      console.log(`硬下落 ${dropDistance} 行`);
      
      // 硬下落后立即锁定
      this.gameController.lockTimer = this.gameController.lockDelay;
    }
  }

  /**
   * 处理触摸输入
   */
  handleTouch(x, y, type) {
    if (!this._isGamePlayable()) return;

    switch (type) {
      case 'start':
        this.touchStartX = x;
        this.touchStartY = y;
        this.isTouchHold = true;
        this.touchHoldTimer = 0;
        break;

      case 'move':
        if (this.isTouchHold) {
          const deltaX = x - this.touchStartX;
          const deltaY = y - this.touchStartY;
          
          // 水平移动检测
          if (Math.abs(deltaX) > 30) {
            if (deltaX > 0) {
              this.handleRight();
            } else {
              this.handleLeft();
            }
            this.touchStartX = x; // 重置起始点，允许连续移动
          }
          
          // 垂直移动检测
          if (deltaY > 50) {
            this.handleSoftDrop(true);
          }
        }
        break;

      case 'end':
        if (this.isTouchHold) {
          const deltaX = x - this.touchStartX;
          const deltaY = y - this.touchStartY;
          const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
          
          // 如果移动距离很小，认为是轻击（旋转）
          if (distance < 20) {
            this.handleRotate();
          }
        }
        
        this.isTouchHold = false;
        this.handleSoftDrop(false);
        break;
    }
  }

  /**
   * 检查游戏是否可以接受输入
   */
  _isGamePlayable() {
    const { state, GAME_STATE } = this.gameController;
    return state === GAME_STATE.PLAYING;
  }

  /**
   * 销毁输入处理器
   */
  destroy() {
    if (typeof wx !== 'undefined') {
      wx.offKeyDown(this._keyDownHandler);
      wx.offKeyUp(this._keyUpHandler);
    }
  }
} 