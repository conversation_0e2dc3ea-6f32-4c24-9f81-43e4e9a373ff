/**
 * 俄罗斯方块组合类
 * 管理不同形状的俄罗斯方块和它们的操作
 */
import Block, { BLOCK_COLORS, BLOCK_EFFECTS } from './block.js';
import Grid, { GRID_COLS } from './grid.js';

// 俄罗斯方块形状定义 (每个数字代表方块的位置，使用相对坐标)
export const TETROMINO_SHAPES = {
  I: [[0, 0], [0, 1], [0, 2], [0, 3]], // I形
  J: [[0, 0], [1, 0], [1, 1], [1, 2]], // J形
  L: [[0, 2], [1, 0], [1, 1], [1, 2]], // L形
  O: [[0, 0], [0, 1], [1, 0], [1, 1]], // O形
  S: [[0, 1], [0, 2], [1, 0], [1, 1]], // S形
  T: [[0, 1], [1, 0], [1, 1], [1, 2]], // T形
  Z: [[0, 0], [0, 1], [1, 1], [1, 2]]  // Z形
};

// 定义不同形状的旋转中心
export const ROTATION_CENTERS = {
  I: [0.5, 1.5], // I型方块的旋转中心在中间点(0.5, 1.5)
  J: [1, 1],     // J型方块的旋转中心
  L: [1, 1],     // L型方块的旋转中心
  O: [0.5, 0.5], // O型方块的旋转中心（实际上O型不需要旋转）
  S: [1, 1],     // S型方块的旋转中心
  T: [1, 1],     // T型方块的旋转中心
  Z: [1, 1]      // Z型方块的旋转中心
};

export default class Tetromino {
  /**
   * 创建一个新的俄罗斯方块组合
   * @param {string} shape - 方块形状，从TETROMINO_SHAPES中选择
   * @param {Object} options - 配置选项
   */
  constructor(shape, options = {}) {
    // 设置默认属性
    this.shape = shape;
    this.blocks = [];

    // 安全的列位置计算
    const safeGridCols = GRID_COLS || 10; // 默认10列
    const startCol = Math.floor(safeGridCols / 2) - 2;

    // 检查计算结果
    if (isNaN(startCol)) {
      console.error(`❌ Tetromino位置计算出现NaN: GRID_COLS=${GRID_COLS}, safeGridCols=${safeGridCols}, startCol=${startCol}`);
      this.position = { row: 0, col: 3 }; // 使用安全默认值
    } else {
      this.position = { row: 0, col: startCol };
    }

    this.rotation = 0; // 0, 1, 2, 3 分别代表 0, 90, 180, 270 度
    this.isLocked = false; // 是否已经锁定到网格

    console.log(`🎲 Tetromino创建: shape=${shape}, position=(${this.position.row}, ${this.position.col})`);

    // 根据形状创建方块
    this._createBlocks(options);
  }
  
  /**
   * 根据形状和配置创建组成这个方块组合的各个方块
   * @private
   */
  _createBlocks(options = {}) {
    const { 
      colorDistribution = 'single', 
      effectProbability = 0.1,
      colorCount = 7,  // 新增：可用颜色数量限制
      allowedEffects = ['frozen', 'mine']  // 新增：允许的效果类型
    } = options;
    
    // 获取形状坐标
    const shape = TETROMINO_SHAPES[this.shape] || TETROMINO_SHAPES.I;
    
    // 根据颜色数量限制获取可用颜色
    const allColorKeys = Object.keys(BLOCK_COLORS);
    const availableColorKeys = allColorKeys.slice(0, Math.min(colorCount, allColorKeys.length));
    
    console.log(`生成方块，可用颜色数量: ${colorCount}, 颜色列表: ${availableColorKeys}`);
    
    // 初始颜色
    let colors;
    if (colorDistribution === 'single') {
      // 单一颜色
      const randomColor = BLOCK_COLORS[availableColorKeys[Math.floor(Math.random() * availableColorKeys.length)]];
      colors = shape.map(() => randomColor);
    } else if (colorDistribution === 'gradient') {
      // 渐变色 (相邻的颜色)
      const startColorIndex = Math.floor(Math.random() * availableColorKeys.length);
      colors = shape.map((_, index) => {
        const colorIndex = (startColorIndex + index) % availableColorKeys.length;
        return BLOCK_COLORS[availableColorKeys[colorIndex]];
      });
    } else {
      // 随机颜色
      colors = shape.map(() => {
        const randomIndex = Math.floor(Math.random() * availableColorKeys.length);
        return BLOCK_COLORS[availableColorKeys[randomIndex]];
      });
    }
    
    // 创建方块
    this.blocks = shape.map((position, index) => {
      const block = new Block(colors[index]);
      
      // 根据允许的效果类型和概率添加特殊效果
      if (Math.random() < effectProbability && allowedEffects.length > 0) {
        // 只从允许的效果中选择
        const availableEffects = allowedEffects.filter(effectName => {
          return BLOCK_EFFECTS[effectName.toUpperCase()] !== undefined;
        });
        
        if (availableEffects.length > 0) {
          const randomEffectName = availableEffects[Math.floor(Math.random() * availableEffects.length)];
          const randomEffect = BLOCK_EFFECTS[randomEffectName.toUpperCase()];
          
          console.log(`方块添加效果: ${randomEffectName}`);
          
          // 如果是冰冻效果，先保存原始颜色
          if (randomEffect === BLOCK_EFFECTS.FROZEN) {
            block.originalColor = block.color;
          }
          
          block.effect = randomEffect;
          block.isFrozen = (randomEffect === BLOCK_EFFECTS.FROZEN);
        }
      }
      
      return block;
    });
  }
  
  /**
   * 获取方块组合中每个方块在网格中的位置
   * @returns {Array<{row: number, col: number, block: Block}>} 方块位置数组
   */
  getBlockPositions() {
    const { row, col } = this.position;

    // 检查位置是否有效
    if (isNaN(row) || isNaN(col)) {
      console.error(`❌ Tetromino位置包含NaN: row=${row}, col=${col}`);
      return []; // 返回空数组避免渲染错误
    }

    const shape = TETROMINO_SHAPES[this.shape];
    if (!shape) {
      console.error(`❌ 未知的Tetromino形状: ${this.shape}`);
      return [];
    }

    return shape.map((position, index) => {
      const [posRow, posCol] = this._getRotatedPosition(position[0], position[1]);

      // 检查旋转后的位置
      if (isNaN(posRow) || isNaN(posCol)) {
        console.error(`❌ 旋转位置计算出现NaN: posRow=${posRow}, posCol=${posCol}`);
        return {
          row: 0,
          col: 0,
          block: this.blocks[index]
        };
      }

      const finalRow = row + posRow;
      const finalCol = col + posCol;

      // 检查最终位置
      if (isNaN(finalRow) || isNaN(finalCol)) {
        console.error(`❌ 最终位置计算出现NaN: finalRow=${finalRow}, finalCol=${finalCol}`);
        return {
          row: 0,
          col: 0,
          block: this.blocks[index]
        };
      }

      return {
        row: finalRow,
        col: finalCol,
        block: this.blocks[index]
      };
    });
  }
  
  /**
   * 根据当前旋转状态获取旋转后的位置
   * @param {number} row - 原始行位置
   * @param {number} col - 原始列位置
   * @returns {[number, number]} 旋转后的位置
   * @private
   */
  _getRotatedPosition(row, col) {
    // 获取当前形状的旋转中心
    const center = ROTATION_CENTERS[this.shape] || [0, 0];
    
    // 计算相对于旋转中心的坐标
    const relRow = row - center[0];
    const relCol = col - center[1];
    
    // 根据旋转角度计算新坐标
    let newRow, newCol;
    switch (this.rotation) {
      case 0: // 0度
        newRow = relRow;
        newCol = relCol;
        break;
      case 1: // 90度
        newRow = relCol;
        newCol = -relRow;
        break;
      case 2: // 180度
        newRow = -relRow;
        newCol = -relCol;
        break;
      case 3: // 270度
        newRow = -relCol;
        newCol = relRow;
        break;
      default:
        newRow = relRow;
        newCol = relCol;
    }
    
    // 转换回绝对坐标并返回
    return [newRow + center[0], newCol + center[1]];
  }
  
  /**
   * 向左移动
   * @returns {{row: number, col: number}} 新的位置
   */
  moveLeft() {
    if (isNaN(this.position.col)) {
      console.error(`❌ moveLeft: position.col是NaN，重置为3`);
      this.position.col = 3;
    }
    this.position.col -= 1;

    // 检查结果
    if (isNaN(this.position.col)) {
      console.error(`❌ moveLeft: 移动后position.col变成NaN，重置为2`);
      this.position.col = 2;
    }

    return this.position;
  }

  /**
   * 向右移动
   * @returns {{row: number, col: number}} 新的位置
   */
  moveRight() {
    if (isNaN(this.position.col)) {
      console.error(`❌ moveRight: position.col是NaN，重置为3`);
      this.position.col = 3;
    }
    this.position.col += 1;

    // 检查结果
    if (isNaN(this.position.col)) {
      console.error(`❌ moveRight: 移动后position.col变成NaN，重置为4`);
      this.position.col = 4;
    }

    return this.position;
  }

  /**
   * 向下移动
   * @returns {{row: number, col: number}} 新的位置
   */
  moveDown() {
    if (isNaN(this.position.row)) {
      console.error(`❌ moveDown: position.row是NaN，重置为0`);
      this.position.row = 0;
    }
    this.position.row += 1;

    // 检查结果
    if (isNaN(this.position.row)) {
      console.error(`❌ moveDown: 移动后position.row变成NaN，重置为1`);
      this.position.row = 1;
    }

    return this.position;
  }

  /**
   * 快速下落
   * @param {number} rows - 下落的行数
   * @returns {{row: number, col: number}} 新的位置
   */
  hardDrop(rows) {
    if (isNaN(this.position.row) || isNaN(rows)) {
      console.error(`❌ hardDrop: 参数包含NaN，row=${this.position.row}, rows=${rows}`);
      if (isNaN(this.position.row)) this.position.row = 0;
      if (isNaN(rows)) rows = 1;
    }
    this.position.row += rows;

    // 检查结果
    if (isNaN(this.position.row)) {
      console.error(`❌ hardDrop: 移动后position.row变成NaN，重置为${rows + 1}`);
      this.position.row = rows + 1;
    }

    return this.position;
  }
  
  /**
   * 旋转
   * @param {boolean} clockwise - 是否顺时针旋转
   * @returns {number} 新的旋转状态
   */
  rotate(clockwise = true) {
    if (clockwise) {
      this.rotation = (this.rotation + 1) % 4;
    } else {
      this.rotation = (this.rotation + 3) % 4; // +3相当于-1但始终保持为正数
    }
    return this.rotation;
  }

  /**
   * 逆时针旋转
   * @returns {number} 新的旋转状态
   */
  rotateCounterClockwise() {
    return this.rotate(false);
  }

  /**
   * 检查是否可以旋转到指定状态
   * @param {Grid} grid - 游戏网格
   * @param {boolean} clockwise - 是否顺时针旋转
   * @returns {boolean} 是否可以旋转
   */
  canRotate(grid, clockwise = true) {
    // 保存当前状态
    const originalRotation = this.rotation;

    // 尝试旋转
    if (clockwise) {
      this.rotation = (this.rotation + 1) % 4;
    } else {
      this.rotation = (this.rotation + 3) % 4;
    }

    // 检查新状态是否有效
    const isValid = this.isValidPosition(grid);

    // 恢复原状态
    this.rotation = originalRotation;

    return isValid;
  }
  
  /**
   * 锁定方块到网格
   */
  lock() {
    this.isLocked = true;
  }
  
  /**
   * 检查方块组合是否与网格中的位置有效（不超出边界且不与已有方块重叠）
   * @param {Grid} grid - 游戏网格
   * @returns {boolean} 如果位置有效则返回true
   */
  isValidPosition(grid) {
    const blockPositions = this.getBlockPositions();
    
    return blockPositions.every(({ row, col }) => {
      // 检查是否超出网格边界
      if (row < 0 || row >= grid.rows || col < 0 || col >= grid.cols) {
        return false;
      }
      
      // 检查该位置是否已有方块
      return grid.isValidPosition(row, col);
    });
  }
  
  /**
   * 检查是否可以向下移动
   * @param {Grid} grid - 游戏网格
   * @returns {boolean} 如果可以向下移动则返回true
   */
  canMoveDown(grid) {
    // 保存当前位置
    const originalPosition = { ...this.position };
    
    // 尝试向下移动
    this.moveDown();
    
    // 检查新位置是否有效
    const isValid = this.isValidPosition(grid);
    
    // 恢复原始位置
    this.position = originalPosition;
    
    return isValid;
  }
  
  /**
   * 将方块组合放置到网格上
   * @param {Grid} grid - 游戏网格
   * @returns {boolean} 如果成功放置则返回true
   */
  placeOnGrid(grid) {
    const blockPositions = this.getBlockPositions();
    let allPlaced = true;
    
    // 尝试放置每个方块
    blockPositions.forEach(({ row, col, block }) => {
      if (!grid.placeBlock(block, row, col)) {
        allPlaced = false;
      }
    });
    
    if (allPlaced) {
      this.lock();
    }
    
    return allPlaced;
  }
  
  /**
   * 渲染方块组合
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Grid} grid - 游戏网格
   */
  render(ctx, grid) {
    const positions = this.getBlockPositions();
    
    positions.forEach(({ row, col, block }) => {
      const { x, y } = grid.gridToScreen(row, col);
      block.render(ctx, x, y, grid.blockSize, true); // 传入true表示正在下落
    });
  }
  
  /**
   * 获取方块组合的预览位置（最终会下落到的位置）
   * @param {Grid} grid - 游戏网格
   * @returns {Array<{row: number, col: number, block: Block}>} 预览位置
   */
  getGhostPosition(grid) {
    // 保存原始位置
    const originalPosition = { ...this.position };
    let dropDistance = 0;
    
    // 找到可以下落的最大距离
    while (this.canMoveDown(grid)) {
      this.moveDown();
      dropDistance++;
    }
    
    // 获取最终位置的方块
    const ghostPosition = this.getBlockPositions();
    
    // 恢复原始位置
    this.position = originalPosition;
    
    return { ghostPosition, dropDistance };
  }
  
  /**
   * 渲染方块组合的预览（显示方块最终会落到哪里）
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Grid} grid - 游戏网格
   */
  renderGhost(ctx, grid) {
    const { ghostPosition } = this.getGhostPosition(grid);

    // 🎯 修复：使用save/restore确保透明度设置不被方块内部重置
    ctx.save();
    ctx.globalAlpha = 0.4; // 使用40%透明度，更容易看到

    ghostPosition.forEach(({ row, col, block }) => {
      const { x, y } = grid.gridToScreen(row, col);

      // 🎯 修复：为虚影渲染创建简化的方块轮廓，避免复杂的特效
      this._renderGhostBlock(ctx, block, x, y, grid.blockSize);
    });

    ctx.restore();
  }

  /**
   * 🎯 新增：渲染虚影方块（简化版本，确保透明度生效）
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Block} block - 方块对象
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderGhostBlock(ctx, block, x, y, size) {
    // 直接绘制简化的方块轮廓，不调用block.render()避免save/restore冲突
    const COLOR_MAP = {
      'red': { fill: '#FF4136', stroke: '#85144b' },
      'blue': { fill: '#0074D9', stroke: '#001f3f' },
      'green': { fill: '#2ECC40', stroke: '#3D9970' },
      'yellow': { fill: '#FFDC00', stroke: '#FF851B' },
      'orange': { fill: '#FF851B', stroke: '#FF4136' },
      'gray': { fill: '#AAAAAA', stroke: '#666666' },
      'black': { fill: '#333333', stroke: '#111111' }
    };

    const colorInfo = COLOR_MAP[block.color] || COLOR_MAP['red'];

    // 绘制虚线边框的方块轮廓
    ctx.setLineDash([4, 4]);
    ctx.strokeStyle = colorInfo.stroke;
    ctx.lineWidth = 2;
    ctx.strokeRect(x + 1, y + 1, size - 2, size - 2);
    ctx.setLineDash([]); // 重置虚线

    // 绘制半透明填充
    ctx.fillStyle = colorInfo.fill;
    ctx.fillRect(x + 2, y + 2, size - 4, size - 4);
  }
} 