import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

/**
 * 连击管理器
 * 职责：专门管理游戏中的连击系统，包括连击检测、奖励计算、连击显示和连击链
 */
export class ComboManager extends Emitter {
  constructor(options = {}) {
    super();
    this.actionHistory = [];
    this.lastActionTime = null;

    
    this.options = {
      minComboLength: 2,           // 最小连击长度
      comboTimeWindow: 3000,       // 连击时间窗口（毫秒）
      baseComboScore: 50,          // 基础连击分数
      comboMultiplier: 1.5,        // 连击倍数
      maxComboMultiplier: 5.0,     // 最大连击倍数
      comboDecayRate: 0.1,         // 连击衰减率
      perfectComboBonus: 2.0,      // 完美连击奖励倍数
      comboBreakPenalty: 0.5,      // 连击中断惩罚
      enableComboChains: true,     // 是否启用连击链
      enableComboEffects: true,    // 是否启用连击特效
      ...options
    };
    
    // 连击状态
    this.comboState = {
      currentCombo: 0,
      maxCombo: 0,
      sessionMaxCombo: 0,
      totalCombos: 0,
      perfectCombos: 0,
      brokenCombos: 0,
      comboScore: 0,
      isActive: false,
      lastComboTime: 0,
      comboStartTime: 0
    };
    
    // 连击历史
    this.comboHistory = {
      recentCombos: [],
      maxHistorySize: 50,
      comboChains: [],
      bestComboChain: null,
      comboPatterns: new Map()
    };
    
    // 连击类型定义
    this.comboTypes = {
      'SINGLE': { name: '单消连击', multiplier: 1.0, minLength: 2 },
      'DOUBLE': { name: '双消连击', multiplier: 1.2, minLength: 2 },
      'TRIPLE': { name: '三消连击', multiplier: 1.5, minLength: 2 },
      'TETRIS': { name: 'Tetris连击', multiplier: 2.0, minLength: 2 },
      'TSPIN': { name: 'T-Spin连击', multiplier: 2.5, minLength: 2 },
      'PERFECT': { name: '完美连击', multiplier: 3.0, minLength: 3 },
      'CHAIN': { name: '连锁连击', multiplier: 4.0, minLength: 4 }
    };
    
    // 连击链系统
    this.comboChain = {
      currentChain: [],
      chainScore: 0,
      chainLength: 0,
      chainMultiplier: 1.0,
      isChainActive: false,
      chainStartTime: 0,
      maxChainLength: 0
    };
    
    // 连击效果系统
    this.comboEffects = {
      activeEffects: new Map(),
      effectQueue: [],
      isProcessing: false,
      effectDuration: 2000,
      effectIntensity: 1.0
    };
    
    // 连击统计
    this.comboStats = {
      totalComboScore: 0,
      averageComboLength: 0,
      longestCombo: 0,
      comboEfficiency: 0,
      comboTypeCounts: new Map(),
      comboPerGame: 0
    };
    
    // 连击奖励系统
    this.comboRewards = {
      milestoneRewards: new Map([
        [5, { score: 500, effect: 'speed_boost' }],
        [10, { score: 1000, effect: 'double_score' }],
        [15, { score: 2000, effect: 'triple_score' }],
        [20, { score: 5000, effect: 'perfect_mode' }]
      ]),
      achievedMilestones: new Set(),
      currentRewardMultiplier: 1.0
    };
    
    console.log('🔥 连击管理器已初始化', this.options);
  }

  /**
   * 获取当前连击数
   * @returns {number} 当前连击数
   */
  getCurrentCombo() {
    return this.comboState.currentCombo;
  }

  /**
   * 获取最大连击数
   * @returns {number} 最大连击数
   */
  getMaxCombo() {
    return this.comboState.maxCombo;
  }

  /**
   * 开始连击
   * @param {Object} triggerData - 触发数据
   * @returns {boolean} 是否成功开始连击
   */
  startCombo(triggerData = {}) {
    const now = Date.now();
    
    // 检查是否在时间窗口内
    const isInTimeWindow = (now - this.comboState.lastComboTime) <= this.options.comboTimeWindow;
    
    if (this.comboState.isActive && isInTimeWindow) {
      // 继续现有连击
      return this.continueCombo(triggerData);
    } else {
      // 开始新连击
      this.comboState.currentCombo = 1;
      this.comboState.isActive = true;
      this.comboState.comboStartTime = now;
      this.comboState.lastComboTime = now;
      
      console.log('🔥 连击开始');
      
      this.emit('combo:started', {
        startTime: now,
        triggerData
      });
      
      return true;
    }
  }

  /**
   * 继续连击
   * @param {Object} actionData - 行为数据
   * @returns {Object} 连击结果
   */
  continueCombo(actionData = {}) {
    if (!this.comboState.isActive) {
      return this.startCombo(actionData);
    }
    
    const now = Date.now();
    const timeSinceLastCombo = now - this.comboState.lastComboTime;
    
    // 检查时间窗口
    if (timeSinceLastCombo > this.options.comboTimeWindow) {
      this.endCombo('timeout');
      return this.startCombo(actionData);
    }
    
    // 增加连击数
    this.comboState.currentCombo++;
    this.comboState.lastComboTime = now;
    
    // 更新最大连击记录
    if (this.comboState.currentCombo > this.comboState.maxCombo) {
      this.comboState.maxCombo = this.comboState.currentCombo;
    }
    if (this.comboState.currentCombo > this.comboState.sessionMaxCombo) {
      this.comboState.sessionMaxCombo = this.comboState.currentCombo;
    }
    
    // 计算连击分数
    const comboScore = this.calculateComboScore(actionData);
    this.comboState.comboScore += comboScore;
    
    // 检查连击类型
    const comboType = this.determineComboType(actionData);
    
    // 检查里程碑奖励
    this._checkComboMilestones();
    
    // 更新连击链
    if (this.options.enableComboChains) {
      this._updateComboChain(actionData, comboScore);
    }
    
    // 触发连击特效
    if (this.options.enableComboEffects) {
      this._triggerComboEffect(comboType, this.comboState.currentCombo);
    }
    
    const result = {
      comboCount: this.comboState.currentCombo,
      comboScore,
      comboType,
      totalComboScore: this.comboState.comboScore,
      isNewRecord: this.comboState.currentCombo === this.comboState.maxCombo
    };
    
    console.log(`🔥 连击继续: ${this.comboState.currentCombo} combo, +${comboScore}分 (${comboType.name})`);
    
    this.emit('combo:continued', result);
    
    return result;
  }

  /**
   * 结束连击
   * @param {string} reason - 结束原因
   * @returns {Object} 连击总结
   */
  endCombo(reason = 'manual') {
    if (!this.comboState.isActive) {
      return null;
    }
    
    const comboSummary = {
      finalCombo: this.comboState.currentCombo,
      totalScore: this.comboState.comboScore,
      duration: Date.now() - this.comboState.comboStartTime,
      reason,
      isRecord: this.comboState.currentCombo === this.comboState.maxCombo,
      isPerfect: this._isPerfectCombo(),
      efficiency: this._calculateComboEfficiency()
    };
    
    // 记录连击历史
    this._recordComboHistory(comboSummary);
    
    // 更新统计信息
    this._updateComboStats(comboSummary);
    
    // 重置连击状态
    this.comboState.isActive = false;
    this.comboState.currentCombo = 0;
    this.comboState.comboScore = 0;
    this.comboState.totalCombos++;
    
    // 结束连击链
    if (this.comboChain.isChainActive) {
      this._endComboChain();
    }
    
    // 清除连击奖励倍数
    this.comboRewards.currentRewardMultiplier = 1.0;
    
    console.log(`🔥 连击结束: ${comboSummary.finalCombo} combo, 总分: ${comboSummary.totalScore}, 原因: ${reason}`);
    
    this.emit('combo:ended', comboSummary);
    
    return comboSummary;
  }

  /**
   * 中断连击
   * @param {string} reason - 中断原因
   * @returns {Object} 中断结果
   */
  breakCombo(reason = 'failed_action') {
    if (!this.comboState.isActive) {
      return null;
    }
    
    // 应用中断惩罚
    const penalty = Math.floor(this.comboState.comboScore * this.options.comboBreakPenalty);
    this.comboState.comboScore = Math.max(0, this.comboState.comboScore - penalty);
    this.comboState.brokenCombos++;
    
    const breakResult = this.endCombo(reason);
    breakResult.penalty = penalty;
    breakResult.wasBroken = true;
    
    console.log(`💥 连击中断: ${reason}, 惩罚: ${penalty}分`);
    
    this.emit('combo:broken', breakResult);
    
    return breakResult;
  }

  /**
   * 计算连击分数
   * @param {Object} actionData - 行为数据
   * @returns {number} 连击分数
   */
  calculateComboScore(actionData = {}) {
    let baseScore = this.options.baseComboScore;
    
    // 根据行为类型调整基础分数
    if (actionData.linesCleared) {
      baseScore *= actionData.linesCleared;
    }
    
    // 连击倍数计算
    const comboMultiplier = Math.min(
      this.options.maxComboMultiplier,
      Math.pow(this.options.comboMultiplier, this.comboState.currentCombo - 1)
    );
    
    // 行为类型奖励
    const actionMultiplier = this._getActionMultiplier(actionData);
    
    // 连击链奖励
    const chainMultiplier = this.comboChain.isChainActive ? this.comboChain.chainMultiplier : 1.0;
    
    // 当前奖励倍数
    const rewardMultiplier = this.comboRewards.currentRewardMultiplier;
    
    // 计算最终分数
    let finalScore = Math.floor(baseScore * comboMultiplier * actionMultiplier * chainMultiplier * rewardMultiplier);
    
    // 完美连击奖励
    if (this._isPerfectCombo()) {
      finalScore = Math.floor(finalScore * this.options.perfectComboBonus);
    }
    
    return finalScore;
  }

  /**
   * 确定连击类型
   * @param {Object} actionData - 行为数据
   * @returns {Object} 连击类型
   */
  determineComboType(actionData = {}) {
    // 根据行为数据确定连击类型
    if (actionData.isTSpin) {
      return this.comboTypes.TSPIN;
    } else if (actionData.linesCleared === 4) {
      return this.comboTypes.TETRIS;
    } else if (actionData.linesCleared === 3) {
      return this.comboTypes.TRIPLE;
    } else if (actionData.linesCleared === 2) {
      return this.comboTypes.DOUBLE;
    } else if (actionData.isPerfectClear) {
      return this.comboTypes.PERFECT;
    } else if (this.comboChain.isChainActive && this.comboChain.chainLength >= 4) {
      return this.comboTypes.CHAIN;
    } else {
      return this.comboTypes.SINGLE;
    }
  }

  /**
   * 获取连击显示数据
   * @returns {Object} 连击显示数据
   */
  getComboDisplayData() {
    return {
      currentCombo: this.comboState.currentCombo,
      comboScore: this.comboState.comboScore,
      isActive: this.comboState.isActive,
      maxCombo: this.comboState.maxCombo,
      comboMultiplier: this._getCurrentComboMultiplier(),
      timeRemaining: this._getComboTimeRemaining(),
      comboType: this._getCurrentComboType(),
      chainInfo: this.comboChain.isChainActive ? {
        chainLength: this.comboChain.chainLength,
        chainScore: this.comboChain.chainScore,
        chainMultiplier: this.comboChain.chainMultiplier
      } : null,
      effects: Array.from(this.comboEffects.activeEffects.keys())
    };
  }

  /**
   * 获取连击统计
   * @returns {Object} 连击统计
   */
  getComboStats() {
    return {
      state: { ...this.comboState },
      chain: { ...this.comboChain },
      stats: { ...this.comboStats },
      rewards: {
        achievedMilestones: Array.from(this.comboRewards.achievedMilestones),
        currentMultiplier: this.comboRewards.currentRewardMultiplier
      },
      history: {
        recentCombos: this.comboHistory.recentCombos.slice(-10),
        bestComboChain: this.comboHistory.bestComboChain
      }
    };
  }

  /**
   * 重置连击系统
   */
  reset() {
    console.log('🔄 重置连击管理器');
    
    // 重置连击状态
    this.comboState = {
      currentCombo: 0,
      maxCombo: 0,
      sessionMaxCombo: 0,
      totalCombos: 0,
      perfectCombos: 0,
      brokenCombos: 0,
      comboScore: 0,
      isActive: false,
      lastComboTime: 0,
      comboStartTime: 0
    };
    
    // 重置连击链
    this.comboChain = {
      currentChain: [],
      chainScore: 0,
      chainLength: 0,
      chainMultiplier: 1.0,
      isChainActive: false,
      chainStartTime: 0,
      maxChainLength: 0
    };
    
    // 重置连击效果
    this.comboEffects.activeEffects.clear();
    this.comboEffects.effectQueue = [];
    this.comboEffects.isProcessing = false;
    
    // 重置奖励
    this.comboRewards.achievedMilestones.clear();
    this.comboRewards.currentRewardMultiplier = 1.0;
    
    // 清空历史记录
    this.comboHistory.recentCombos = [];
    this.comboHistory.comboChains = [];
    
    this.emit('combo:reset');
  }

  // =================== 私有方法 ===================

  /**
   * 获取行为倍数
   * @param {Object} actionData - 行为数据
   * @returns {number} 行为倍数
   * @private
   */
  _getActionMultiplier(actionData) {
    const comboType = this.determineComboType(actionData);
    return comboType.multiplier;
  }

  /**
   * 检查连击里程碑
   * @private
   */
  _checkComboMilestones() {
    const currentCombo = this.comboState.currentCombo;
    
    for (const [milestone, reward] of this.comboRewards.milestoneRewards) {
      if (currentCombo >= milestone && !this.comboRewards.achievedMilestones.has(milestone)) {
        this.comboRewards.achievedMilestones.add(milestone);
        this._applyMilestoneReward(milestone, reward);
        
        this.emit('combo:milestone', {
          milestone,
          reward,
          currentCombo
        });
        
        console.log(`🏆 连击里程碑: ${milestone} combo达成, 奖励: ${reward.score}分`);
      }
    }
  }

  /**
   * 应用里程碑奖励
   * @param {number} milestone - 里程碑
   * @param {Object} reward - 奖励
   * @private
   */
  _applyMilestoneReward(milestone, reward) {
    if (reward.score) {
      this.comboState.comboScore += reward.score;
    }
    
    if (reward.effect) {
      this._activateComboEffect(reward.effect, 10000); // 10秒持续时间
    }
    
    // 增加奖励倍数
    this.comboRewards.currentRewardMultiplier += 0.2;
  }

  /**
   * 更新连击链
   * @param {Object} actionData - 行为数据
   * @param {number} comboScore - 连击分数
   * @private
   */
  _updateComboChain(actionData, comboScore) {
    if (!this.comboChain.isChainActive) {
      this.comboChain.isChainActive = true;
      this.comboChain.chainStartTime = Date.now();
    }
    
    this.comboChain.currentChain.push({
      combo: this.comboState.currentCombo,
      actionData,
      score: comboScore,
      timestamp: Date.now()
    });
    
    this.comboChain.chainLength = this.comboChain.currentChain.length;
    this.comboChain.chainScore += comboScore;
    
    // 更新链倍数
    this.comboChain.chainMultiplier = 1.0 + (this.comboChain.chainLength * 0.1);
    
    // 更新最大链长度
    if (this.comboChain.chainLength > this.comboChain.maxChainLength) {
      this.comboChain.maxChainLength = this.comboChain.chainLength;
    }
  }

  /**
   * 结束连击链
   * @private
   */
  _endComboChain() {
    if (!this.comboChain.isChainActive) return;
    
    const chainSummary = {
      chainLength: this.comboChain.chainLength,
      chainScore: this.comboChain.chainScore,
      duration: Date.now() - this.comboChain.chainStartTime,
      chain: [...this.comboChain.currentChain]
    };
    
    // 记录最佳连击链
    if (!this.comboHistory.bestComboChain || 
        chainSummary.chainLength > this.comboHistory.bestComboChain.chainLength) {
      this.comboHistory.bestComboChain = chainSummary;
    }
    
    this.comboHistory.comboChains.push(chainSummary);
    
    // 重置连击链状态
    this.comboChain.isChainActive = false;
    this.comboChain.currentChain = [];
    this.comboChain.chainScore = 0;
    this.comboChain.chainLength = 0;
    this.comboChain.chainMultiplier = 1.0;
    
    this.emit('combo:chainEnded', chainSummary);
  }

  /**
   * 触发连击特效
   * @param {Object} comboType - 连击类型
   * @param {number} comboCount - 连击数
   * @private
   */
  _triggerComboEffect(comboType, comboCount) {
    const effectId = `${comboType.name}_${comboCount}`;
    
    this.comboEffects.activeEffects.set(effectId, {
      type: comboType.name,
      intensity: Math.min(2.0, comboCount / 10),
      startTime: Date.now(),
      duration: this.comboEffects.effectDuration
    });
    
    // 自动清除特效
    setTimeout(() => {
      this.comboEffects.activeEffects.delete(effectId);
    }, this.comboEffects.effectDuration);
    
    this.emit('combo:effect', {
      effectId,
      type: comboType.name,
      comboCount,
      intensity: this.comboEffects.activeEffects.get(effectId).intensity
    });
  }

  /**
   * 激活连击效果
   * @param {string} effectType - 效果类型
   * @param {number} duration - 持续时间
   * @private
   */
  _activateComboEffect(effectType, duration) {
    const effectId = `reward_${effectType}_${Date.now()}`;
    
    this.comboEffects.activeEffects.set(effectId, {
      type: effectType,
      startTime: Date.now(),
      duration
    });
    
    setTimeout(() => {
      this.comboEffects.activeEffects.delete(effectId);
    }, duration);
  }

  /**
   * 记录连击历史
   * @param {Object} comboSummary - 连击总结
   * @private
   */
  _recordComboHistory(comboSummary) {
    this.comboHistory.recentCombos.push(comboSummary);
    
    // 限制历史记录大小
    if (this.comboHistory.recentCombos.length > this.comboHistory.maxHistorySize) {
      this.comboHistory.recentCombos.shift();
    }
  }

  /**
   * 更新连击统计
   * @param {Object} comboSummary - 连击总结
   * @private
   */
  _updateComboStats(comboSummary) {
    this.comboStats.totalComboScore += comboSummary.totalScore;
    
    if (comboSummary.finalCombo > this.comboStats.longestCombo) {
      this.comboStats.longestCombo = comboSummary.finalCombo;
    }
    
    // 更新平均连击长度
    const totalCombos = this.comboState.totalCombos;
    this.comboStats.averageComboLength = 
      (this.comboStats.averageComboLength * (totalCombos - 1) + comboSummary.finalCombo) / totalCombos;
    
    // 更新连击效率
    this.comboStats.comboEfficiency = comboSummary.efficiency;
  }

  /**
   * 检查是否为完美连击
   * @returns {boolean} 是否完美连击
   * @private
   */
  _isPerfectCombo() {
    // 完美连击的条件：无中断、高效率、长时间
    return this.comboState.currentCombo >= 10 && 
           this._calculateComboEfficiency() > 0.8;
  }

  /**
   * 计算连击效率
   * @returns {number} 连击效率
   * @private
   */
  _calculateComboEfficiency() {
    if (this.comboState.currentCombo === 0) return 0;
    
    const duration = Date.now() - this.comboState.comboStartTime;
    const avgTimePerCombo = duration / this.comboState.currentCombo;
    const maxTimePerCombo = this.options.comboTimeWindow;
    
    return Math.max(0, 1 - (avgTimePerCombo / maxTimePerCombo));
  }

  /**
   * 获取当前连击倍数
   * @returns {number} 当前连击倍数
   * @private
   */
  _getCurrentComboMultiplier() {
    if (!this.comboState.isActive) return 1.0;
    
    return Math.min(
      this.options.maxComboMultiplier,
      Math.pow(this.options.comboMultiplier, this.comboState.currentCombo - 1)
    );
  }

  /**
   * 计算连击倍率 - 公开接口
   * @param {number} comboCount - 连击数（可选，默认使用当前连击数）
   * @returns {number} 连击倍率
   */
  calculateMultiplier(comboCount = null) {
    const actualComboCount = comboCount !== null ? comboCount : this.comboState.currentCombo;
    
    if (actualComboCount <= 0) return 1;
    
    // 基础倍率计算
    let multiplier = 1 + (actualComboCount - 1) * this.options.comboMultiplier;
    
    // 应用最大倍率限制
    if (multiplier > this.options.maxComboMultiplier) {
      multiplier = this.options.maxComboMultiplier;
    }
    
    console.log(`🔥 连击倍率计算: ${actualComboCount}连击 → ${multiplier.toFixed(2)}倍`);
    return multiplier;
  }

  /**
   * 获取连击剩余时间
   * @returns {number} 剩余时间（毫秒）
   * @private
   */
  _getComboTimeRemaining() {
    if (!this.comboState.isActive) return 0;
    
    const elapsed = Date.now() - this.comboState.lastComboTime;
    return Math.max(0, this.options.comboTimeWindow - elapsed);
  }

  /**
   * 获取当前连击类型
   * @returns {Object} 连击类型
   * @private
   */
  _getCurrentComboType() {
    if (!this.comboState.isActive) return null;
    
    // 根据当前连击状态推断类型
    if (this.comboChain.isChainActive && this.comboChain.chainLength >= 4) {
      return this.comboTypes.CHAIN;
    } else if (this._isPerfectCombo()) {
      return this.comboTypes.PERFECT;
    } else {
      return this.comboTypes.SINGLE;
    }
  }

  /**
   * 销毁连击管理器
   */
  destroy() {
    this.removeAllListeners();
    
    // 清除所有定时器
    this.comboEffects.activeEffects.forEach((effect, effectId) => {
      if (effect.timerId) {
        clearTimeout(effect.timerId);
      }
    });
    
    this.comboEffects.activeEffects.clear();
    
    console.log('🔥 连击管理器已销毁');
  }

  /**
   * 处理游戏动作并检测连击
   */
  processAction(actionType, actionData = {}) {
    const currentTime = Date.now();
    
    // 检查是否在连击时间窗口内
    if (this.lastActionTime && 
        currentTime - this.lastActionTime > this.options.comboTimeWindow) {
      this.resetCombo();
    }
    
    // 记录动作
    this.actionHistory.push({
      type: actionType,
      data: actionData,
      timestamp: currentTime
    });
    
    // 更新连击状态
    const isCombo = this.actionHistory.length >= this.options.minComboLength;
    if (isCombo) {
      this.comboState.currentCombo = this.actionHistory.length;
      this.comboState.isActive = true;
    }
    
    this.lastActionTime = currentTime;
    
    return {
      isCombo,
      comboCount: this.comboState.currentCombo,
      multiplier: this.calculateMultiplier()
    };
  }
} 