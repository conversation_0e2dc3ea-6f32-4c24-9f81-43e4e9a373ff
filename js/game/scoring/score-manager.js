import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

/**
 * 分数管理器
 * 职责：专门管理游戏中的分数计算、等级系统、奖励机制和成就系统
 */
export class ScoreManager extends Emitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      baseLineScore: 100,           // 基础消行分数
      levelMultiplier: 1.2,         // 等级倍数
      hardDropMultiplier: 2,        // 硬降分数倍数
      softDropMultiplier: 1,        // 软降分数倍数
      comboBonus: 50,              // 连击奖励基础分数
      perfectClearBonus: 2000,     // 完美清除奖励
      tSpinBonus: 400,             // T-Spin奖励
      enableLevelProgression: true, // 是否启用等级提升
      linesPerLevel: 10,           // 每级需要的行数
      maxLevel: 20,                // 最大等级
      enableAchievements: true,     // 是否启用成就系统
      ...options
    };
    
    // 分数状态
    this.scoreState = {
      currentScore: 0,
      previousScore: 0,
      sessionHighScore: 0,
      totalLinesCleared: 0,
      currentLevel: 1,
      targetLevel: 1,
      progressToNextLevel: 0,
      experiencePoints: 0
    };
    
    // 分数历史
    this.scoreHistory = {
      recentScores: [],
      maxHistorySize: 50,
      scoringActions: [],
      milestones: new Set()
    };
    
    // 等级系统
    this.levelSystem = {
      currentLevel: 1,
      baseLevel: 1,
      levelThresholds: this._generateLevelThresholds(),
      levelRewards: new Map(),
      levelProgress: 0,
      canLevelUp: true
    };
    
    // 奖励系统
    this.rewardSystem = {
      dailyBonus: false,
      streakBonus: 0,
      perfectGameBonus: false,
      achievementBonuses: new Map(),
      multiplierStack: [],
      currentMultiplier: 1.0
    };
    
    // 成就系统
    this.achievementSystem = {
      unlockedAchievements: new Set(),
      achievementProgress: new Map(),
      achievementRewards: new Map(),
      categories: ['scoring', 'combo', 'level', 'special', 'endurance']
    };
    
    // 性能统计
    this.statistics = {
      totalGames: 0,
      averageScore: 0,
      bestScore: 0,
      totalPlayTime: 0,
      scoringEfficiency: 0,
      scorePerSecond: 0
    };
    
    console.log('💰 分数管理器已初始化', this.options);
    
    // 初始化系统
    this._initializeAchievements();
    this._initializeLevelRewards();
  }

  /**
   * 获取当前分数
   * @returns {number} 当前分数
   */
  getCurrentScore() {
    return this.scoreState.currentScore;
  }

  /**
   * 获取当前等级
   * @returns {number} 当前等级
   */
  getCurrentLevel() {
    return this.levelSystem.currentLevel;
  }

  /**
   * 计算消行分数
   * @param {number} linesCleared - 消除的行数
   * @param {number} level - 当前等级
   * @param {Object} context - 消除上下文（连击、特殊消除等）
   * @returns {number} 获得的分数
   */
  calculateLineScore(linesCleared, level = null, context = {}) {
    if (linesCleared <= 0) return 0;
    
    const currentLevel = level || this.levelSystem.currentLevel;
    let baseScore = 0;
    
    // 基础分数计算（标准Tetris评分）
    switch (linesCleared) {
      case 1:
        baseScore = this.options.baseLineScore * 1;  // Single
        break;
      case 2:
        baseScore = this.options.baseLineScore * 3;  // Double  
        break;
      case 3:
        baseScore = this.options.baseLineScore * 5;  // Triple
        break;
      case 4:
        baseScore = this.options.baseLineScore * 8;  // Tetris
        break;
      default:
        // 超过4行的特殊情况
        baseScore = this.options.baseLineScore * (linesCleared * 2);
        break;
    }
    
    // 等级倍数
    const levelMultiplier = Math.pow(this.options.levelMultiplier, currentLevel - 1);
    let finalScore = Math.floor(baseScore * levelMultiplier);
    
    // 应用上下文奖励
    finalScore = this._applyContextBonuses(finalScore, context);
    
    // 应用当前倍数
    finalScore = Math.floor(finalScore * this.rewardSystem.currentMultiplier);
    
    console.log(`💰 消行分数计算: ${linesCleared}行 × ${currentLevel}级 = ${finalScore}分`);
    
    // 记录评分行为
    this._recordScoringAction('lines', {
      linesCleared,
      level: currentLevel,
      baseScore,
      finalScore,
      context
    });
    
    return finalScore;
  }

  /**
   * 计算下降分数
   * @param {number} distance - 下降距离
   * @param {string} dropType - 下降类型：'soft' | 'hard'
   * @returns {number} 获得的分数
   */
  calculateDropScore(distance, dropType = 'soft') {
    if (distance <= 0) return 0;
    
    let multiplier;
    switch (dropType) {
      case 'hard':
        multiplier = this.options.hardDropMultiplier;
        break;
      case 'soft':
        multiplier = this.options.softDropMultiplier;
        break;
      default:
        multiplier = 1;
    }
    
    const dropScore = distance * multiplier;
    
    console.log(`📉 下降分数: ${distance}格 × ${multiplier} = ${dropScore}分 (${dropType})`);
    
    // 记录评分行为
    this._recordScoringAction('drop', {
      distance,
      dropType,
      score: dropScore
    });
    
    return dropScore;
  }

  /**
   * 计算特殊奖励分数
   * @param {string} bonusType - 奖励类型
   * @param {Object} data - 奖励数据
   * @returns {number} 奖励分数
   */
  calculateBonusScore(bonusType, data = {}) {
    let bonusScore = 0;
    
    switch (bonusType) {
      case 'perfectClear':
        bonusScore = this.options.perfectClearBonus;
        break;
      case 'tSpin':
        bonusScore = this.options.tSpinBonus * (data.difficulty || 1);
        break;
      case 'combo':
        bonusScore = this.options.comboBonus * (data.comboCount || 1);
        break;
      case 'levelUp':
        bonusScore = 500 * this.levelSystem.currentLevel;
        break;
      case 'achievement':
        bonusScore = data.rewardPoints || 1000;
        break;
      case 'streak':
        bonusScore = 100 * (data.streakLength || 1);
        break;
      default:
        console.warn('未知的奖励类型:', bonusType);
        return 0;
    }
    
    // 应用等级倍数
    const levelMultiplier = Math.pow(this.options.levelMultiplier, this.levelSystem.currentLevel - 1);
    bonusScore = Math.floor(bonusScore * levelMultiplier);
    
    console.log(`🎁 特殊奖励: ${bonusType} = ${bonusScore}分`);
    
    // 记录评分行为
    this._recordScoringAction('bonus', {
      bonusType,
      data,
      score: bonusScore
    });
    
    return bonusScore;
  }

  /**
   * 添加分数
   * @param {number} points - 要添加的分数
   * @param {string} source - 分数来源
   * @param {Object} metadata - 元数据
   */
  addScore(points, source = 'unknown', metadata = {}) {
    if (points <= 0) return;
    
    this.scoreState.previousScore = this.scoreState.currentScore;
    this.scoreState.currentScore += points;
    
    // 更新会话最高分
    if (this.scoreState.currentScore > this.scoreState.sessionHighScore) {
      this.scoreState.sessionHighScore = this.scoreState.currentScore;
    }
    
    // 添加到历史记录
    this._addToScoreHistory(points, source, metadata);
    
    // 检查成就和里程碑
    this._checkScoreMilestones();
    
    // 发出分数更新事件
    this.emit('score:updated', {
      currentScore: this.scoreState.currentScore,
      addedPoints: points,
      source,
      metadata
    });
    
    console.log(`💰 分数增加: +${points} (来源: ${source}) 总分: ${this.scoreState.currentScore}`);
  }

  /**
   * 处理等级提升
   * @param {number} linesCleared - 消除的行数
   */
  processLevelProgression(linesCleared) {
    if (!this.options.enableLevelProgression || linesCleared <= 0) return;
    
    this.scoreState.totalLinesCleared += linesCleared;
    
    // 计算新等级
    const targetLevel = Math.min(
      this.options.maxLevel,
      this.options.baseLevel + Math.floor(this.scoreState.totalLinesCleared / this.options.linesPerLevel)
    );
    
    // 检查是否升级
    if (targetLevel > this.levelSystem.currentLevel && this.levelSystem.canLevelUp) {
      const oldLevel = this.levelSystem.currentLevel;
      this.levelSystem.currentLevel = targetLevel;
      
      // 计算升级奖励
      const levelUpBonus = this.calculateBonusScore('levelUp');
      this.addScore(levelUpBonus, 'levelUp', { 
        fromLevel: oldLevel, 
        toLevel: targetLevel 
      });
      
      // 应用等级奖励
      this._applyLevelRewards(targetLevel);
      
      // 发出升级事件
      this.emit('level:up', {
        oldLevel,
        newLevel: targetLevel,
        bonus: levelUpBonus,
        totalLines: this.scoreState.totalLinesCleared
      });
      
      console.log(`🆙 等级提升: ${oldLevel} → ${targetLevel}, 奖励: ${levelUpBonus}分`);
    }
    
    // 更新进度
    this._updateLevelProgress();
  }

  /**
   * 设置分数倍数
   * @param {number} multiplier - 倍数值
   * @param {string} source - 倍数来源
   * @param {number} duration - 持续时间（毫秒，0表示永久）
   */
  setScoreMultiplier(multiplier, source = 'unknown', duration = 0) {
    const multiplierData = {
      value: multiplier,
      source,
      startTime: Date.now(),
      duration,
      id: Math.random().toString(36).substr(2, 9)
    };
    
    this.rewardSystem.multiplierStack.push(multiplierData);
    this._updateCurrentMultiplier();
    
    // 如果有持续时间，设置清除定时器
    if (duration > 0) {
      setTimeout(() => {
        this.removeScoreMultiplier(multiplierData.id);
      }, duration);
    }
    
    this.emit('multiplier:added', {
      multiplier,
      source,
      duration,
      currentMultiplier: this.rewardSystem.currentMultiplier
    });
    
    console.log(`⚡ 分数倍数设置: ×${multiplier} (来源: ${source}), 当前总倍数: ×${this.rewardSystem.currentMultiplier}`);
  }

  /**
   * 移除分数倍数
   * @param {string} multiplierId - 倍数ID
   */
  removeScoreMultiplier(multiplierId) {
    const index = this.rewardSystem.multiplierStack.findIndex(m => m.id === multiplierId);
    
    if (index !== -1) {
      const removed = this.rewardSystem.multiplierStack.splice(index, 1)[0];
      this._updateCurrentMultiplier();
      
      this.emit('multiplier:removed', {
        removedMultiplier: removed.value,
        source: removed.source,
        currentMultiplier: this.rewardSystem.currentMultiplier
      });
      
      console.log(`⚡ 分数倍数移除: ×${removed.value} (来源: ${removed.source})`);
    }
  }

  /**
   * 解锁成就
   * @param {string} achievementId - 成就ID
   * @param {Object} data - 成就数据
   */
  unlockAchievement(achievementId, data = {}) {
    if (this.achievementSystem.unlockedAchievements.has(achievementId)) {
      return false; // 已经解锁
    }
    
    this.achievementSystem.unlockedAchievements.add(achievementId);
    
    // 获取成就奖励
    const reward = this.achievementSystem.achievementRewards.get(achievementId);
    if (reward) {
      if (reward.score) {
        this.addScore(reward.score, 'achievement', { achievementId, ...data });
      }
      if (reward.multiplier) {
        this.setScoreMultiplier(reward.multiplier, `achievement_${achievementId}`, reward.duration || 0);
      }
    }
    
    this.emit('achievement:unlocked', {
      achievementId,
      reward,
      data,
      totalAchievements: this.achievementSystem.unlockedAchievements.size
    });
    
    console.log(`🏆 成就解锁: ${achievementId}`, reward);
    return true;
  }

  /**
   * 检查成就进度
   * @param {string} category - 成就类别
   * @param {Object} progress - 进度数据
   */
  updateAchievementProgress(category, progress) {
    if (!this.options.enableAchievements) return;
    
    // 更新进度
    if (!this.achievementSystem.achievementProgress.has(category)) {
      this.achievementSystem.achievementProgress.set(category, {});
    }
    
    const categoryProgress = this.achievementSystem.achievementProgress.get(category);
    Object.assign(categoryProgress, progress);
    
    // 检查成就触发条件
    this._checkAchievementTriggers(category, categoryProgress);
  }

  /**
   * 重置分数系统
   */
  reset() {
    console.log('🔄 重置分数管理器');
    
    // 重置分数状态
    this.scoreState = {
      currentScore: 0,
      previousScore: 0,
      sessionHighScore: 0,
      totalLinesCleared: 0,
      currentLevel: 1,
      targetLevel: 1,
      progressToNextLevel: 0,
      experiencePoints: 0
    };
    
    // 重置等级系统
    this.levelSystem.currentLevel = this.options.baseLevel || 1;
    this.levelSystem.levelProgress = 0;
    this.levelSystem.canLevelUp = true;
    
    // 重置倍数系统
    this.rewardSystem.multiplierStack = [];
    this.rewardSystem.currentMultiplier = 1.0;
    this.rewardSystem.streakBonus = 0;
    this.rewardSystem.perfectGameBonus = false;
    
    // 清空历史记录
    this.scoreHistory.recentScores = [];
    this.scoreHistory.scoringActions = [];
    this.scoreHistory.milestones.clear();
    
    // 重置成就进度（但不清除已解锁的成就）
    this.achievementSystem.achievementProgress.clear();
    
    this.emit('score:reset');
  }

  /**
   * 获取分数系统状态
   * @returns {Object} 状态信息
   */
  getState() {
    return {
      score: { ...this.scoreState },
      level: { ...this.levelSystem },
      rewards: {
        currentMultiplier: this.rewardSystem.currentMultiplier,
        activeMultipliers: this.rewardSystem.multiplierStack.length,
        streakBonus: this.rewardSystem.streakBonus
      },
      achievements: {
        unlocked: this.achievementSystem.unlockedAchievements.size,
        categories: this.achievementSystem.categories.length
      },
      statistics: { ...this.statistics }
    };
  }

  /**
   * 获取详细统计信息
   * @returns {Object} 详细统计
   */
  getDetailedStats() {
    return {
      scoreState: { ...this.scoreState },
      levelSystem: { ...this.levelSystem },
      rewardSystem: {
        currentMultiplier: this.rewardSystem.currentMultiplier,
        activeMultipliers: this.rewardSystem.multiplierStack.map(m => ({
          value: m.value,
          source: m.source,
          remaining: m.duration > 0 ? Math.max(0, m.duration - (Date.now() - m.startTime)) : -1
        })),
        streakBonus: this.rewardSystem.streakBonus
      },
      achievements: {
        unlocked: Array.from(this.achievementSystem.unlockedAchievements),
        progress: Object.fromEntries(this.achievementSystem.achievementProgress)
      },
      history: {
        recentScores: this.scoreHistory.recentScores.slice(-10),
        milestones: Array.from(this.scoreHistory.milestones)
      },
      statistics: { ...this.statistics }
    };
  }

  // =================== 私有方法 ===================

  /**
   * 应用上下文奖励
   * @param {number} baseScore - 基础分数
   * @param {Object} context - 上下文信息
   * @returns {number} 最终分数
   * @private
   */
  _applyContextBonuses(baseScore, context) {
    let finalScore = baseScore;
    
    // T-Spin奖励
    if (context.isTSpin) {
      finalScore += this.options.tSpinBonus * (context.tSpinType === 'triple' ? 3 : context.tSpinType === 'double' ? 2 : 1);
    }
    
    // 完美清除奖励
    if (context.isPerfectClear) {
      finalScore += this.options.perfectClearBonus;
    }
    
    // 困难消除奖励
    if (context.isDifficult) {
      finalScore *= 1.5;
    }
    
    return Math.floor(finalScore);
  }

  /**
   * 记录评分行为
   * @param {string} type - 行为类型
   * @param {Object} data - 行为数据
   * @private
   */
  _recordScoringAction(type, data) {
    const action = {
      type,
      data,
      timestamp: Date.now(),
      score: this.scoreState.currentScore
    };
    
    this.scoreHistory.scoringActions.push(action);
    
    // 限制历史记录大小
    if (this.scoreHistory.scoringActions.length > this.scoreHistory.maxHistorySize) {
      this.scoreHistory.scoringActions.shift();
    }
  }

  /**
   * 添加到分数历史
   * @param {number} points - 分数
   * @param {string} source - 来源
   * @param {Object} metadata - 元数据
   * @private
   */
  _addToScoreHistory(points, source, metadata) {
    const entry = {
      points,
      source,
      metadata,
      timestamp: Date.now(),
      totalScore: this.scoreState.currentScore
    };
    
    this.scoreHistory.recentScores.push(entry);
    
    // 限制历史记录大小
    if (this.scoreHistory.recentScores.length > this.scoreHistory.maxHistorySize) {
      this.scoreHistory.recentScores.shift();
    }
  }

  /**
   * 检查分数里程碑
   * @private
   */
  _checkScoreMilestones() {
    const milestones = [1000, 5000, 10000, 25000, 50000, 100000, 250000, 500000, 1000000];
    
    for (const milestone of milestones) {
      if (this.scoreState.currentScore >= milestone && !this.scoreHistory.milestones.has(milestone)) {
        this.scoreHistory.milestones.add(milestone);
        
        // 里程碑奖励
        const bonus = Math.floor(milestone * 0.1);
        this.addScore(bonus, 'milestone', { milestone });
        
        this.emit('milestone:reached', {
          milestone,
          bonus,
          totalScore: this.scoreState.currentScore
        });
        
        console.log(`🎯 里程碑达成: ${milestone}分, 奖励: ${bonus}分`);
      }
    }
  }

  /**
   * 更新等级进度
   * @private
   */
  _updateLevelProgress() {
    if (this.levelSystem.currentLevel >= this.options.maxLevel) {
      this.levelSystem.levelProgress = 1.0;
      return;
    }
    
    const currentLevelLines = (this.levelSystem.currentLevel - this.options.baseLevel) * this.options.linesPerLevel;
    const progressLines = this.scoreState.totalLinesCleared - currentLevelLines;
    this.levelSystem.levelProgress = Math.min(1.0, progressLines / this.options.linesPerLevel);
  }

  /**
   * 更新当前倍数
   * @private
   */
  _updateCurrentMultiplier() {
    // 计算所有活跃倍数的乘积
    this.rewardSystem.currentMultiplier = this.rewardSystem.multiplierStack.reduce(
      (total, multiplier) => total * multiplier.value,
      1.0
    );
  }

  /**
   * 生成等级阈值
   * @returns {Array} 等级阈值数组
   * @private
   */
  _generateLevelThresholds() {
    const thresholds = [];
    for (let level = 1; level <= this.options.maxLevel; level++) {
      thresholds[level] = (level - 1) * this.options.linesPerLevel;
    }
    return thresholds;
  }

  /**
   * 初始化成就系统
   * @private
   */
  _initializeAchievements() {
    // 预定义成就
    const achievements = [
      { id: 'first_tetris', category: 'scoring', reward: { score: 1000 } },
      { id: 'combo_master', category: 'combo', reward: { score: 2000, multiplier: 1.5, duration: 30000 } },
      { id: 'level_10', category: 'level', reward: { score: 5000 } },
      { id: 'perfect_clear', category: 'special', reward: { score: 10000 } },
      { id: 'marathon_100k', category: 'endurance', reward: { score: 20000, multiplier: 2.0, duration: 60000 } }
    ];
    
    for (const achievement of achievements) {
      this.achievementSystem.achievementRewards.set(achievement.id, achievement.reward);
    }
  }

  /**
   * 初始化等级奖励
   * @private
   */
  _initializeLevelRewards() {
    // 每5级给予特殊奖励
    for (let level = 5; level <= this.options.maxLevel; level += 5) {
      this.levelSystem.levelRewards.set(level, {
        multiplier: 1.2,
        duration: 60000, // 1分钟
        description: `Level ${level} Speed Boost`
      });
    }
  }

  /**
   * 应用等级奖励
   * @param {number} level - 等级
   * @private
   */
  _applyLevelRewards(level) {
    const reward = this.levelSystem.levelRewards.get(level);
    if (reward) {
      this.setScoreMultiplier(
        reward.multiplier,
        `level_${level}`,
        reward.duration
      );
      
      console.log(`🎁 等级奖励应用: ${reward.description}`);
    }
  }

  /**
   * 检查成就触发条件
   * @param {string} category - 类别
   * @param {Object} progress - 进度
   * @private
   */
  _checkAchievementTriggers(category, progress) {
    // 这里可以实现具体的成就触发逻辑
    // 例如：if (category === 'scoring' && progress.tetrisCount >= 1) { this.unlockAchievement('first_tetris'); }
  }

  /**
   * 销毁分数管理器
   */
  destroy() {
    this.removeAllListeners();
    
    // 清除所有定时器
    this.rewardSystem.multiplierStack.forEach(multiplier => {
      if (multiplier.timerId) {
        clearTimeout(multiplier.timerId);
      }
    });
    
    console.log('💰 分数管理器已销毁');
  }
} 