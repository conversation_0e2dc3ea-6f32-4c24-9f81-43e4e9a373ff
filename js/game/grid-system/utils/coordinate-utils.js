/**
 * 坐标转换工具类
 * 负责网格坐标和屏幕坐标的转换
 */
export default class CoordinateUtils {
  constructor(gridDataManager) {
    this.gridData = gridDataManager;
  }

  /**
   * 将网格坐标转换为屏幕坐标
   * @param {number} row - 网格行索引
   * @param {number} col - 网格列索引
   * @returns {Object} 屏幕坐标 {x, y}
   */
  gridToScreen(row, col) {
    return {
      x: this.gridData.offsetX + col * this.gridData.blockSize,
      y: this.gridData.offsetY + row * this.gridData.blockSize
    };
  }

  /**
   * 将屏幕坐标转换为网格坐标
   * @param {number} x - 屏幕X坐标
   * @param {number} y - 屏幕Y坐标
   * @returns {Object} 网格坐标 {row, col}
   */
  screenToGrid(x, y) {
    const col = Math.floor((x - this.gridData.offsetX) / this.gridData.blockSize);
    const row = Math.floor((y - this.gridData.offsetY) / this.gridData.blockSize);
    
    return {
      row: Math.max(0, Math.min(row, this.gridData.rows - 1)),
      col: Math.max(0, Math.min(col, this.gridData.cols - 1))
    };
  }

  /**
   * 获取网格中心点的屏幕坐标
   * @param {number} row - 网格行索引
   * @param {number} col - 网格列索引
   * @returns {Object} 中心点屏幕坐标 {x, y}
   */
  gridCenterToScreen(row, col) {
    const pos = this.gridToScreen(row, col);
    return {
      x: pos.x + this.gridData.blockSize / 2,
      y: pos.y + this.gridData.blockSize / 2
    };
  }

  /**
   * 检查屏幕坐标是否在网格范围内
   * @param {number} x - 屏幕X坐标
   * @param {number} y - 屏幕Y坐标
   * @returns {boolean} 是否在网格范围内
   */
  isInGridBounds(x, y) {
    return x >= this.gridData.offsetX &&
           x < this.gridData.offsetX + this.gridData.cols * this.gridData.blockSize &&
           y >= this.gridData.offsetY &&
           y < this.gridData.offsetY + this.gridData.rows * this.gridData.blockSize;
  }

  /**
   * 检查网格坐标是否有效
   * @param {number} row - 网格行索引
   * @param {number} col - 网格列索引
   * @returns {boolean} 坐标是否有效
   */
  isValidGridPosition(row, col) {
    return row >= 0 && row < this.gridData.rows &&
           col >= 0 && col < this.gridData.cols;
  }

  /**
   * 计算两个网格位置之间的距离
   * @param {number} row1 - 第一个位置的行
   * @param {number} col1 - 第一个位置的列
   * @param {number} row2 - 第二个位置的行
   * @param {number} col2 - 第二个位置的列
   * @returns {number} 网格距离
   */
  gridDistance(row1, col1, row2, col2) {
    return Math.abs(row1 - row2) + Math.abs(col1 - col2);
  }

  /**
   * 计算两个网格位置之间的欧几里得距离
   * @param {number} row1 - 第一个位置的行
   * @param {number} col1 - 第一个位置的列
   * @param {number} row2 - 第二个位置的行
   * @param {number} col2 - 第二个位置的列
   * @returns {number} 欧几里得距离
   */
  euclideanDistance(row1, col1, row2, col2) {
    const dx = row1 - row2;
    const dy = col1 - col2;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 获取指定位置周围的邻居位置
   * @param {number} row - 中心行
   * @param {number} col - 中心列
   * @param {number} radius - 半径（默认为1）
   * @param {boolean} includeDiagonals - 是否包含对角线邻居（默认为false）
   * @returns {Array} 邻居位置数组
   */
  getNeighbors(row, col, radius = 1, includeDiagonals = false) {
    const neighbors = [];
    
    for (let r = row - radius; r <= row + radius; r++) {
      for (let c = col - radius; c <= col + radius; c++) {
        // 跳过中心点
        if (r === row && c === col) continue;
        
        // 检查是否在网格范围内
        if (!this.isValidGridPosition(r, c)) continue;
        
        // 如果不包含对角线，只添加直接邻居
        if (!includeDiagonals) {
          const distance = Math.abs(r - row) + Math.abs(c - col);
          if (distance > radius) continue;
        }
        
        neighbors.push({ row: r, col: c });
      }
    }
    
    return neighbors;
  }

  /**
   * 获取指定范围内的所有位置
   * @param {number} centerRow - 中心行
   * @param {number} centerCol - 中心列
   * @param {number} range - 范围
   * @returns {Array} 范围内的位置数组
   */
  getPositionsInRange(centerRow, centerCol, range) {
    const positions = [];
    
    for (let row = centerRow - range; row <= centerRow + range; row++) {
      for (let col = centerCol - range; col <= centerCol + range; col++) {
        if (this.isValidGridPosition(row, col)) {
          const distance = this.gridDistance(centerRow, centerCol, row, col);
          if (distance <= range) {
            positions.push({
              row: row,
              col: col,
              distance: distance
            });
          }
        }
      }
    }
    
    return positions;
  }

  /**
   * 获取两点之间的路径
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {number} endRow - 结束行
   * @param {number} endCol - 结束列
   * @returns {Array} 路径数组
   */
  getPath(startRow, startCol, endRow, endCol) {
    const path = [];
    
    let currentRow = startRow;
    let currentCol = startCol;
    
    while (currentRow !== endRow || currentCol !== endCol) {
      path.push({ row: currentRow, col: currentCol });
      
      // 朝目标方向移动
      if (currentRow < endRow) {
        currentRow++;
      } else if (currentRow > endRow) {
        currentRow--;
      }
      
      if (currentCol < endCol) {
        currentCol++;
      } else if (currentCol > endCol) {
        currentCol--;
      }
    }
    
    path.push({ row: endRow, col: endCol });
    
    return path;
  }

  /**
   * 获取网格的边界信息
   * @returns {Object} 边界信息
   */
  getGridBounds() {
    return {
      left: this.gridData.offsetX,
      top: this.gridData.offsetY,
      right: this.gridData.offsetX + this.gridData.cols * this.gridData.blockSize,
      bottom: this.gridData.offsetY + this.gridData.rows * this.gridData.blockSize,
      width: this.gridData.cols * this.gridData.blockSize,
      height: this.gridData.rows * this.gridData.blockSize
    };
  }

  /**
   * 将网格坐标转换为一维索引
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {number} 一维索引
   */
  gridToIndex(row, col) {
    return row * this.gridData.cols + col;
  }

  /**
   * 将一维索引转换为网格坐标
   * @param {number} index - 一维索引
   * @returns {Object} 网格坐标 {row, col}
   */
  indexToGrid(index) {
    const row = Math.floor(index / this.gridData.cols);
    const col = index % this.gridData.cols;
    return { row, col };
  }

  /**
   * 获取随机网格位置
   * @returns {Object} 随机网格坐标 {row, col}
   */
  getRandomGridPosition() {
    return {
      row: Math.floor(Math.random() * this.gridData.rows),
      col: Math.floor(Math.random() * this.gridData.cols)
    };
  }

  /**
   * 检查位置是否在边界上
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {Object} 边界信息
   */
  getBorderInfo(row, col) {
    return {
      isOnBorder: row === 0 || row === this.gridData.rows - 1 || 
                  col === 0 || col === this.gridData.cols - 1,
      isTopBorder: row === 0,
      isBottomBorder: row === this.gridData.rows - 1,
      isLeftBorder: col === 0,
      isRightBorder: col === this.gridData.cols - 1
    };
  }

  /**
   * 获取网格的角落位置
   * @returns {Object} 角落位置
   */
  getCornerPositions() {
    return {
      topLeft: { row: 0, col: 0 },
      topRight: { row: 0, col: this.gridData.cols - 1 },
      bottomLeft: { row: this.gridData.rows - 1, col: 0 },
      bottomRight: { row: this.gridData.rows - 1, col: this.gridData.cols - 1 }
    };
  }
}