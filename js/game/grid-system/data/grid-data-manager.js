/**
 * 网格数据管理器
 * 负责网格的基础数据存储和操作
 */
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../../../render.js';

// 网格配置
const GRID_ROWS = 20; // 行数
const GRID_COLS = 10; // 列数
const BLOCK_SIZE = Math.floor(SCREEN_WIDTH * 0.8 / GRID_COLS); // 方块大小，屏幕宽度的70%
const GRID_OFFSET_X = (SCREEN_WIDTH - BLOCK_SIZE * GRID_COLS) / 2 - SCREEN_WIDTH * 0.09; // 网格X轴偏移量，使网格水平居中
const GRID_OFFSET_Y = SCREEN_HEIGHT * 0.1; // 网格Y轴偏移量，留出顶部空间

export default class GridDataManager {
  constructor() {
    // 初始化网格，创建一个二维数组
    this.matrix = Array(GRID_ROWS).fill().map(() => Array(GRID_COLS).fill(null));
    
    // 边界和大小信息
    this.rows = GRID_ROWS;
    this.cols = GRID_COLS;
    this.blockSize = BLOCK_SIZE;
    this.offsetX = GRID_OFFSET_X;
    this.offsetY = GRID_OFFSET_Y;
  }

  /**
   * 检查指定位置是否有效（在网格内且没有方块）
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {boolean} 如果位置有效且空闲则返回true
   */
  isValidPosition(row, col) {
    // 检查是否在网格范围内
    if (row < 0 || row >= this.rows || col < 0 || col >= this.cols) {
      return false;
    }
    
    // 检查该位置是否已有方块
    return this.matrix[row][col] === null;
  }

  /**
   * 在网格上放置方块
   * @param {Block} block - 要放置的方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {boolean} 如果放置成功则返回true
   */
  placeBlock(block, row, col) {
    if (this.isValidPosition(row, col)) {
      this.matrix[row][col] = block;
      block.row = row;
      block.col = col;
      return true;
    }
    return false;
  }

  /**
   * 移除指定位置的方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {Block|null} 被移除的方块，如果没有则返回null
   */
  removeBlock(row, col) {
    if (row >= 0 && row < this.rows && col >= 0 && col < this.cols) {
      const block = this.matrix[row][col];
      this.matrix[row][col] = null;
      return block;
    }
    return null;
  }

  /**
   * 获取指定位置的方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {Block|null} 指定位置的方块，如果没有则返回null
   */
  getBlock(row, col) {
    if (row >= 0 && row < this.rows && col >= 0 && col < this.cols) {
      return this.matrix[row][col];
    }
    return null;
  }

  /**
   * 设置指定位置的方块
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @param {Block|null} block - 要设置的方块
   */
  setBlock(row, col, block) {
    if (row >= 0 && row < this.rows && col >= 0 && col < this.cols) {
      this.matrix[row][col] = block;
      if (block) {
        block.row = row;
        block.col = col;
      }
    }
  }

  /**
   * 检查一行是否已满
   * @param {number} row - 行索引
   * @returns {boolean} 如果该行已满则返回true
   */
  isRowFull(row) {
    if (row >= 0 && row < this.rows) {
      return this.matrix[row].every(block => block !== null);
    }
    return false;
  }

  /**
   * 检查一行是否为空
   * @param {number} row - 行索引
   * @returns {boolean} 如果该行为空则返回true
   */
  isRowEmpty(row) {
    if (row >= 0 && row < this.rows) {
      return this.matrix[row].every(block => block === null);
    }
    return false;
  }

  /**
   * 清除指定的行
   * @param {number} row - 行索引
   * @returns {Block[]} 被清除的方块数组
   */
  clearRow(row) {
    if (row >= 0 && row < this.rows) {
      const clearedBlocks = [...this.matrix[row]].filter(block => block !== null);
      this.matrix[row] = Array(this.cols).fill(null);
      return clearedBlocks;
    }
    return [];
  }

  /**
   * 移动方块从一个位置到另一个位置
   * @param {number} fromRow - 源行索引
   * @param {number} fromCol - 源列索引
   * @param {number} toRow - 目标行索引
   * @param {number} toCol - 目标列索引
   * @returns {boolean} 如果移动成功则返回true
   */
  moveBlock(fromRow, fromCol, toRow, toCol) {
    const block = this.getBlock(fromRow, fromCol);
    if (block && this.isValidPosition(toRow, toCol)) {
      this.removeBlock(fromRow, fromCol);
      this.setBlock(toRow, toCol, block);
      return true;
    }
    return false;
  }

  /**
   * 行下移操作
   * @param {number} clearedRow - 被清除的行索引
   */
  shiftRowsDown(clearedRow) {
    // 从被清除的行开始，向上遍历所有行
    for (let row = clearedRow; row > 0; row--) {
      // 将上一行的内容复制到当前行
      for (let col = 0; col < this.cols; col++) {
        const upperBlock = this.matrix[row - 1][col];
        this.matrix[row][col] = upperBlock;
        
        if (upperBlock) {
          upperBlock.row = row; // 更新方块的行位置
        }
      }
    }
    
    // 清空顶行
    for (let col = 0; col < this.cols; col++) {
      this.matrix[0][col] = null;
    }
  }

  /**
   * 清空整个网格
   */
  clear() {
    for (let row = 0; row < this.rows; row++) {
      for (let col = 0; col < this.cols; col++) {
        this.matrix[row][col] = null;
      }
    }
  }

  /**
   * 获取网格的副本
   * @returns {Array} 网格矩阵的深拷贝
   */
  getMatrixCopy() {
    return this.matrix.map(row => [...row]);
  }

  /**
   * 统计网格中的方块数量
   * @returns {number} 方块总数
   */
  getBlockCount() {
    let count = 0;
    for (let row = 0; row < this.rows; row++) {
      for (let col = 0; col < this.cols; col++) {
        if (this.matrix[row][col] !== null) {
          count++;
        }
      }
    }
    return count;
  }

  /**
   * 获取指定列的最高方块行索引
   * @param {number} col - 列索引
   * @returns {number} 最高方块的行索引，如果列为空则返回-1
   */
  getColumnTop(col) {
    if (col < 0 || col >= this.cols) return -1;
    
    for (let row = 0; row < this.rows; row++) {
      if (this.matrix[row][col] !== null) {
        return row;
      }
    }
    return -1; // 列为空
  }

  /**
   * 获取指定列的方块数量
   * @param {number} col - 列索引
   * @returns {number} 方块数量
   */
  getColumnBlockCount(col) {
    if (col < 0 || col >= this.cols) return 0;
    
    let count = 0;
    for (let row = 0; row < this.rows; row++) {
      if (this.matrix[row][col] !== null) {
        count++;
      }
    }
    return count;
  }

  /**
   * 添加下落动画（委托给动画系统）
   * @param {Block} block - 下落的方块
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {number} endRow - 结束行
   * @param {number} endCol - 结束列
   */
  addFallingAnimation(block, startRow, startCol, endRow, endCol) {
    // 如果有动画系统引用，委托给它
    if (this.animationSystem && typeof this.animationSystem.addFallingAnimation === 'function') {
      this.animationSystem.addFallingAnimation(block, startRow, startCol, endRow, endCol);
    } else {
      console.log(`🎬 动画占位：方块从 [${startRow}, ${startCol}] 下落到 [${endRow}, ${endCol}]`);
    }
  }

  /**
   * 设置动画系统引用
   * @param {Object} animationSystem - 动画系统实例
   */
  setAnimationSystem(animationSystem) {
    this.animationSystem = animationSystem;
  }

  /**
   * 调试输出网格状态
   * @param {string} label - 调试标签
   * @param {boolean} detailed - 是否显示详细信息
   */
  debugGridState(label = '网格状态', detailed = true) {
    console.log(`=== ${label} ===`);
    console.log(`网格大小: ${this.rows}x${this.cols}`);
    console.log(`方块总数: ${this.getBlockCount()}`);

    if (detailed) {
      this._printVisualGrid();
    }
  }

  /**
   * 打印可视化网格
   * @private
   */
  _printVisualGrid() {
    console.log('网格可视化:');
    let output = '  ';

    // 打印列号标题
    for (let col = 0; col < this.cols; col++) {
      output += col.toString().padStart(2);
    }
    console.log(output);

    // 打印每一行
    for (let row = 0; row < this.rows; row++) {
      output = row.toString().padStart(2) + ' ';
      for (let col = 0; col < this.cols; col++) {
        const block = this.matrix[row][col];
        output += (block ? '■' : '□') + ' ';
      }
      console.log(output);
    }
    console.log('=================');
  }
}