/**
 * 网格渲染器
 * 负责网格和方块的视觉渲染
 */
export default class GridRenderer {
  constructor(gridDataManager, coordinateUtils) {
    this.gridData = gridDataManager;
    this.coordinateUtils = coordinateUtils;
    
    // 渲染配置
    this.showGrid = true;
    this.showGhost = true;
    this.gridLineWidth = 1;
    this.gridLineColor = 'rgba(255, 255, 255, 0.2)';
    this.backgroundColor = 'rgba(0, 0, 0, 0.8)';
  }

  /**
   * 渲染整个网格
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   */
  render(ctx) {
    // 渲染背景
    this._renderBackground(ctx);
    
    // 渲染网格线
    if (this.showGrid) {
      this._renderGridLines(ctx);
    }
    
    // 渲染所有方块
    this._renderBlocks(ctx);
    
    // 渲染边框
    this._renderBorder(ctx);
  }

  /**
   * 渲染背景
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderBackground(ctx) {
    ctx.save();
    
    ctx.fillStyle = this.backgroundColor;
    ctx.fillRect(
      this.gridData.offsetX,
      this.gridData.offsetY,
      this.gridData.cols * this.gridData.blockSize,
      this.gridData.rows * this.gridData.blockSize
    );
    
    ctx.restore();
  }

  /**
   * 渲染网格线
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderGridLines(ctx) {
    ctx.save();
    
    ctx.strokeStyle = this.gridLineColor;
    ctx.lineWidth = this.gridLineWidth;
    
    const startX = this.gridData.offsetX;
    const startY = this.gridData.offsetY;
    const endX = startX + this.gridData.cols * this.gridData.blockSize;
    const endY = startY + this.gridData.rows * this.gridData.blockSize;
    
    // 绘制垂直线
    for (let col = 0; col <= this.gridData.cols; col++) {
      const x = startX + col * this.gridData.blockSize;
      ctx.beginPath();
      ctx.moveTo(x, startY);
      ctx.lineTo(x, endY);
      ctx.stroke();
    }
    
    // 绘制水平线
    for (let row = 0; row <= this.gridData.rows; row++) {
      const y = startY + row * this.gridData.blockSize;
      ctx.beginPath();
      ctx.moveTo(startX, y);
      ctx.lineTo(endX, y);
      ctx.stroke();
    }
    
    ctx.restore();
  }

  /**
   * 渲染所有方块
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderBlocks(ctx) {
    for (let row = 0; row < this.gridData.rows; row++) {
      for (let col = 0; col < this.gridData.cols; col++) {
        const block = this.gridData.getBlock(row, col);
        if (block) {
          this._renderBlock(ctx, block, row, col);
        }
      }
    }
  }

  /**
   * 渲染单个方块
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Block} block - 方块对象
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @private
   */
  _renderBlock(ctx, block, row, col) {
    if (!block || !block.color) return;

    // 🎬 跳过正在动画中的方块，它们由动画系统单独渲染
    if (block.isAnimating) {
      return;
    }

    const pos = this.coordinateUtils.gridToScreen(row, col);
    const size = this.gridData.blockSize;

    ctx.save();

    // 检查方块是否有自定义渲染位置（用于动画）
    let renderX = pos.x;
    let renderY = pos.y;

    if (block.renderPosition) {
      renderX = block.renderPosition.x;
      renderY = block.renderPosition.y;
    }
    
    // 渲染方块主体
    ctx.fillStyle = block.color;
    ctx.fillRect(renderX, renderY, size, size);
    
    // 渲染方块边框
    ctx.strokeStyle = this._getDarkerColor(block.color);
    ctx.lineWidth = 2;
    ctx.strokeRect(renderX, renderY, size, size);
    
    // 渲染高光效果
    this._renderBlockHighlight(ctx, renderX, renderY, size);
    
    // 渲染方块类型标识
    if (block.type && block.type !== 'normal') {
      this._renderBlockTypeIcon(ctx, block, renderX, renderY, size);
    }
    
    // 渲染特殊效果
    if (block.effect) {
      this._renderBlockEffect(ctx, block, renderX, renderY, size);
    }
    
    ctx.restore();
  }

  /**
   * 渲染方块高光效果
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderBlockHighlight(ctx, x, y, size) {
    const gradient = ctx.createLinearGradient(x, y, x + size, y + size);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(x, y, size * 0.6, size * 0.6);
  }

  /**
   * 渲染方块类型图标
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Block} block - 方块对象
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderBlockTypeIcon(ctx, block, x, y, size) {
    const centerX = x + size / 2;
    const centerY = y + size / 2;
    const iconSize = size * 0.4;
    
    ctx.fillStyle = 'white';
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 1;
    
    switch (block.type) {
      case 'bomb':
        // 渲染炸弹图标
        ctx.beginPath();
        ctx.arc(centerX, centerY, iconSize / 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        break;
        
      case 'fireball':
        // 渲染火球图标
        ctx.fillStyle = '#FF6600';
        ctx.beginPath();
        ctx.arc(centerX, centerY, iconSize / 2, 0, Math.PI * 2);
        ctx.fill();
        break;
        
      case 'lightning':
        // 渲染闪电图标
        ctx.fillStyle = '#00FFFF';
        ctx.beginPath();
        ctx.moveTo(centerX - iconSize/3, centerY - iconSize/2);
        ctx.lineTo(centerX + iconSize/6, centerY);
        ctx.lineTo(centerX - iconSize/6, centerY);
        ctx.lineTo(centerX + iconSize/3, centerY + iconSize/2);
        ctx.closePath();
        ctx.fill();
        break;
    }
  }

  /**
   * 渲染方块特殊效果
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Block} block - 方块对象
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderBlockEffect(ctx, block, x, y, size) {
    switch (block.effect) {
      case 'glow':
        this._renderGlowEffect(ctx, x, y, size, block.color);
        break;
      case 'pulse':
        this._renderPulseEffect(ctx, x, y, size, block.color);
        break;
      case 'shake':
        this._renderShakeEffect(ctx, x, y, size);
        break;
    }
  }

  /**
   * 渲染发光效果
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @param {string} color - 发光颜色
   * @private
   */
  _renderGlowEffect(ctx, x, y, size, color) {
    const centerX = x + size / 2;
    const centerY = y + size / 2;
    
    const gradient = ctx.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, size
    );
    gradient.addColorStop(0, color);
    gradient.addColorStop(1, 'transparent');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(x - size/2, y - size/2, size * 2, size * 2);
  }

  /**
   * 渲染脉动效果
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @param {string} color - 脉动颜色
   * @private
   */
  _renderPulseEffect(ctx, x, y, size, color) {
    const time = Date.now() * 0.005;
    const alpha = (Math.sin(time) + 1) / 2 * 0.5 + 0.3;
    
    ctx.save();
    ctx.globalAlpha = alpha;
    ctx.fillStyle = color;
    ctx.fillRect(x, y, size, size);
    ctx.restore();
  }

  /**
   * 渲染震动效果
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 方块大小
   * @private
   */
  _renderShakeEffect(ctx, x, y, size) {
    const shakeAmount = 2;
    const offsetX = (Math.random() - 0.5) * shakeAmount;
    const offsetY = (Math.random() - 0.5) * shakeAmount;
    
    ctx.translate(offsetX, offsetY);
  }

  /**
   * 渲染边框
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @private
   */
  _renderBorder(ctx) {
    ctx.save();
    
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 3;
    ctx.strokeRect(
      this.gridData.offsetX - 1,
      this.gridData.offsetY - 1,
      this.gridData.cols * this.gridData.blockSize + 2,
      this.gridData.rows * this.gridData.blockSize + 2
    );
    
    ctx.restore();
  }

  /**
   * 渲染幽灵方块（预览下落位置）
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Block} block - 方块对象
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   */
  renderGhostBlock(ctx, block, row, col) {
    if (!this.showGhost || !block) return;

    const pos = this.coordinateUtils.gridToScreen(row, col);
    const size = this.gridData.blockSize;
    
    ctx.save();
    
    // 半透明渲染
    ctx.globalAlpha = 0.3;
    ctx.fillStyle = block.color;
    ctx.fillRect(pos.x, pos.y, size, size);
    
    // 虚线边框
    ctx.setLineDash([5, 5]);
    ctx.strokeStyle = block.color;
    ctx.lineWidth = 2;
    ctx.strokeRect(pos.x, pos.y, size, size);
    
    ctx.restore();
  }

  /**
   * 获取更深的颜色
   * @param {string} color - 原始颜色
   * @returns {string} 更深的颜色
   * @private
   */
  _getDarkerColor(color) {
    // 简单的颜色加深算法
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      
      const darkerR = Math.max(0, r - 50);
      const darkerG = Math.max(0, g - 50);
      const darkerB = Math.max(0, b - 50);
      
      return `rgb(${darkerR}, ${darkerG}, ${darkerB})`;
    }
    
    return color;
  }

  /**
   * 设置渲染选项
   * @param {Object} options - 渲染选项
   */
  setRenderOptions(options) {
    if (options.showGrid !== undefined) {
      this.showGrid = options.showGrid;
    }
    if (options.showGhost !== undefined) {
      this.showGhost = options.showGhost;
    }
    if (options.gridLineColor !== undefined) {
      this.gridLineColor = options.gridLineColor;
    }
    if (options.backgroundColor !== undefined) {
      this.backgroundColor = options.backgroundColor;
    }
  }

  /**
   * 获取渲染统计信息
   * @returns {Object} 渲染统计
   */
  getRenderStats() {
    return {
      totalBlocks: this.gridData.getBlockCount(),
      gridSize: `${this.gridData.rows}x${this.gridData.cols}`,
      blockSize: this.gridData.blockSize,
      showGrid: this.showGrid,
      showGhost: this.showGhost
    };
  }
} 