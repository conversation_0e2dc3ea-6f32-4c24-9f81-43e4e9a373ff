/**
 * 重力系统
 * 负责处理方块下落和重力相关的物理计算
 */
export default class GravitySystem {
  constructor(gridDataManager) {
    this.gridData = gridDataManager;
  }

  /**
   * 应用重力让方块下落
   * @param {Set<number>} columnsToCheck - 需要检查的列
   * @param {Set<Block>} blocksToCheck - 需要检查的方块
   * @param {Array<{row: number, col: number}>} removedPositions - 被消除的方块位置
   * @returns {boolean} 是否有方块下落
   */
  applyGravity(columnsToCheck = null, blocksToCheck = null, removedPositions = []) {
    console.log('🌊 GravitySystem.applyGravity', {
      columnsToCheck: columnsToCheck ? Array.from(columnsToCheck) : null,
      blocksToCheck: blocksToCheck ? `Set(${blocksToCheck.size || blocksToCheck.length || 0})` : null,
      removedPositions: removedPositions.length
    });

    // 1. 首先处理整行消除的平移重力
    if (removedPositions.length > 0) {
      const fullRowsRemoved = this._detectAndHandleFullRowClear(removedPositions);
      if (fullRowsRemoved) {
        console.log('✅ 整行消除处理完成');
        return true;
      }

      // 如果不是整行消除，处理部分消除的个体重力
      const partialClearResult = this._handlePartialRowClear(removedPositions);
      if (partialClearResult) {
        console.log('✅ 部分消除重力处理完成');
        return true;
      }
    }

    // 2. 处理指定列和方块的个体重力
    const validColumns = this._validateGravityColumns(columnsToCheck);
    if (validColumns.length === 0) {
      console.log('没有有效的列需要检查');
      return false;
    }

    const allBlocksToProcess = this._collectAllBlocksToProcess(validColumns, blocksToCheck, removedPositions);
    if (allBlocksToProcess.length === 0) {
      console.log('没有需要应用重力的方块');
      return false;
    }

    console.log(`找到 ${allBlocksToProcess.length} 个方块需要应用个体重力`);
    return this._applyGravityToSpecificBlocks(validColumns, allBlocksToProcess);
  }

  /**
   * 应用全网格重力
   * @returns {boolean} 是否有方块下落
   */
  applyFullGridGravity() {
    console.log('🌍 应用全网格重力');

    let hasFallen = false;

    // 从下往上，从左往右处理每个方块
    for (let col = 0; col < this.gridData.cols; col++) {
      // 找到该列最底部的空位置
      let writePos = this.gridData.rows - 1;

      // 从底部开始扫描
      for (let readPos = this.gridData.rows - 1; readPos >= 0; readPos--) {
        const block = this.gridData.getBlock(readPos, col);

        if (block !== null) {
          // 如果当前位置有方块，且需要移动
          if (readPos !== writePos) {
            // 移动方块
            this.gridData.removeBlock(readPos, col);
            this.gridData.setBlock(writePos, col, block);

            // 添加下落动画（如果网格支持）
            if (this.gridData.addFallingAnimation) {
              this.gridData.addFallingAnimation(block, readPos, col, writePos, col);
            }

            hasFallen = true;

            console.log(`方块从 (${readPos}, ${col}) 下落到 (${writePos}, ${col})`);
          }

          // 下一个写入位置向上移动
          writePos--;
        }
      }
    }

    return hasFallen;
  }

  /**
   * 查找最低可用位置
   * @param {number} row - 当前行
   * @param {number} col - 当前列
   * @returns {number} 最低可用行索引
   */
  _findLowestAvailablePosition(row, col) {
    let targetRow = row;
    
    // 向下寻找最低可用位置
    for (let checkRow = row + 1; checkRow < this.gridData.rows; checkRow++) {
      if (this.gridData.getBlock(checkRow, col) === null) {
        targetRow = checkRow;
      } else {
        break; // 遇到障碍物停止
      }
    }
    
    return targetRow;
  }

  /**
   * 验证重力列参数
   * @param {Set<number>|null} columnsToCheck - 需要检查的列
   * @returns {number[]} 有效的列数组
   */
  _validateGravityColumns(columnsToCheck) {
    if (!columnsToCheck || columnsToCheck.size === 0) {
      // 如果没有指定列，检查所有列
      return Array.from({ length: this.gridData.cols }, (_, i) => i);
    }
    
    // 过滤出有效的列
    return Array.from(columnsToCheck).filter(col => 
      col >= 0 && col < this.gridData.cols
    );
  }

  /**
   * 收集所有需要处理的方块
   * @param {number[]} validColumns - 有效的列
   * @param {Set<Block>|null} blocksToCheck - 需要检查的方块
   * @param {Array<{row: number, col: number}>} removedPositions - 被移除的位置
   * @returns {Array<{block: Block, row: number, col: number}>} 需要处理的方块
   */
  _collectAllBlocksToProcess(validColumns, blocksToCheck, removedPositions) {
    console.log('🔍 开始收集需要处理的方块');
    console.log('🔍 参数:', {
      validColumns,
      blocksToCheckSize: blocksToCheck ? blocksToCheck.size : 0,
      removedPositionsCount: removedPositions.length
    });

    const allBlocksToProcess = [];
    const removedPositionSet = new Set(removedPositions.map(pos => `${pos.row},${pos.col}`));

    if (blocksToCheck && blocksToCheck.size > 0) {
      console.log('🔍 处理指定的解体方块');
      // 如果指定了特定方块，只处理这些方块
      for (const blockInfo of blocksToCheck) {
        console.log('🔍 检查方块信息:', blockInfo);
        // 🧩 修复：处理新的数据结构 {block, row, col}
        let block, row, col;

        if (blockInfo && typeof blockInfo === 'object') {
          if (blockInfo.block && blockInfo.row !== undefined && blockInfo.col !== undefined) {
            // 新格式：{block, row, col}
            block = blockInfo.block;
            row = blockInfo.row;
            col = blockInfo.col;
            console.log(`🔍 解析新格式方块: [${row}, ${col}]`);
          } else if (blockInfo.row !== undefined && blockInfo.col !== undefined) {
            // 旧格式：方块对象直接有 row, col 属性
            block = blockInfo;
            row = blockInfo.row;
            col = blockInfo.col;
            console.log(`🔍 解析旧格式方块: [${row}, ${col}]`);
          } else {
            console.warn('🧩 无效的方块信息格式:', blockInfo);
            continue;
          }

          // 检查方块是否在有效列中且未被移除
          console.log(`🔍 检查方块 [${row}, ${col}]: 在有效列=${validColumns.includes(col)}, 未被移除=${!removedPositionSet.has(`${row},${col}`)}`);

          if (validColumns.includes(col) &&
              !removedPositionSet.has(`${row},${col}`)) {

            // 检查网格中是否真的有这个方块
            const gridBlock = this.gridData.getBlock(row, col);
            console.log(`🔍 网格检查 [${row}, ${col}]: 有方块=${!!gridBlock}, 匹配=${gridBlock === block}`);

            if (gridBlock === block) {
              allBlocksToProcess.push({
                block: block,
                row: row,
                col: col
              });
              console.log(`✅ 添加方块到处理列表: [${row}, ${col}]`);
            } else {
              console.warn(`❌ 方块不匹配 [${row}, ${col}]: 网格中的方块与解体方块不同`);
            }
          } else {
            console.log(`❌ 方块 [${row}, ${col}] 被跳过: 不在有效列或已被移除`);
          }
        }
      }
    } else {
      // 否则处理指定列中的所有方块
      for (const col of validColumns) {
        for (let row = this.gridData.rows - 1; row >= 0; row--) {
          const block = this.gridData.getBlock(row, col);
          if (block && !removedPositionSet.has(`${row},${col}`)) {
            allBlocksToProcess.push({
              block: block,
              row: row,
              col: col
            });
          }
        }
      }
    }

    // 特殊处理：寻找连续方块链
    const additionalBlocks = [];
    for (const col of validColumns) {
      const chainBlocks = this._findContinuousBlockChainAbove(col, this.gridData.rows - 1);
      for (const chainBlock of chainBlocks) {
        if (!removedPositionSet.has(`${chainBlock.row},${chainBlock.col}`) &&
            !allBlocksToProcess.find(b => b.row === chainBlock.row && b.col === chainBlock.col)) {
          additionalBlocks.push(chainBlock);
        }
      }
    }
    
    allBlocksToProcess.push(...additionalBlocks);
    
    return allBlocksToProcess;
  }

  /**
   * 查找连续方块链
   * @param {number} col - 列索引
   * @param {number} startRow - 开始行
   * @returns {Array<{block: Block, row: number, col: number}>} 连续方块链
   */
  _findContinuousBlockChainAbove(col, startRow) {
    const chain = [];
    
    for (let row = startRow; row >= 0; row--) {
      const block = this.gridData.getBlock(row, col);
      if (block) {
        chain.push({
          block: block,
          row: row,
          col: col
        });
      } else {
        break; // 遇到空位置，链条断开
      }
    }
    
    return chain;
  }

  /**
   * 应用重力到特定方块
   * @param {number[]} validColumns - 有效的列
   * @param {Array<{block: Block, row: number, col: number}>} blocksToProcess - 需要处理的方块
   * @returns {boolean} 是否有方块下落
   */
  _applyGravityToSpecificBlocks(validColumns, blocksToProcess) {
    console.log('🎯 开始应用重力到特定方块');
    console.log('🎯 参数:', {
      validColumns,
      blocksToProcessCount: blocksToProcess.length,
      blocks: blocksToProcess.map(b => `[${b.row}, ${b.col}]`)
    });

    let hasFallen = false;

    // 按列分组处理方块
    const blocksByColumn = {};
    for (const blockInfo of blocksToProcess) {
      const col = blockInfo.col;
      if (!blocksByColumn[col]) {
        blocksByColumn[col] = [];
      }
      blocksByColumn[col].push(blockInfo);
    }

    console.log('🎯 按列分组结果:', Object.keys(blocksByColumn).map(col =>
      `列${col}: ${blocksByColumn[col].length}个方块`
    ));
    
    // 对每一列进行重力处理
    for (const col of validColumns) {
      const columnBlocks = blocksByColumn[col] || [];

      console.log(`🎯 处理列 ${col}: ${columnBlocks.length} 个方块`);

      if (columnBlocks.length === 0) {
        console.log(`🎯 列 ${col} 没有方块，跳过`);
        continue;
      }

      // 按行索引从下到上排序
      columnBlocks.sort((a, b) => b.row - a.row);
      console.log(`🎯 列 ${col} 方块排序后:`, columnBlocks.map(b => `[${b.row}, ${b.col}]`));

      // 找到底部的空位置开始填充
      let targetRow = this.gridData.rows - 1;

      // 找到最底部的空位置
      while (targetRow >= 0 && this.gridData.getBlock(targetRow, col) !== null) {
        targetRow--;
      }

      console.log(`🎯 列 ${col} 最底部空位置: ${targetRow}`);

      // 如果没有空位置，跳过这一列
      if (targetRow < 0) {
        console.log(`🎯 列 ${col} 没有空位置，跳过`);
        continue;
      }
      
      // 从底部开始重新放置方块
      console.log(`🎯 开始重新放置列 ${col} 的方块，起始目标位置: ${targetRow}`);
      for (const blockInfo of columnBlocks) {
        const { block, row } = blockInfo;

        console.log(`🎯 处理方块 [${row}, ${col}] → 目标位置 [${targetRow}, ${col}]`);

        // 如果方块需要移动
        if (row !== targetRow) {
          console.log(`🎯 方块需要移动: [${row}, ${col}] → [${targetRow}, ${col}]`);

          // 检查目标位置是否真的为空
          const targetBlock = this.gridData.getBlock(targetRow, col);
          if (targetBlock !== null) {
            console.error(`❌ 目标位置 [${targetRow}, ${col}] 不为空！有方块:`, targetBlock);
            break;
          }

          // 清除原位置
          this.gridData.removeBlock(row, col);
          console.log(`🎯 清除原位置 [${row}, ${col}]`);

          // 放置到新位置
          this.gridData.setBlock(targetRow, col, block);
          console.log(`🎯 放置到新位置 [${targetRow}, ${col}]`);

          console.log(`✅ 重力作用：方块从 (${row}, ${col}) 移动到 (${targetRow}, ${col})`);
          hasFallen = true;
        } else {
          console.log(`🎯 方块 [${row}, ${col}] 已在正确位置，无需移动`);
        }

        // 下一个目标位置向上移动
        targetRow--;
        console.log(`🎯 下一个目标位置: ${targetRow}`);

        // 如果已经没有空位置，停止处理
        if (targetRow < 0) {
          console.log(`🎯 没有更多空位置，停止处理列 ${col}`);
          break;
        }
      }
    }

    console.log(`🎯 重力处理完成，结果: ${hasFallen ? '有方块下落' : '无方块下落'}`);
    return hasFallen;
  }

  /**
   * 检查方块是否悬浮（使用三点支撑检测）
   * 只有当左下、正下、右下三个位置都没有支撑时，才认为是严重悬空
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {boolean} 是否悬浮
   */
  _isBlockFloating(row, col) {
    const block = this.gridData.getBlock(row, col);
    if (!block) return false;

    // 如果已经在底行，不算悬空
    if (row === this.gridData.rows - 1) return false;

    // 检查三个支撑点：左下、正下、右下
    const supportPositions = [
      { row: row + 1, col: col - 1 }, // 左下
      { row: row + 1, col: col },     // 正下
      { row: row + 1, col: col + 1 }  // 右下
    ];

    let hasSupport = false;

    for (const pos of supportPositions) {
      // 如果位置超出墙外，算作没有支撑
      if (pos.col < 0 || pos.col >= this.gridData.cols) {
        continue; // 墙外位置，跳过
      }

      // 如果超出底部，算作有支撑（地面支撑）
      if (pos.row >= this.gridData.rows) {
        hasSupport = true;
        break;
      }

      // 检查该位置是否有方块支撑
      const supportBlock = this.gridData.getBlock(pos.row, pos.col);
      if (supportBlock) {
        hasSupport = true;
        break;
      }
    }

    // 如果三个位置都没有支撑，且下方还有空位可以下落，则认为是悬空
    if (!hasSupport) {
      // 进一步检查：确认下方确实有空位可以下落
      for (let checkRow = row + 1; checkRow < this.gridData.rows; checkRow++) {
        if (!this.gridData.getBlock(checkRow, col)) {
          console.log(`发现严重悬空方块 [${row}, ${col}]：左下、正下、右下都无支撑`);
          return true; // 确认是严重悬空
        }
      }
    }

    return false;
  }

  /**
   * 计算方块的下落距离
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {number} 下落距离
   */
  _calculateFallDistance(row, col) {
    const block = this.gridData.getBlock(row, col);
    
    if (!block) {
      return 0; // 没有方块，下落距离为0
    }
    
    let fallDistance = 0;
    let checkRow = row + 1;
    
    // 向下检查直到遇到障碍物或边界
    while (checkRow < this.gridData.rows && this.gridData.getBlock(checkRow, col) === null) {
      fallDistance++;
      checkRow++;
    }
    
    return fallDistance;
  }

  /**
   * 获取所有悬浮的方块
   * @returns {Array<{row: number, col: number, block: Block}>} 悬浮方块列表
   */
  getFloatingBlocks() {
    const floatingBlocks = [];
    
    for (let row = 0; row < this.gridData.rows - 1; row++) { // 最底行不可能悬浮
      for (let col = 0; col < this.gridData.cols; col++) {
        if (this._isBlockFloating(row, col)) {
          const block = this.gridData.getBlock(row, col);
          floatingBlocks.push({
            row: row,
            col: col,
            block: block
          });
        }
      }
    }
    
    return floatingBlocks;
  }

  /**
   * 执行智能重力检查
   * @returns {boolean} 是否有变化
   */
  performSmartGravityCheck() {
    console.log('🧠 执行智能重力检查');

    let hasChanges = false;
    let iterations = 0;
    const maxIterations = 10;

    while (iterations < maxIterations) {
      const beforeBlockCount = this.gridData.getBlockCount();

      // 应用全网格重力
      const hadFall = this.applyFullGridGravity();

      const afterBlockCount = this.gridData.getBlockCount();

      if (!hadFall && beforeBlockCount === afterBlockCount) {
        break; // 没有变化，结束
      }

      hasChanges = true;
      iterations++;
      console.log(`智能重力检查第 ${iterations} 轮完成`);
    }

    if (iterations >= maxIterations) {
      console.warn('智能重力检查达到最大迭代次数');
    }

    return hasChanges;
  }

  /**
   * 检测并处理整行消除的平移重力
   * @param {Array<{row: number, col: number}>} removedPositions - 被消除的方块位置
   * @returns {boolean} 是否处理了整行消除
   */
  _detectAndHandleFullRowClear(removedPositions) {
    // 统计每行被消除的方块数量
    const rowCounts = {};
    for (const { row } of removedPositions) {
      rowCounts[row] = (rowCounts[row] || 0) + 1;
    }

    // 找出完整被消除的行
    const fullRowsRemoved = [];
    for (const [row, count] of Object.entries(rowCounts)) {
      if (count === this.gridData.cols) {
        fullRowsRemoved.push(parseInt(row));
      }
    }

    if (fullRowsRemoved.length === 0) {
      return false; // 没有整行被消除
    }

    console.log(`检测到 ${fullRowsRemoved.length} 个完整行被消除:`, fullRowsRemoved);

    // 使用多行下移算法处理
    return this._handleMultipleRowClear(fullRowsRemoved);
  }

  /**
   * 处理多行消除后的方块下移（重构前的算法）
   * @param {Array<number>} emptyRows - 空行数组
   * @returns {boolean} 是否有方块下移
   */
  _handleMultipleRowClear(emptyRows) {
    if (emptyRows.length === 0) return false;

    console.log('🔄 处理多行消除，空行:', emptyRows);

    // 排序空行，从上到下
    emptyRows.sort((a, b) => a - b);

    let hasFallen = false;

    // 从下往上遍历行，避免连锁错误移动
    for (let row = this.gridData.rows - 1; row >= 0; row--) {
      // 计算当前行下方有多少个空行
      const emptyRowsBelowCount = emptyRows.filter(emptyRow => emptyRow > row).length;

      // 如果下方有空行，且当前行不是空行，则需要下移
      if (emptyRowsBelowCount > 0 && !emptyRows.includes(row)) {
        // 检查当前行是否有方块需要移动
        let hasBlocksInRow = false;
        for (let col = 0; col < this.gridData.cols; col++) {
          if (this.gridData.getBlock(row, col)) {
            hasBlocksInRow = true;
            break;
          }
        }

        if (hasBlocksInRow) {
          // 移动整行方块
          const targetRow = row + emptyRowsBelowCount;
          console.log(`行平移：第${row}行 → 第${targetRow}行，下移${emptyRowsBelowCount}行`);

          // 确保目标行为空
          let targetRowEmpty = true;
          for (let col = 0; col < this.gridData.cols; col++) {
            if (this.gridData.getBlock(targetRow, col)) {
              targetRowEmpty = false;
              console.error(`目标行${targetRow}列${col}不为空！`);
              break;
            }
          }

          if (!targetRowEmpty) {
            console.error(`跳过移动第${row}行，目标行${targetRow}不为空`);
            continue;
          }

          // 移动整行的所有方块
          for (let col = 0; col < this.gridData.cols; col++) {
            const block = this.gridData.getBlock(row, col);
            if (block) {
              // 移除原位置
              this.gridData.removeBlock(row, col);

              // 放置到新位置
              this.gridData.setBlock(targetRow, col, block);

              // 添加下落动画（如果网格支持）
              if (this.gridData.addFallingAnimation) {
                this.gridData.addFallingAnimation(block, row, col, targetRow, col);
              }

              hasFallen = true;
            }
          }
        }
      }
    }

    return hasFallen;
  }

  /**
   * 处理部分行消除后的下落逻辑
   * @param {Array<{row: number, col: number}>} removedPositions - 被移除的方块位置
   * @returns {boolean} 是否有方块下落
   */
  _handlePartialRowClear(removedPositions) {
    console.log('🔄 处理部分行消除，被移除位置:', removedPositions.length);

    // 按列分组被移除的方块
    const removedByColumn = {};
    for (const position of removedPositions) {
      if (position && typeof position.col === 'number' && typeof position.row === 'number') {
        if (!removedByColumn[position.col]) {
          removedByColumn[position.col] = [];
        }
        removedByColumn[position.col].push(position.row);
      }
    }

    let hasFallen = false;

    // 对每个受影响的列进行处理
    for (const colStr of Object.keys(removedByColumn)) {
      const col = parseInt(colStr);
      const removedRows = removedByColumn[col];

      console.log(`处理列${col}，被移除的行:`, removedRows);

      // 对这列进行下落处理
      const columnHasFallen = this._handleColumnDrop(col, removedRows);
      if (columnHasFallen) {
        hasFallen = true;
      }
    }

    return hasFallen;
  }

  /**
   * 处理单列的方块下落
   * @param {number} col - 列索引
   * @param {Array<number>} removedRows - 该列中被移除的行
   * @returns {boolean} 是否有方块下落
   */
  _handleColumnDrop(col, removedRows) {
    console.log(`🔄 处理列${col}下落，移除的行:`, removedRows);

    // 对移除的行进行排序，从下到上处理
    const sortedRemovedRows = [...removedRows].sort((a, b) => b - a);
    let hasFallen = false;

    // 对每个被移除的位置，让其上方的方块下落
    for (const removedRow of sortedRemovedRows) {
      // 从被移除位置的上方开始，向上查找需要下落的方块
      for (let sourceRow = removedRow - 1; sourceRow >= 0; sourceRow--) {
        const block = this.gridData.getBlock(sourceRow, col);

        if (block) {
          // 找到这个方块可以下落到的最低位置
          const targetRow = this._findLowestAvailableRowInColumn(col, sourceRow);

          if (targetRow > sourceRow) {
            console.log(`列${col}下落：第${sourceRow}行 → 第${targetRow}行`);

            // 移除原位置
            this.gridData.removeBlock(sourceRow, col);

            // 放置到新位置
            this.gridData.setBlock(targetRow, col, block);

            // 添加下落动画（如果网格支持）
            if (this.gridData.addFallingAnimation) {
              this.gridData.addFallingAnimation(block, sourceRow, col, targetRow, col);
            }

            hasFallen = true;
          }
        }
      }
    }

    return hasFallen;
  }

  /**
   * 在指定列中找到方块可以下落到的最低位置
   * @param {number} col - 列索引
   * @param {number} sourceRow - 源行位置
   * @returns {number} 目标行位置
   */
  _findLowestAvailableRowInColumn(col, sourceRow) {
    // 从底部开始向上查找第一个空位置
    for (let targetRow = this.gridData.rows - 1; targetRow > sourceRow; targetRow--) {
      if (!this.gridData.getBlock(targetRow, col)) {
        return targetRow;
      }
    }

    // 如果没有找到空位置，返回原位置（不移动）
    return sourceRow;
  }

  /**
   * 全局悬空方块检测和处理（兜底机制）
   * 定期检查整个网格中的悬空方块并让它们下落
   * @returns {boolean} 是否有悬空方块被处理
   */
  detectAndHandleFloatingBlocks() {
    console.log('🔍 开始全局悬空方块检测');

    const floatingBlocks = [];

    // 从下往上扫描整个网格，寻找悬空方块
    for (let row = this.gridData.rows - 2; row >= 0; row--) { // 从倒数第二行开始
      for (let col = 0; col < this.gridData.cols; col++) {
        const block = this.gridData.getBlock(row, col);

        if (block && this._isBlockFloating(row, col)) {
          floatingBlocks.push({
            block,
            row: Number(row),    // 确保是数字
            col: Number(col)     // 确保是数字
          });
          console.log(`发现悬空方块 [${row}, ${col}]`);
        }
      }
    }

    if (floatingBlocks.length === 0) {
      console.log('✅ 未发现悬空方块');
      return false;
    }

    console.log(`🎯 发现${floatingBlocks.length}个悬空方块，开始一次性计算最终位置`);

    // 一次性计算所有方块的最终位置
    const movePlan = this._calculateOptimalFloatingBlockPositions(floatingBlocks);

    if (movePlan.length === 0) {
      console.log('❌ 未找到有效的移动方案');
      return false;
    }

    // 一次性执行所有移动并启动动画
    return this._executeOptimalMovePlan(movePlan);
  }

  /**
   * 计算悬空方块的最优移动方案
   * 一次性计算所有方块的最终位置，避免冲突
   * @param {Array} floatingBlocks - 悬空方块列表
   * @returns {Array} 移动计划
   */
  _calculateOptimalFloatingBlockPositions(floatingBlocks) {
    console.log('🎯 开始计算最优移动方案...');

    // 创建网格状态快照，用于计算
    const gridSnapshot = this._createGridSnapshot();

    // 按列分组悬空方块
    const blocksByColumn = {};
    for (const blockInfo of floatingBlocks) {
      const { col } = blockInfo;
      if (!blocksByColumn[col]) {
        blocksByColumn[col] = [];
      }
      blocksByColumn[col].push(blockInfo);
    }

    // 对每列的方块按从下到上排序（重要：保证下落顺序）
    for (const col in blocksByColumn) {
      blocksByColumn[col].sort((a, b) => b.row - a.row);
    }

    const movePlan = [];

    // 逐列计算每个方块的最终位置
    for (const col in blocksByColumn) {
      const colIndex = parseInt(col, 10);
      const columnBlocks = blocksByColumn[col];
      console.log(`📊 处理第${colIndex}列的${columnBlocks.length}个悬空方块`);

      for (const blockInfo of columnBlocks) {
        const { block, row } = blockInfo;

        // 在快照中移除当前方块（模拟它开始下落）
        gridSnapshot[row][colIndex] = null;

        // 计算最终位置
        const targetRow = this._findLowestPositionInSnapshot(gridSnapshot, row, colIndex);

        if (targetRow > row) {
          // 记录移动计划
          movePlan.push({
            block,
            fromRow: row,
            fromCol: colIndex,
            toRow: targetRow,
            toCol: colIndex
          });

          // 在快照中放置方块到新位置（影响后续计算）
          gridSnapshot[targetRow][colIndex] = block;

          console.log(`📋 计划移动：[${row}, ${colIndex}] -> [${targetRow}, ${colIndex}]`);
        } else {
          // 方块无需移动，放回快照
          gridSnapshot[row][colIndex] = block;
          console.log(`📋 方块[${row}, ${colIndex}]无需移动`);
        }
      }
    }

    console.log(`🎯 移动方案计算完成，共${movePlan.length}个方块需要移动`);
    return movePlan;
  }

  /**
   * 创建网格状态快照
   * @returns {Array} 网格快照
   */
  _createGridSnapshot() {
    const snapshot = [];
    for (let row = 0; row < this.gridData.rows; row++) {
      snapshot[row] = [];
      for (let col = 0; col < this.gridData.cols; col++) {
        snapshot[row][col] = this.gridData.getBlock(row, col);
      }
    }
    return snapshot;
  }

  /**
   * 在网格快照中找到最低可放置位置
   * @param {Array} gridSnapshot - 网格快照
   * @param {number} currentRow - 当前行
   * @param {number} col - 列
   * @returns {number} 最低可放置的行索引
   */
  _findLowestPositionInSnapshot(gridSnapshot, currentRow, col) {
    // 从底部向上查找第一个空位
    for (let targetRow = this.gridData.rows - 1; targetRow > currentRow; targetRow--) {
      if (gridSnapshot[targetRow][col] === null) {
        return targetRow;
      }
    }

    // 如果没有找到空位，返回当前位置
    return currentRow;
  }

  /**
   * 执行最优移动方案
   * 同时移动所有方块并启动动画，实现自然的统一下落效果
   * @param {Array} movePlan - 移动计划
   * @returns {boolean} 是否成功执行
   */
  _executeOptimalMovePlan(movePlan) {
    console.log(`🚀 开始执行移动方案，共${movePlan.length}个方块同时下落`);

    let successCount = 0;
    const animations = [];

    // 第一阶段：移动所有方块到新位置
    for (const move of movePlan) {
      const { block, fromRow, fromCol, toRow, toCol } = move;

      try {
        const numFromRow = Number(fromRow);
        const numFromCol = Number(fromCol);
        const numToRow = Number(toRow);
        const numToCol = Number(toCol);

        // 验证转换后的数字是否有效
        if (!Number.isFinite(numFromRow) || !Number.isFinite(numFromCol) ||
            !Number.isFinite(numToRow) || !Number.isFinite(numToCol)) {
          console.error(`❌ 无效的数字参数：fromRow=${fromRow}, fromCol=${fromCol}, toRow=${toRow}, toCol=${toCol}`);
          continue;
        }

        // 验证移动的有效性
        if (this.gridData.getBlock(numFromRow, numFromCol) !== block) {
          console.warn(`⚠️ 方块位置不匹配 [${numFromRow}, ${numFromCol}]`);
          continue;
        }

        if (this.gridData.getBlock(numToRow, numToCol) !== null) {
          console.warn(`⚠️ 目标位置 [${numToRow}, ${numToCol}] 不为空`);
          continue;
        }

        // 移除原位置
        this.gridData.removeBlock(numFromRow, numFromCol);

        // 放置到新位置
        this.gridData.setBlock(numToRow, numToCol, block);

        // 记录动画信息
        animations.push({
          block,
          fromRow: numFromRow,
          fromCol: numFromCol,
          toRow: numToRow,
          toCol: numToCol
        });

        successCount++;

      } catch (error) {
        console.error(`❌ 移动方块失败 [${fromRow}, ${fromCol}] -> [${toRow}, ${toCol}]:`, error);
      }
    }

    // 第二阶段：统一创建所有下落动画
    console.log(`🎬 创建${animations.length}个同步下落动画`);

    for (const anim of animations) {
      const { block, fromRow, fromCol, toRow, toCol } = anim;

      // 添加下落动画（如果网格支持）
      if (this.gridData.addFallingAnimation) {
        this.gridData.addFallingAnimation(block, fromRow, fromCol, toRow, toCol);
        console.log(`🎭 同步动画：[${fromRow}, ${fromCol}] -> [${toRow}, ${toCol}]`);
      }
    }

    console.log(`✅ 移动方案执行完成，成功移动${successCount}个方块，创建${animations.length}个同步动画`);

    return successCount > 0;
  }
}