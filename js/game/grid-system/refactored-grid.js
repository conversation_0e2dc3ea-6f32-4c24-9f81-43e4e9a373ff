/**
 * 重构后的Grid系统
 * 整合所有子系统并提供向后兼容的API
 */
import GridDataManager from './data/grid-data-manager.js';
import GravitySystem from './physics/gravity-system.js';
import GridAnimationSystem from './animation/grid-animation-system.js';
import GridRenderer from './rendering/grid-renderer.js';
import CoordinateUtils from './utils/coordinate-utils.js';

export default class RefactoredGrid {
  constructor() {
    // 初始化所有子系统
    this.dataManager = new GridDataManager();
    this.coordinateUtils = new CoordinateUtils(this.dataManager);
    this.gravitySystem = new GravitySystem(this.dataManager);
    this.animationSystem = new GridAnimationSystem(this.dataManager, this.coordinateUtils);
    this.renderer = new GridRenderer(this.dataManager, this.coordinateUtils);

    // 设置系统间的引用关系
    this.dataManager.setAnimationSystem(this.animationSystem);

    console.log('🎮 RefactoredGrid 系统初始化完成');
    console.log(`网格大小: ${this.dataManager.rows}x${this.dataManager.cols}`);
    console.log(`方块大小: ${this.dataManager.blockSize}px`);
  }

  // =================== 向后兼容API ===================

  /**
   * 检查指定位置是否有效
   */
  isValidPosition(row, col) {
    return this.dataManager.isValidPosition(row, col);
  }

  /**
   * 在网格上放置方块
   */
  placeBlock(block, row, col) {
    return this.dataManager.placeBlock(block, row, col);
  }

  /**
   * 移除指定位置的方块
   */
  removeBlock(row, col) {
    return this.dataManager.removeBlock(row, col);
  }

  /**
   * 获取指定位置的方块
   */
  getBlock(row, col) {
    return this.dataManager.getBlock(row, col);
  }

  /**
   * 检查一行是否已满
   */
  isRowFull(row) {
    return this.dataManager.isRowFull(row);
  }

  /**
   * 检查一行是否为空
   */
  isRowEmpty(row) {
    return this.dataManager.isRowEmpty(row);
  }

  /**
   * 清除指定的行
   */
  clearRow(row) {
    return this.dataManager.clearRow(row);
  }

  /**
   * 网格坐标转屏幕坐标
   */
  gridToScreen(row, col) {
    return this.coordinateUtils.gridToScreen(row, col);
  }

  /**
   * 屏幕坐标转网格坐标
   */
  screenToGrid(x, y) {
    return this.coordinateUtils.screenToGrid(x, y);
  }

  /**
   * 应用重力
   */
  applyGravity(columnsToCheck = null, blocksToCheck = null, removedPositions = []) {
    return this.gravitySystem.applyGravity(columnsToCheck, blocksToCheck, removedPositions);
  }

  /**
   * 应用全网格重力
   */
  applyFullGridGravity() {
    return this.gravitySystem.applyFullGridGravity();
  }

  /**
   * 添加下落动画
   */
  addFallingAnimation(block, startRow, startCol, endRow, endCol) {
    return this.animationSystem.addFallingAnimation(block, startRow, startCol, endRow, endCol);
  }

  /**
   * 更新动画
   */
  updateAnimations() {
    return this.animationSystem.updateAnimations();
  }

  /**
   * 渲染网格
   */
  render(ctx) {
    // 渲染网格和方块
    this.renderer.render(ctx);
    
    // 渲染动画
    this.animationSystem.renderAnimations(ctx);
  }

  /**
   * 处理方块效果
   */
  handleBlockEffect(block, row, col) {
    if (!block || !block.type) return;

    switch (block.type) {
      case 'bomb':
        this.createExplosionAnimation(row, col, 2);
        break;
      case 'fireball':
        this.createFireballAnimation(row, col, 3);
        break;
      case 'lightning':
        const path = this._generateLightningPath(row, col);
        this.createLightningAnimation(path);
        break;
    }
  }

  /**
   * 创建爆炸动画
   */
  createExplosionAnimation(row, col, range) {
    return this.animationSystem.createExplosionAnimation(row, col, range);
  }

  /**
   * 创建火球动画
   */
  createFireballAnimation(row, col, range) {
    return this.animationSystem.createFireballAnimation(row, col, range);
  }

  /**
   * 创建闪电动画
   */
  createLightningAnimation(path) {
    return this.animationSystem.createLightningAnimation(path);
  }

  /**
   * 行下移操作
   */
  shiftRowsDown(clearedRow) {
    return this.dataManager.shiftRowsDown(clearedRow);
  }

  /**
   * 移动方块
   */
  moveBlock(fromRow, fromCol, toRow, toCol) {
    return this.dataManager.moveBlock(fromRow, fromCol, toRow, toCol);
  }

  /**
   * 调试网格状态
   */
  debugGridState(label = '网格状态', detailed = true) {
    return this.dataManager.debugGridState(label, detailed);
  }

  // =================== 新增增强API ===================

  /**
   * 获取网格数据管理器
   */
  getDataManager() {
    return this.dataManager;
  }

  /**
   * 获取重力系统
   */
  getGravitySystem() {
    return this.gravitySystem;
  }

  /**
   * 获取动画系统
   */
  getAnimationSystem() {
    return this.animationSystem;
  }

  /**
   * 获取渲染器
   */
  getRenderer() {
    return this.renderer;
  }

  /**
   * 获取坐标工具
   */
  getCoordinateUtils() {
    return this.coordinateUtils;
  }

  /**
   * 批量操作：移动多个方块
   */
  batchMoveBlocks(moves) {
    let success = true;
    for (const move of moves) {
      const result = this.dataManager.moveBlock(
        move.fromRow, move.fromCol, 
        move.toRow, move.toCol
      );
      if (!result) {
        success = false;
      }
    }
    return success;
  }

  /**
   * 智能重力处理
   */
  performSmartGravity() {
    return this.gravitySystem.performSmartGravityCheck();
  }

  /**
   * 获取悬浮方块
   */
  getFloatingBlocks() {
    return this.gravitySystem.getFloatingBlocks();
  }

  /**
   * 检测并处理悬空方块
   */
  detectAndHandleFloatingBlocks() {
    return this.gravitySystem.detectAndHandleFloatingBlocks();
  }

  /**
   * 设置渲染选项
   */
  setRenderOptions(options) {
    return this.renderer.setRenderOptions(options);
  }

  /**
   * 获取网格统计信息
   */
  getGridStats() {
    return {
      dataManager: {
        totalBlocks: this.dataManager.getBlockCount(),
        gridSize: `${this.dataManager.rows}x${this.dataManager.cols}`,
        blockSize: this.dataManager.blockSize
      },
      gravitySystem: {
        floatingBlocks: this.gravitySystem.getFloatingBlocks().length
      },
      animationSystem: {
        activeAnimations: this.animationSystem.getAnimationCount(),
        hasAnimations: this.animationSystem.hasActiveAnimations()
      },
      renderer: this.renderer.getRenderStats()
    };
  }

  /**
   * 清空网格和所有系统
   */
  reset() {
    this.dataManager.clear();
    this.animationSystem.clearAnimations();
    console.log('🔄 Grid系统已重置');
  }

  /**
   * 检查系统健康状态
   */
  healthCheck() {
    const stats = this.getGridStats();
    const issues = [];

    // 检查悬浮方块
    if (stats.gravitySystem.floatingBlocks > 0) {
      issues.push(`发现 ${stats.gravitySystem.floatingBlocks} 个悬浮方块`);
    }

    // 检查过多动画
    if (stats.animationSystem.activeAnimations > 50) {
      issues.push(`动画数量过多: ${stats.animationSystem.activeAnimations}`);
    }

    return {
      healthy: issues.length === 0,
      issues: issues,
      stats: stats
    };
  }

  // =================== 私有辅助方法 ===================

  /**
   * 生成闪电路径
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @returns {Array} 闪电路径
   * @private
   */
  _generateLightningPath(startRow, startCol) {
    const path = [];
    let currentRow = startRow;
    let currentCol = startCol;

    // 向下闪电路径
    while (currentRow < this.dataManager.rows - 1) {
      path.push({ row: currentRow, col: currentCol });
      currentRow++;
      
      // 随机左右偏移
      if (Math.random() < 0.3) {
        currentCol += Math.random() < 0.5 ? -1 : 1;
        currentCol = Math.max(0, Math.min(currentCol, this.dataManager.cols - 1));
      }
    }

    path.push({ row: currentRow, col: currentCol });
    return path;
  }

  // =================== Getter Properties ===================

  /**
   * 获取网格行数
   */
  get rows() {
    return this.dataManager.rows;
  }

  /**
   * 获取网格列数
   */
  get cols() {
    return this.dataManager.cols;
  }

  /**
   * 获取方块大小
   */
  get blockSize() {
    return this.dataManager.blockSize;
  }

  /**
   * 获取网格偏移X
   */
  get offsetX() {
    return this.dataManager.offsetX;
  }

  /**
   * 获取网格偏移Y
   */
  get offsetY() {
    return this.dataManager.offsetY;
  }

  /**
   * 获取网格矩阵（只读）
   */
  get matrix() {
    return this.dataManager.getMatrixCopy();
  }
} 