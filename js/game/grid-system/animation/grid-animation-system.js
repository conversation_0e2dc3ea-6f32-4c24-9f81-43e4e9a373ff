/**
 * 网格动画系统
 * 负责处理下落动画和特效动画
 */
import {
  calculateFallDuration,
  applyEasing,
  calculateGhostAlpha,
  FALLING_ANIMATION_CONFIG
} from '../../../config/animation-config.js';
import { animationDebug } from '../../../debug/animation-debug.js';

export default class GridAnimationSystem {
  constructor(gridDataManager, coordinateUtils) {
    this.gridData = gridDataManager;
    this.coordinateUtils = coordinateUtils;
    
    // 动画列表
    this.animations = [];
  }

  /**
   * 添加下落动画
   * @param {Block} block - 下落的方块
   * @param {number} startRow - 起始行
   * @param {number} startCol - 起始列
   * @param {number} endRow - 结束行
   * @param {number} endCol - 结束列
   */
  addFallingAnimation(block, startRow, startCol, endRow, endCol) {
    if (!block || startRow === endRow) {
      return; // 没有方块或没有移动，不需要动画
    }

    const startPos = this.coordinateUtils.gridToScreen(startRow, startCol);
    const endPos = this.coordinateUtils.gridToScreen(endRow, endCol);
    const distance = Math.abs(endRow - startRow);
    
    // 计算动画持续时间
    const duration = calculateFallDuration(distance);
    
    const animation = {
      type: 'falling',
      block: block,
      startPos: startPos,
      endPos: endPos,
      currentPos: { ...startPos },
      startTime: Date.now(),
      duration: duration,
      progress: 0,
      isActive: true,
      startRow: startRow,
      startCol: startCol,
      endRow: endRow,
      endCol: endCol
    };

    this.animations.push(animation);
    
    // 设置方块的动画状态
    if (block.setAnimationState) {
      block.setAnimationState('falling', {
        startPos: startPos,
        endPos: endPos,
        duration: duration
      });
    }

    console.log(`添加下落动画: (${startRow}, ${startCol}) → (${endRow}, ${endCol}), 持续时间: ${duration}ms`);
  }

  /**
   * 更新所有动画
   */
  updateAnimations() {
    const currentTime = Date.now();
    
    // 更新所有动画
    for (let i = this.animations.length - 1; i >= 0; i--) {
      const animation = this.animations[i];
      
      if (!animation.isActive) {
        this.animations.splice(i, 1);
        continue;
      }
      
      // 计算动画进度
      const elapsed = currentTime - animation.startTime;
      animation.progress = Math.min(elapsed / animation.duration, 1);
      
      // 更新动画
      this._updateAnimation(animation);
      
      // 检查动画是否完成
      if (animation.progress >= 1) {
        this._finishAnimation(animation);
        this.animations.splice(i, 1);
      }
    }
  }

  /**
   * 更新单个动画
   * @param {Object} animation - 动画对象
   * @private
   */
  _updateAnimation(animation) {
    switch (animation.type) {
      case 'falling':
        this._updateFallingAnimation(animation);
        break;
      case 'explosion':
        this._updateExplosionAnimation(animation);
        break;
      case 'fireball':
        this._updateFireballAnimation(animation);
        break;
      case 'lightning':
        this._updateLightningAnimation(animation);
        break;
    }
  }

  /**
   * 更新下落动画
   * @param {Object} animation - 下落动画对象
   * @private
   */
  _updateFallingAnimation(animation) {
    const easedProgress = applyEasing(animation.progress, 'easeInQuad');
    
    // 更新当前位置
    animation.currentPos.x = animation.startPos.x + 
      (animation.endPos.x - animation.startPos.x) * easedProgress;
    animation.currentPos.y = animation.startPos.y + 
      (animation.endPos.y - animation.startPos.y) * easedProgress;
    
    // 更新方块的渲染位置
    if (animation.block && animation.block.setRenderPosition) {
      animation.block.setRenderPosition(animation.currentPos.x, animation.currentPos.y);
    }
  }

  /**
   * 完成动画
   * @param {Object} animation - 动画对象
   * @private
   */
  _finishAnimation(animation) {
    switch (animation.type) {
      case 'falling':
        this._finishFallingAnimation(animation);
        break;
    }
  }

  /**
   * 完成下落动画
   * @param {Object} animation - 下落动画对象
   * @private
   */
  _finishFallingAnimation(animation) {
    // 重置方块的渲染状态
    if (animation.block && animation.block.clearAnimationState) {
      animation.block.clearAnimationState();
    }
    
    console.log(`下落动画完成: (${animation.startRow}, ${animation.startCol}) → (${animation.endRow}, ${animation.endCol})`);
  }

  /**
   * 创建爆炸动画
   * @param {number} row - 爆炸中心行
   * @param {number} col - 爆炸中心列
   * @param {number} range - 爆炸范围
   */
  createExplosionAnimation(row, col, range) {
    const centerPos = this.coordinateUtils.gridToScreen(row, col);
    const animation = {
      type: 'explosion',
      centerPos: centerPos,
      range: range,
      startTime: Date.now(),
      duration: 500, // 500ms动画
      progress: 0,
      isActive: true,
      particles: this._generateExplosionParticles(centerPos, range)
    };

    this.animations.push(animation);
    console.log(`创建爆炸动画: 中心(${row}, ${col}), 范围: ${range}`);
  }

  /**
   * 生成爆炸粒子
   * @param {Object} centerPos - 中心位置
   * @param {number} range - 爆炸范围
   * @returns {Array} 粒子数组
   * @private
   */
  _generateExplosionParticles(centerPos, range) {
    const particles = [];
    const particleCount = range * 8; // 每个范围8个粒子
    
    for (let i = 0; i < particleCount; i++) {
      const angle = (i / particleCount) * Math.PI * 2;
      const speed = Math.random() * 100 + 50; // 随机速度
      const size = Math.random() * 6 + 2; // 随机大小
      
      particles.push({
        x: centerPos.x,
        y: centerPos.y,
        vx: Math.cos(angle) * speed,
        vy: Math.sin(angle) * speed,
        size: size,
        life: 1.0,
        decay: Math.random() * 0.02 + 0.01
      });
    }
    
    return particles;
  }

  /**
   * 更新爆炸动画
   * @param {Object} animation - 爆炸动画对象
   * @private
   */
  _updateExplosionAnimation(animation) {
    // 更新粒子
    for (const particle of animation.particles) {
      particle.x += particle.vx * 0.016; // 假设60fps
      particle.y += particle.vy * 0.016;
      particle.life -= particle.decay;
      
      // 添加重力效果
      particle.vy += 200 * 0.016;
    }
    
    // 移除死亡的粒子
    animation.particles = animation.particles.filter(p => p.life > 0);
  }

  /**
   * 创建火球动画
   * @param {number} row - 火球中心行
   * @param {number} col - 火球中心列
   * @param {number} range - 火球范围
   */
  createFireballAnimation(row, col, range) {
    const centerPos = this.coordinateUtils.gridToScreen(row, col);
    const animation = {
      type: 'fireball',
      centerPos: centerPos,
      range: range,
      startTime: Date.now(),
      duration: 800, // 800ms动画
      progress: 0,
      isActive: true,
      flames: this._generateFlameParticles(centerPos, range)
    };

    this.animations.push(animation);
    console.log(`创建火球动画: 中心(${row}, ${col}), 范围: ${range}`);
  }

  /**
   * 生成火焰粒子
   * @param {Object} centerPos - 中心位置
   * @param {number} range - 火球范围
   * @returns {Array} 火焰粒子数组
   * @private
   */
  _generateFlameParticles(centerPos, range) {
    const flames = [];
    const flameCount = range * 12; // 每个范围12个火焰粒子
    
    for (let i = 0; i < flameCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * range * this.gridData.blockSize;
      
      flames.push({
        x: centerPos.x + Math.cos(angle) * distance,
        y: centerPos.y + Math.sin(angle) * distance,
        size: Math.random() * 8 + 4,
        intensity: Math.random() * 0.8 + 0.2,
        flickerSpeed: Math.random() * 0.1 + 0.05
      });
    }
    
    return flames;
  }

  /**
   * 更新火球动画
   * @param {Object} animation - 火球动画对象
   * @private
   */
  _updateFireballAnimation(animation) {
    // 更新火焰粒子
    for (const flame of animation.flames) {
      flame.intensity = Math.sin(Date.now() * flame.flickerSpeed) * 0.3 + 0.7;
      flame.size += Math.sin(Date.now() * flame.flickerSpeed * 2) * 0.5;
    }
  }

  /**
   * 创建闪电动画
   * @param {Array} path - 闪电路径
   */
  createLightningAnimation(path) {
    const animation = {
      type: 'lightning',
      path: path,
      startTime: Date.now(),
      duration: 300, // 300ms动画
      progress: 0,
      isActive: true,
      segments: this._generateLightningSegments(path),
      branches: []
    };

    this.animations.push(animation);
    console.log(`创建闪电动画: 路径长度 ${path.length}`);
  }

  /**
   * 生成闪电段
   * @param {Array} path - 闪电路径
   * @returns {Array} 闪电段数组
   * @private
   */
  _generateLightningSegments(path) {
    const segments = [];
    
    for (let i = 0; i < path.length - 1; i++) {
      const start = this.coordinateUtils.gridToScreen(path[i].row, path[i].col);
      const end = this.coordinateUtils.gridToScreen(path[i + 1].row, path[i + 1].col);
      
      segments.push({
        start: start,
        end: end,
        intensity: Math.random() * 0.5 + 0.5,
        width: Math.random() * 3 + 2
      });
    }
    
    return segments;
  }

  /**
   * 更新闪电动画
   * @param {Object} animation - 闪电动画对象
   * @private
   */
  _updateLightningAnimation(animation) {
    // 更新闪电段的强度
    for (const segment of animation.segments) {
      segment.intensity = Math.random() * 0.8 + 0.2;
      segment.width = Math.random() * 2 + 1;
    }
  }

  /**
   * 渲染所有动画
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   */
  renderAnimations(ctx) {
    for (const animation of this.animations) {
      this._renderAnimation(ctx, animation);
    }
  }

  /**
   * 渲染单个动画
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Object} animation - 动画对象
   * @private
   */
  _renderAnimation(ctx, animation) {
    switch (animation.type) {
      case 'falling':
        this._renderFallingAnimation(ctx, animation);
        break;
      case 'explosion':
        this._renderExplosionAnimation(ctx, animation);
        break;
      case 'fireball':
        this._renderFireballAnimation(ctx, animation);
        break;
      case 'lightning':
        this._renderLightningAnimation(ctx, animation);
        break;
    }
  }

  /**
   * 渲染下落动画
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Object} animation - 下落动画对象
   * @private
   */
  _renderFallingAnimation(ctx, animation) {
    if (!animation.block || !animation.currentPos) return;

    ctx.save();

    // 添加轻微的透明度效果表示正在下落
    ctx.globalAlpha = 0.9;

    // 渲染下落中的方块
    if (animation.block.render) {
      animation.block.render(
        ctx,
        animation.currentPos.x,
        animation.currentPos.y,
        this.gridData.blockSize,
        true // 标记为下落中的方块
      );
    }

    ctx.restore();

    console.log(`📉 方块下落: (${animation.startRow}, ${animation.startCol}) → (${animation.endRow}, ${animation.endCol})`);
  }

  /**
   * 渲染爆炸动画
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Object} animation - 爆炸动画对象
   * @private
   */
  _renderExplosionAnimation(ctx, animation) {
    ctx.save();

    for (const particle of animation.particles) {
      ctx.globalAlpha = particle.life;
      ctx.fillStyle = `hsl(${Math.random() * 60}, 100%, 50%)`; // 随机橙红色
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      ctx.fill();
    }

    ctx.restore();
  }

  /**
   * 渲染火球动画
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Object} animation - 火球动画对象
   * @private
   */
  _renderFireballAnimation(ctx, animation) {
    ctx.save();
    
    for (const flame of animation.flames) {
      ctx.globalAlpha = flame.intensity;
      ctx.fillStyle = `hsl(${Math.random() * 30}, 100%, 50%)`; // 随机红橙色
      ctx.beginPath();
      ctx.arc(flame.x, flame.y, flame.size, 0, Math.PI * 2);
      ctx.fill();
    }
    
    ctx.restore();
  }

  /**
   * 渲染闪电动画
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   * @param {Object} animation - 闪电动画对象
   * @private
   */
  _renderLightningAnimation(ctx, animation) {
    ctx.save();
    
    for (const segment of animation.segments) {
      ctx.globalAlpha = segment.intensity;
      ctx.strokeStyle = '#00FFFF'; // 青色闪电
      ctx.lineWidth = segment.width;
      ctx.beginPath();
      ctx.moveTo(segment.start.x, segment.start.y);
      ctx.lineTo(segment.end.x, segment.end.y);
      ctx.stroke();
    }
    
    ctx.restore();
  }

  /**
   * 清除所有动画
   */
  clearAnimations() {
    this.animations = [];
  }

  /**
   * 获取动画数量
   * @returns {number} 当前活跃的动画数量
   */
  getAnimationCount() {
    return this.animations.length;
  }

  /**
   * 检查是否有动画在播放
   * @returns {boolean} 是否有动画在播放
   */
  hasActiveAnimations() {
    return this.animations.length > 0;
  }
} 