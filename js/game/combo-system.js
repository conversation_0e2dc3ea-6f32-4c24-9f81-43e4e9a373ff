/**
 * 连击积累系统
 * 实现"精心布局，瞬间收获"的核心机制
 */

export default class ComboSystem {
  constructor() {
    // 连击状态
    this.combo = 0;
    this.maxCombo = 0;
    this.comboTimer = 0;
    this.comboBufferTime = 300; // 5秒缓冲时间（60fps * 5）
    
    // 能量系统
    this.energy = 0;
    this.maxEnergy = 500;
    this.energyDecay = 0; // 能量衰减（暂时关闭）
    
    // 连击阶段定义（从第1个连击开始就有倍数）
    this.comboStages = [
      { min: 1, max: 3, name: "初级连击", multiplier: 1.2, color: "#4CAF50" },
      { min: 4, max: 7, name: "中级连击", multiplier: 1.8, color: "#FF9800" },
      { min: 8, max: 12, name: "高级连击", multiplier: 2.5, color: "#F44336" },
      { min: 13, max: 18, name: "超级连击", multiplier: 3.5, color: "#9C27B0" },
      { min: 19, max: 999, name: "传奇连击", multiplier: 5.0, color: "#FFD700" }
    ];
    
    // 能量等级定义
    this.energyLevels = [
      { threshold: 25, name: "小型爆发", multiplier: 2, effect: "small_burst" },
      { threshold: 50, name: "中型爆发", multiplier: 3, effect: "medium_burst" },
      { threshold: 100, name: "大型爆发", multiplier: 5, effect: "large_burst" },
      { threshold: 200, name: "超级爆发", multiplier: 8, effect: "super_burst" },
      { threshold: 500, name: "传奇爆发", multiplier: 15, effect: "legendary_burst" }
    ];
    
    // 布局模式定义
    this.layoutPatterns = {
      rainbow_stairs: {
        name: "彩虹阶梯",
        description: "不同颜色方块呈阶梯状排列",
        multiplier: 3.0,
        energyBonus: 20,
        detection: this._detectRainbowStairs.bind(this)
      },
      symmetric_garden: {
        name: "对称花园", 
        description: "左右对称的颜色布局",
        multiplier: 2.5,
        energyBonus: 15,
        detection: this._detectSymmetricGarden.bind(this)
      },
      color_layers: {
        name: "颜色分层",
        description: "相同颜色集中在特定区域",
        multiplier: 2.0,
        energyBonus: 10,
        detection: this._detectColorLayers.bind(this)
      },
      effect_chain: {
        name: "特效链条",
        description: "特效方块形成连锁反应",
        multiplier: 4.0,
        energyBonus: 30,
        detection: this._detectEffectChain.bind(this)
      }
    };
    
    // 耐心奖励
    this.patienceTimer = 0;
    this.patienceRewards = [
      { time: 1800, multiplier: 1.2, message: "耐心初现" }, // 30秒
      { time: 3600, multiplier: 1.5, message: "沉着冷静" }, // 60秒
      { time: 7200, multiplier: 2.0, message: "深谋远虑" }, // 120秒
      { time: 18000, multiplier: 3.0, message: "大师风范" } // 300秒
    ];
    
    // 事件监听器
    this.listeners = {};
    
    console.log('连击积累系统初始化完成');
  }
  
  /**
   * 添加连击
   * @param {number} matchCount - 本次匹配的方块数量
   * @param {Array} specialEffects - 触发的特殊效果
   */
  addCombo(matchCount = 3, specialEffects = []) {
    this.combo++;
    this.maxCombo = Math.max(this.maxCombo, this.combo);
    this.comboTimer = this.comboBufferTime; // 重置缓冲时间
    
    // 计算能量获得
    let energyGain = this._calculateEnergyGain(matchCount, specialEffects);
    this.addEnergy(energyGain);
    
    // 检查连击阶段变化
    const newStage = this.getCurrentComboStage();
    const oldStage = this.getCurrentComboStage(this.combo - 1);

    if (newStage && newStage !== oldStage) {
      this.emit('combo_stage_up', {
        stage: newStage,
        combo: this.combo,
        multiplier: newStage.multiplier
      });

      // 只在阶段升级时显示详细信息
      console.log(`🎯 连击阶段升级! ${this.combo}连击 → ${newStage.name} (${newStage.multiplier}x倍数)`);
    }

    // 触发连击事件
    this.emit('combo_added', {
      combo: this.combo,
      energyGain: energyGain,
      multiplier: this.getComboMultiplier(),
      stage: newStage
    });

    // 简化的连击日志
    if (this.combo % 5 === 0 || this.combo <= 3) {
      console.log(`🎯 连击: ${this.combo}, 能量: ${this.energy}/${this.maxEnergy}, 倍数: ${this.getComboMultiplier()}x`);
    }
  }
  
  /**
   * 添加能量
   * @param {number} amount - 能量数量
   */
  addEnergy(amount) {
    const oldEnergy = this.energy;
    this.energy = Math.min(this.maxEnergy, this.energy + amount);
    
    // 检查能量等级变化
    const newLevel = this.getCurrentEnergyLevel();
    const oldLevel = this.getCurrentEnergyLevel(oldEnergy);
    
    if (newLevel && newLevel !== oldLevel) {
      this.emit('energy_level_up', {
        level: newLevel,
        energy: this.energy,
        multiplier: newLevel.multiplier
      });
    }
    
    // 检查是否达到最大能量
    if (this.energy >= this.maxEnergy && oldEnergy < this.maxEnergy) {
      this.emit('energy_max_reached', {
        energy: this.energy,
        autoTrigger: true
      });
    }
  }
  
  /**
   * 触发能量爆发
   * @param {boolean} manual - 是否手动触发
   * @returns {Object} 爆发结果
   */
  triggerEnergyBurst(manual = false) {
    if (this.energy < 25) {
      return { success: false, reason: 'insufficient_energy' };
    }
    
    const level = this.getCurrentEnergyLevel();
    const comboMultiplier = this.getComboMultiplier();
    const patienceMultiplier = this.getPatienceMultiplier();
    
    // 计算最终倍数
    const finalMultiplier = level.multiplier * comboMultiplier * patienceMultiplier;
    
    // 爆发结果
    const burstResult = {
      success: true,
      level: level,
      energyUsed: this.energy,
      comboCount: this.combo,
      finalMultiplier: finalMultiplier,
      manual: manual,
      effects: this._generateBurstEffects(level, this.combo)
    };
    
    // 重置状态
    this.energy = 0;
    this.combo = 0;
    this.patienceTimer = 0;
    
    // 触发爆发事件
    this.emit('energy_burst', burstResult);
    
    console.log(`能量爆发! 等级: ${level.name}, 倍数: ${finalMultiplier.toFixed(2)}x`);
    
    return burstResult;
  }
  
  /**
   * 更新系统状态
   */
  update() {
    // 更新连击计时器
    if (this.combo > 0) {
      this.comboTimer--;
      if (this.comboTimer <= 0) {
        this._resetCombo();
      }
    }
    
    // 更新耐心计时器
    this.patienceTimer++;
    
    // 检查耐心奖励
    this._checkPatienceRewards();
    
    // 能量衰减（如果启用）
    if (this.energyDecay > 0 && this.energy > 0) {
      this.energy = Math.max(0, this.energy - this.energyDecay);
    }
  }
  
  /**
   * 检测布局模式
   * @param {Grid} grid - 游戏网格
   * @returns {Array} 检测到的布局模式
   */
  detectLayoutPatterns(grid) {
    const detectedPatterns = [];
    
    for (const [key, pattern] of Object.entries(this.layoutPatterns)) {
      if (pattern.detection(grid)) {
        detectedPatterns.push({
          key: key,
          ...pattern
        });
      }
    }
    
    return detectedPatterns;
  }
  
  /**
   * 应用布局奖励
   * @param {Array} patterns - 检测到的布局模式
   */
  applyLayoutRewards(patterns) {
    for (const pattern of patterns) {
      this.addEnergy(pattern.energyBonus);
      
      this.emit('layout_pattern_detected', {
        pattern: pattern,
        energyBonus: pattern.energyBonus,
        multiplier: pattern.multiplier
      });
      
      console.log(`检测到布局模式: ${pattern.name}, 能量奖励: +${pattern.energyBonus}`);
    }
  }
  
  /**
   * 获取当前连击倍数
   * @returns {number} 连击倍数
   */
  getComboMultiplier() {
    const stage = this.getCurrentComboStage();
    const multiplier = stage ? stage.multiplier : 1.0;

    return multiplier;
  }
  
  /**
   * 获取当前连击阶段
   * @param {number} combo - 连击数（可选，默认使用当前连击）
   * @returns {Object|null} 连击阶段
   */
  getCurrentComboStage(combo = this.combo) {
    const stage = this.comboStages.find(stage =>
      combo >= stage.min && combo <= stage.max
    ) || null;

    return stage;
  }
  
  /**
   * 获取当前能量等级
   * @param {number} energy - 能量值（可选，默认使用当前能量）
   * @returns {Object|null} 能量等级
   */
  getCurrentEnergyLevel(energy = this.energy) {
    for (let i = this.energyLevels.length - 1; i >= 0; i--) {
      if (energy >= this.energyLevels[i].threshold) {
        return this.energyLevels[i];
      }
    }
    return null;
  }
  
  /**
   * 获取耐心倍数
   * @returns {number} 耐心倍数
   */
  getPatienceMultiplier() {
    for (let i = this.patienceRewards.length - 1; i >= 0; i--) {
      if (this.patienceTimer >= this.patienceRewards[i].time) {
        return this.patienceRewards[i].multiplier;
      }
    }
    return 1.0;
  }
  
  /**
   * 计算能量获得
   * @param {number} matchCount - 匹配方块数量
   * @param {Array} specialEffects - 特殊效果
   * @returns {number} 能量获得量
   * @private
   */
  _calculateEnergyGain(matchCount, specialEffects) {
    let energy = 3; // 增加基础能量从1到3

    // 匹配数量奖励（增加奖励）
    if (matchCount > 3) {
      energy += (matchCount - 3) * 2; // 从0.5增加到2
    }

    // 特殊效果奖励
    energy += specialEffects.length * 3; // 从2增加到3

    // 连击奖励（增加奖励）
    if (this.combo > 3) { // 从5降低到3
      energy += Math.floor(this.combo / 3) * 2; // 增加倍数
    }

    const finalEnergy = Math.ceil(energy);

    return finalEnergy;
  }
  
  /**
   * 重置连击
   * @private
   */
  _resetCombo() {
    if (this.combo > 0) {
      this.emit('combo_reset', {
        finalCombo: this.combo,
        reason: 'timeout'
      });
      
      console.log(`连击重置: ${this.combo} -> 0`);
      this.combo = 0;
    }
  }
  
  /**
   * 检查耐心奖励
   * @private
   */
  _checkPatienceRewards() {
    for (const reward of this.patienceRewards) {
      if (this.patienceTimer === reward.time) {
        this.emit('patience_reward', {
          time: reward.time,
          multiplier: reward.multiplier,
          message: reward.message
        });
        
        console.log(`耐心奖励: ${reward.message} (${reward.multiplier}x)`);
        break;
      }
    }
  }
  
  /**
   * 生成爆发特效
   * @param {Object} level - 能量等级
   * @param {number} combo - 连击数
   * @returns {Array} 特效列表
   * @private
   */
  _generateBurstEffects(level, combo) {
    const effects = [level.effect];
    
    // 根据连击数添加额外特效
    if (combo >= 10) {
      effects.push('combo_explosion');
    }
    if (combo >= 20) {
      effects.push('screen_shake');
    }
    
    return effects;
  }
  
  // 布局模式检测方法（简化版本，实际需要更复杂的算法）
  _detectRainbowStairs(grid) {
    // 简化的彩虹阶梯检测
    return false; // 暂时返回false，需要实际实现
  }
  
  _detectSymmetricGarden(grid) {
    // 简化的对称花园检测
    return false; // 暂时返回false，需要实际实现
  }
  
  _detectColorLayers(grid) {
    // 简化的颜色分层检测
    return false; // 暂时返回false，需要实际实现
  }
  
  _detectEffectChain(grid) {
    // 简化的特效链条检测
    return false; // 暂时返回false，需要实际实现
  }
  
  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }
  
  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   */
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  }

  /**
   * 移除指定事件的监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 要移除的回调函数
   */
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback);
      if (index > -1) {
        this.listeners[event].splice(index, 1);
      }
      // 如果该事件没有监听器了，删除该事件
      if (this.listeners[event].length === 0) {
        delete this.listeners[event];
      }
    }
  }

  /**
   * 移除所有事件监听器
   * 用于清理资源，防止内存泄漏
   */
  removeAllListeners() {
    this.listeners = {};
  }

  /**
   * 获取系统状态
   * @returns {Object} 系统状态
   */
  getStatus() {
    return {
      combo: this.combo,
      maxCombo: this.maxCombo,
      energy: this.energy,
      maxEnergy: this.maxEnergy,
      comboStage: this.getCurrentComboStage(),
      energyLevel: this.getCurrentEnergyLevel(),
      patienceMultiplier: this.getPatienceMultiplier(),
      patienceTime: Math.floor(this.patienceTimer / 60) // 转换为秒
    };
  }
}
