import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

/**
 * 匹配引擎
 * 职责：专门管理游戏中的匹配检测、消除逻辑、匹配模式和连锁反应
 */
export class MatchEngine extends Emitter {
  constructor(grid, options = {}) {
    super();
    
    this.grid = grid;
    this.options = {
      enableLineClear: true,       // 启用行消除
      enableShapeMatch: false,     // 启用形状匹配
      enableColorMatch: false,     // 启用颜色匹配
      enableSpecialBlocks: true,   // 启用特殊方块
      maxCascadeDepth: 10,         // 最大连锁深度
      cascadeDelay: 300,           // 连锁延迟（毫秒）
      animationDuration: 500,      // 动画持续时间
      ...options
    };
    
    // 匹配状态
    this.matchState = {
      isProcessing: false,
      currentMatches: [],
      cascadeLevel: 0,
      totalMatchesInTurn: 0,
      lastMatchTime: 0
    };
    
    // 匹配类型定义
    this.matchTypes = {
      'LINE_HORIZONTAL': { name: '水平消行', baseScore: 100, priority: 1 },
      'LINE_VERTICAL': { name: '垂直消列', baseScore: 120, priority: 2 },
      'SHAPE_L': { name: 'L形匹配', baseScore: 150, priority: 3 },
      'SHAPE_T': { name: 'T形匹配', baseScore: 200, priority: 4 },
      'COLOR_GROUP': { name: '颜色组合', baseScore: 80, priority: 5 },
      'SPECIAL_CLEAR': { name: '特殊清除', baseScore: 300, priority: 6 }
    };
    
    // 匹配统计
    this.matchStats = {
      totalMatches: 0,
      matchesByType: new Map(),
      longestCascade: 0,
      perfectClears: 0
    };
    
    console.log('🔍 匹配引擎已初始化', this.options);
  }

  /**
   * 检测所有匹配
   * @param {Object} context - 检测上下文
   * @returns {Array} 匹配结果数组
   */
  detectMatches(context = {}) {
    if (!this.grid) return [];

    console.log('🗂️ 网格布局');
    this.grid.debugGridState('匹配引擎检测前的网格状态', true);
    console.log('🔍 开始匹配检测', context);
    let allMatches = [];
    
    // 检测行消除
    if (this.options.enableLineClear) {
      const lineMatches = this._detectLineMatches();
      allMatches = allMatches.concat(lineMatches);
    }
    
    // 检测形状匹配
    if (this.options.enableShapeMatch) {
      const shapeMatches = this._detectShapeMatches();
      allMatches = allMatches.concat(shapeMatches);
    }
    
    // 检测颜色匹配
    if (this.options.enableColorMatch) {
      const colorMatches = this._detectColorMatches();
      allMatches = allMatches.concat(colorMatches);
    }
    
    // 检测特殊方块
    if (this.options.enableSpecialBlocks) {
      const specialMatches = this._detectSpecialMatches();
      allMatches = allMatches.concat(specialMatches);
    }
    
    // 过滤重复匹配并排序
    allMatches = this._filterAndSortMatches(allMatches);
    
    console.log(`🔍 匹配检测完成: ${allMatches.length}个匹配`);
    
    this.emit('matches:detected', {
      matches: allMatches,
      context
    });
    
    return allMatches;
  }

  /**
   * 处理匹配消除
   * @param {Array} matches - 匹配数组
   * @param {Object} context - 处理上下文
   * @returns {Object} 处理结果
   */
  async processMatches(matches, context = {}) {
    if (!matches || matches.length === 0) {
      return { success: false, reason: 'no_matches' };
    }
    
    console.log(`🎯 开始处理${matches.length}个匹配`);
    
    this.matchState.isProcessing = true;
    this.matchState.currentMatches = matches;
    this.matchState.lastMatchTime = Date.now();
    
    const processResult = {
      success: true,
      processedMatches: [],
      removedBlocks: [],
      totalScore: 0,
      cascadeTriggered: false,
      specialEffects: []
    };
    
    try {
      // 处理每个匹配
      for (const match of matches) {
        const matchResult = await this._processMatch(match);
        
        processResult.processedMatches.push(matchResult.match);
        processResult.removedBlocks = processResult.removedBlocks.concat(matchResult.removedBlocks);
        processResult.totalScore += matchResult.score;
        processResult.specialEffects = processResult.specialEffects.concat(matchResult.specialEffects);
      }
      
      // 更新统计
      this._updateMatchStats(processResult);
      
      // 检查连锁反应
      if (this.options.enableGravityAfterMatch && processResult.removedBlocks.length > 0) {
        const cascadeResult = await this._checkForCascade(context);
        if (cascadeResult.triggered) {
          processResult.cascadeTriggered = true;
          processResult.totalScore += cascadeResult.score;
        }
      }
      
      this.emit('matches:processed', processResult);
      
      console.log(`✅ 匹配处理完成: 移除${processResult.removedBlocks.length}个方块, 得分${processResult.totalScore}`);
      
    } catch (error) {
      console.error('❌ 匹配处理失败:', error);
      processResult.success = false;
      processResult.error = error;
    } finally {
      this.matchState.isProcessing = false;
      this.matchState.currentMatches = [];
    }
    
    return processResult;
  }

  /**
   * 检查完美清除
   * @returns {boolean} 是否为完美清除
   */
  checkPerfectClear() {
    if (!this.grid) return false;
    
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.grid[row][col] !== 0) {
          return false;
        }
      }
    }
    
    console.log('🌟 完美清除！');
    this.matchStats.perfectClears++;
    
    this.emit('perfect:clear', {
      timestamp: Date.now()
    });
    
    return true;
  }

  /**
   * 获取匹配统计
   * @returns {Object} 匹配统计信息
   */
  getMatchStats() {
    return {
      state: { ...this.matchState },
      stats: { ...this.matchStats }
    };
  }

  /**
   * 重置匹配引擎
   */
  reset() {
    console.log('🔄 重置匹配引擎');
    
    this.matchState = {
      isProcessing: false,
      currentMatches: [],
      cascadeLevel: 0,
      totalMatchesInTurn: 0,
      lastMatchTime: 0
    };
    
    this.matchStats = {
      totalMatches: 0,
      matchesByType: new Map(),
      longestCascade: 0,
      perfectClears: 0
    };
    
    this.emit('match:reset');
  }

  // =================== 私有方法 ===================

  /**
   * 检测行匹配
   * @returns {Array} 行匹配数组
   * @private
   */
  _detectLineMatches() {
    const matches = [];
    
    // 检测满行
    for (let row = 0; row < this.grid.rows; row++) {
      if (this._isRowFull(row)) {
        matches.push({
          type: 'LINE_HORIZONTAL',
          blocks: this._getRowBlocks(row),
          score: this.matchTypes.LINE_HORIZONTAL.baseScore,
          row: row,
          priority: this.matchTypes.LINE_HORIZONTAL.priority
        });
      }
    }
    
    return matches;
  }

  /**
   * 检测形状匹配
   * @returns {Array} 形状匹配数组
   * @private
   */
  _detectShapeMatches() {
    const matches = [];
    // 形状检测逻辑（可以后续扩展）
    return matches;
  }

  /**
   * 检测颜色匹配
   * @returns {Array} 颜色匹配数组
   * @private
   */
  _detectColorMatches() {
    const matches = [];
    // 颜色匹配逻辑（可以后续扩展）
    return matches;
  }

  /**
   * 检测特殊方块匹配
   * @returns {Array} 特殊匹配数组
   * @private
   */
  _detectSpecialMatches() {
    const matches = [];
    
    // 检测特殊方块（炸弹、冰冻等）
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.grid[row][col];
        
        if (block && block.effect) {
          const affectedBlocks = this._getSpecialBlockAffectedArea(row, col, block.effect);
          
          if (affectedBlocks.length > 0) {
            matches.push({
              type: 'SPECIAL_CLEAR',
              blocks: affectedBlocks,
              score: this.matchTypes.SPECIAL_CLEAR.baseScore,
              triggerBlock: { row, col },
              effect: block.effect,
              priority: this.matchTypes.SPECIAL_CLEAR.priority
            });
          }
        }
      }
    }
    
    return matches;
  }

  /**
   * 检查行是否满
   * @param {number} row - 行索引
   * @returns {boolean} 是否满行
   * @private
   */
  _isRowFull(row) {
    for (let col = 0; col < this.grid.cols; col++) {
      if (this.grid.grid[row][col] === 0) {
        return false;
      }
    }
    return true;
  }

  /**
   * 获取行的所有方块
   * @param {number} row - 行索引
   * @returns {Array} 方块数组
   * @private
   */
  _getRowBlocks(row) {
    const blocks = [];
    for (let col = 0; col < this.grid.cols; col++) {
      blocks.push({ row, col, value: this.grid.grid[row][col] });
    }
    return blocks;
  }

  /**
   * 获取特殊方块影响区域
   * @param {number} row - 方块行
   * @param {number} col - 方块列
   * @param {string} effect - 特殊效果
   * @returns {Array} 影响的方块
   * @private
   */
  _getSpecialBlockAffectedArea(row, col, effect) {
    const affected = [];
    
    switch (effect) {
      case 'bomb':
        // 炸弹影响3x3区域
        for (let r = Math.max(0, row - 1); r <= Math.min(this.grid.rows - 1, row + 1); r++) {
          for (let c = Math.max(0, col - 1); c <= Math.min(this.grid.cols - 1, col + 1); c++) {
            if (this.grid.grid[r][c] !== 0) {
              affected.push({ row: r, col: c, value: this.grid.grid[r][c] });
            }
          }
        }
        break;
      
      case 'line':
        // 行炸弹影响整行
        for (let c = 0; c < this.grid.cols; c++) {
          if (this.grid.grid[row][c] !== 0) {
            affected.push({ row, col: c, value: this.grid.grid[row][c] });
          }
        }
        break;
    }
    
    return affected;
  }

  /**
   * 过滤和排序匹配
   * @param {Array} matches - 原始匹配
   * @returns {Array} 处理后的匹配
   * @private
   */
  _filterAndSortMatches(matches) {
    // 排序（按优先级和分数）
    return matches.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      return b.score - a.score;
    });
  }

  /**
   * 处理单个匹配
   * @param {Object} match - 匹配对象
   * @returns {Object} 处理结果
   * @private
   */
  async _processMatch(match) {
    const result = {
      match,
      removedBlocks: [],
      score: 0,
      specialEffects: []
    };
    
    // 移除匹配的方块
    for (const block of match.blocks) {
      const originalValue = this.grid.grid[block.row][block.col];
      this.grid.grid[block.row][block.col] = 0;
      
      result.removedBlocks.push({
        row: block.row,
        col: block.col,
        originalValue,
        matchType: match.type
      });
    }
    
    // 计算分数
    result.score = this._calculateMatchScore(match);
    
    // 处理特殊效果
    if (match.type === 'SPECIAL_CLEAR') {
      result.specialEffects.push({
        type: match.effect,
        triggerBlock: match.triggerBlock,
        affectedBlocks: match.blocks,
        timestamp: Date.now()
      });
    }
    
    this.emit('blocks:removed', {
      removedBlocks: result.removedBlocks,
      match,
      timestamp: Date.now()
    });
    
    return result;
  }

  /**
   * 计算匹配分数
   * @param {Object} match - 匹配对象
   * @returns {number} 分数
   * @private
   */
  _calculateMatchScore(match) {
    let baseScore = match.score || this.matchTypes[match.type]?.baseScore || 0;
    
    // 根据方块数量调整分数
    const blockMultiplier = match.blocks.length / 4;
    
    // 根据连锁等级调整分数
    const cascadeMultiplier = 1 + (this.matchState.cascadeLevel * 0.5);
    
    return Math.floor(baseScore * blockMultiplier * cascadeMultiplier);
  }

  /**
   * 检查连锁反应
   * @param {Object} context - 上下文
   * @returns {Object} 连锁结果
   * @private
   */
  async _checkForCascade(context) {
    if (this.matchState.cascadeLevel >= this.options.maxCascadeDepth) {
      return { triggered: false, reason: 'max_depth_reached' };
    }
    
    // 模拟重力下落
    this._simulateGravity();
    
    // 检测新的匹配
    const cascadeMatches = this.detectMatches({ ...context, cascade: true });
    
    if (cascadeMatches.length > 0) {
      console.log(`🔗 触发连锁反应: ${cascadeMatches.length}个新匹配`);
      
      this.matchState.cascadeLevel++;
      
      // 延迟处理连锁
      if (this.options.cascadeDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, this.options.cascadeDelay));
      }
      
      const cascadeResult = await this.processMatches(cascadeMatches, context);
      
      return {
        triggered: true,
        matches: cascadeMatches,
        score: cascadeResult.totalScore,
        level: this.matchState.cascadeLevel
      };
    }
    
    return { triggered: false, reason: 'no_new_matches' };
  }

  /**
   * 模拟重力效果
   * @private
   */
  _simulateGravity() {
    for (let col = 0; col < this.grid.cols; col++) {
      const column = [];
      
      // 收集列中的非空方块
      for (let row = this.grid.rows - 1; row >= 0; row--) {
        if (this.grid.grid[row][col] !== 0) {
          column.push(this.grid.grid[row][col]);
        }
      }
      
      // 清空列
      for (let row = 0; row < this.grid.rows; row++) {
        this.grid.grid[row][col] = 0;
      }
      
      // 重新放置方块（从底部开始）
      for (let i = 0; i < column.length; i++) {
        this.grid.grid[this.grid.rows - 1 - i][col] = column[i];
      }
    }
  }

  /**
   * 更新匹配统计
   * @param {Object} result - 处理结果
   * @private
   */
  _updateMatchStats(result) {
    this.matchStats.totalMatches++;
    
    for (const match of result.processedMatches) {
      const type = match.type;
      this.matchStats.matchesByType.set(type, (this.matchStats.matchesByType.get(type) || 0) + 1);
    }
    
    if (this.matchState.cascadeLevel > this.matchStats.longestCascade) {
      this.matchStats.longestCascade = this.matchState.cascadeLevel;
    }
  }

  /**
   * 销毁匹配引擎
   */
  destroy() {
    this.removeAllListeners();
    console.log('�� 匹配引擎已销毁');
  }

  /**
   * 检查匹配模式
   */
  checkMatches() {
    const matches = [];
    
    if (this.options.enableLineClear) {
      const lineMatches = this.findLineMatches();
      matches.push(...lineMatches);
    }
    
    return matches;
  }
  
  /**
   * 查找完整行匹配
   */
  findLineMatches() {
    const matches = [];
    
    for (let row = 0; row < this.grid.rows; row++) {
      let isFullLine = true;
      for (let col = 0; col < this.grid.cols; col++) {
        if (!this.grid.blocks[row][col]) {
          isFullLine = false;
          break;
        }
      }
      
      if (isFullLine) {
        matches.push({
          type: 'line',
          row: row,
          positions: Array.from({ length: this.grid.cols }, (_, col) => ({ row, col }))
        });
      }
    }
    
    return matches;
  }
} 