import TinyEmitter from '../../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

/**
 * 特效处理器
 * 职责：专门管理游戏中的特殊效果、动画效果、视觉反馈和连锁反应特效
 */
export class EffectProcessor extends Emitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      enableAnimations: true,      // 启用动画
      enableParticles: true,       // 启用粒子效果
      enableScreenShake: true,     // 启用屏幕震动
      animationSpeed: 1.0,         // 动画速度倍数
      maxParticles: 100,           // 最大粒子数
      effectDuration: 1000,        // 默认特效持续时间
      fadeOutDuration: 500,        // 淡出持续时间
      enableSoundEffects: true,    // 启用音效
      ...options
    };
    
    // 特效状态
    this.effectState = {
      activeEffects: new Map(),
      effectQueue: [],
      isProcessing: false,
      totalEffectsCreated: 0,
      maxConcurrentEffects: 20
    };
    
    // 特效类型定义
    this.effectTypes = {
      'MATCH_CLEAR': {
        name: '匹配清除',
        duration: 800,
        particles: true,
        animation: 'fadeOut',
        sound: 'clear'
      },
      'LINE_CLEAR': {
        name: '行消除',
        duration: 1000,
        particles: true,
        animation: 'slideOut',
        sound: 'line_clear',
        screenShake: true
      },
      'COMBO_HIT': {
        name: '连击特效',
        duration: 600,
        particles: true,
        animation: 'burst',
        sound: 'combo'
      },
      'SPECIAL_EFFECT': {
        name: '特殊方块',
        duration: 1200,
        particles: true,
        animation: 'explosion',
        sound: 'special',
        screenShake: true
      },
      'CASCADE': {
        name: '连锁反应',
        duration: 800,
        particles: true,
        animation: 'chain',
        sound: 'cascade'
      },
      'PERFECT_CLEAR': {
        name: '完美清除',
        duration: 2000,
        particles: true,
        animation: 'celebration',
        sound: 'perfect',
        screenShake: true
      },
      'LEVEL_UP': {
        name: '等级提升',
        duration: 1500,
        particles: true,
        animation: 'levelUp',
        sound: 'level_up'
      }
    };
    
    // 动画系统
    this.animationSystem = {
      activeAnimations: new Map(),
      animationId: 0,
      frameCallbacks: new Map(),
      tweens: new Map()
    };
    
    // 粒子系统
    this.particleSystem = {
      particles: [],
      particleId: 0,
      emitters: new Map(),
      particlePool: []
    };
    
    // 屏幕震动系统
    this.shakeSystem = {
      isShaking: false,
      intensity: 0,
      duration: 0,
      startTime: 0,
      offsetX: 0,
      offsetY: 0
    };
    
    // 特效统计
    this.effectStats = {
      totalEffectsPlayed: 0,
      effectsByType: new Map(),
      averageEffectDuration: 0,
      peakConcurrentEffects: 0,
      performanceMetrics: {
        averageProcessingTime: 0,
        frameDrops: 0,
        memoryUsage: 0
      }
    };
    
    console.log('✨ 特效处理器已初始化', this.options);
  }

  /**
   * 触发特效
   * @param {string} effectType - 特效类型
   * @param {Object} data - 特效数据
   * @param {Object} options - 特效选项
   * @returns {string} 特效ID
   */
  triggerEffect(effectType, data = {}, options = {}) {
    if (!this.options.enableAnimations && !this.options.enableParticles) {
      return null;
    }
    
    const effectId = `effect_${this.effectState.totalEffectsCreated++}`;
    const effectConfig = this.effectTypes[effectType];
    
    if (!effectConfig) {
      console.warn(`未知的特效类型: ${effectType}`);
      return null;
    }
    
    const effect = {
      id: effectId,
      type: effectType,
      config: effectConfig,
      data,
      options: { ...effectConfig, ...options },
      startTime: Date.now(),
      duration: options.duration || effectConfig.duration,
      isActive: true,
      animations: [],
      particles: [],
      sounds: []
    };
    
    console.log(`✨ 触发特效: ${effectType}`, data);
    
    // 检查并发限制
    if (this.effectState.activeEffects.size >= this.effectState.maxConcurrentEffects) {
      this._cleanupOldestEffect();
    }
    
    this.effectState.activeEffects.set(effectId, effect);
    
    // 立即开始处理特效
    this._processEffect(effect);
    
    // 更新统计
    this._updateEffectStats(effectType);
    
    // 发出特效触发事件
    this.emit('effect:triggered', {
      effectId,
      effectType,
      data,
      timestamp: Date.now()
    });
    
    return effectId;
  }

  /**
   * 触发匹配清除特效
   * @param {Array} blocks - 清除的方块
   * @param {string} matchType - 匹配类型
   */
  triggerMatchClearEffect(blocks, matchType) {
    return this.triggerEffect('MATCH_CLEAR', {
      blocks,
      matchType,
      blockCount: blocks.length
    });
  }

  /**
   * 触发行消除特效
   * @param {Array} rows - 消除的行
   * @param {Array} blocks - 消除的方块
   */
  triggerLineClearEffect(rows, blocks) {
    return this.triggerEffect('LINE_CLEAR', {
      rows,
      blocks,
      lineCount: rows.length
    });
  }

  /**
   * 触发连击特效
   * @param {number} comboCount - 连击数
   * @param {Object} comboData - 连击数据
   */
  triggerComboEffect(comboCount, comboData = {}) {
    const intensity = Math.min(3.0, comboCount / 5); // 根据连击数调整强度
    
    return this.triggerEffect('COMBO_HIT', {
      comboCount,
      comboData,
      intensity
    }, {
      duration: 600 + (comboCount * 50), // 连击越高，特效越长
      particles: comboCount >= 5 // 5连击以上才有粒子
    });
  }

  /**
   * 触发特殊方块特效
   * @param {Object} block - 特殊方块
   * @param {string} effectType - 特效类型
   * @param {Array} affectedBlocks - 影响的方块
   */
  triggerSpecialBlockEffect(block, effectType, affectedBlocks) {
    return this.triggerEffect('SPECIAL_EFFECT', {
      triggerBlock: block,
      effectType,
      affectedBlocks,
      affectedCount: affectedBlocks.length
    });
  }

  /**
   * 触发连锁反应特效
   * @param {number} cascadeLevel - 连锁等级
   * @param {Array} newMatches - 新匹配
   */
  triggerCascadeEffect(cascadeLevel, newMatches) {
    const intensity = Math.min(2.0, cascadeLevel / 3);
    
    return this.triggerEffect('CASCADE', {
      cascadeLevel,
      newMatches,
      matchCount: newMatches.length,
      intensity
    }, {
      duration: 800 + (cascadeLevel * 200)
    });
  }

  /**
   * 触发完美清除特效
   */
  triggerPerfectClearEffect() {
    return this.triggerEffect('PERFECT_CLEAR', {
      achievement: 'perfect_clear'
    }, {
      duration: 2000,
      screenShake: true,
      particles: true
    });
  }

  /**
   * 触发等级提升特效
   * @param {number} newLevel - 新等级
   * @param {Object} levelData - 等级数据
   */
  triggerLevelUpEffect(newLevel, levelData = {}) {
    return this.triggerEffect('LEVEL_UP', {
      newLevel,
      levelData
    });
  }

  /**
   * 停止特效
   * @param {string} effectId - 特效ID
   */
  stopEffect(effectId) {
    const effect = this.effectState.activeEffects.get(effectId);
    if (!effect) return;
    
    console.log(`⏹️ 停止特效: ${effect.type}`);
    
    // 停止相关动画
    this._stopEffectAnimations(effect);
    
    // 清理粒子
    this._cleanupEffectParticles(effect);
    
    // 停止音效
    this._stopEffectSounds(effect);
    
    // 从活跃特效中移除
    this.effectState.activeEffects.delete(effectId);
    
    this.emit('effect:stopped', {
      effectId,
      effectType: effect.type,
      duration: Date.now() - effect.startTime
    });
  }

  /**
   * 触发屏幕震动
   * @param {number} intensity - 震动强度 (0-1)
   * @param {number} duration - 震动持续时间
   */
  triggerScreenShake(intensity = 0.5, duration = 300) {
    if (!this.options.enableScreenShake) return;
    
    this.shakeSystem.isShaking = true;
    this.shakeSystem.intensity = Math.min(1.0, intensity);
    this.shakeSystem.duration = duration;
    this.shakeSystem.startTime = Date.now();
    
    console.log(`📳 屏幕震动: 强度${intensity}, 持续${duration}ms`);
    
    this.emit('screen:shake', {
      intensity,
      duration,
      startTime: this.shakeSystem.startTime
    });
  }

  /**
   * 更新特效系统
   */
  update() {
    const currentTime = Date.now();
    
    // 更新所有活跃特效
    for (const [effectId, effect] of this.effectState.activeEffects) {
      const elapsed = currentTime - effect.startTime;
      
      if (elapsed >= effect.duration) {
        // 特效已完成，开始淡出
        this._fadeOutEffect(effect);
      } else {
        // 更新特效
        this._updateEffect(effect, elapsed);
      }
    }
    
    // 更新动画系统
    this._updateAnimations();
    
    // 更新粒子系统
    this._updateParticles();
    
    // 更新屏幕震动
    this._updateScreenShake();
    
    // 处理特效队列
    this._processEffectQueue();
  }

  /**
   * 获取屏幕震动偏移
   * @returns {Object} 震动偏移量 {x, y}
   */
  getScreenShakeOffset() {
    if (!this.shakeSystem.isShaking) {
      return { x: 0, y: 0 };
    }
    
    return {
      x: this.shakeSystem.offsetX,
      y: this.shakeSystem.offsetY
    };
  }

  /**
   * 获取特效统计
   * @returns {Object} 特效统计信息
   */
  getEffectStats() {
    return {
      state: {
        activeEffects: this.effectState.activeEffects.size,
        queuedEffects: this.effectState.effectQueue.length,
        isProcessing: this.effectState.isProcessing,
        totalCreated: this.effectState.totalEffectsCreated
      },
      stats: { ...this.effectStats },
      systems: {
        animations: this.animationSystem.activeAnimations.size,
        particles: this.particleSystem.particles.length,
        isShaking: this.shakeSystem.isShaking
      }
    };
  }

  /**
   * 重置特效处理器
   */
  reset() {
    console.log('🔄 重置特效处理器');
    
    // 停止所有活跃特效
    for (const effectId of this.effectState.activeEffects.keys()) {
      this.stopEffect(effectId);
    }
    
    // 清空特效队列
    this.effectState.effectQueue = [];
    this.effectState.isProcessing = false;
    this.effectState.totalEffectsCreated = 0;
    
    // 重置动画系统
    this.animationSystem.activeAnimations.clear();
    this.animationSystem.frameCallbacks.clear();
    this.animationSystem.tweens.clear();
    this.animationSystem.animationId = 0;
    
    // 重置粒子系统
    this.particleSystem.particles = [];
    this.particleSystem.emitters.clear();
    this.particleSystem.particleId = 0;
    
    // 重置屏幕震动
    this.shakeSystem.isShaking = false;
    this.shakeSystem.intensity = 0;
    this.shakeSystem.offsetX = 0;
    this.shakeSystem.offsetY = 0;
    
    // 重置统计
    this.effectStats = {
      totalEffectsPlayed: 0,
      effectsByType: new Map(),
      averageEffectDuration: 0,
      peakConcurrentEffects: 0,
      performanceMetrics: {
        averageProcessingTime: 0,
        frameDrops: 0,
        memoryUsage: 0
      }
    };
    
    this.emit('effects:reset');
  }

  // =================== 私有方法 ===================

  /**
   * 处理特效
   * @param {Object} effect - 特效对象
   * @private
   */
  _processEffect(effect) {
    const { config, data, options } = effect;
    
    // 创建动画
    if (options.animation && this.options.enableAnimations) {
      this._createEffectAnimation(effect);
    }
    
    // 创建粒子
    if (options.particles && this.options.enableParticles) {
      this._createEffectParticles(effect);
    }
    
    // 播放音效
    if (options.sound && this.options.enableSoundEffects) {
      this._playEffectSound(effect);
    }
    
    // 触发屏幕震动
    if (options.screenShake && this.options.enableScreenShake) {
      this.triggerScreenShake(data.intensity || 0.3, 200);
    }
  }

  /**
   * 创建特效动画
   * @param {Object} effect - 特效对象
   * @private
   */
  _createEffectAnimation(effect) {
    const animationId = `anim_${this.animationSystem.animationId++}`;
    
    const animation = {
      id: animationId,
      type: effect.options.animation,
      startTime: Date.now(),
      duration: effect.duration,
      progress: 0,
      effectId: effect.id
    };
    
    this.animationSystem.activeAnimations.set(animationId, animation);
    effect.animations.push(animationId);
    
    console.log(`🎬 创建动画: ${animation.type} (${animationId})`);
  }

  /**
   * 创建特效粒子
   * @param {Object} effect - 特效对象
   * @private
   */
  _createEffectParticles(effect) {
    const particleCount = this._getParticleCount(effect);
    
    for (let i = 0; i < particleCount; i++) {
      const particle = this._createParticle(effect);
      this.particleSystem.particles.push(particle);
      effect.particles.push(particle.id);
    }
    
    console.log(`✨ 创建粒子: ${particleCount}个 (${effect.type})`);
  }

  /**
   * 创建单个粒子
   * @param {Object} effect - 特效对象
   * @returns {Object} 粒子对象
   * @private
   */
  _createParticle(effect) {
    const particleId = `particle_${this.particleSystem.particleId++}`;
    
    // 根据特效类型设置粒子属性
    const particle = {
      id: particleId,
      x: (effect.data.blocks?.[0]?.col || 0) * 25,
      y: (effect.data.blocks?.[0]?.row || 0) * 25,
      vx: (Math.random() - 0.5) * 10,
      vy: (Math.random() - 0.5) * 10,
      life: 1.0,
      decay: 0.02,
      size: Math.random() * 5 + 2,
      color: this._getParticleColor(effect.type),
      effectId: effect.id
    };
    
    return particle;
  }

  /**
   * 播放特效音效
   * @param {Object} effect - 特效对象
   * @private
   */
  _playEffectSound(effect) {
    const soundName = effect.options.sound;
    
    // 这里可以集成音效系统
    if (typeof GameGlobal !== 'undefined' && GameGlobal.musicManager) {
      const soundMethod = `play${soundName.charAt(0).toUpperCase() + soundName.slice(1)}`;
      if (typeof GameGlobal.musicManager[soundMethod] === 'function') {
        GameGlobal.musicManager[soundMethod]();
      }
    }
    
    effect.sounds.push(soundName);
    console.log(`🔊 播放音效: ${soundName}`);
  }

  /**
   * 更新特效
   * @param {Object} effect - 特效对象
   * @param {number} elapsed - 已过时间
   * @private
   */
  _updateEffect(effect, elapsed) {
    const progress = elapsed / effect.duration;
    
    // 更新特效进度
    effect.progress = progress;
    
    // 可以在这里添加特效的实时更新逻辑
  }

  /**
   * 淡出特效
   * @param {Object} effect - 特效对象
   * @private
   */
  _fadeOutEffect(effect) {
    // 开始淡出过程
    setTimeout(() => {
      this.stopEffect(effect.id);
    }, this.options.fadeOutDuration);
  }

  /**
   * 更新动画系统
   * @private
   */
  _updateAnimations() {
    const currentTime = Date.now();
    const toRemove = [];
    
    for (const [animId, animation] of this.animationSystem.activeAnimations) {
      const elapsed = currentTime - animation.startTime;
      animation.progress = Math.min(1.0, elapsed / animation.duration);
      
      if (animation.progress >= 1.0) {
        toRemove.push(animId);
      }
    }
    
    // 移除完成的动画
    for (const animId of toRemove) {
      this.animationSystem.activeAnimations.delete(animId);
    }
  }

  /**
   * 更新粒子系统
   * @private
   */
  _updateParticles() {
    for (let i = this.particleSystem.particles.length - 1; i >= 0; i--) {
      const particle = this.particleSystem.particles[i];
      
      // 更新粒子位置
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      // 更新粒子生命
      particle.life -= particle.decay;
      
      // 移除死亡的粒子
      if (particle.life <= 0) {
        this.particleSystem.particles.splice(i, 1);
      }
    }
  }

  /**
   * 更新屏幕震动
   * @private
   */
  _updateScreenShake() {
    if (!this.shakeSystem.isShaking) return;
    
    const elapsed = Date.now() - this.shakeSystem.startTime;
    
    if (elapsed >= this.shakeSystem.duration) {
      // 震动结束
      this.shakeSystem.isShaking = false;
      this.shakeSystem.offsetX = 0;
      this.shakeSystem.offsetY = 0;
      
      this.emit('screen:shakeEnd');
    } else {
      // 计算震动偏移
      const progress = elapsed / this.shakeSystem.duration;
      const intensity = this.shakeSystem.intensity * (1 - progress); // 逐渐减弱
      
      this.shakeSystem.offsetX = (Math.random() - 0.5) * intensity * 10;
      this.shakeSystem.offsetY = (Math.random() - 0.5) * intensity * 10;
    }
  }

  /**
   * 处理特效队列
   * @private
   */
  _processEffectQueue() {
    if (this.effectState.effectQueue.length > 0 && !this.effectState.isProcessing) {
      // 处理队列中的特效
      const nextEffect = this.effectState.effectQueue.shift();
      if (nextEffect) {
        this.triggerEffect(nextEffect.type, nextEffect.data, nextEffect.options);
      }
    }
  }

  /**
   * 获取粒子数量
   * @param {Object} effect - 特效对象
   * @returns {number} 粒子数量
   * @private
   */
  _getParticleCount(effect) {
    switch (effect.type) {
      case 'MATCH_CLEAR':
        return Math.min(20, effect.data.blockCount * 2);
      case 'LINE_CLEAR':
        return Math.min(50, effect.data.lineCount * 10);
      case 'COMBO_HIT':
        return Math.min(30, effect.data.comboCount * 3);
      case 'SPECIAL_EFFECT':
        return 40;
      case 'CASCADE':
        return Math.min(25, effect.data.cascadeLevel * 5);
      case 'PERFECT_CLEAR':
        return 100;
      case 'LEVEL_UP':
        return 60;
      default:
        return 10;
    }
  }

  /**
   * 获取粒子颜色
   * @param {string} effectType - 特效类型
   * @returns {string} 颜色
   * @private
   */
  _getParticleColor(effectType) {
    const colors = {
      'MATCH_CLEAR': '#FFD700',
      'LINE_CLEAR': '#FF6B6B',
      'COMBO_HIT': '#4ECDC4',
      'SPECIAL_EFFECT': '#FF8E53',
      'CASCADE': '#45B7D1',
      'PERFECT_CLEAR': '#96CEB4',
      'LEVEL_UP': '#FECA57'
    };
    
    return colors[effectType] || '#FFFFFF';
  }

  /**
   * 清理最旧的特效
   * @private
   */
  _cleanupOldestEffect() {
    let oldestTime = Date.now();
    let oldestId = null;
    
    for (const [id, effect] of this.effectState.activeEffects) {
      if (effect.startTime < oldestTime) {
        oldestTime = effect.startTime;
        oldestId = id;
      }
    }
    
    if (oldestId) {
      this.stopEffect(oldestId);
    }
  }

  /**
   * 停止特效动画
   * @param {Object} effect - 特效对象
   * @private
   */
  _stopEffectAnimations(effect) {
    for (const animId of effect.animations) {
      this.animationSystem.activeAnimations.delete(animId);
    }
  }

  /**
   * 清理特效粒子
   * @param {Object} effect - 特效对象
   * @private
   */
  _cleanupEffectParticles(effect) {
    this.particleSystem.particles = this.particleSystem.particles.filter(
      particle => !effect.particles.includes(particle.id)
    );
  }

  /**
   * 停止特效音效
   * @param {Object} effect - 特效对象
   * @private
   */
  _stopEffectSounds(effect) {
    // 这里可以添加停止音效的逻辑
  }

  /**
   * 更新特效统计
   * @param {string} effectType - 特效类型
   * @private
   */
  _updateEffectStats(effectType) {
    this.effectStats.totalEffectsPlayed++;
    this.effectStats.effectsByType.set(
      effectType, 
      (this.effectStats.effectsByType.get(effectType) || 0) + 1
    );
    
    const currentConcurrent = this.effectState.activeEffects.size;
    if (currentConcurrent > this.effectStats.peakConcurrentEffects) {
      this.effectStats.peakConcurrentEffects = currentConcurrent;
    }
  }

  /**
   * 销毁特效处理器
   */
  destroy() {
    // 停止所有特效
    this.reset();
    
    // 移除所有事件监听器
    this.removeAllListeners();
    
    console.log('✨ 特效处理器已销毁');
  }

  /**
   * 处理特效 - 完整版本
   */
  processEffect(effectType, effectData = {}) {
    if (!this.options.enableAnimations) {
      return Promise.resolve();
    }
    
    console.log(`🎊 处理特效: ${effectType}`, effectData);
    
    switch (effectType) {
      case 'line_clear':
        return this.processLineEffect(effectData);
      case 'combo':
        return this.processComboEffect(effectData);
      case 'explosion':
        return this.processExplosionEffect(effectData);
      default:
        return this.processGenericEffect(effectType, effectData);
    }
  }
  
  /**
   * 处理行清除特效
   */
  processLineEffect(data) {
    console.log('✨ 行清除特效', data);
    return new Promise(resolve => {
      setTimeout(resolve, this.options.effectDuration || 300);
    });
  }
  
  /**
   * 处理连击特效
   */
  processComboEffect(data) {
    console.log('🔥 连击特效', data);
    return new Promise(resolve => {
      setTimeout(resolve, this.options.effectDuration || 500);
    });
  }
  
  /**
   * 处理爆炸特效
   */
  processExplosionEffect(data) {
    console.log('💥 爆炸特效', data);
    return new Promise(resolve => {
      setTimeout(resolve, this.options.effectDuration || 400);
    });
  }
  
  /**
   * 处理通用特效
   */
  processGenericEffect(type, data) {
    console.log(`🎭 通用特效: ${type}`, data);
    return new Promise(resolve => {
      setTimeout(resolve, this.options.effectDuration || 300);
    });
  }
} 