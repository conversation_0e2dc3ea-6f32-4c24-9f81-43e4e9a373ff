/**
 * 输入处理器
 * 负责处理键盘、触摸输入以及手势识别等输入相关逻辑
 */
import Emitter from '../libs/tinyemitter.js';

// 输入事件类型
export const INPUT_EVENTS = {
  MOVE_LEFT: 'move_left',
  MOVE_RIGHT: 'move_right',
  MOVE_DOWN: 'move_down',
  ROTATE: 'rotate',
  HARD_DROP: 'hard_drop',
  PAUSE: 'pause',
  SOFT_DROP_START: 'soft_drop_start',
  SOFT_DROP_END: 'soft_drop_end'
};

// 手势类型
const GESTURES = {
  TAP: 'tap',
  SWIPE_LEFT: 'swipe_left',
  SWIPE_RIGHT: 'swipe_right',
  SWIPE_DOWN: 'swipe_down',
  SWIPE_UP: 'swipe_up',
  LONG_PRESS: 'long_press'
};

// 输入配置
const INPUT_CONFIG = {
  SWIPE_THRESHOLD: 30,      // 滑动最小距离
  SWIPE_TIME_LIMIT: 500,    // 滑动最大时间
  LONG_PRESS_TIME: 500,     // 长按时间
  REPEAT_DELAY: 200,        // 重复输入延迟
  REPEAT_INTERVAL: 100      // 重复输入间隔
};

export default class InputHandler extends Emitter {
  constructor() {
    super();
    
    // 键盘状态
    this.pressedKeys = new Set();
    this.keyRepeatTimers = new Map();
    
    // 触摸状态
    this.touchState = {
      isActive: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
      startTime: 0,
      lastMoveTime: 0
    };
    
    // 手势识别
    this.gestureDetector = {
      isEnabled: true,
      minSwipeDistance: INPUT_CONFIG.SWIPE_THRESHOLD,
      maxSwipeTime: INPUT_CONFIG.SWIPE_TIME_LIMIT
    };
    
    // 长按检测
    this.longPressTimer = null;
    this.isLongPressing = false;
    
    // 输入统计
    this.inputStats = {
      keyPresses: 0,
      touchEvents: 0,
      gestures: new Map()
    };
    
    this._initInputHandlers();
    
    console.log('🎮 InputHandler初始化完成');
  }
  
  /**
   * 初始化输入处理器
   * @private
   */
  _initInputHandlers() {
    // 键盘事件处理
    this._keyDownHandler = (res) => this._handleKeyDown(res);
    this._keyUpHandler = (res) => this._handleKeyUp(res);
    
    // 触摸事件处理
    this._touchStartHandler = (res) => this._handleTouchStart(res);
    this._touchMoveHandler = (res) => this._handleTouchMove(res);
    this._touchEndHandler = (res) => this._handleTouchEnd(res);
    
    // 注册事件监听器
    if (typeof wx !== 'undefined') {
      wx.onKeyDown(this._keyDownHandler);
      wx.onKeyUp(this._keyUpHandler);
      // 触摸事件将通过外部调用handleTouch方法处理
    }
    
    console.log('🎮 输入事件监听器注册完成');
  }
  
  /**
   * 处理键盘按下事件
   * @private
   */
  _handleKeyDown(res) {
    const { key } = res;
    
    // 防止重复触发
    if (this.pressedKeys.has(key)) {
      return;
    }
    
    this.pressedKeys.add(key);
    this.inputStats.keyPresses++;
    
    console.log(`⌨️ 按键按下: ${key}`);
    
    // 处理按键映射
    const inputEvent = this._mapKeyToInputEvent(key);
    if (inputEvent) {
      this._emitInputEvent(inputEvent, { key, source: 'keyboard' });
      
      // 设置重复输入
      if (this._isRepeatableKey(key)) {
        this._setupKeyRepeat(key, inputEvent);
      }
    }
  }
  
  /**
   * 处理键盘释放事件
   * @private
   */
  _handleKeyUp(res) {
    const { key } = res;
    
    if (this.pressedKeys.has(key)) {
      this.pressedKeys.delete(key);
      
      console.log(`⌨️ 按键释放: ${key}`);
      
      // 清除重复计时器
      if (this.keyRepeatTimers.has(key)) {
        clearInterval(this.keyRepeatTimers.get(key));
        this.keyRepeatTimers.delete(key);
      }
      
      // 处理软下落结束
      if (key === 'ArrowDown') {
        this._emitInputEvent(INPUT_EVENTS.SOFT_DROP_END, { key, source: 'keyboard' });
      }
    }
  }
  
  /**
   * 映射按键到输入事件
   * @private
   */
  _mapKeyToInputEvent(key) {
    const keyMap = {
      'ArrowLeft': INPUT_EVENTS.MOVE_LEFT,
      'ArrowRight': INPUT_EVENTS.MOVE_RIGHT,
      'ArrowDown': INPUT_EVENTS.SOFT_DROP_START,
      'ArrowUp': INPUT_EVENTS.ROTATE,
      'w': INPUT_EVENTS.ROTATE,
      'W': INPUT_EVENTS.ROTATE,
      ' ': INPUT_EVENTS.HARD_DROP,
      'Escape': INPUT_EVENTS.PAUSE,
      'p': INPUT_EVENTS.PAUSE,
      'P': INPUT_EVENTS.PAUSE
    };
    
    return keyMap[key] || null;
  }
  
  /**
   * 检查是否为可重复的按键
   * @private
   */
  _isRepeatableKey(key) {
    return ['ArrowLeft', 'ArrowRight', 'ArrowDown'].includes(key);
  }
  
  /**
   * 设置按键重复
   * @private
   */
  _setupKeyRepeat(key, inputEvent) {
    setTimeout(() => {
      if (this.pressedKeys.has(key)) {
        const interval = setInterval(() => {
          if (this.pressedKeys.has(key)) {
            this._emitInputEvent(inputEvent, { key, source: 'keyboard_repeat' });
          } else {
            clearInterval(interval);
            this.keyRepeatTimers.delete(key);
          }
        }, INPUT_CONFIG.REPEAT_INTERVAL);
        
        this.keyRepeatTimers.set(key, interval);
      }
    }, INPUT_CONFIG.REPEAT_DELAY);
  }
  
  /**
   * 处理触摸输入（外部调用）
   * @param {string} type - 触摸事件类型 ('start', 'move', 'end')
   * @param {Object} event - 触摸事件数据
   */
  handleTouch(type, event) {
    this.inputStats.touchEvents++;
    
    switch (type) {
      case 'start':
        this._handleTouchStart(event);
        break;
      case 'move':
        this._handleTouchMove(event);
        break;
      case 'end':
        this._handleTouchEnd(event);
        break;
    }
  }
  
  /**
   * 处理触摸开始
   * @private
   */
  _handleTouchStart(event) {
    const { clientX: x, clientY: y } = event;
    const now = Date.now();
    
    this.touchState = {
      isActive: true,
      startX: x,
      startY: y,
      currentX: x,
      currentY: y,
      startTime: now,
      lastMoveTime: now
    };
    
    // 开始长按检测
    this._startLongPressDetection();
    
    // console.log(`👆 触摸开始: (${x}, ${y})`);
  }
  
  /**
   * 处理触摸移动
   * @private
   */
  _handleTouchMove(event) {
    if (!this.touchState.isActive) return;
    
    const { clientX: x, clientY: y } = event;
    const now = Date.now();
    
    this.touchState.currentX = x;
    this.touchState.currentY = y;
    this.touchState.lastMoveTime = now;
    
    // 如果移动距离超过阈值，取消长按
    const moveDistance = this._calculateDistance(
      this.touchState.startX, this.touchState.startY,
      x, y
    );
    
    if (moveDistance > INPUT_CONFIG.SWIPE_THRESHOLD) {
      this._cancelLongPress();
    }
  }
  
  /**
   * 处理触摸结束
   * @private
   */
  _handleTouchEnd(event) {
    if (!this.touchState.isActive) return;
    
    const { clientX: x, clientY: y } = event;
    const now = Date.now();
    
    // 更新触摸状态
    this.touchState.currentX = x;
    this.touchState.currentY = y;
    
    // 取消长按检测
    this._cancelLongPress();
    
    // 识别手势
    const gesture = this._recognizeGesture();
    if (gesture) {
      this._handleGesture(gesture);
    }
    
    // 重置触摸状态
    this.touchState.isActive = false;
    
    // console.log(`👆 触摸结束: (${x}, ${y}), 手势: ${gesture || '无'}`);
  }
  
  /**
   * 开始长按检测
   * @private
   */
  _startLongPressDetection() {
    this._cancelLongPress();
    
    this.longPressTimer = setTimeout(() => {
      if (this.touchState.isActive && !this.isLongPressing) {
        this.isLongPressing = true;
        this._handleGesture(GESTURES.LONG_PRESS);
      }
    }, INPUT_CONFIG.LONG_PRESS_TIME);
  }
  
  /**
   * 取消长按检测
   * @private
   */
  _cancelLongPress() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
    this.isLongPressing = false;
  }
  
  /**
   * 识别手势
   * @private
   */
  _recognizeGesture() {
    const { startX, startY, currentX, currentY, startTime } = this.touchState;
    const now = Date.now();
    const duration = now - startTime;
    
    // 如果是长按，不再识别其他手势
    if (this.isLongPressing) {
      return null;
    }
    
    const deltaX = currentX - startX;
    const deltaY = currentY - startY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    // 轻点检测
    if (distance < INPUT_CONFIG.SWIPE_THRESHOLD && duration < INPUT_CONFIG.LONG_PRESS_TIME) {
      return GESTURES.TAP;
    }
    
    // 滑动检测
    if (distance >= INPUT_CONFIG.SWIPE_THRESHOLD && duration < INPUT_CONFIG.SWIPE_TIME_LIMIT) {
      const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
      
      // 判断滑动方向
      if (Math.abs(angle) < 45) {
        return GESTURES.SWIPE_RIGHT;
      } else if (Math.abs(angle) > 135) {
        return GESTURES.SWIPE_LEFT;
      } else if (angle > 45 && angle < 135) {
        return GESTURES.SWIPE_DOWN;
      } else if (angle < -45 && angle > -135) {
        return GESTURES.SWIPE_UP;
      }
    }
    
    return null;
  }
  
  /**
   * 处理手势
   * @private
   */
  _handleGesture(gesture) {
    // 更新手势统计
    const count = this.inputStats.gestures.get(gesture) || 0;
    this.inputStats.gestures.set(gesture, count + 1);
    
    console.log(`👆 手势识别: ${gesture}`);
    
    // 映射手势到输入事件
    const inputEvent = this._mapGestureToInputEvent(gesture);
    if (inputEvent) {
      this._emitInputEvent(inputEvent, { gesture, source: 'touch' });
    }
  }
  
  /**
   * 映射手势到输入事件
   * @private
   */
  _mapGestureToInputEvent(gesture) {
    const gestureMap = {
      [GESTURES.TAP]: INPUT_EVENTS.ROTATE,
      [GESTURES.SWIPE_LEFT]: INPUT_EVENTS.MOVE_LEFT,
      [GESTURES.SWIPE_RIGHT]: INPUT_EVENTS.MOVE_RIGHT,
      [GESTURES.SWIPE_DOWN]: INPUT_EVENTS.MOVE_DOWN,
      [GESTURES.SWIPE_UP]: INPUT_EVENTS.HARD_DROP,
      [GESTURES.LONG_PRESS]: INPUT_EVENTS.PAUSE
    };
    
    return gestureMap[gesture] || null;
  }
  
  /**
   * 发射输入事件
   * @private
   */
  _emitInputEvent(eventType, data = {}) {
    this.emit('input', {
      type: eventType,
      timestamp: Date.now(),
      ...data
    });
    
    // 也发射具体的事件类型
    this.emit(eventType, data);
  }
  
  /**
   * 计算两点间距离
   * @private
   */
  _calculateDistance(x1, y1, x2, y2) {
    const dx = x2 - x1;
    const dy = y2 - y1;
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  /**
   * 启用/禁用手势识别
   * @param {boolean} enabled - 是否启用
   */
  setGestureEnabled(enabled) {
    this.gestureDetector.isEnabled = enabled;
    console.log(`👆 手势识别: ${enabled ? '启用' : '禁用'}`);
  }
  
  /**
   * 设置手势参数
   * @param {Object} config - 配置参数
   */
  setGestureConfig(config) {
    Object.assign(this.gestureDetector, config);
    console.log('👆 手势配置更新:', config);
  }
  
  /**
   * 获取输入统计
   */
  getInputStats() {
    return {
      ...this.inputStats,
      pressedKeys: Array.from(this.pressedKeys),
      touchActive: this.touchState.isActive,
      gestureStats: Object.fromEntries(this.inputStats.gestures)
    };
  }
  
  /**
   * 重置输入统计
   */
  resetStats() {
    this.inputStats = {
      keyPresses: 0,
      touchEvents: 0,
      gestures: new Map()
    };
    
    console.log('🎮 输入统计重置完成');
  }
  
  /**
   * 检查是否有按键被按下
   * @param {string} key - 按键名称
   */
  isKeyPressed(key) {
    return this.pressedKeys.has(key);
  }
  
  /**
   * 获取当前按下的所有按键
   */
  getPressedKeys() {
    return Array.from(this.pressedKeys);
  }
  
  /**
   * 强制释放所有按键
   */
  releaseAllKeys() {
    this.pressedKeys.clear();
    
    // 清除所有重复计时器
    this.keyRepeatTimers.forEach(timer => clearInterval(timer));
    this.keyRepeatTimers.clear();
    
    console.log('🎮 所有按键已释放');
  }
  
  /**
   * 销毁输入处理器
   */
  destroy() {
    // 移除事件监听器
    if (typeof wx !== 'undefined') {
      wx.offKeyDown(this._keyDownHandler);
      wx.offKeyUp(this._keyUpHandler);
    }
    
    // 清理计时器
    this.keyRepeatTimers.forEach(timer => clearInterval(timer));
    this.keyRepeatTimers.clear();
    this._cancelLongPress();
    
    // 清理状态
    this.pressedKeys.clear();
    this.touchState.isActive = false;
    
    // 移除所有事件监听器
    this.removeAllListeners();
    
    console.log('🎮 InputHandler销毁完成');
  }
} 