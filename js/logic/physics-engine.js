/**
 * 物理引擎
 * 负责处理方块的物理行为，包括重力下落、碰撞检测等
 */
import Emitter from '../libs/tinyemitter.js';

export default class PhysicsEngine extends Emitter {
  constructor(grid, gameState) {
    super();
    
    this.grid = grid;
    this.gameState = gameState;
    
    // 当前活动方块
    this.activeTetromino = null;
    this.nextTetromino = null;
    
    // 下落速度控制
    this.fallSpeed = 30; // 基础下落间隔帧数
    this.fallTimer = 0;
    
    console.log('✅ PhysicsEngine初始化完成');
  }
  
  /**
   * 设置当前活动方块
   * @param {Tetromino} tetromino - 活动方块
   */
  setActiveTetromino(tetromino) {
    this.activeTetromino = tetromino;
  }
  
  /**
   * 设置下一个方块
   * @param {Tetromino} tetromino - 下一个方块
   */
  setNextTetromino(tetromino) {
    this.nextTetromino = tetromino;
  }
  
  /**
   * 更新物理系统
   */
  update() {
    // 更新活动方块的下落
    if (this.activeTetromino && !this.activeTetromino.isLocked) {
      this._updateActiveTetromino();
    }
  }
  
  /**
   * 更新活动方块
   * @private
   */
  _updateActiveTetromino() {
    // 检查是否可以下落
    const canMoveDown = this.activeTetromino.canMoveDown(this.grid);
    
    if (!canMoveDown) {
      // 不能下落，锁定方块
      this._lockActiveTetromino();
      return;
    }
    
    // 应用下落逻辑
    this.fallTimer++;
    if (this.fallTimer >= this.fallSpeed) {
      this.fallTimer = 0;
      this.activeTetromino.moveDown();
    }
  }
  
  /**
   * 锁定当前活动方块
   * @private
   */
  _lockActiveTetromino() {
    if (!this.activeTetromino) return;
    
    // 获取方块位置
    const positions = this.activeTetromino.getBlockPositions();
    
    // 放置到网格
    this.activeTetromino.placeOnGrid(this.grid);
    
    // 发出锁定事件
    this.emit('tetrominolocked', { positions });
    
    // 清除活动方块
    this.activeTetromino = null;
  }
  
  /**
   * 移动活动方块
   * @param {string} direction - 移动方向 ('left' 或 'right')
   */
  moveActiveTetromino(direction) {
    if (!this.activeTetromino || this.activeTetromino.isLocked) return;
    
    if (direction === 'left' && this.activeTetromino.canMoveLeft(this.grid)) {
      this.activeTetromino.moveLeft();
    } else if (direction === 'right' && this.activeTetromino.canMoveRight(this.grid)) {
      this.activeTetromino.moveRight();
    }
  }
  
  /**
   * 旋转活动方块
   */
  rotateActiveTetromino() {
    if (!this.activeTetromino || this.activeTetromino.isLocked) return;
    
    // 保存原始状态
    const originalRotation = this.activeTetromino.rotation;
    const originalPosition = { ...this.activeTetromino.position };
    
    // 尝试旋转
    this.activeTetromino.rotate();
    
    // 检查旋转后是否有效
    if (!this.activeTetromino.isValidPosition(this.grid)) {
      // 恢复原状态
      this.activeTetromino.rotation = originalRotation;
      this.activeTetromino.position = originalPosition;
    }
  }
  
  /**
   * 硬下落
   */
  hardDrop() {
    if (!this.activeTetromino || this.activeTetromino.isLocked) return;
    
    // 快速下落到底部
    while (this.activeTetromino.canMoveDown(this.grid)) {
      this.activeTetromino.moveDown();
    }
    
    // 立即锁定
    this._lockActiveTetromino();
  }
  
  /**
   * 应用重力使方块下落
   * @param {Array} removedPositions - 被移除的方块位置
   * @param {Array} columnsToCheck - 需要检查的列
   * @param {Set} blocksToCheck - 需要检查的方块
   * @returns {boolean} 是否有方块下落
   */
  applyGravity(removedPositions = [], columnsToCheck = [], blocksToCheck = new Set()) {
    console.log('🌍 PhysicsEngine.applyGravity 调用', {
      removedPositions: removedPositions.length,
      columnsToCheck: columnsToCheck.length,
      blocksToCheck: blocksToCheck.size
    });
    
    // 委托给网格的重力系统
    const hasFallen = this.grid.applyGravity(
      new Set(columnsToCheck), // 转换为Set
      blocksToCheck,           // 直接传递Set
      removedPositions         // 被移除的位置
    );
    
    if (hasFallen) {
      this.emit('gravityapplied', { 
        affectedColumns: columnsToCheck 
      });
    }
    
    return hasFallen;
  }
  
  /**
   * 设置下落速度
   * @param {number} speed - 下落间隔帧数
   */
  setFallSpeed(speed) {
    this.fallSpeed = Math.max(1, speed);
  }
  
  /**
   * 触发方块接地事件
   */
  _onTetrominoGrounded() {
    this.emit('tetrominogrounded');
  }
  
  /**
   * 获取物理状态
   * @returns {Object} 物理状态信息
   */
  getPhysicsStatus() {
    return {
      hasActiveTetromino: !!this.activeTetromino,
      fallSpeed: this.fallSpeed,
      fallTimer: this.fallTimer
    };
  }
  
  /**
   * 重置物理引擎
   */
  reset() {
    this.activeTetromino = null;
    this.nextTetromino = null;
    this.fallTimer = 0;
    console.log('🔄 PhysicsEngine 重置完成');
  }
  
  /**
   * 销毁物理引擎
   */
  destroy() {
    this.removeAllListeners();
    this.activeTetromino = null;
    this.nextTetromino = null;
    console.log('🗑️ PhysicsEngine 销毁完成');
  }
} 