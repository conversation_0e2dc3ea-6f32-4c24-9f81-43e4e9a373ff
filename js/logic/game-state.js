/**
 * 游戏状态管理器
 * 负责管理游戏状态、分数、连击、计时器等核心状态
 */
import Emitter from '../libs/tinyemitter.js';

// 游戏状态枚举
export const GAME_STATE = {
  READY: 'ready',         // 准备开始
  PLAYING: 'playing',     // 游戏中
  CHECKING: 'checking',   // 检查匹配
  ANIMATING: 'animating', // 动画中
  PAUSED: 'paused',       // 暂停
  GAME_OVER: 'gameover',  // 游戏结束
  LEVEL_CLEAR: 'levelclear' // 关卡通关
};

// 游戏配置常量
export const GAME_CONFIG = {
  SOFT_DROP_SPEED: 20,     // 软下落的速度倍数
  BASE_SPEED: 30,          // 基础下落间隔帧数
  LOCK_DELAY: 30,          // 锁定延迟帧数
  EFFECTS_DELAY: 15,       // 特效延迟帧数
  DESTROY_DURATION: 20,    // 消除动画持续时间
  MAX_LOCK_RESETS: 15      // 最多允许重置15次
};

export default class GameState extends Emitter {
  constructor(options = {}) {
    super();
    
    // 游戏配置
    this.options = Object.assign({
      level: 1,
      colorCount: 4,
      effectProbability: 0.1,
    }, options);
    
    // 游戏状态
    this.state = GAME_STATE.READY;
    this.previousState = null;
    
    // 分数系统
    this.score = 0;
    this.combo = 0;
    this.level = this.options.level;
    
    // 计时器系统
    this.timers = {
      fall: 0,          // 下落计时器
      lock: 0,          // 锁定计时器
      animation: 0,     // 动画计时器
      effects: 0,       // 特效计时器
      frame: 0          // 帧计数器
    };
    
    // 锁定重置系统
    this.lockResetCount = 0;
    
    // 游戏控制标记
    this.isPaused = false;
    this.isSoftDropping = false;
    this.isAnimating = false;
    this.hasAnimations = false;
    this.hasMatches = false;
    this._pendingAfterAnimationCheck = false;
    
    // 存储相关数据
    this.effectsToApply = [];
    this.affectedBlocks = new Set();
    this.lastLockedBlocks = new Set();
    this.lastRemovedPositions = [];
    
    console.log(`🎮 [${Date.now()}] GameState构造函数完成 - score=${this.score}, level=${this.level}`);
  }
  
  /**
   * 改变游戏状态
   * @param {string} newState - 新状态
   * @param {Object} data - 附加数据
   */
  changeState(newState, data = {}) {
    const oldState = this.state;
    this.previousState = oldState;
    this.state = newState;
    
    console.log(`🎮 状态变化: ${oldState} -> ${newState}`, data);
    
    // 发射状态变化事件
    this.emit('statechange', {
      from: oldState,
      to: newState,
      data
    });
    
    // 发射具体的状态事件
    this.emit(`state:${newState}`, data);
  }
  
  /**
   * 获取当前状态
   */
  getState() {
    return this.state;
  }
  
  /**
   * 检查是否为指定状态
   */
  isState(state) {
    return this.state === state;
  }
  
  /**
   * 暂停游戏
   */
  pause() {
    if (this.state === GAME_STATE.PLAYING) {
      this.isPaused = true;
      this.changeState(GAME_STATE.PAUSED);
    }
  }
  
  /**
   * 恢复游戏
   */
  resume() {
    if (this.state === GAME_STATE.PAUSED) {
      this.isPaused = false;
      this.changeState(GAME_STATE.PLAYING);
    }
  }
  
  /**
   * 开始游戏
   */
  start() {
    this.reset();
    this.changeState(GAME_STATE.PLAYING);
  }
  
  /**
   * 游戏结束
   */
  gameOver() {
    this.changeState(GAME_STATE.GAME_OVER, { 
      finalScore: this.score,
      finalCombo: this.combo,
      level: this.level
    });
  }
  
  /**
   * 重置游戏状态
   */
  reset() {
    this.score = 0;
    this.combo = 0;
    this.lockResetCount = 0;
    this.isPaused = false;
    this.isSoftDropping = false;
    this.isAnimating = false;
    this.hasAnimations = false;
    this.hasMatches = false;
    this._pendingAfterAnimationCheck = false;
    
    // 重置所有计时器
    Object.keys(this.timers).forEach(key => {
      this.timers[key] = 0;
    });
    
    // 清空数据集合
    this.effectsToApply = [];
    this.affectedBlocks.clear();
    this.lastLockedBlocks.clear();
    this.lastRemovedPositions = [];
    
    this.changeState(GAME_STATE.READY);
    
    console.log(`🎮 游戏状态重置完成 - score=${this.score}`);
  }
  
  /**
   * 更新分数
   * @param {number} points - 增加的分数
   * @param {string} reason - 得分原因
   */
  addScore(points, reason = 'unknown') {
    const oldScore = this.score;
    this.score += points;
    
    console.log(`💰 得分: +${points} (${reason}) - 总分: ${this.score}`);
    
    this.emit('scorechange', {
      points,
      reason,
      oldScore,
      newScore: this.score
    });
  }
  
  /**
   * 更新连击数
   * @param {number} combo - 连击数
   */
  setCombo(combo) {
    const oldCombo = this.combo;
    this.combo = combo;
    
    if (combo > oldCombo) {
      console.log(`🔥 连击: ${combo}x`);
      this.emit('comboincrease', {
        oldCombo,
        newCombo: combo
      });
    } else if (combo === 0 && oldCombo > 0) {
      console.log(`💫 连击结束: ${oldCombo}x`);
      this.emit('comboend', {
        finalCombo: oldCombo
      });
    }
  }
  
  /**
   * 增加连击数
   */
  incrementCombo() {
    this.setCombo(this.combo + 1);
  }
  
  /**
   * 重置连击数
   */
  resetCombo() {
    this.setCombo(0);
  }
  
  /**
   * 更新等级
   * @param {number} level - 新等级
   */
  setLevel(level) {
    const oldLevel = this.level;
    this.level = level;
    
    if (level > oldLevel) {
      console.log(`⬆️ 升级: Level ${level}`);
      this.emit('levelup', {
        oldLevel,
        newLevel: level
      });
    }
  }
  
  /**
   * 更新计时器
   * @param {string} timerName - 计时器名称
   * @param {number} value - 新值，如果为null则增加1
   */
  updateTimer(timerName, value = null) {
    if (this.timers.hasOwnProperty(timerName)) {
      if (value !== null) {
        this.timers[timerName] = value;
      } else {
        this.timers[timerName]++;
      }
    }
  }
  
  /**
   * 获取计时器值
   * @param {string} timerName - 计时器名称
   */
  getTimer(timerName) {
    return this.timers[timerName] || 0;
  }
  
  /**
   * 重置计时器
   * @param {string} timerName - 计时器名称
   */
  resetTimer(timerName) {
    if (this.timers.hasOwnProperty(timerName)) {
      this.timers[timerName] = 0;
    }
  }
  
  /**
   * 锁定重置相关方法
   */
  canResetLock() {
    return this.lockResetCount < GAME_CONFIG.MAX_LOCK_RESETS;
  }
  
  resetLock() {
    if (this.canResetLock()) {
      this.lockResetCount++;
      this.resetTimer('lock');
      return true;
    }
    return false;
  }
  
  resetLockCount() {
    this.lockResetCount = 0;
  }
  
  /**
   * 动画状态管理
   */
  setAnimating(isAnimating) {
    this.isAnimating = isAnimating;
    this.hasAnimations = isAnimating;
  }
  
  setPendingCheck(pending) {
    this._pendingAfterAnimationCheck = pending;
  }
  
  isPendingCheck() {
    return this._pendingAfterAnimationCheck;
  }
  
  /**
   * 获取完整的游戏状态快照
   */
  getSnapshot() {
    return {
      state: this.state,
      previousState: this.previousState,
      score: this.score,
      combo: this.combo,
      level: this.level,
      timers: { ...this.timers },
      lockResetCount: this.lockResetCount,
      flags: {
        isPaused: this.isPaused,
        isSoftDropping: this.isSoftDropping,
        isAnimating: this.isAnimating,
        hasAnimations: this.hasAnimations,
        hasMatches: this.hasMatches,
        pendingAfterAnimationCheck: this._pendingAfterAnimationCheck
      },
      collections: {
        effectsToApplyCount: this.effectsToApply.length,
        affectedBlocksCount: this.affectedBlocks.size,
        lastLockedBlocksCount: this.lastLockedBlocks.size,
        lastRemovedPositionsCount: this.lastRemovedPositions.length
      }
    };
  }
  
  /**
   * 销毁状态管理器
   */
  destroy() {
    this.removeAllListeners();
    
    // 清空所有集合
    this.effectsToApply = [];
    this.affectedBlocks.clear();
    this.lastLockedBlocks.clear();
    this.lastRemovedPositions = [];
    
    console.log('🎮 GameState销毁完成');
  }
} 