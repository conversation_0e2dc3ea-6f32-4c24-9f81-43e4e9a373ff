/**
 * 连击通知UI组件
 * 在屏幕中央显示连击信息，确保玩家能看到
 */

export default class ComboNotification {
  constructor() {
    this.notifications = [];
    this.maxNotifications = 5;
    
    console.log('连击通知UI组件初始化');
  }
  
  /**
   * 显示连击通知
   * @param {Object} data - 连击数据
   */
  showComboNotification(data) {
    const notification = {
      id: Date.now(),
      type: 'combo',
      combo: data.combo,
      multiplier: data.multiplier || 1,
      stage: data.stage,
      x: 200, // 调整到游戏区域左侧
      y: 150, // 调整到更靠上的位置
      alpha: 1.0,
      scale: 1.5, // 增大显示
      life: 240, // 延长显示时间到4秒
      maxLife: 240
    };
    
    this.notifications.push(notification);
    
    // 限制通知数量
    if (this.notifications.length > this.maxNotifications) {
      this.notifications.shift();
    }
    
    console.log(`🎯 显示连击通知: ${data.combo}连击 ${data.multiplier.toFixed(2)}x`);
  }
  
  /**
   * 显示能量通知
   * @param {Object} data - 能量数据
   */
  showEnergyNotification(data) {
    const notification = {
      id: Date.now(),
      type: 'energy',
      level: data.level,
      energy: data.energy,
      x: 200,
      y: 200, // 在连击通知下方
      alpha: 1.0,
      scale: 1.3,
      life: 240, // 4秒显示时间
      maxLife: 240
    };
    
    this.notifications.push(notification);
    
    console.log(`⚡ 显示能量通知: ${data.level.name} ${data.energy}能量`);
  }
  
  /**
   * 显示爆发通知
   * @param {Object} data - 爆发数据
   */
  showBurstNotification(data) {
    const notification = {
      id: Date.now(),
      type: 'burst',
      level: data.level,
      multiplier: data.finalMultiplier,
      x: 200,
      y: 100, // 在连击通知上方
      alpha: 1.0,
      scale: 2.5, // 更大的显示
      life: 360, // 6秒显示时间
      maxLife: 360
    };
    
    this.notifications.push(notification);
    
    console.log(`💥 显示爆发通知: ${data.level.name} ${data.finalMultiplier.toFixed(2)}x`);
  }
  
  /**
   * 更新通知
   */
  update() {
    for (let i = this.notifications.length - 1; i >= 0; i--) {
      const notification = this.notifications[i];
      
      notification.life--;
      
      // 计算透明度和缩放
      const progress = notification.life / notification.maxLife;
      notification.alpha = Math.max(0, progress);
      
      // 移除过期通知
      if (notification.life <= 0) {
        this.notifications.splice(i, 1);
      }
    }
  }
  
  /**
   * 渲染通知
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (this.notifications.length === 0) return;
    
    ctx.save();
    
    for (const notification of this.notifications) {
      this._renderNotification(ctx, notification);
    }
    
    ctx.restore();
  }
  
  /**
   * 渲染单个通知
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} notification - 通知对象
   * @private
   */
  _renderNotification(ctx, notification) {
    ctx.save();
    
    // 设置透明度
    ctx.globalAlpha = notification.alpha;
    
    // 移动到通知位置
    ctx.translate(notification.x, notification.y);
    ctx.scale(notification.scale, notification.scale);
    
    switch (notification.type) {
      case 'combo':
        this._renderComboNotification(ctx, notification);
        break;
      case 'energy':
        this._renderEnergyNotification(ctx, notification);
        break;
      case 'burst':
        this._renderBurstNotification(ctx, notification);
        break;
    }
    
    ctx.restore();
  }
  
  /**
   * 渲染连击通知
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} notification - 通知对象
   * @private
   */
  _renderComboNotification(ctx, notification) {
    // 添加发光效果
    const time = (notification.maxLife - notification.life) * 0.1;
    const glow = 0.5 + Math.sin(time) * 0.5;

    // 外发光
    ctx.shadowColor = '#FFD700';
    ctx.shadowBlur = 20 * glow;

    // 背景（更大更明显）
    ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
    ctx.fillRect(-120, -40, 240, 80);

    // 边框（更粗更亮）
    const stageColor = notification.stage ? notification.stage.color : '#FFD700';
    ctx.strokeStyle = stageColor;
    ctx.lineWidth = 4;
    ctx.strokeRect(-120, -40, 240, 80);

    // 重置阴影
    ctx.shadowBlur = 0;

    // 连击文字（更大）
    ctx.fillStyle = stageColor;
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(`${notification.combo}连击!`, 0, -8);

    // 倍数文字（更大更亮）
    ctx.fillStyle = '#FFD700';
    ctx.font = 'bold 20px Arial';
    ctx.fillText(`${notification.multiplier.toFixed(1)}x`, 0, 18);

    // 阶段名称
    if (notification.stage) {
      ctx.fillStyle = '#FFF';
      ctx.font = 'bold 14px Arial';
      ctx.fillText(notification.stage.name, 0, -30);
    }
  }
  
  /**
   * 渲染能量通知
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} notification - 通知对象
   * @private
   */
  _renderEnergyNotification(ctx, notification) {
    // 背景
    ctx.fillStyle = 'rgba(0, 100, 200, 0.8)';
    ctx.fillRect(-80, -25, 160, 50);
    
    // 边框
    ctx.strokeStyle = '#00BFFF';
    ctx.lineWidth = 2;
    ctx.strokeRect(-80, -25, 160, 50);
    
    // 能量等级
    ctx.fillStyle = '#00BFFF';
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(notification.level.name, 0, -5);
    
    // 能量值
    ctx.fillStyle = '#FFF';
    ctx.font = '14px Arial';
    ctx.fillText(`${notification.energy}能量`, 0, 10);
  }
  
  /**
   * 渲染爆发通知
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} notification - 通知对象
   * @private
   */
  _renderBurstNotification(ctx, notification) {
    // 背景（更大更醒目）
    ctx.fillStyle = 'rgba(255, 0, 100, 0.9)';
    ctx.fillRect(-120, -40, 240, 80);
    
    // 边框
    ctx.strokeStyle = '#FFD700';
    ctx.lineWidth = 4;
    ctx.strokeRect(-120, -40, 240, 80);
    
    // 爆发等级
    ctx.fillStyle = '#FFD700';
    ctx.font = 'bold 28px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(notification.level.name, 0, -10);
    
    // 倍数
    ctx.fillStyle = '#FFF';
    ctx.font = 'bold 20px Arial';
    ctx.fillText(`${notification.multiplier.toFixed(1)}x 爆发!`, 0, 15);
    
    // 添加闪烁效果
    const time = (notification.maxLife - notification.life) * 0.2;
    const glow = 0.5 + Math.sin(time) * 0.5;
    ctx.shadowColor = '#FFD700';
    ctx.shadowBlur = 20 * glow;
    ctx.strokeRect(-120, -40, 240, 80);
  }
  
  /**
   * 清除所有通知
   */
  clear() {
    this.notifications = [];
  }
  
  /**
   * 获取通知数量
   * @returns {number} 当前通知数量
   */
  getNotificationCount() {
    return this.notifications.length;
  }
}
