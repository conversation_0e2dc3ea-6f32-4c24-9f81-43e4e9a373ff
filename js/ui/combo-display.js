/**
 * 连击显示UI组件
 * 显示连击数、能量条、爆发按钮等
 */

export default class ComboDisplay {
  constructor(options = {}) {
    // 位置和大小配置
    this.x = options.x || 20;
    this.y = options.y || 20;
    this.width = options.width || 250;
    this.height = options.height || 120;
    
    // 组件状态
    this.combo = 0;
    this.energy = 0;
    this.maxEnergy = 500;
    this.comboStage = null;
    this.energyLevel = null;
    this.patienceMultiplier = 1.0;
    this.patienceTime = 0;
    
    // 动画状态
    this.pulseTimer = 0;
    this.glowIntensity = 0;
      this.burstAnimation = Math.max(0, this.burstAnimation - 0.05);
    this.shakeOffset = { x: 0, y: 0 };
    
    // 颜色配置
    this.colors = {
      background: 'rgba(0, 0, 0, 0.8)',
      border: '#333',
      text: '#FFF',
      energy: {
        low: '#4CAF50',
        medium: '#FF9800', 
        high: '#F44336',
        max: '#9C27B0'
      },
      combo: {
        normal: '#FFF',
        stage1: '#4CAF50',
        stage2: '#FF9800',
        stage3: '#F44336',
        stage4: '#9C27B0',
        stage5: '#FFD700'
      }
    };
    
    // 爆发按钮状态
    this.burstButton = {
      x: this.x + this.width - 60,
      y: this.y + this.height - 40,
      width: 50,
      height: 30,
      enabled: false,
      pressed: false,
      hover: false
    };
    
    console.log('连击显示UI组件初始化完成');
  }
  
  /**
   * 更新显示数据
   * @param {Object} status - 连击系统状态
   */
  updateStatus(status) {
    this.combo = status.combo;
    this.energy = status.energy;
    this.maxEnergy = status.maxEnergy;
    this.comboStage = status.comboStage;
    this.energyLevel = status.energyLevel;
    this.patienceMultiplier = status.patienceMultiplier;
    this.patienceTime = status.patienceTime;

    // 更新爆发按钮状态
    this.burstButton.enabled = this.energy >= 25;
  }
  
  /**
   * 更新动画
   */
  update() {
    // 更新脉冲动画
    this.pulseTimer += 0.1;
    
    // 更新光芒强度
    if (this.energyLevel) {
      this.glowIntensity = 0.5 + Math.sin(this.pulseTimer * 2) * 0.3;
    } else {
      this.glowIntensity = 0;
    }
    
    // 更新爆发动画
    if (this.burstAnimation > 0) {
      this.burstAnimation = Math.max(0, this.burstAnimation - 0.05);
      
      // 震动效果
      const intensity = this.burstAnimation * 5;
      this.shakeOffset.x = (Math.random() - 0.5) * intensity;
      this.shakeOffset.y = (Math.random() - 0.5) * intensity;
    } else {
      this.shakeOffset.x = 0;
      this.shakeOffset.y = 0;
    }
  }
  
  /**
   * 渲染UI组件
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    ctx.save();
    
    // 应用震动偏移
    const renderX = this.x + this.shakeOffset.x;
    const renderY = this.y + this.shakeOffset.y;
    
    // 绘制背景
    this._renderBackground(ctx, renderX, renderY);
    
    // 绘制连击信息
    this._renderComboInfo(ctx, renderX, renderY);
    
    // 绘制能量条
    this._renderEnergyBar(ctx, renderX, renderY);
    
    // 绘制耐心奖励
    this._renderPatienceInfo(ctx, renderX, renderY);
    
    // 绘制爆发按钮
    this._renderBurstButton(ctx, renderX, renderY);
    
    // 绘制光芒效果
    if (this.glowIntensity > 0) {
      this._renderGlowEffect(ctx, renderX, renderY);
    }
    
    ctx.restore();
  }
  
  /**
   * 渲染背景
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @private
   */
  _renderBackground(ctx, x, y) {
    // 主背景
    ctx.fillStyle = this.colors.background;
    ctx.fillRect(x, y, this.width, this.height);
    
    // 边框
    ctx.strokeStyle = this.colors.border;
    ctx.lineWidth = 2;
    ctx.strokeRect(x, y, this.width, this.height);
    
    // 阶段背景色
    if (this.comboStage) {
      ctx.fillStyle = this.comboStage.color + '20'; // 20% 透明度
      ctx.fillRect(x + 2, y + 2, this.width - 4, this.height - 4);
    }
  }
  
  /**
   * 渲染连击信息
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @private
   */
  _renderComboInfo(ctx, x, y) {
    // 连击数字
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    
    // 连击颜色
    let comboColor = this.colors.combo.normal;
    if (this.comboStage) {
      comboColor = this.comboStage.color;
    }
    
    // 脉冲效果
    const pulse = 1 + Math.sin(this.pulseTimer * 3) * 0.1;
    ctx.save();
    ctx.scale(pulse, pulse);
    
    ctx.fillStyle = comboColor;
    ctx.fillText(`连击: ${this.combo}`, (x + 10) / pulse, (y + 10) / pulse);
    
    ctx.restore();
    
    // 连击阶段名称
    if (this.comboStage) {
      ctx.font = '14px Arial';
      ctx.fillStyle = this.comboStage.color;
      ctx.fillText(this.comboStage.name, x + 10, y + 40);
      
      // 倍数显示
      ctx.fillStyle = '#FFF';
      ctx.fillText(`${this.comboStage.multiplier.toFixed(1)}x`, x + 120, y + 40);
    }
  }
  
  /**
   * 渲染能量条
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @private
   */
  _renderEnergyBar(ctx, x, y) {
    const barX = x + 10;
    const barY = y + 60;
    const barWidth = this.width - 80;
    const barHeight = 20;

    // 能量条背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.fillRect(barX, barY, barWidth, barHeight);

    // 安全的能量值处理
    const safeEnergy = isNaN(this.energy) ? 0 : Math.max(0, this.energy);
    const safeMaxEnergy = isNaN(this.maxEnergy) || this.maxEnergy <= 0 ? 500 : this.maxEnergy;

    // 能量条填充
    const fillRatio = Math.min(1, safeEnergy / safeMaxEnergy);
    const fillWidth = fillRatio * barWidth;

    // 根据能量等级选择颜色
    let energyColor = this.colors.energy.low;
    if (safeEnergy >= 200) energyColor = this.colors.energy.max;
    else if (safeEnergy >= 100) energyColor = this.colors.energy.high;
    else if (safeEnergy >= 50) energyColor = this.colors.energy.medium;

    // 只有当fillWidth有效时才创建渐变
    if (fillWidth > 0 && isFinite(fillWidth)) {
      // 渐变填充
      const gradient = ctx.createLinearGradient(barX, barY, barX + fillWidth, barY);
      gradient.addColorStop(0, energyColor);
      gradient.addColorStop(1, energyColor + 'AA');

      ctx.fillStyle = gradient;
      ctx.fillRect(barX, barY, fillWidth, barHeight);
    }
    
    // 能量条边框
    ctx.strokeStyle = '#FFF';
    ctx.lineWidth = 1;
    ctx.strokeRect(barX, barY, barWidth, barHeight);
    
    // 能量数值
    ctx.font = '12px Arial';
    ctx.fillStyle = '#FFF';
    ctx.textAlign = 'center';
    ctx.fillText(`${safeEnergy}/${safeMaxEnergy}`, barX + barWidth / 2, barY + 14);
    
    // 能量等级标记
    if (this.energyLevel) {
      ctx.font = 'bold 10px Arial';
      ctx.fillStyle = energyColor;
      ctx.textAlign = 'right';
      ctx.fillText(this.energyLevel.name, barX + barWidth - 5, barY - 5);
    }
  }
  
  /**
   * 渲染耐心信息
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @private
   */
  _renderPatienceInfo(ctx, x, y) {
    if (this.patienceMultiplier > 1.0) {
      ctx.font = '12px Arial';
      ctx.fillStyle = '#FFD700';
      ctx.textAlign = 'left';
      ctx.fillText(`耐心奖励: ${this.patienceMultiplier.toFixed(1)}x`, x + 10, y + 95);
      
      // 时间显示
      ctx.fillStyle = '#CCC';
      ctx.fillText(`${this.patienceTime}秒`, x + 120, y + 95);
    }
  }
  
  /**
   * 渲染爆发按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @private
   */
  _renderBurstButton(ctx, x, y) {
    const btn = this.burstButton;
    const btnX = x + this.width - 60;
    const btnY = y + this.height - 40;
    
    // 按钮背景
    if (btn.enabled) {
      ctx.fillStyle = btn.pressed ? '#FF6B35' : (btn.hover ? '#FF8C42' : '#FF9800');
    } else {
      ctx.fillStyle = '#666';
    }
    
    ctx.fillRect(btnX, btnY, btn.width, btn.height);
    
    // 按钮边框
    ctx.strokeStyle = btn.enabled ? '#FFF' : '#999';
    ctx.lineWidth = 2;
    ctx.strokeRect(btnX, btnY, btn.width, btn.height);
    
    // 按钮文字
    ctx.font = 'bold 12px Arial';
    ctx.fillStyle = btn.enabled ? '#FFF' : '#999';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('爆发', btnX + btn.width / 2, btnY + btn.height / 2);
    
    // 能量充满时的特效
    if (this.energy >= this.maxEnergy) {
      const pulse = 1 + Math.sin(this.pulseTimer * 5) * 0.2;
      ctx.save();
      ctx.globalAlpha = 0.7;
      ctx.strokeStyle = '#FFD700';
      ctx.lineWidth = 3;
      ctx.scale(pulse, pulse);
      ctx.strokeRect((btnX - 2) / pulse, (btnY - 2) / pulse, (btn.width + 4) / pulse, (btn.height + 4) / pulse);
      ctx.restore();
    }
  }
  
  /**
   * 渲染光芒效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @private
   */
  _renderGlowEffect(ctx, x, y) {
    const centerX = x + this.width / 2;
    const centerY = y + this.height / 2;
    const radius = Math.max(this.width, this.height) * 0.6;
    
    // 创建径向渐变
    const gradient = ctx.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, radius
    );
    
    const glowColor = this.energyLevel ? 
      (this.energyLevel.threshold >= 200 ? '#9C27B0' : '#FF9800') : 
      '#4CAF50';
    const alpha = Math.round(this.glowIntensity * 255)
      .toString(16).padStart(2, '0');
    gradient.addColorStop(0, glowColor + alpha);
    gradient.addColorStop(1, glowColor + '00');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(x - radius / 2, y - radius / 2, this.width + radius, this.height + radius);
  }
  
  /**
   * 触发爆发动画
   */
  triggerBurstAnimation() {
    this.burstAnimation = 1.0;
  }
  
  /**
   * 检查点击事件
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否点击了爆发按钮
   */
  checkClick(x, y) {
    const btn = this.burstButton;
    const btnX = this.x + this.width - 60;
    const btnY = this.y + this.height - 40;
    
    if (x >= btnX && x <= btnX + btn.width &&
        y >= btnY && y <= btnY + btn.height &&
        btn.enabled) {
      return true;
    }
    
    return false;
  }
  
  /**
   * 设置按钮状态
   * @param {boolean} pressed - 是否按下
   * @param {boolean} hover - 是否悬停
   */
  setBurstButtonState(pressed = false, hover = false) {
    this.burstButton.pressed = pressed;
    this.burstButton.hover = hover;
  }
  
  /**
   * 设置位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
  }
  
  /**
   * 设置大小
   * @param {number} width - 宽度
   * @param {number} height - 高度
   */
  setSize(width, height) {
    this.width = width;
    this.height = height;
  }
}
