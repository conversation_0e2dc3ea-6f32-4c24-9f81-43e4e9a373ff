/**
 * 垃圾生成预警UI组件
 * 显示底部方块即将生成的预警信息
 */

export default class GarbageWarning {
  /**
   * 创建垃圾预警组件
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    // 显示配置
    this.x = options.x || 20;
    this.y = options.y || 100;
    this.width = options.width || 200;
    this.height = options.height || 60;
    
    // 预警状态
    this.isVisible = false;
    this.timeLeft = 0;
    this.maxTime = 300; // 5秒预警时间
    
    // 动画配置
    this.pulseTimer = 0;
    this.pulseSpeed = 0.1;
    this.flashTimer = 0;
    this.flashSpeed = 0.2;
    
    // 颜色配置
    this.warningColor = '#FF6B35';
    this.urgentColor = '#FF0000';
    this.textColor = '#FFFFFF';
    this.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    
    console.log('垃圾预警UI组件初始化');
  }
  
  /**
   * 显示预警
   * @param {number} timeLeft - 剩余时间（帧数）
   */
  showWarning(timeLeft) {
    this.isVisible = true;
    this.timeLeft = timeLeft;
    this.maxTime = timeLeft;
    this.pulseTimer = 0;
    this.flashTimer = 0;
    
    console.log(`显示垃圾生成预警：${Math.floor(timeLeft / 60)}秒后生成`);
  }
  
  /**
   * 隐藏预警
   */
  hideWarning() {
    this.isVisible = false;
    this.timeLeft = 0;
    console.log('隐藏垃圾生成预警');
  }
  
  /**
   * 更新预警状态
   * @param {number} deltaTime - 时间增量（帧数）
   */
  update(deltaTime = 1) {
    if (!this.isVisible) return;
    
    // 更新剩余时间
    this.timeLeft = Math.max(0, this.timeLeft - deltaTime);
    
    // 更新动画计时器
    this.pulseTimer += this.pulseSpeed;
    this.flashTimer += this.flashSpeed;
    
    // 时间用完时隐藏预警
    if (this.timeLeft <= 0) {
      this.hideWarning();
    }
  }
  
  /**
   * 渲染预警UI
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isVisible) return;
    
    // 保存上下文
    ctx.save();
    
    // 计算进度和紧急程度
    const progress = this.timeLeft / this.maxTime;
    const isUrgent = progress < 0.3; // 剩余时间少于30%时进入紧急状态
    const seconds = Math.ceil(this.timeLeft / 60);
    
    // 计算动画效果
    const pulseScale = 1 + Math.sin(this.pulseTimer) * 0.1;
    const flashAlpha = isUrgent ? (0.7 + Math.sin(this.flashTimer * 3) * 0.3) : 1;
    
    // 选择颜色
    const currentColor = isUrgent ? this.urgentColor : this.warningColor;
    
    // 绘制背景
    ctx.fillStyle = this.backgroundColor;
    ctx.fillRect(this.x, this.y, this.width, this.height);
    
    // 绘制边框（带脉冲效果）
    ctx.strokeStyle = currentColor;
    ctx.lineWidth = 3 * pulseScale;
    ctx.globalAlpha = flashAlpha;
    ctx.strokeRect(this.x, this.y, this.width, this.height);
    
    // 绘制进度条
    const progressBarHeight = 6;
    const progressBarY = this.y + this.height - progressBarHeight - 5;
    
    // 进度条背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.fillRect(this.x + 10, progressBarY, this.width - 20, progressBarHeight);
    
    // 进度条填充
    const progressWidth = (this.width - 20) * progress;
    ctx.fillStyle = currentColor;
    ctx.fillRect(this.x + 10, progressBarY, progressWidth, progressBarHeight);
    
    // 绘制警告图标
    const iconX = this.x + 15;
    const iconY = this.y + 15;
    const iconSize = 20;
    
    ctx.fillStyle = currentColor;
    ctx.globalAlpha = flashAlpha;
    
    // 绘制三角形警告图标
    ctx.beginPath();
    ctx.moveTo(iconX + iconSize / 2, iconY);
    ctx.lineTo(iconX, iconY + iconSize);
    ctx.lineTo(iconX + iconSize, iconY + iconSize);
    ctx.closePath();
    ctx.fill();
    
    // 绘制感叹号
    ctx.fillStyle = this.backgroundColor;
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('!', iconX + iconSize / 2, iconY + iconSize - 4);
    
    // 绘制主要文本
    ctx.fillStyle = this.textColor;
    ctx.globalAlpha = 1;
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('底部生成预警', this.x + 45, this.y + 20);
    
    // 绘制倒计时
    ctx.font = 'bold 14px Arial';
    ctx.fillStyle = currentColor;
    ctx.fillText(`${seconds}秒后生成`, this.x + 45, this.y + 38);
    
    // 绘制提示文本
    if (isUrgent) {
      ctx.font = '12px Arial';
      ctx.fillStyle = this.urgentColor;
      ctx.fillText('准备应对！', this.x + 130, this.y + 38);
    }
    
    // 恢复上下文
    ctx.restore();
  }
  
  /**
   * 设置位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
  }
  
  /**
   * 设置大小
   * @param {number} width - 宽度
   * @param {number} height - 高度
   */
  setSize(width, height) {
    this.width = width;
    this.height = height;
  }
  
  /**
   * 检查是否可见
   * @returns {boolean} 是否可见
   */
  isWarningVisible() {
    return this.isVisible;
  }
  
  /**
   * 获取剩余时间（秒）
   * @returns {number} 剩余秒数
   */
  getRemainingSeconds() {
    return Math.ceil(this.timeLeft / 60);
  }
  
  /**
   * 获取进度（0-1）
   * @returns {number} 进度值
   */
  getProgress() {
    return this.maxTime > 0 ? this.timeLeft / this.maxTime : 0;
  }
}
