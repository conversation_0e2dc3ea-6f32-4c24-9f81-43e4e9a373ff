/**
 * 重试和失败处理管理器
 * 减少玩家因失败产生的挫败感，提供多种重试选项
 */
import Emitter from '../libs/tinyemitter.js';

// 重试选项类型
export const RETRY_OPTIONS = {
  RESTART: 'restart',           // 重新开始关卡
  REVIVE: 'revive',            // 复活继续
  SKIP: 'skip',                // 跳过关卡
  ASSISTANCE: 'assistance',     // 开启辅助模式
  PRACTICE: 'practice'          // 练习模式
};

// 失败原因
export const FAILURE_REASONS = {
  TIME_UP: 'time_up',          // 时间用完
  GRID_FULL: 'grid_full',      // 网格满了
  TARGET_MISSED: 'target_missed', // 未达到目标
  NO_MOVES: 'no_moves'         // 无法移动
};

export default class RetryManager extends Emitter {
  constructor(progressionManager) {
    super();
    
    this.progressionManager = progressionManager;
    
    // 重试相关数据
    this.retryData = {
      currentLevel: null,
      failureCount: 0,
      consecutiveFailures: 0,
      lastFailureReason: null,
      revivesUsed: 0,
      skipsUsed: 0
    };
    
    // 重试选项配置
    this.retryConfig = {
      maxRevives: 3,           // 每关最大复活次数
      maxSkips: 5,             // 总跳过次数限制
      reviveCost: 50,          // 复活消耗金币
      skipCost: 100,           // 跳过消耗金币
      assistanceThreshold: 3,   // 连续失败多少次后提供辅助
      practiceThreshold: 5      // 连续失败多少次后建议练习
    };
    
    // 复活机制
    this.reviveOptions = {
      clearBottom: {
        name: '清理底部',
        description: '清除底部3行方块',
        cost: 30,
        available: true
      },
      addTime: {
        name: '延长时间',
        description: '增加60秒游戏时间',
        cost: 40,
        available: true
      },
      slowDown: {
        name: '减速模式',
        description: '降低方块下落速度',
        cost: 20,
        available: true
      },
      extraItems: {
        name: '道具补给',
        description: '获得额外道具',
        cost: 50,
        available: true
      }
    };
    
    this._loadData();
  }
  
  /**
   * 处理游戏失败
   * @param {string} reason - 失败原因
   * @param {Object} gameState - 游戏状态
   * @returns {Object} 重试选项
   */
  handleFailure(reason, gameState) {
    this.retryData.lastFailureReason = reason;
    this.retryData.failureCount++;
    this.retryData.consecutiveFailures++;
    
    // 分析失败原因并生成重试选项
    const retryOptions = this._generateRetryOptions(reason, gameState);
    
    // 记录失败数据用于分析
    this._recordFailureData(reason, gameState);
    
    this.emit('failure:handled', {
      reason,
      failureCount: this.retryData.failureCount,
      consecutiveFailures: this.retryData.consecutiveFailures,
      retryOptions
    });
    
    this._saveData();
    
    return retryOptions;
  }
  
  /**
   * 生成重试选项
   * @param {string} reason - 失败原因
   * @param {Object} gameState - 游戏状态
   * @returns {Array} 重试选项列表
   * @private
   */
  _generateRetryOptions(reason, gameState) {
    const options = [];
    
    // 基础重新开始选项（总是可用）
    options.push({
      type: RETRY_OPTIONS.RESTART,
      name: '重新开始',
      description: '从关卡开始重新挑战',
      cost: 0,
      available: true,
      recommended: this.retryData.consecutiveFailures <= 2
    });
    
    // 复活选项（根据失败原因和剩余复活次数）
    if (this.retryData.revivesUsed < this.retryConfig.maxRevives) {
      const reviveOption = this._getReviveOption(reason, gameState);
      if (reviveOption) {
        options.push(reviveOption);
      }
    }
    
    // 辅助模式选项（连续失败达到阈值后）
    if (this.retryData.consecutiveFailures >= this.retryConfig.assistanceThreshold) {
      options.push({
        type: RETRY_OPTIONS.ASSISTANCE,
        name: '辅助模式',
        description: '开启辅助功能，降低游戏难度',
        cost: 0,
        available: true,
        recommended: true,
        features: ['慢速模式', '匹配高亮', '危险暂停', '扩展思考时间']
      });
    }
    
    // 跳过关卡选项（有限制）
    if (this.retryData.skipsUsed < this.retryConfig.maxSkips && 
        this.progressionManager.playerData.coins >= this.retryConfig.skipCost) {
      options.push({
        type: RETRY_OPTIONS.SKIP,
        name: '跳过关卡',
        description: '花费金币跳过当前关卡',
        cost: this.retryConfig.skipCost,
        available: true,
        recommended: this.retryData.consecutiveFailures >= 5
      });
    }
    
    // 练习模式选项（连续失败较多时）
    if (this.retryData.consecutiveFailures >= this.retryConfig.practiceThreshold) {
      options.push({
        type: RETRY_OPTIONS.PRACTICE,
        name: '练习模式',
        description: '在无压力环境下练习相关技巧',
        cost: 0,
        available: true,
        recommended: true
      });
    }
    
    return options;
  }
  
  /**
   * 获取复活选项
   * @param {string} reason - 失败原因
   * @param {Object} gameState - 游戏状态
   * @returns {Object|null} 复活选项
   * @private
   */
  _getReviveOption(reason, gameState) {
    let reviveType;
    let baseCost = this.retryConfig.reviveCost;
    
    // 根据失败原因选择最合适的复活方式
    switch (reason) {
      case FAILURE_REASONS.GRID_FULL:
        reviveType = 'clearBottom';
        break;
      case FAILURE_REASONS.TIME_UP:
        reviveType = 'addTime';
        break;
      case FAILURE_REASONS.NO_MOVES:
        reviveType = 'slowDown';
        break;
      default:
        reviveType = 'extraItems';
        break;
    }
    
    const reviveOption = this.reviveOptions[reviveType];
    if (!reviveOption || !reviveOption.available) {
      return null;
    }
    
    // 检查玩家是否有足够的金币
    const totalCost = reviveOption.cost;
    const canAfford = this.progressionManager.playerData.coins >= totalCost;
    
    return {
      type: RETRY_OPTIONS.REVIVE,
      name: reviveOption.name,
      description: reviveOption.description,
      cost: totalCost,
      available: canAfford,
      recommended: this.retryData.consecutiveFailures <= 3 && canAfford,
      reviveType: reviveType
    };
  }
  
  /**
   * 执行重试选项
   * @param {string} optionType - 选项类型
   * @param {Object} optionData - 选项数据
   * @returns {Object} 执行结果
   */
  executeRetryOption(optionType, optionData = {}) {
    switch (optionType) {
      case RETRY_OPTIONS.RESTART:
        return this._executeRestart();
      case RETRY_OPTIONS.REVIVE:
        return this._executeRevive(optionData);
      case RETRY_OPTIONS.SKIP:
        return this._executeSkip();
      case RETRY_OPTIONS.ASSISTANCE:
        return this._executeAssistance(optionData);
      case RETRY_OPTIONS.PRACTICE:
        return this._executePractice();
      default:
        return { success: false, reason: 'unknown_option' };
    }
  }
  
  /**
   * 执行重新开始
   * @returns {Object} 执行结果
   * @private
   */
  _executeRestart() {
    // 重置连续失败计数（给玩家一些鼓励）
    if (this.retryData.consecutiveFailures > 3) {
      this.retryData.consecutiveFailures = Math.max(0, this.retryData.consecutiveFailures - 1);
    }
    
    this.emit('retry:restart');
    
    return { 
      success: true, 
      action: 'restart',
      message: '重新开始关卡，加油！'
    };
  }
  
  /**
   * 执行复活
   * @param {Object} optionData - 复活选项数据
   * @returns {Object} 执行结果
   * @private
   */
  _executeRevive(optionData) {
    const { reviveType, cost } = optionData;
    
    // 检查金币是否足够
    if (this.progressionManager.playerData.coins < cost) {
      return { success: false, reason: 'insufficient_coins' };
    }
    
    // 扣除金币
    this.progressionManager.playerData.coins -= cost;
    this.retryData.revivesUsed++;
    
    // 重置连续失败计数
    this.retryData.consecutiveFailures = 0;
    
    this.emit('retry:revive', { reviveType, cost });
    
    return { 
      success: true, 
      action: 'revive',
      reviveType,
      message: '复活成功！继续挑战吧！'
    };
  }
  
  /**
   * 执行跳过关卡
   * @returns {Object} 执行结果
   * @private
   */
  _executeSkip() {
    const cost = this.retryConfig.skipCost;
    
    // 检查金币是否足够
    if (this.progressionManager.playerData.coins < cost) {
      return { success: false, reason: 'insufficient_coins' };
    }
    
    // 扣除金币
    this.progressionManager.playerData.coins -= cost;
    this.retryData.skipsUsed++;
    
    // 重置连续失败计数
    this.retryData.consecutiveFailures = 0;
    
    this.emit('retry:skip', { cost });
    
    return { 
      success: true, 
      action: 'skip',
      message: '关卡已跳过，继续下一关！'
    };
  }
  
  /**
   * 执行辅助模式
   * @param {Object} optionData - 辅助选项数据
   * @returns {Object} 执行结果
   * @private
   */
  _executeAssistance(optionData) {
    // 重置连续失败计数
    this.retryData.consecutiveFailures = Math.max(0, this.retryData.consecutiveFailures - 2);
    
    this.emit('retry:assistance', optionData);
    
    return { 
      success: true, 
      action: 'assistance',
      message: '辅助模式已开启，游戏会更容易一些！'
    };
  }
  
  /**
   * 执行练习模式
   * @returns {Object} 执行结果
   * @private
   */
  _executePractice() {
    this.emit('retry:practice');
    
    return { 
      success: true, 
      action: 'practice',
      message: '进入练习模式，慢慢熟悉游戏机制吧！'
    };
  }
  
  /**
   * 记录失败数据用于分析
   * @param {string} reason - 失败原因
   * @param {Object} gameState - 游戏状态
   * @private
   */
  _recordFailureData(reason, gameState) {
    // 这里可以记录更详细的失败数据用于后续分析和优化
    const failureRecord = {
      timestamp: Date.now(),
      reason,
      level: this.retryData.currentLevel,
      score: gameState.score || 0,
      timeElapsed: gameState.timeElapsed || 0,
      consecutiveFailures: this.retryData.consecutiveFailures
    };
    
    // 可以发送到服务器进行数据分析
    console.log('失败数据记录:', failureRecord);
  }
  
  /**
   * 重置关卡数据
   * @param {number} levelId - 关卡ID
   */
  resetLevelData(levelId) {
    this.retryData.currentLevel = levelId;
    this.retryData.revivesUsed = 0;
    
    this._saveData();
  }
  
  /**
   * 关卡成功时调用
   */
  onLevelSuccess() {
    // 重置连续失败计数
    this.retryData.consecutiveFailures = 0;
    this._saveData();
  }
  
  /**
   * 获取重试统计信息
   * @returns {Object} 统计信息
   */
  getRetryStats() {
    return {
      totalFailures: this.retryData.failureCount,
      consecutiveFailures: this.retryData.consecutiveFailures,
      revivesUsed: this.retryData.revivesUsed,
      skipsUsed: this.retryData.skipsUsed,
      skipsRemaining: this.retryConfig.maxSkips - this.retryData.skipsUsed
    };
  }
  
  /**
   * 保存数据
   * @private
   */
  _saveData() {
    try {
      // 验证数据是否可以序列化
      const jsonString = this._safeStringify(this.retryData);
      if (jsonString === null) {
        console.error('重试数据序列化失败，跳过保存');
        return;
      }

      wx.setStorageSync('retryData', jsonString);
      console.log('重试数据保存成功');
    } catch (e) {
      console.error('保存重试数据失败:', e);
    }
  }

  /**
   * 安全的JSON序列化
   * @param {Object} obj - 要序列化的对象
   * @returns {string|null} 序列化后的字符串，失败返回null
   * @private
   */
  _safeStringify(obj) {
    try {
      return JSON.stringify(obj, (key, value) => {
        // 过滤掉函数、Symbol、undefined等不可序列化的值
        if (typeof value === 'function' || typeof value === 'symbol' || value === undefined) {
          return null;
        }
        return value;
      });
    } catch (e) {
      console.error('JSON序列化失败:', e);
      return null;
    }
  }
  
  /**
   * 加载数据
   * @private
   */
  _loadData() {
    try {
      const data = wx.getStorageSync('retryData');
      
      if (data) {
        this.retryData = { ...this.retryData, ...JSON.parse(data) };
      }
    } catch (e) {
      console.error('加载重试数据失败:', e);
    }
  }
}
