/**
 * 视图模型 - MVVM架构的核心
 * 负责将游戏数据转换为渲染数据，实现数据驱动的UI更新
 */
import Emitter from '../libs/tinyemitter.js';

export default class ViewModel extends Emitter {
  constructor() {
    super();
    
    // 视图状态
    this.viewState = {
      // 游戏网格状态
      grid: {
        visible: true,
        showGrid: true,
        showGhost: true,
        showEffects: true
      },
      
      // UI面板状态
      ui: {
        showScore: true,
        showPreview: true,
        showStats: false,
        showDebug: false
      },
      
      // 覆盖层状态
      overlay: {
        type: null,
        visible: false,
        content: null
      },
      
      // 动画状态
      animations: {
        enabled: true,
        speed: 1.0,
        effects: true
      }
    };
    
    // 数据绑定映射
    this.bindings = new Map();
    
    // 计算属性缓存
    this.computed = new Map();
    
    // 变化追踪
    this.changeTracking = {
      enabled: true,
      changes: new Set(),
      lastUpdate: 0
    };
    
    console.log('📊 ViewModel初始化完成');
  }
  
  /**
   * 绑定数据源
   * @param {string} key - 绑定键
   * @param {Function} getter - 数据获取函数
   * @param {Function} formatter - 数据格式化函数
   */
  bind(key, getter, formatter = null) {
    this.bindings.set(key, {
      getter,
      formatter,
      lastValue: null,
      dirty: true
    });
    
    console.log(`📊 绑定数据源: ${key}`);
  }
  
  /**
   * 定义计算属性
   * @param {string} key - 属性键
   * @param {Function} computer - 计算函数
   * @param {Array} deps - 依赖列表
   */
  computed(key, computer, deps = []) {
    this.computed.set(key, {
      computer,
      deps,
      value: null,
      dirty: true,
      lastComputed: 0
    });
    
    console.log(`📊 定义计算属性: ${key}, 依赖: [${deps.join(', ')}]`);
  }
  
  /**
   * 更新视图模型
   * @param {Object} gameData - 游戏数据
   */
  update(gameData) {
    const startTime = performance.now();
    
    // 更新绑定数据
    this._updateBindings(gameData);
    
    // 更新计算属性
    this._updateComputed();
    
    // 检查变化
    if (this.changeTracking.enabled) {
      this._trackChanges();
    }
    
    // 发出更新事件
    if (this.changeTracking.changes.size > 0) {
      this.emit('viewupdate', {
        changes: Array.from(this.changeTracking.changes),
        updateTime: performance.now() - startTime
      });
    }
    
    this.changeTracking.lastUpdate = Date.now();
  }
  
  /**
   * 更新绑定数据
   * @private
   */
  _updateBindings(gameData) {
    for (const [key, binding] of this.bindings) {
      try {
        const newValue = binding.getter(gameData);
        const formattedValue = binding.formatter ? binding.formatter(newValue) : newValue;
        
        // 检查值是否变化
        if (!this._deepEquals(formattedValue, binding.lastValue)) {
          binding.lastValue = formattedValue;
          binding.dirty = true;
          this.changeTracking.changes.add(key);
        }
      } catch (error) {
        console.error(`📊 绑定数据更新失败 [${key}]:`, error);
      }
    }
  }
  
  /**
   * 更新计算属性
   * @private
   */
  _updateComputed() {
    for (const [key, computed] of this.computed) {
      // 检查依赖是否变化
      const depsChanged = computed.deps.some(dep => 
        this.changeTracking.changes.has(dep)
      );
      
      if (depsChanged || computed.dirty) {
        try {
          const newValue = computed.computer(this.getValue.bind(this));
          
          if (!this._deepEquals(newValue, computed.value)) {
            computed.value = newValue;
            computed.dirty = false;
            computed.lastComputed = Date.now();
            this.changeTracking.changes.add(key);
          }
        } catch (error) {
          console.error(`📊 计算属性更新失败 [${key}]:`, error);
        }
      }
    }
  }
  
  /**
   * 追踪变化
   * @private
   */
  _trackChanges() {
    // 这里可以添加更复杂的变化追踪逻辑
    // 比如变化历史、性能监控等
  }
  
  /**
   * 深度比较两个值
   * @private
   */
  _deepEquals(a, b) {
    if (a === b) return true;
    
    if (a == null || b == null) return false;
    
    if (typeof a !== typeof b) return false;
    
    if (typeof a === 'object') {
      const keysA = Object.keys(a);
      const keysB = Object.keys(b);
      
      if (keysA.length !== keysB.length) return false;
      
      return keysA.every(key => this._deepEquals(a[key], b[key]));
    }
    
    return false;
  }
  
  /**
   * 获取绑定值
   * @param {string} key - 绑定键
   */
  getValue(key) {
    const binding = this.bindings.get(key);
    if (binding) {
      return binding.lastValue;
    }
    
    const computed = this.computed.get(key);
    if (computed) {
      return computed.value;
    }
    
    return null;
  }
  
  /**
   * 设置视图状态
   * @param {string} path - 状态路径 (用.分隔)
   * @param {*} value - 状态值
   */
  setViewState(path, value) {
    const keys = path.split('.');
    let current = this.viewState;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current)) {
        current[key] = {};
      }
      current = current[key];
    }
    
    const lastKey = keys[keys.length - 1];
    if (current[lastKey] !== value) {
      current[lastKey] = value;
      this.changeTracking.changes.add(`viewState.${path}`);
      
      this.emit('statechange', {
        path,
        value,
        oldValue: current[lastKey]
      });
    }
  }
  
  /**
   * 获取视图状态
   * @param {string} path - 状态路径
   */
  getViewState(path) {
    const keys = path.split('.');
    let current = this.viewState;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return null;
      }
    }
    
    return current;
  }
  
  /**
   * 获取渲染数据
   * @param {string} componentType - 组件类型
   */
  getRenderData(componentType) {
    switch (componentType) {
      case 'grid':
        return this._getGridRenderData();
      case 'tetromino':
        return this._getTetrominoRenderData();
      case 'ghost':
        return this._getGhostRenderData();
      case 'preview':
        return this._getPreviewRenderData();
      case 'score':
        return this._getScoreRenderData();
      case 'overlay':
        return this._getOverlayRenderData();
      default:
        return null;
    }
  }
  
  /**
   * 获取网格渲染数据
   * @private
   */
  _getGridRenderData() {
    return {
      grid: this.getValue('grid'),
      showGrid: this.getViewState('grid.showGrid'),
      showEffects: this.getViewState('grid.showEffects'),
      visible: this.getViewState('grid.visible')
    };
  }
  
  /**
   * 获取方块渲染数据
   * @private
   */
  _getTetrominoRenderData() {
    return {
      tetromino: this.getValue('currentTetromino'),
      grid: this.getValue('grid'),
      visible: this.getViewState('grid.visible')
    };
  }
  
  /**
   * 获取虚影渲染数据
   * @private
   */
  _getGhostRenderData() {
    return {
      tetromino: this.getValue('currentTetromino'),
      grid: this.getValue('grid'),
      visible: this.getViewState('grid.showGhost')
    };
  }
  
  /**
   * 获取预览渲染数据
   * @private
   */
  _getPreviewRenderData() {
    return {
      tetromino: this.getValue('nextTetromino'),
      grid: this.getValue('grid'),
      visible: this.getViewState('ui.showPreview')
    };
  }
  
  /**
   * 获取分数渲染数据
   * @private
   */
  _getScoreRenderData() {
    return {
      scoreState: this.getValue('scoreState'),
      grid: this.getValue('grid'),
      visible: this.getViewState('ui.showScore'),
      position: this.getValue('scorePosition')
    };
  }
  
  /**
   * 获取覆盖层渲染数据
   * @private
   */
  _getOverlayRenderData() {
    const overlayState = this.getViewState('overlay');
    
    if (!overlayState.visible) {
      return null;
    }
    
    return {
      type: overlayState.type,
      content: overlayState.content,
      visible: overlayState.visible
    };
  }
  
  /**
   * 显示覆盖层
   * @param {string} type - 覆盖层类型
   * @param {Object} content - 覆盖层内容
   */
  showOverlay(type, content = {}) {
    this.setViewState('overlay.type', type);
    this.setViewState('overlay.content', content);
    this.setViewState('overlay.visible', true);
    
    console.log(`📊 显示覆盖层: ${type}`);
  }
  
  /**
   * 隐藏覆盖层
   */
  hideOverlay() {
    this.setViewState('overlay.visible', false);
    this.setViewState('overlay.type', null);
    this.setViewState('overlay.content', null);
    
    console.log('📊 隐藏覆盖层');
  }
  
  /**
   * 切换UI元素可见性
   * @param {string} element - UI元素名称
   */
  toggleUI(element) {
    const currentState = this.getViewState(`ui.show${element}`);
    this.setViewState(`ui.show${element}`, !currentState);
    
    console.log(`📊 切换UI ${element}: ${!currentState ? '显示' : '隐藏'}`);
  }
  
  /**
   * 获取统计信息
   */
  getStats() {
    return {
      bindingCount: this.bindings.size,
      computedCount: this.computed.size,
      changeCount: this.changeTracking.changes.size,
      lastUpdate: this.changeTracking.lastUpdate
    };
  }
  
  /**
   * 重置变化追踪
   */
  resetChangeTracking() {
    this.changeTracking.changes.clear();
    console.log('📊 重置变化追踪');
  }
  
  /**
   * 销毁视图模型
   */
  destroy() {
    this.bindings.clear();
    this.computed.clear();
    this.changeTracking.changes.clear();
    this.removeAllListeners();
    
    console.log('📊 ViewModel销毁完成');
  }
} 