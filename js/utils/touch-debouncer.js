/**
 * 智能触摸事件管理器
 * 专门解决跨UI状态的触摸事件冲突问题
 */

class TouchDebouncer {
  constructor() {
    // 状态切换保护配置
    this.stateTransitionProtection = 400; // 状态切换后的保护时间
    this.samePositionProtection = 200; // 相同位置重复触摸保护
    this.positionTolerance = 30; // 位置容差（像素）

    // 状态跟踪
    this.lastStateChangeTime = 0;
    this.pendingStateChange = null; // 待处理的状态切换
    this.lastTouchInfo = null; // 上次触摸信息
    this.isStateTransitioning = false;

    // 跨状态触摸检测
    this.touchStartState = null; // 触摸开始时的状态
    this.touchStartTime = 0;

    console.log('智能触摸管理器已初始化');
  }
  
  /**
   * 开始触摸事件（在微信的onTouchStart中调用）
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   * @param {string} gameState - 当前游戏状态
   * @param {string} uiScreen - 当前UI界面
   */
  startTouch(x, y, gameState, uiScreen) {
    const now = Date.now();

    // 记录触摸开始时的状态
    this.touchStartState = { gameState, uiScreen };
    this.touchStartTime = now;

    // 记录触摸信息
    this.lastTouchInfo = { x, y, time: now, gameState, uiScreen };
  }
  
  /**
   * 检查是否应该阻止触摸处理（核心逻辑）
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   * @param {string} gameState - 当前游戏状态
   * @param {string} uiScreen - 当前UI界面
   * @returns {Object} 检查结果
   */
  shouldBlockTouch(x, y, gameState, uiScreen) {
    const now = Date.now();
    const result = {
      shouldBlock: false,
      reason: '',
      isStateTransition: false
    };

    // 1. 检查是否是跨状态触摸（最重要的检查）
    if (this.touchStartState) {
      const stateChanged =
        this.touchStartState.gameState !== gameState ||
        this.touchStartState.uiScreen !== uiScreen;

      if (stateChanged) {
        const timeSinceTouch = now - this.touchStartTime;
        // 如果状态在触摸后很快就改变了，说明是跨状态触摸
        if (timeSinceTouch < 1000) { // 1秒内的状态变化
          result.shouldBlock = true;
          result.reason = `跨状态触摸: ${this.touchStartState.uiScreen}→${uiScreen}`;
          result.isStateTransition = true;
          console.log(`🚫 阻止跨状态触摸: ${this.touchStartState.uiScreen} → ${uiScreen}`);
          return result;
        }
      }
    }

    // 2. 检查状态切换保护期
    const timeSinceStateChange = now - this.lastStateChangeTime;
    if (this.isStateTransitioning && timeSinceStateChange < this.stateTransitionProtection) {
      result.shouldBlock = true;
      result.reason = `状态切换保护期 (${this.stateTransitionProtection - timeSinceStateChange}ms)`;
      return result;
    }

    // 3. 检查相同位置重复触摸
    if (this.lastTouchInfo) {
      const distance = Math.sqrt(
        Math.pow(x - this.lastTouchInfo.x, 2) +
        Math.pow(y - this.lastTouchInfo.y, 2)
      );

      const timeSinceLastTouch = now - this.lastTouchInfo.time;

      if (distance < this.positionTolerance && timeSinceLastTouch < this.samePositionProtection) {
        result.shouldBlock = true;
        result.reason = `相同位置重复触摸 (${distance.toFixed(1)}px, ${timeSinceLastTouch}ms)`;
        return result;
      }
    }

    // 4. 特殊情况：暂停后立即触摸游戏区域
    if (gameState === 'playing' && this.lastTouchInfo &&
        this.lastTouchInfo.gameState === 'paused') {
      const timeSinceResume = now - this.lastTouchInfo.time;
      if (timeSinceResume < 300) { // 300ms保护期
        result.shouldBlock = true;
        result.reason = `暂停后立即触摸保护 (${timeSinceResume}ms)`;
        return result;
      }
    }

    return result;
  }
  
  /**
   * 记录状态切换
   * @param {string} fromState - 切换前状态
   * @param {string} toState - 切换后状态
   */
  recordStateChange(fromState, toState) {
    const now = Date.now();
    this.lastStateChangeTime = now;
    this.isStateTransitioning = true;

    console.log(`🔄 状态切换: ${fromState} → ${toState}`);

    // 设置状态切换结束时间
    setTimeout(() => {
      this.isStateTransitioning = false;
    }, this.stateTransitionProtection);
  }
  
  /**
   * 完成触摸处理
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   * @param {string} gameState - 当前游戏状态
   * @param {string} uiScreen - 当前UI界面
   */
  finishTouch(x, y, gameState, uiScreen) {
    const now = Date.now();

    // 更新最后触摸信息
    this.lastTouchInfo = { x, y, time: now, gameState, uiScreen };

    // 清除触摸开始状态（延迟清除，给状态切换留时间）
    setTimeout(() => {
      this.touchStartState = null;
    }, 100);
  }
  
  /**
   * 重置状态（紧急情况使用）
   */
  reset() {
    this.lastStateChangeTime = 0;
    this.lastTouchInfo = null;
    this.touchStartState = null;
    this.isStateTransitioning = false;
    console.log('智能触摸管理器状态已重置');
  }

  /**
   * 获取当前状态信息（调试用）
   * @returns {Object} 状态信息
   */
  getStatus() {
    const now = Date.now();
    return {
      timeSinceStateChange: now - this.lastStateChangeTime,
      isStateTransitioning: this.isStateTransitioning,
      lastTouchInfo: this.lastTouchInfo,
      touchStartState: this.touchStartState,
      touchStartTime: this.touchStartTime
    };
  }
}

export default TouchDebouncer;
