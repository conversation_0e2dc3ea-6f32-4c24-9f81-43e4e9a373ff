import GameEngine from '../core/game-engine.js';
import SceneManager, { SCENE_TYPES } from '../core/scene-manager.js';
import InputManager from '../core/input-manager.js';
import { adapterFactory } from '../adapters/adapter-factory.js';

// 导入现有的游戏模块（逐步迁移）
import BackGround from '../runtime/background.js';
import GameInfo from '../runtime/gameinfo.js';
import Music from '../runtime/music.js';
import LevelManager from '../level/level-manager.js';
import DataBus from '../databus.js';
import TutorialManager from '../tutorial/tutorial-manager.js';
import ProgressionManager from '../progression/progression-manager.js';

import EffectBalanceManager from '../effects/effect-balance-manager.js';
import ItemProgressionManager from '../item/item-progression-manager.js';
import GameBalanceManager from '../balance/game-balance-manager.js';
import RetryManager from '../retry/retry-manager.js';
import { setDebugMode, isDebugMode, toggleDebugMode } from '../config/game-config.js';
import ElementIntroduction from '../tutorial/element-introduction.js';
import { preloadBlockImages } from '../game/block.js';

import TouchDebouncer from '../utils/touch-debouncer.js';

/**
 * 微信小游戏平台入口
 * 使用新的游戏引擎架构，逐步迁移原有功能
 */
export default class WeChatGame {
  constructor() {
    // 创建适配器
    this.adapters = adapterFactory.createAllAdapters();
    
    // 创建核心系统
    this.gameEngine = new GameEngine(this.adapters);
    this.sceneManager = new SceneManager(this.gameEngine);
    this.inputManager = new InputManager(this.adapters.input, this.sceneManager);
    
    // 现有的游戏模块（保持兼容性）
    this.bg = new BackGround();
    this.gameInfo = new GameInfo();
    this.gameController = null;
    this.itemManager = null;
    this.levelManager = null;
    this.tutorialManager = null;
    this.progressionManager = null;
    
    // 其他系统
    this.assistanceManager = null;
    this.effectBalanceManager = null;
    this.itemProgressionManager = null;
    this.gameBalanceManager = null;
    this.retryManager = null;
    this.garbageWarning = null;
    this.touchDebouncer = null;
    this.elementIntroduction = null;
    
    // 游戏状态
    this.isTransitioning = false;
    this.initialized = false;
    
    console.log('WeChatGame 构造函数完成');
  }

  /**
   * 初始化游戏
   */
  async initialize() {
    try {
      console.log('开始初始化微信小游戏...');
      
      // 设置全局引用（保持兼容性）
      this._setupGlobalReferences();
      
      // 初始化调试模式
      this._initDebugMode();
      
      // 预加载资源
      await this._preloadResources();
      
      // 初始化游戏模块
      this._initializeGameModules();
      
      // 初始化游戏引擎
      const engineInitialized = await this.gameEngine.initialize();
      if (!engineInitialized) {
        throw new Error('游戏引擎初始化失败');
      }
      
      // 设置游戏模块到引擎
      this.gameEngine.setGameModules({
        gameController: this.gameController,
        levelManager: this.levelManager,
        itemManager: this.itemManager,
        tutorialManager: this.tutorialManager,
        progressionManager: this.progressionManager
      });
      
      // 设置场景和输入事件
      this._setupSceneEvents();
      this._setupInputEvents();
      
      // 初始场景
      await this.sceneManager.switchTo(SCENE_TYPES.LEVEL_SELECT);
      
      // 启动游戏引擎
      this.gameEngine.start();
      
      this.initialized = true;
      console.log('微信小游戏初始化完成');
      
      return true;
      
    } catch (error) {
      console.error('微信小游戏初始化失败:', error);
      return false;
    }
  }

  /**
   * 设置全局引用（保持兼容性）
   * @private
   */
  _setupGlobalReferences() {
    // 设置全局数据管理
    GameGlobal.databus = new DataBus();
    GameGlobal.musicManager = new Music();
    
    // 设置微信API兼容性处理
    this._setupWechatAPICompatibility();
  }

  /**
   * 设置微信API兼容性处理
   * @private
   */
  _setupWechatAPICompatibility() {
    if (typeof wx === 'undefined') return;

    const unsupportedAPIs = [
      'setNavigationBarColor',
      'setNavigationBarTitle',
      'showNavigationBarLoading',
      'hideNavigationBarLoading'
    ];

    unsupportedAPIs.forEach(apiName => {
      if (!wx[apiName]) {
        wx[apiName] = function(options = {}) {
          console.log(`🚫 忽略不支持的API调用: ${apiName}`, options);
          if (options.success) {
            options.success({ errMsg: `${apiName}:ok` });
          }
          if (options.complete) {
            options.complete({ errMsg: `${apiName}:ok` });
          }
        };
      } else {
        const originalAPI = wx[apiName];
        wx[apiName] = function(options = {}) {
          try {
            return originalAPI.call(this, options);
          } catch (error) {
            console.warn(`⚠️ API调用失败，已忽略: ${apiName}`, error);
            if (options.fail) {
              options.fail({ errMsg: `${apiName}:fail ${error.message}` });
            }
            if (options.complete) {
              options.complete({ errMsg: `${apiName}:fail ${error.message}` });
            }
          }
        };
      }
    });

    console.log('✅ 已设置微信小游戏API兼容性处理');
  }

  /**
   * 初始化调试模式
   * @private
   */
  _initDebugMode() {
    console.log(`调试模式: ${isDebugMode() ? '开启' : '关闭'}`);

    // 暴露调试函数到全局
    if (typeof window !== 'undefined') {
      window.toggleDebugMode = toggleDebugMode;
      window.setDebugMode = setDebugMode;
      window.isDebugMode = isDebugMode;
    }

    if (typeof global !== 'undefined') {
      global.toggleDebugMode = toggleDebugMode;
      global.setDebugMode = setDebugMode;
      global.isDebugMode = isDebugMode;
    }

    console.log('调试模式控制函数已暴露到全局');
  }

  /**
   * 预加载资源
   * @private
   */
  async _preloadResources() {
    // 预加载方块图片
    await preloadBlockImages();
    console.log('资源预加载完成');
  }

  /**
   * 初始化游戏模块
   * @private
   */
  _initializeGameModules() {
    // 初始化管理器
    this.levelManager = new LevelManager();
    this.tutorialManager = new TutorialManager();
    this.progressionManager = new ProgressionManager();
    this.elementIntroduction = new ElementIntroduction();

    // 初始化重试管理器
    this.retryManager = new RetryManager(this.progressionManager);

    // 初始化特效平衡管理器
    this.effectBalanceManager = new EffectBalanceManager(this.progressionManager);

    // 初始化道具进度管理器
    this.itemProgressionManager = new ItemProgressionManager(this.progressionManager, null);

    // 创建触摸去抖动管理器
    this.touchDebouncer = new TouchDebouncer();

    // 初始化游戏平衡管理器
    this.gameBalanceManager = new GameBalanceManager(
      this.progressionManager,
      this.itemProgressionManager,
      this.retryManager
    );

    // 生成每日任务
    this.progressionManager.generateDailyTasks();

    console.log('游戏模块初始化完成');
  }

  /**
   * 设置场景事件
   * @private
   */
  _setupSceneEvents() {
    // 场景切换事件
    this.sceneManager.on('scene:enter', (data) => {
      console.log(`进入场景: ${data.to}`);
      this._onSceneEnter(data.to, data.data);
    });

    this.sceneManager.on('scene:leave', (data) => {
      console.log(`离开场景: ${data.from}`);
      this._onSceneLeave(data.from);
    });
  }

  /**
   * 设置输入事件
   * @private
   */
  _setupInputEvents() {
    // 这里可以注册全局输入处理器
    // 具体的游戏输入将在各个场景中处理
  }

  /**
   * 场景进入处理
   * @param {string} sceneType - 场景类型
   * @param {Object} data - 场景数据
   * @private
   */
  _onSceneEnter(sceneType, data) {
    switch (sceneType) {
      case SCENE_TYPES.LEVEL_SELECT:
        this._enterLevelSelectScene(data);
        break;
      case SCENE_TYPES.GAME_PLAYING:
        this._enterGamePlayingScene(data);
        break;
      case SCENE_TYPES.GAME_PAUSED:
        this._enterGamePausedScene(data);
        break;
      // 可以添加更多场景处理
    }
  }

  /**
   * 场景离开处理
   * @param {string} sceneType - 场景类型
   * @private
   */
  _onSceneLeave(sceneType) {
    switch (sceneType) {
      case SCENE_TYPES.GAME_PLAYING:
        this._leaveGamePlayingScene();
        break;
      // 可以添加更多场景处理
    }
  }

  /**
   * 进入关卡选择场景
   * @param {Object} data - 场景数据
   * @private
   */
  _enterLevelSelectScene(data) {
    // 这里是原来的showLevelSelection逻辑
    // 暂时保持原有实现，逐步迁移
    console.log('进入关卡选择场景');
  }

  /**
   * 进入游戏中场景
   * @param {Object} data - 场景数据
   * @private
   */
  _enterGamePlayingScene(data) {
    if (data.levelData) {
      this._loadLevel(data.levelData);
    }
  }

  /**
   * 进入游戏暂停场景
   * @param {Object} data - 场景数据
   * @private
   */
  _enterGamePausedScene(data) {
    if (this.gameController) {
      this.gameController.pause();
    }
  }

  /**
   * 离开游戏中场景
   * @private
   */
  _leaveGamePlayingScene() {
    if (this.gameController) {
      this.gameController.pause();
    }
  }

  /**
   * 加载关卡（从原main.js迁移）
   * @param {Object} levelData - 关卡数据
   * @private
   */
  _loadLevel(levelData) {
    // 这里是原来的loadLevel逻辑
    // 暂时保持原有实现，逐步迁移
    console.log('加载关卡:', levelData);
  }

  /**
   * 获取游戏状态
   * @returns {Object} 游戏状态
   */
  getGameState() {
    return {
      initialized: this.initialized,
      currentScene: this.sceneManager.getCurrentScene(),
      isTransitioning: this.sceneManager.isInTransition(),
      engineState: this.gameEngine.getGameState(),
      adapters: {
        platform: this.adapters.platform,
        renderSupported: !!this.adapters.render,
        inputSupported: !!this.adapters.input,
        audioSupported: !!this.adapters.audio
      }
    };
  }

  /**
   * 销毁游戏
   */
  destroy() {
    console.log('销毁微信小游戏...');

    // 销毁核心系统
    if (this.inputManager) {
      this.inputManager.destroy();
    }

    if (this.sceneManager) {
      this.sceneManager.destroy();
    }

    if (this.gameEngine) {
      this.gameEngine.destroy();
    }

    // 销毁适配器
    if (adapterFactory) {
      adapterFactory.destroyAll();
    }

    // 清理其他模块
    // TODO: 添加其他模块的清理

    this.initialized = false;
    console.log('微信小游戏已销毁');
  }
} 