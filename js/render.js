import WeChatRenderAdapter from './adapters/wechat-render-adapter.js';
import WebRenderAdapter from './adapters/web-render-adapter.js';

/**
 * 渲染系统初始化
 * 使用适配器模式支持多平台
 */

// 创建平台适配器
let renderAdapter;

// 自动检测并创建适当的渲染适配器
if (typeof wx !== 'undefined') {
  // 微信小游戏环境
  renderAdapter = new WeChatRenderAdapter();
  console.log('使用微信小游戏渲染适配器');
} else if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  // Web浏览器环境
  renderAdapter = new WebRenderAdapter();
  console.log('使用Web浏览器渲染适配器');
} else if (typeof global !== 'undefined') {
  // Node.js测试环境 - 创建虚拟适配器
  renderAdapter = {
    createCanvas: () => ({
      width: 750,
      height: 1334,
      getContext: () => ({
        clearRect: () => {},
        fillRect: () => {},
        drawImage: () => {},
        fillText: () => {},
        save: () => {},
        restore: () => {},
        translate: () => {},
        scale: () => {},
        rotate: () => {},
        canvas: { width: 750, height: 1334 }
      }),
      addEventListener: () => {},
      removeEventListener: () => {},
      getBoundingClientRect: () => ({ left: 0, top: 0 })
    }),
    getScreenSize: () => ({ width: 750, height: 1334 }),
    setCanvasSize: () => {}
  };
  console.log('使用Node.js虚拟渲染适配器');
} else {
  // 未知环境
  throw new Error('不支持的运行环境：无法创建渲染适配器');
}

// 创建画布
const canvas = renderAdapter.createCanvas();

// 获取屏幕尺寸
const screenSize = renderAdapter.getScreenSize();

// 设置画布尺寸
renderAdapter.setCanvasSize(canvas, screenSize.width, screenSize.height);

// 设置全局画布引用
if (typeof GameGlobal !== 'undefined') {
  GameGlobal.canvas = canvas;
} else {
  // 创建全局对象（用于Web环境）
  globalThis.GameGlobal = {
    canvas: canvas
  };
}

// 导出屏幕尺寸常量（保持向后兼容）
export const SCREEN_WIDTH = screenSize.width;
export const SCREEN_HEIGHT = screenSize.height;

// 导出渲染适配器（供其他模块使用）
export { renderAdapter };

// 导出画布引用
export const gameCanvas = canvas;

console.log(`渲染系统初始化完成 - 屏幕尺寸: ${SCREEN_WIDTH}x${SCREEN_HEIGHT}`);