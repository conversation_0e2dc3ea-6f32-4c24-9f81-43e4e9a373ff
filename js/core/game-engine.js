import Emitter from '../libs/tinyemitter.js';

/**
 * 核心游戏引擎
 * 负责纯逻辑的游戏循环管理，平台无关
 */
export default class GameEngine extends Emitter {
  constructor(adapters) {
    super();
    
    // 平台适配器
    this.adapters = adapters;
    
    // 游戏状态
    this.isRunning = false;
    this.isPaused = false;
    this.lastFrameTime = 0;
    this.deltaTime = 0;
    this.frameCount = 0;
    
    // 游戏模块 - 稍后从main.js迁移过来
    this.gameController = null;
    this.levelManager = null;
    this.itemManager = null;
    this.tutorialManager = null;
    this.progressionManager = null;
    
    // 渲染上下文
    this.canvas = null;
    this.ctx = null;
    
    // 动画帧ID
    this.animationFrameId = 0;
    
    console.log('GameEngine 初始化完成');
  }

  /**
   * 初始化游戏引擎
   */
  async initialize() {
    try {
      // 初始化渲染系统
      await this._initializeRender();
      
      // 初始化输入系统
      this._initializeInput();
      
      // 初始化音频系统
      this._initializeAudio();
      
      // 发出初始化完成事件
      this.emit('engine:initialized');
      
      console.log('游戏引擎初始化完成');
      return true;
    } catch (error) {
      console.error('游戏引擎初始化失败:', error);
      this.emit('engine:error', { error, phase: 'initialization' });
      return false;
    }
  }

  /**
   * 启动游戏循环
   */
  start() {
    if (this.isRunning) {
      console.warn('游戏引擎已在运行中');
      return;
    }

    this.isRunning = true;
    this.isPaused = false;
    this.lastFrameTime = performance.now();
    
    // 启动主循环
    this._startMainLoop();
    
    // 发出启动事件
    this.emit('engine:started');
    
    console.log('游戏引擎启动');
  }

  /**
   * 暂停游戏
   */
  pause() {
    if (!this.isRunning || this.isPaused) return;
    
    this.isPaused = true;
    
    // 暂停所有游戏模块
    if (this.gameController) {
      this.gameController.pause();
    }
    
    // 发出暂停事件
    this.emit('engine:paused');
    
    console.log('游戏引擎暂停');
  }

  /**
   * 恢复游戏
   */
  resume() {
    if (!this.isRunning || !this.isPaused) return;
    
    this.isPaused = false;
    this.lastFrameTime = performance.now(); // 重置时间，避免大的deltaTime
    
    // 恢复所有游戏模块
    if (this.gameController) {
      this.gameController.resume();
    }
    
    // 发出恢复事件
    this.emit('engine:resumed');
    
    console.log('游戏引擎恢复');
  }

  /**
   * 停止游戏
   */
  stop() {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    this.isPaused = false;
    
    // 取消动画帧
    if (this.animationFrameId && this.adapters.render) {
      this.adapters.render.cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = 0;
    }
    
    // 发出停止事件
    this.emit('engine:stopped');
    
    console.log('游戏引擎停止');
  }

  /**
   * 重启游戏
   */
  restart() {
    this.stop();
    
    // 重置游戏模块
    if (this.gameController) {
      this.gameController.reset();
    }
    
    // 重新启动
    this.start();
    
    // 发出重启事件
    this.emit('engine:restarted');
    
    console.log('游戏引擎重启');
  }

  /**
   * 设置游戏模块
   * @param {Object} modules - 游戏模块对象
   */
  setGameModules(modules) {
    this.gameController = modules.gameController;
    this.levelManager = modules.levelManager;
    this.itemManager = modules.itemManager;
    this.tutorialManager = modules.tutorialManager;
    this.progressionManager = modules.progressionManager;
    
    console.log('游戏模块已设置');
  }

  /**
   * 获取游戏状态
   * @returns {Object} 游戏状态对象
   */
  getGameState() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      frameCount: this.frameCount,
      deltaTime: this.deltaTime,
      fps: this._calculateFPS()
    };
  }

  /**
   * 初始化渲染系统
   * @private
   */
  async _initializeRender() {
    if (!this.adapters.render) {
      throw new Error('渲染适配器未提供');
    }

    // 创建画布
    this.canvas = this.adapters.render.createCanvas();
    this.ctx = this.adapters.render.getContext2D(this.canvas);
    
    // 设置画布尺寸
    const screenSize = this.adapters.render.getScreenSize();
    this.adapters.render.setCanvasSize(this.canvas, screenSize.width, screenSize.height);
    
    console.log('渲染系统初始化完成');
  }

  /**
   * 初始化输入系统
   * @private
   */
  _initializeInput() {
    if (!this.adapters.input) {
      console.warn('输入适配器未提供');
      return;
    }

    // 初始化输入适配器
    this.adapters.input.initialize();
    
    // 设置基础输入事件监听
    this._setupInputEvents();
    
    console.log('输入系统初始化完成');
  }

  /**
   * 初始化音频系统
   * @private
   */
  _initializeAudio() {
    if (!this.adapters.audio) {
      console.warn('音频适配器未提供');
      return;
    }

    // TODO: 初始化音频适配器
    console.log('音频系统初始化完成');
  }

  /**
   * 设置输入事件监听
   * @private
   */
  _setupInputEvents() {
    // 暂停/恢复
    this.adapters.input.on('pause', () => {
      if (this.isPaused) {
        this.resume();
      } else {
        this.pause();
      }
    });

    // 其他输入事件将由具体的游戏模块处理
  }

  /**
   * 启动主循环
   * @private
   */
  _startMainLoop() {
    const mainLoop = (currentTime) => {
      if (!this.isRunning) return;

      // 计算deltaTime
      this.deltaTime = currentTime - this.lastFrameTime;
      this.lastFrameTime = currentTime;
      this.frameCount++;

      // 如果没有暂停，更新游戏逻辑
      if (!this.isPaused) {
        this._update(this.deltaTime);
      }

      // 渲染（暂停时也要渲染暂停界面）
      this._render();

      // 请求下一帧
      this.animationFrameId = this.adapters.render.requestAnimationFrame(mainLoop);
    };

    // 启动循环
    this.animationFrameId = this.adapters.render.requestAnimationFrame(mainLoop);
  }

  /**
   * 更新游戏逻辑
   * @param {number} deltaTime - 帧间隔时间
   * @private
   */
  _update(deltaTime) {
    // 发出更新事件
    this.emit('engine:update', { deltaTime, frameCount: this.frameCount });

    // 更新游戏控制器
    if (this.gameController) {
      this.gameController.update();
    }

    // 更新其他模块
    // TODO: 更新其他游戏模块
  }

  /**
   * 渲染游戏
   * @private
   */
  _render() {
    if (!this.ctx) return;

    // 清空画布
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // 发出渲染事件
    this.emit('engine:render', { ctx: this.ctx, canvas: this.canvas });

    // 渲染游戏控制器
    // const gameController = this.systems.get('GameController');
    // if (gameController && typeof gameController.render === 'function') {
    //   gameController.render(ctx);
    // }

    // 渲染其他模块
    // TODO: 渲染其他游戏模块
  }

  /**
   * 计算FPS
   * @returns {number} 当前FPS
   * @private
   */
  _calculateFPS() {
    if (this.deltaTime === 0) return 0;
    return Math.round(1000 / this.deltaTime);
  }

  /**
   * 销毁游戏引擎
   */
  destroy() {
    // 停止游戏循环
    this.stop();

    // 清理输入适配器
    if (this.adapters.input) {
      this.adapters.input.destroy();
    }

    // 清理音频适配器
    if (this.adapters.audio) {
      this.adapters.audio.destroy();
    }

    // 清理事件监听器
    this.removeAllListeners();

    // 清空引用
    this.gameController = null;
    this.levelManager = null;
    this.itemManager = null;
    this.tutorialManager = null;
    this.progressionManager = null;
    this.canvas = null;
    this.ctx = null;

    console.log('游戏引擎已销毁');
  }
} 