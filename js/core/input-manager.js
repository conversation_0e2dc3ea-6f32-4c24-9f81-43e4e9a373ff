import Emitter from '../libs/tinyemitter.js';
import { INPUT_EVENTS } from '../adapters/input-adapter.js';

/**
 * 输入管理器
 * 负责统一处理和分发输入事件到不同的游戏模块
 */
export default class InputManager extends Emitter {
  constructor(inputAdapter, sceneManager) {
    super();
    
    this.inputAdapter = inputAdapter;
    this.sceneManager = sceneManager;
    
    // 输入状态
    this.isEnabled = true;
    this.inputBuffer = []; // 输入缓冲区
    this.lastInputTime = 0;
    this.inputCooldown = 50; // 输入防抖时间(ms)
    
    // 场景特定的输入处理器
    this.sceneInputHandlers = new Map();
    
    // 全局输入处理器
    this.globalInputHandlers = new Map();
    
    this._setupInputEvents();
    
    console.log('InputManager 初始化完成');
  }

  /**
   * 启用输入处理
   */
  enable() {
    this.isEnabled = true;
    if (this.inputAdapter) {
      this.inputAdapter.enable();
    }
    console.log('输入管理器已启用');
  }

  /**
   * 禁用输入处理
   */
  disable() {
    this.isEnabled = false;
    if (this.inputAdapter) {
      this.inputAdapter.disable();
    }
    console.log('输入管理器已禁用');
  }

  /**
   * 检查是否启用
   * @returns {boolean} 是否启用
   */
  isInputEnabled() {
    return this.isEnabled;
  }

  /**
   * 注册场景特定的输入处理器
   * @param {string} sceneType - 场景类型
   * @param {string} inputEvent - 输入事件类型
   * @param {Function} handler - 处理函数
   */
  registerSceneHandler(sceneType, inputEvent, handler) {
    if (!this.sceneInputHandlers.has(sceneType)) {
      this.sceneInputHandlers.set(sceneType, new Map());
    }
    
    const sceneHandlers = this.sceneInputHandlers.get(sceneType);
    if (!sceneHandlers.has(inputEvent)) {
      sceneHandlers.set(inputEvent, []);
    }
    
    sceneHandlers.get(inputEvent).push(handler);
    console.log(`注册场景输入处理器: ${sceneType}.${inputEvent}`);
  }

  /**
   * 注册全局输入处理器
   * @param {string} inputEvent - 输入事件类型
   * @param {Function} handler - 处理函数
   */
  registerGlobalHandler(inputEvent, handler) {
    if (!this.globalInputHandlers.has(inputEvent)) {
      this.globalInputHandlers.set(inputEvent, []);
    }
    
    this.globalInputHandlers.get(inputEvent).push(handler);
    console.log(`注册全局输入处理器: ${inputEvent}`);
  }

  /**
   * 移除场景输入处理器
   * @param {string} sceneType - 场景类型
   * @param {string} inputEvent - 输入事件类型
   * @param {Function} handler - 处理函数
   */
  unregisterSceneHandler(sceneType, inputEvent, handler) {
    const sceneHandlers = this.sceneInputHandlers.get(sceneType);
    if (!sceneHandlers) return;
    
    const eventHandlers = sceneHandlers.get(inputEvent);
    if (!eventHandlers) return;
    
    const index = eventHandlers.indexOf(handler);
    if (index !== -1) {
      eventHandlers.splice(index, 1);
      console.log(`移除场景输入处理器: ${sceneType}.${inputEvent}`);
    }
  }

  /**
   * 移除全局输入处理器
   * @param {string} inputEvent - 输入事件类型
   * @param {Function} handler - 处理函数
   */
  unregisterGlobalHandler(inputEvent, handler) {
    const handlers = this.globalInputHandlers.get(inputEvent);
    if (!handlers) return;
    
    const index = handlers.indexOf(handler);
    if (index !== -1) {
      handlers.splice(index, 1);
      console.log(`移除全局输入处理器: ${inputEvent}`);
    }
  }

  /**
   * 清空场景的所有输入处理器
   * @param {string} sceneType - 场景类型
   */
  clearSceneHandlers(sceneType) {
    this.sceneInputHandlers.delete(sceneType);
    console.log(`清空场景输入处理器: ${sceneType}`);
  }

  /**
   * 清空所有全局输入处理器
   */
  clearGlobalHandlers() {
    this.globalInputHandlers.clear();
    console.log('清空所有全局输入处理器');
  }

  /**
   * 设置输入防抖时间
   * @param {number} cooldown - 防抖时间(ms)
   */
  setInputCooldown(cooldown) {
    this.inputCooldown = Math.max(0, cooldown);
  }

  /**
   * 检查输入是否在冷却期
   * @returns {boolean} 是否在冷却期
   */
  isInputInCooldown() {
    const currentTime = Date.now();
    return currentTime - this.lastInputTime < this.inputCooldown;
  }

  /**
   * 处理输入事件
   * @param {string} inputEvent - 输入事件类型
   * @param {*} data - 事件数据
   * @private
   */
  _handleInputEvent(inputEvent, data) {
    if (!this.isEnabled) {
      return;
    }

    // 输入防抖
    if (this.isInputInCooldown()) {
      return;
    }

    this.lastInputTime = Date.now();

    // 发出原始输入事件
    this.emit('input:raw', { event: inputEvent, data });

    // 处理全局输入处理器
    this._executeGlobalHandlers(inputEvent, data);

    // 处理场景特定的输入处理器
    if (this.sceneManager) {
      const currentScene = this.sceneManager.getCurrentScene();
      if (currentScene) {
        this._executeSceneHandlers(currentScene, inputEvent, data);
      }
    }

    // 发出处理完成事件
    this.emit('input:processed', { event: inputEvent, data });
  }

  /**
   * 执行全局输入处理器
   * @param {string} inputEvent - 输入事件类型
   * @param {*} data - 事件数据
   * @private
   */
  _executeGlobalHandlers(inputEvent, data) {
    const handlers = this.globalInputHandlers.get(inputEvent);
    if (!handlers || handlers.length === 0) return;

    for (const handler of handlers) {
      try {
        handler(data);
      } catch (error) {
        console.error(`全局输入处理器错误 (${inputEvent}):`, error);
      }
    }
  }

  /**
   * 执行场景特定的输入处理器
   * @param {string} sceneType - 场景类型
   * @param {string} inputEvent - 输入事件类型
   * @param {*} data - 事件数据
   * @private
   */
  _executeSceneHandlers(sceneType, inputEvent, data) {
    const sceneHandlers = this.sceneInputHandlers.get(sceneType);
    if (!sceneHandlers) return;

    const handlers = sceneHandlers.get(inputEvent);
    if (!handlers || handlers.length === 0) return;

    for (const handler of handlers) {
      try {
        handler(data);
      } catch (error) {
        console.error(`场景输入处理器错误 (${sceneType}.${inputEvent}):`, error);
      }
    }
  }

  /**
   * 设置输入事件监听
   * @private
   */
  _setupInputEvents() {
    if (!this.inputAdapter) {
      console.warn('输入适配器未提供');
      return;
    }

    // 监听所有输入事件
    Object.values(INPUT_EVENTS).forEach(event => {
      this.inputAdapter.on(event, (data) => {
        this._handleInputEvent(event, data);
      });
    });

    console.log('输入事件监听已设置');
  }

  /**
   * 模拟输入事件（用于测试）
   * @param {string} inputEvent - 输入事件类型
   * @param {*} data - 事件数据
   */
  simulateInput(inputEvent, data = {}) {
    console.log(`模拟输入: ${inputEvent}`, data);
    this._handleInputEvent(inputEvent, data);
  }

  /**
   * 获取输入统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const sceneHandlerCount = Array.from(this.sceneInputHandlers.values())
      .reduce((total, sceneMap) => {
        return total + Array.from(sceneMap.values())
          .reduce((sceneTotal, handlers) => sceneTotal + handlers.length, 0);
      }, 0);

    const globalHandlerCount = Array.from(this.globalInputHandlers.values())
      .reduce((total, handlers) => total + handlers.length, 0);

    return {
      isEnabled: this.isEnabled,
      inputCooldown: this.inputCooldown,
      lastInputTime: this.lastInputTime,
      sceneCount: this.sceneInputHandlers.size,
      sceneHandlerCount,
      globalHandlerCount,
      totalHandlerCount: sceneHandlerCount + globalHandlerCount
    };
  }

  /**
   * 获取当前场景的输入处理器信息
   * @returns {Object} 处理器信息
   */
  getCurrentSceneHandlers() {
    if (!this.sceneManager) return {};

    const currentScene = this.sceneManager.getCurrentScene();
    if (!currentScene) return {};

    const sceneHandlers = this.sceneInputHandlers.get(currentScene);
    if (!sceneHandlers) return {};

    const result = {};
    for (const [event, handlers] of sceneHandlers) {
      result[event] = handlers.length;
    }

    return result;
  }

  /**
   * 销毁输入管理器
   */
  destroy() {
    // 清理所有处理器
    this.sceneInputHandlers.clear();
    this.globalInputHandlers.clear();

    // 清理输入适配器
    if (this.inputAdapter) {
      this.inputAdapter.destroy();
      this.inputAdapter = null;
    }

    // 清理事件监听器
    this.removeAllListeners();

    // 清空引用
    this.sceneManager = null;
    this.inputBuffer.length = 0;

    console.log('输入管理器已销毁');
  }
} 