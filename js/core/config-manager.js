/**
 * 配置管理器
 * 统一管理游戏配置、设置和常量，支持动态配置和持久化
 */
import Emitter from '../libs/tinyemitter.js';

// 默认配置
const DEFAULT_CONFIG = {
  // 游戏基础配置
  game: {
    fps: 60,
    timeStep: 1000 / 60,
    maxTimeStep: 1000 / 30,
    debugMode: false,
    enableStatistics: true
  },
  
  // 网格配置
  grid: {
    rows: 22,
    cols: 10,
    blockSize: 30,
    offsetX: 50,
    offsetY: 50,
    showGridLines: true,
    showGhostPiece: true
  },
  
  // 渲染配置
  render: {
    enableVSync: true,
    enableDirtyRegions: true,
    enableAnimations: true,
    animationSpeed: 1.0,
    backgroundColor: '#000000',
    gridLineColor: 'rgba(255, 255, 255, 0.1)',
    gridLineWidth: 1
  },
  
  // 音频配置
  audio: {
    enabled: true,
    masterVolume: 0.8,
    sfxVolume: 0.7,
    musicVolume: 0.5,
    enableSpatialAudio: false
  },
  
  // 输入配置
  input: {
    enableKeyboardRepeat: true,
    keyRepeatDelay: 150,
    keyRepeatRate: 50,
    enableTouchInput: true,
    touchSensitivity: 1.0,
    enableGestures: true
  },
  
  // 物理配置
  physics: {
    gravity: 500, // 像素/秒²
    lockDelay: 500, // 毫秒
    enableSoftDrop: true,
    softDropSpeed: 20,
    enableHardDrop: true
  },
  
  // 分数配置
  scoring: {
    singleLinePoints: 100,
    doubleLinePoints: 300,
    tripleLinePoints: 500,
    quadLinePoints: 800,
    comboMultiplier: 1.2,
    levelUpLines: 10,
    enableComboBonus: true
  },
  
  // UI配置
  ui: {
    showScore: true,
    showPreview: true,
    showLevel: true,
    showLines: true,
    showStatistics: false,
    fontSize: 16,
    fontFamily: 'Arial, sans-serif',
    primaryColor: '#FFFFFF',
    secondaryColor: '#CCCCCC',
    accentColor: '#FFD700'
  },
  
  // 性能配置
  performance: {
    enableObjectPooling: true,
    enableEventThrottling: true,
    enablePerformanceMonitoring: true,
    enableAutoGC: true,
    frameRateTarget: 60,
    renderTimeTarget: 16.67,
    memoryThreshold: 100
  },
  
  // 调试配置
  debug: {
    enableDebugOverlay: false,
    showFPS: false,
    showMemoryUsage: false,
    showRenderStats: false,
    enableConsoleLogging: true,
    logLevel: 'info' // debug, info, warn, error
  }
};

// 配置验证器
const CONFIG_VALIDATORS = {
  game: {
    fps: (value) => typeof value === 'number' && value > 0 && value <= 120,
    timeStep: (value) => typeof value === 'number' && value > 0,
    debugMode: (value) => typeof value === 'boolean'
  },
  
  grid: {
    rows: (value) => typeof value === 'number' && value >= 10 && value <= 50,
    cols: (value) => typeof value === 'number' && value >= 5 && value <= 20,
    blockSize: (value) => typeof value === 'number' && value >= 10 && value <= 100
  },
  
  render: {
    animationSpeed: (value) => typeof value === 'number' && value >= 0.1 && value <= 5.0,
    backgroundColor: (value) => typeof value === 'string' && /^#[0-9A-Fa-f]{6}$/.test(value)
  },
  
  audio: {
    masterVolume: (value) => typeof value === 'number' && value >= 0 && value <= 1,
    sfxVolume: (value) => typeof value === 'number' && value >= 0 && value <= 1,
    musicVolume: (value) => typeof value === 'number' && value >= 0 && value <= 1
  },
  
  input: {
    keyRepeatDelay: (value) => typeof value === 'number' && value >= 50 && value <= 1000,
    keyRepeatRate: (value) => typeof value === 'number' && value >= 10 && value <= 200,
    touchSensitivity: (value) => typeof value === 'number' && value >= 0.1 && value <= 5.0
  },
  
  physics: {
    gravity: (value) => typeof value === 'number' && value >= 0 && value <= 2000,
    lockDelay: (value) => typeof value === 'number' && value >= 0 && value <= 2000
  },
  
  scoring: {
    singleLinePoints: (value) => typeof value === 'number' && value >= 0,
    comboMultiplier: (value) => typeof value === 'number' && value >= 1.0 && value <= 5.0
  },
  
  performance: {
    frameRateTarget: (value) => typeof value === 'number' && value >= 15 && value <= 120,
    memoryThreshold: (value) => typeof value === 'number' && value >= 50 && value <= 1000
  }
};

export default class ConfigManager extends Emitter {
  constructor() {
    super();
    
    // 当前配置
    this.config = this._deepClone(DEFAULT_CONFIG);
    
    // 配置历史（用于撤销/重做）
    this.configHistory = [];
    this.historyIndex = -1;
    this.maxHistorySize = 50;
    
    // 配置监听器
    this.watchers = new Map();
    
    // 存储键
    this.storageKey = 'game-config';
    
    // 预设配置
    this.presets = new Map();
    
    console.log('⚙️ ConfigManager初始化完成');
    
    this._initPresets();
    this._loadFromStorage();
  }
  
  /**
   * 初始化预设配置
   * @private
   */
  _initPresets() {
    // 性能模式 - 优化性能
    this.presets.set('performance', {
      render: {
        enableDirtyRegions: true,
        enableAnimations: false,
        animationSpeed: 0.5
      },
      physics: {
        gravity: 1000
      },
      performance: {
        enableObjectPooling: true,
        enableEventThrottling: true,
        frameRateTarget: 30
      },
      debug: {
        enableDebugOverlay: false,
        enableConsoleLogging: false
      }
    });
    
    // 质量模式 - 最佳视觉效果
    this.presets.set('quality', {
      render: {
        enableDirtyRegions: false,
        enableAnimations: true,
        animationSpeed: 1.0
      },
      physics: {
        gravity: 500
      },
      performance: {
        frameRateTarget: 60
      },
      audio: {
        masterVolume: 1.0,
        sfxVolume: 0.8,
        musicVolume: 0.6
      }
    });
    
    // 开发模式 - 调试和开发
    this.presets.set('development', {
      game: {
        debugMode: true
      },
      debug: {
        enableDebugOverlay: true,
        showFPS: true,
        showMemoryUsage: true,
        showRenderStats: true,
        enableConsoleLogging: true,
        logLevel: 'debug'
      },
      performance: {
        enablePerformanceMonitoring: true
      }
    });
    
    // 移动设备模式
    this.presets.set('mobile', {
      grid: {
        blockSize: 25
      },
      render: {
        enableAnimations: true,
        animationSpeed: 0.8
      },
      input: {
        enableTouchInput: true,
        enableGestures: true,
        touchSensitivity: 1.2
      },
      performance: {
        frameRateTarget: 30,
        enableObjectPooling: true
      },
      audio: {
        masterVolume: 0.6
      }
    });
    
    console.log('⚙️ 预设配置初始化完成');
  }
  
  /**
   * 从存储加载配置
   * @private
   */
  _loadFromStorage() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.config = this._mergeConfig(DEFAULT_CONFIG, parsed);
        console.log('⚙️ 从存储加载配置成功');
      }
    } catch (error) {
      console.warn('⚙️ 从存储加载配置失败:', error);
    }
  }
  
  /**
   * 保存配置到存储
   * @private
   */
  _saveToStorage() {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.config));
      console.log('⚙️ 配置保存到存储成功');
    } catch (error) {
      console.warn('⚙️ 配置保存到存储失败:', error);
    }
  }
  
  /**
   * 深度克隆对象
   * @private
   */
  _deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    if (obj instanceof Date) {
      return new Date(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this._deepClone(item));
    }
    
    const cloned = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this._deepClone(obj[key]);
      }
    }
    
    return cloned;
  }
  
  /**
   * 合并配置
   * @private
   */
  _mergeConfig(base, override) {
    const result = this._deepClone(base);
    
    for (const key in override) {
      if (override.hasOwnProperty(key)) {
        if (typeof override[key] === 'object' && !Array.isArray(override[key])) {
          result[key] = this._mergeConfig(result[key] || {}, override[key]);
        } else {
          result[key] = override[key];
        }
      }
    }
    
    return result;
  }
  
  /**
   * 验证配置值
   * @private
   */
  _validateConfig(path, value) {
    const pathParts = path.split('.');
    const section = pathParts[0];
    const key = pathParts[1];
    
    const sectionValidators = CONFIG_VALIDATORS[section];
    if (!sectionValidators) {
      return true; // 没有验证器，认为有效
    }
    
    const validator = sectionValidators[key];
    if (!validator) {
      return true; // 没有验证器，认为有效
    }
    
    return validator(value);
  }
  
  /**
   * 添加到历史记录
   * @private
   */
  _addToHistory() {
    // 移除当前位置之后的历史
    this.configHistory = this.configHistory.slice(0, this.historyIndex + 1);
    
    // 添加当前配置
    this.configHistory.push(this._deepClone(this.config));
    this.historyIndex++;
    
    // 限制历史大小
    if (this.configHistory.length > this.maxHistorySize) {
      this.configHistory.shift();
      this.historyIndex--;
    }
  }
  
  /**
   * 获取配置值
   * @param {string} path - 配置路径 (用.分隔)
   * @param {*} defaultValue - 默认值
   */
  get(path, defaultValue = null) {
    const keys = path.split('.');
    let current = this.config;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  }
  
  /**
   * 设置配置值
   * @param {string} path - 配置路径
   * @param {*} value - 配置值
   * @param {boolean} validate - 是否验证
   */
  set(path, value, validate = true) {
    // 验证配置
    if (validate && !this._validateConfig(path, value)) {
      console.warn(`⚙️ 配置验证失败: ${path} = ${value}`);
      return false;
    }
    
    // 保存到历史
    this._addToHistory();
    
    // 设置值
    const keys = path.split('.');
    let current = this.config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    const lastKey = keys[keys.length - 1];
    const oldValue = current[lastKey];
    current[lastKey] = value;
    
    // 触发监听器
    this._notifyWatchers(path, value, oldValue);
    
    // 发出事件
    this.emit('configchange', {
      path,
      value,
      oldValue
    });
    
    // 自动保存
    this._saveToStorage();
    
    console.log(`⚙️ 配置更新: ${path} = ${value}`);
    return true;
  }
  
  /**
   * 批量设置配置
   * @param {Object} configs - 配置对象
   * @param {boolean} validate - 是否验证
   */
  setBatch(configs, validate = true) {
    this._addToHistory();
    
    const changes = [];
    
    for (const [path, value] of Object.entries(configs)) {
      if (validate && !this._validateConfig(path, value)) {
        console.warn(`⚙️ 配置验证失败: ${path} = ${value}`);
        continue;
      }
      
      const keys = path.split('.');
      let current = this.config;
      
      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!(key in current) || typeof current[key] !== 'object') {
          current[key] = {};
        }
        current = current[key];
      }
      
      const lastKey = keys[keys.length - 1];
      const oldValue = current[lastKey];
      current[lastKey] = value;
      
      changes.push({ path, value, oldValue });
      this._notifyWatchers(path, value, oldValue);
    }
    
    // 发出批量更新事件
    this.emit('configbatchchange', { changes });
    
    // 自动保存
    this._saveToStorage();
    
    console.log(`⚙️ 批量配置更新: ${changes.length}项`);
  }
  
  /**
   * 监听配置变化
   * @param {string} path - 配置路径
   * @param {Function} callback - 回调函数
   */
  watch(path, callback) {
    if (!this.watchers.has(path)) {
      this.watchers.set(path, new Set());
    }
    
    this.watchers.get(path).add(callback);
    
    console.log(`⚙️ 监听配置: ${path}`);
  }
  
  /**
   * 取消监听配置变化
   * @param {string} path - 配置路径
   * @param {Function} callback - 回调函数
   */
  unwatch(path, callback) {
    const pathWatchers = this.watchers.get(path);
    if (pathWatchers) {
      pathWatchers.delete(callback);
      if (pathWatchers.size === 0) {
        this.watchers.delete(path);
      }
    }
  }
  
  /**
   * 通知监听器
   * @private
   */
  _notifyWatchers(path, newValue, oldValue) {
    // 通知精确路径监听器
    const pathWatchers = this.watchers.get(path);
    if (pathWatchers) {
      pathWatchers.forEach(callback => {
        try {
          callback(newValue, oldValue, path);
        } catch (error) {
          console.error('⚙️ 配置监听器错误:', error);
        }
      });
    }
    
    // 通知父路径监听器
    const pathParts = path.split('.');
    for (let i = pathParts.length - 1; i > 0; i--) {
      const parentPath = pathParts.slice(0, i).join('.');
      const parentWatchers = this.watchers.get(parentPath);
      if (parentWatchers) {
        parentWatchers.forEach(callback => {
          try {
            callback(this.get(parentPath), undefined, parentPath);
          } catch (error) {
            console.error('⚙️ 配置监听器错误:', error);
          }
        });
      }
    }
  }
  
  /**
   * 应用预设配置
   * @param {string} presetName - 预设名称
   */
  applyPreset(presetName) {
    const preset = this.presets.get(presetName);
    if (!preset) {
      console.warn(`⚙️ 预设配置不存在: ${presetName}`);
      return false;
    }
    
    this._addToHistory();
    
    // 合并预设配置
    this.config = this._mergeConfig(this.config, preset);
    
    // 发出事件
    this.emit('presetapplied', { presetName, preset });
    
    // 自动保存
    this._saveToStorage();
    
    console.log(`⚙️ 应用预设配置: ${presetName}`);
    return true;
  }
  
  /**
   * 添加预设配置
   * @param {string} name - 预设名称
   * @param {Object} config - 配置对象
   */
  addPreset(name, config) {
    this.presets.set(name, this._deepClone(config));
    console.log(`⚙️ 添加预设配置: ${name}`);
  }
  
  /**
   * 获取所有预设名称
   */
  getPresetNames() {
    return Array.from(this.presets.keys());
  }
  
  /**
   * 重置为默认配置
   */
  resetToDefault() {
    this._addToHistory();
    
    this.config = this._deepClone(DEFAULT_CONFIG);
    
    // 发出事件
    this.emit('configreset');
    
    // 自动保存
    this._saveToStorage();
    
    console.log('⚙️ 重置为默认配置');
  }
  
  /**
   * 撤销配置变更
   */
  undo() {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      this.config = this._deepClone(this.configHistory[this.historyIndex]);
      
      this.emit('configundo');
      this._saveToStorage();
      
      console.log('⚙️ 撤销配置变更');
      return true;
    }
    
    return false;
  }
  
  /**
   * 重做配置变更
   */
  redo() {
    if (this.historyIndex < this.configHistory.length - 1) {
      this.historyIndex++;
      this.config = this._deepClone(this.configHistory[this.historyIndex]);
      
      this.emit('configredo');
      this._saveToStorage();
      
      console.log('⚙️ 重做配置变更');
      return true;
    }
    
    return false;
  }
  
  /**
   * 获取完整配置
   */
  getAll() {
    return this._deepClone(this.config);
  }
  
  /**
   * 导出配置
   */
  export() {
    return JSON.stringify(this.config, null, 2);
  }
  
  /**
   * 导入配置
   * @param {string} configJson - 配置JSON字符串
   */
  import(configJson) {
    try {
      const importedConfig = JSON.parse(configJson);
      
      this._addToHistory();
      this.config = this._mergeConfig(DEFAULT_CONFIG, importedConfig);
      
      this.emit('configimport');
      this._saveToStorage();
      
      console.log('⚙️ 配置导入成功');
      return true;
    } catch (error) {
      console.error('⚙️ 配置导入失败:', error);
      return false;
    }
  }
  
  /**
   * 获取配置统计
   */
  getStats() {
    return {
      totalConfigs: this._countConfigs(this.config),
      historySize: this.configHistory.length,
      historyIndex: this.historyIndex,
      presetCount: this.presets.size,
      watcherCount: this.watchers.size
    };
  }
  
  /**
   * 计算配置项数量
   * @private
   */
  _countConfigs(obj) {
    let count = 0;
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
          count += this._countConfigs(obj[key]);
        } else {
          count++;
        }
      }
    }
    
    return count;
  }
  
  /**
   * 销毁配置管理器
   */
  destroy() {
    this.watchers.clear();
    this.presets.clear();
    this.configHistory = [];
    this.removeAllListeners();
    
    console.log('⚙️ ConfigManager销毁完成');
  }
} 