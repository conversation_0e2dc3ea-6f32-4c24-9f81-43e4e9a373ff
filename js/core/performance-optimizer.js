/**
 * 性能优化器
 * 提供对象池管理、事件节流、渲染优化等性能优化功能
 */
import Emitter from '../libs/tinyemitter.js';

// 对象池类
class ObjectPool {
  constructor(createFn, resetFn = null, maxSize = 100) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.maxSize = maxSize;
    this.pool = [];
    this.activeObjects = new Set();
  }
  
  acquire() {
    let obj;
    
    if (this.pool.length > 0) {
      obj = this.pool.pop();
    } else {
      obj = this.createFn();
    }
    
    this.activeObjects.add(obj);
    return obj;
  }
  
  release(obj) {
    if (!this.activeObjects.has(obj)) {
      return false;
    }
    
    this.activeObjects.delete(obj);
    
    if (this.resetFn) {
      this.resetFn(obj);
    }
    
    if (this.pool.length < this.maxSize) {
      this.pool.push(obj);
    }
    
    return true;
  }
  
  clear() {
    this.pool.length = 0;
    this.activeObjects.clear();
  }
  
  getStats() {
    return {
      poolSize: this.pool.length,
      activeCount: this.activeObjects.size,
      maxSize: this.maxSize
    };
  }
}

// 事件节流器
class EventThrottler {
  constructor() {
    this.throttles = new Map();
  }
  
  throttle(key, fn, delay = 16) {
    if (this.throttles.has(key)) {
      return false;
    }
    
    this.throttles.set(key, setTimeout(() => {
      this.throttles.delete(key);
      fn();
    }, delay));
    
    return true;
  }
  
  debounce(key, fn, delay = 100) {
    if (this.throttles.has(key)) {
      clearTimeout(this.throttles.get(key));
    }
    
    this.throttles.set(key, setTimeout(() => {
      this.throttles.delete(key);
      fn();
    }, delay));
  }
  
  clear(key) {
    if (this.throttles.has(key)) {
      clearTimeout(this.throttles.get(key));
      this.throttles.delete(key);
    }
  }
  
  clearAll() {
    for (const timeout of this.throttles.values()) {
      clearTimeout(timeout);
    }
    this.throttles.clear();
  }
}

// 性能监控器
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      frameRate: {
        samples: [],
        average: 0,
        min: Infinity,
        max: 0
      },
      renderTime: {
        samples: [],
        average: 0,
        min: Infinity,
        max: 0
      },
      memoryUsage: {
        samples: [],
        average: 0,
        current: 0
      }
    };
    
    this.maxSamples = 60; // 保留最近60帧的数据
    this.lastFrameTime = performance.now();
  }
  
  recordFrame() {
    const now = performance.now();
    const frameTime = now - this.lastFrameTime;
    const fps = 1000 / frameTime;
    
    this._updateMetric('frameRate', fps);
    this.lastFrameTime = now;
  }
  
  recordRenderTime(renderTime) {
    this._updateMetric('renderTime', renderTime);
  }
  
  recordMemoryUsage() {
    if (performance.memory) {
      const usage = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
      this._updateMetric('memoryUsage', usage);
    }
  }
  
  _updateMetric(metricName, value) {
    const metric = this.metrics[metricName];
    
    metric.samples.push(value);
    if (metric.samples.length > this.maxSamples) {
      metric.samples.shift();
    }
    
    metric.min = Math.min(metric.min, value);
    metric.max = Math.max(metric.max, value);
    metric.average = metric.samples.reduce((sum, v) => sum + v, 0) / metric.samples.length;
    metric.current = value;
  }
  
  getMetrics() {
    return {
      ...this.metrics,
      sampleCount: this.metrics.frameRate.samples.length
    };
  }
  
  reset() {
    for (const metric of Object.values(this.metrics)) {
      metric.samples = [];
      metric.average = 0;
      metric.min = Infinity;
      metric.max = 0;
      metric.current = 0;
    }
  }
}

export default class PerformanceOptimizer extends Emitter {
  constructor() {
    super();
    
    // 对象池管理
    this.objectPools = new Map();
    
    // 事件节流器
    this.throttler = new EventThrottler();
    
    // 性能监控
    this.monitor = new PerformanceMonitor();
    
    // 优化配置
    this.config = {
      enableObjectPooling: true,
      enableEventThrottling: true,
      enablePerformanceMonitoring: true,
      enableAutoGC: true,
      frameRateTarget: 60,
      renderTimeTarget: 16.67, // 60fps
      memoryThreshold: 100 // MB
    };
    
    // 自动垃圾回收
    this.gcTimer = null;
    
    // 统计信息
    this.stats = {
      poolHits: 0,
      poolMisses: 0,
      throttledEvents: 0,
      gcRuns: 0
    };
    
    this._initObjectPools();
    this._startAutoGC();
    
    console.log('⚡ PerformanceOptimizer初始化完成');
  }
  
  /**
   * 初始化对象池
   * @private
   */
  _initObjectPools() {
    // 动画对象池
    this.createObjectPool('animation', () => ({
      type: null,
      startTime: 0,
      duration: 0,
      progress: 0,
      data: {}
    }), (obj) => {
      obj.type = null;
      obj.startTime = 0;
      obj.duration = 0;
      obj.progress = 0;
      obj.data = {};
    });
    
    // 特效对象池
    this.createObjectPool('effect', () => ({
      id: null,
      type: null,
      x: 0,
      y: 0,
      scale: 1,
      alpha: 1,
      active: false
    }), (obj) => {
      obj.id = null;
      obj.type = null;
      obj.x = 0;
      obj.y = 0;
      obj.scale = 1;
      obj.alpha = 1;
      obj.active = false;
    });
    
    // 事件对象池
    this.createObjectPool('event', () => ({
      type: null,
      data: null,
      timestamp: 0,
      handled: false
    }), (obj) => {
      obj.type = null;
      obj.data = null;
      obj.timestamp = 0;
      obj.handled = false;
    });
    
    console.log('⚡ 对象池初始化完成');
  }
  
  /**
   * 创建对象池
   * @param {string} name - 池名称
   * @param {Function} createFn - 创建函数
   * @param {Function} resetFn - 重置函数
   * @param {number} maxSize - 最大大小
   */
  createObjectPool(name, createFn, resetFn = null, maxSize = 100) {
    this.objectPools.set(name, new ObjectPool(createFn, resetFn, maxSize));
    console.log(`⚡ 创建对象池: ${name}, 最大大小: ${maxSize}`);
  }
  
  /**
   * 从对象池获取对象
   * @param {string} poolName - 池名称
   */
  acquireObject(poolName) {
    if (!this.config.enableObjectPooling) {
      return null;
    }
    
    const pool = this.objectPools.get(poolName);
    if (!pool) {
      console.warn(`⚡ 对象池不存在: ${poolName}`);
      return null;
    }
    
    this.stats.poolHits++;
    return pool.acquire();
  }
  
  /**
   * 释放对象到对象池
   * @param {string} poolName - 池名称
   * @param {Object} obj - 对象
   */
  releaseObject(poolName, obj) {
    if (!this.config.enableObjectPooling) {
      return false;
    }
    
    const pool = this.objectPools.get(poolName);
    if (!pool) {
      return false;
    }
    
    return pool.release(obj);
  }
  
  /**
   * 节流执行函数
   * @param {string} key - 节流键
   * @param {Function} fn - 执行函数
   * @param {number} delay - 延迟时间
   */
  throttle(key, fn, delay = 16) {
    if (!this.config.enableEventThrottling) {
      fn();
      return true;
    }
    
    const executed = this.throttler.throttle(key, fn, delay);
    if (!executed) {
      this.stats.throttledEvents++;
    }
    
    return executed;
  }
  
  /**
   * 防抖执行函数
   * @param {string} key - 防抖键
   * @param {Function} fn - 执行函数
   * @param {number} delay - 延迟时间
   */
  debounce(key, fn, delay = 100) {
    if (!this.config.enableEventThrottling) {
      fn();
      return;
    }
    
    this.throttler.debounce(key, fn, delay);
  }
  
  /**
   * 记录帧性能
   */
  recordFrame() {
    if (!this.config.enablePerformanceMonitoring) {
      return;
    }
    
    this.monitor.recordFrame();
    
    // 检查性能警告
    const metrics = this.monitor.getMetrics();
    
    if (metrics.frameRate.current < this.config.frameRateTarget * 0.8) {
      this.emit('performance-warning', {
        type: 'low-framerate',
        current: metrics.frameRate.current,
        target: this.config.frameRateTarget
      });
    }
    
    if (metrics.renderTime.current > this.config.renderTimeTarget * 1.5) {
      this.emit('performance-warning', {
        type: 'high-rendertime',
        current: metrics.renderTime.current,
        target: this.config.renderTimeTarget
      });
    }
  }
  
  /**
   * 记录渲染时间
   * @param {number} renderTime - 渲染时间
   */
  recordRenderTime(renderTime) {
    if (!this.config.enablePerformanceMonitoring) {
      return;
    }
    
    this.monitor.recordRenderTime(renderTime);
  }
  
  /**
   * 记录内存使用
   */
  recordMemoryUsage() {
    if (!this.config.enablePerformanceMonitoring) {
      return;
    }
    
    this.monitor.recordMemoryUsage();
    
    const metrics = this.monitor.getMetrics();
    if (metrics.memoryUsage.current > this.config.memoryThreshold) {
      this.emit('performance-warning', {
        type: 'high-memory',
        current: metrics.memoryUsage.current,
        threshold: this.config.memoryThreshold
      });
      
      // 触发垃圾回收
      this.forceGC();
    }
  }
  
  /**
   * 启动自动垃圾回收
   * @private
   */
  _startAutoGC() {
    if (!this.config.enableAutoGC) {
      return;
    }
    
    this.gcTimer = setInterval(() => {
      this.runGC();
    }, 10000); // 每10秒运行一次
  }
  
  /**
   * 运行垃圾回收
   */
  runGC() {
    // 清理对象池中的过期对象
    for (const [name, pool] of this.objectPools) {
      if (pool.pool.length > pool.maxSize * 0.5) {
        const keepCount = Math.floor(pool.maxSize * 0.3);
        pool.pool.splice(0, pool.pool.length - keepCount);
      }
    }
    
    // 清理节流器
    this.throttler.clearAll();
    
    // 记录内存使用
    this.recordMemoryUsage();
    
    this.stats.gcRuns++;
    
    this.emit('gc-run', {
      timestamp: Date.now(),
      poolStats: this.getPoolStats()
    });
  }
  
  /**
   * 强制垃圾回收
   */
  forceGC() {
    // 清空所有对象池
    for (const pool of this.objectPools.values()) {
      pool.clear();
    }
    
    // 清空节流器
    this.throttler.clearAll();
    
    // 如果浏览器支持，调用垃圾回收
    if (window.gc) {
      window.gc();
    }
    
    this.stats.gcRuns++;
    
    console.log('⚡ 强制垃圾回收完成');
  }
  
  /**
   * 获取对象池统计
   */
  getPoolStats() {
    const stats = {};
    
    for (const [name, pool] of this.objectPools) {
      stats[name] = pool.getStats();
    }
    
    return stats;
  }
  
  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    return this.monitor.getMetrics();
  }
  
  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      poolStats: this.getPoolStats(),
      performanceMetrics: this.getPerformanceMetrics()
    };
  }
  
  /**
   * 设置配置
   * @param {Object} config - 配置项
   */
  setConfig(config) {
    Object.assign(this.config, config);
    
    // 重新启动自动GC
    if (this.gcTimer) {
      clearInterval(this.gcTimer);
      this.gcTimer = null;
    }
    
    if (this.config.enableAutoGC) {
      this._startAutoGC();
    }
    
    console.log('⚡ 性能优化配置更新:', config);
  }
  
  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      poolHits: 0,
      poolMisses: 0,
      throttledEvents: 0,
      gcRuns: 0
    };
    
    this.monitor.reset();
    
    console.log('⚡ 性能统计重置');
  }
  
  /**
   * 优化建议
   */
  getOptimizationSuggestions() {
    const metrics = this.getPerformanceMetrics();
    const suggestions = [];
    
    if (metrics.frameRate.average < this.config.frameRateTarget * 0.9) {
      suggestions.push({
        type: 'framerate',
        priority: 'high',
        message: '帧率偏低，建议减少渲染复杂度或优化游戏逻辑',
        current: metrics.frameRate.average,
        target: this.config.frameRateTarget
      });
    }
    
    if (metrics.renderTime.average > this.config.renderTimeTarget * 1.2) {
      suggestions.push({
        type: 'rendertime',
        priority: 'medium',
        message: '渲染时间过长，建议优化渲染逻辑或启用脏区域渲染',
        current: metrics.renderTime.average,
        target: this.config.renderTimeTarget
      });
    }
    
    if (metrics.memoryUsage.current > this.config.memoryThreshold * 0.8) {
      suggestions.push({
        type: 'memory',
        priority: 'medium',
        message: '内存使用较高，建议增加垃圾回收频率或优化内存使用',
        current: metrics.memoryUsage.current,
        threshold: this.config.memoryThreshold
      });
    }
    
    const poolStats = this.getPoolStats();
    for (const [name, stats] of Object.entries(poolStats)) {
      if (stats.activeCount > stats.maxSize * 0.8) {
        suggestions.push({
          type: 'pool',
          priority: 'low',
          message: `对象池 ${name} 使用率较高，建议增加池大小`,
          pool: name,
          usage: stats.activeCount / stats.maxSize
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * 销毁性能优化器
   */
  destroy() {
    // 清理定时器
    if (this.gcTimer) {
      clearInterval(this.gcTimer);
    }
    
    // 清理对象池
    for (const pool of this.objectPools.values()) {
      pool.clear();
    }
    this.objectPools.clear();
    
    // 清理节流器
    this.throttler.clearAll();
    
    // 清理监听器
    this.removeAllListeners();
    
    console.log('⚡ PerformanceOptimizer销毁完成');
  }
} 