import Emitter from '../libs/tinyemitter.js';

/**
 * 场景管理器
 * 负责管理游戏不同场景的切换和状态
 */

// 场景类型
export const SCENE_TYPES = {
  SPLASH: 'splash',           // 启动画面
  LEVEL_SELECT: 'level_select', // 关卡选择
  GAME_PLAYING: 'game_playing', // 游戏中
  GAME_PAUSED: 'game_paused',   // 游戏暂停
  GAME_OVER: 'game_over',       // 游戏结束
  LEVEL_COMPLETE: 'level_complete', // 关卡完成
  SETTINGS: 'settings',         // 设置界面
  TUTORIAL: 'tutorial'          // 教程
};

export default class SceneManager extends Emitter {
  constructor(gameEngine) {
    super();
    
    this.gameEngine = gameEngine;
    this.currentScene = null;
    this.previousScene = null;
    this.sceneStack = []; // 场景栈，支持场景嵌套
    this.transitionDuration = 300; // 场景切换动画时长(ms)
    this.isTransitioning = false;
    
    // 场景数据
    this.sceneData = new Map();
    
    console.log('SceneManager 初始化完成');
  }

  /**
   * 获取当前场景
   * @returns {string} 当前场景类型
   */
  getCurrentScene() {
    return this.currentScene;
  }

  /**
   * 获取上一个场景
   * @returns {string} 上一个场景类型
   */
  getPreviousScene() {
    return this.previousScene;
  }

  /**
   * 检查是否在过渡中
   * @returns {boolean} 是否在过渡中
   */
  isInTransition() {
    return this.isTransitioning;
  }

  /**
   * 切换到指定场景
   * @param {string} sceneType - 场景类型
   * @param {Object} data - 场景数据
   * @param {Object} options - 切换选项
   */
  async switchTo(sceneType, data = {}, options = {}) {
    if (this.isTransitioning) {
      console.warn('场景正在切换中，忽略请求');
      return false;
    }

    if (this.currentScene === sceneType) {
      console.warn(`已经在场景 ${sceneType} 中`);
      return false;
    }

    console.log(`场景切换: ${this.currentScene} -> ${sceneType}`);

    this.isTransitioning = true;

    try {
      // 保存场景数据
      this.sceneData.set(sceneType, data);

      // 发出场景离开事件
      if (this.currentScene) {
        this.emit('scene:leave', {
          from: this.currentScene,
          to: sceneType,
          data: this.sceneData.get(this.currentScene)
        });
      }

      // 执行切换动画
      if (options.animated !== false) {
        await this._playTransitionAnimation(this.currentScene, sceneType);
      }

      // 更新场景状态
      this.previousScene = this.currentScene;
      this.currentScene = sceneType;

      // 发出场景进入事件
      this.emit('scene:enter', {
        from: this.previousScene,
        to: sceneType,
        data: data
      });

      // 发出场景切换完成事件
      this.emit('scene:changed', {
        from: this.previousScene,
        to: sceneType,
        data: data
      });

      this.isTransitioning = false;
      return true;

    } catch (error) {
      console.error('场景切换失败:', error);
      this.isTransitioning = false;
      this.emit('scene:error', { error, from: this.currentScene, to: sceneType });
      return false;
    }
  }

  /**
   * 推入场景到栈中（支持嵌套场景）
   * @param {string} sceneType - 场景类型
   * @param {Object} data - 场景数据
   */
  async pushScene(sceneType, data = {}) {
    if (this.currentScene) {
      this.sceneStack.push({
        scene: this.currentScene,
        data: this.sceneData.get(this.currentScene)
      });
    }

    return this.switchTo(sceneType, data);
  }

  /**
   * 弹出场景栈，返回上一个场景
   */
  async popScene() {
    if (this.sceneStack.length === 0) {
      console.warn('场景栈为空，无法弹出');
      return false;
    }

    const previousSceneInfo = this.sceneStack.pop();
    return this.switchTo(previousSceneInfo.scene, previousSceneInfo.data);
  }

  /**
   * 获取场景数据
   * @param {string} sceneType - 场景类型
   * @returns {Object} 场景数据
   */
  getSceneData(sceneType = null) {
    const targetScene = sceneType || this.currentScene;
    return this.sceneData.get(targetScene) || {};
  }

  /**
   * 设置场景数据
   * @param {string} sceneType - 场景类型
   * @param {Object} data - 场景数据
   */
  setSceneData(sceneType, data) {
    this.sceneData.set(sceneType, data);
  }

  /**
   * 更新当前场景数据
   * @param {Object} data - 要更新的数据
   */
  updateCurrentSceneData(data) {
    if (!this.currentScene) return;
    
    const currentData = this.sceneData.get(this.currentScene) || {};
    this.sceneData.set(this.currentScene, { ...currentData, ...data });
  }

  /**
   * 检查是否可以返回上一场景
   * @returns {boolean} 是否可以返回
   */
  canGoBack() {
    return this.sceneStack.length > 0 || this.previousScene !== null;
  }

  /**
   * 返回上一场景
   */
  async goBack() {
    if (this.sceneStack.length > 0) {
      return this.popScene();
    } else if (this.previousScene) {
      return this.switchTo(this.previousScene);
    } else {
      console.warn('没有可返回的场景');
      return false;
    }
  }

  /**
   * 清空场景栈
   */
  clearSceneStack() {
    this.sceneStack.length = 0;
  }

  /**
   * 重置场景管理器
   */
  reset() {
    this.currentScene = null;
    this.previousScene = null;
    this.sceneStack.length = 0;
    this.sceneData.clear();
    this.isTransitioning = false;
    
    console.log('场景管理器已重置');
  }

  /**
   * 播放场景切换动画
   * @param {string} fromScene - 源场景
   * @param {string} toScene - 目标场景
   * @private
   */
  async _playTransitionAnimation(fromScene, toScene) {
    // 发出动画开始事件
    this.emit('scene:transition:start', { from: fromScene, to: toScene });

    // 简单的淡入淡出效果
    return new Promise((resolve) => {
      setTimeout(() => {
        // 发出动画结束事件
        this.emit('scene:transition:end', { from: fromScene, to: toScene });
        resolve();
      }, this.transitionDuration);
    });
  }

  /**
   * 设置切换动画时长
   * @param {number} duration - 动画时长(ms)
   */
  setTransitionDuration(duration) {
    this.transitionDuration = Math.max(0, duration);
  }

  /**
   * 获取场景管理器状态
   * @returns {Object} 状态信息
   */
  getState() {
    return {
      currentScene: this.currentScene,
      previousScene: this.previousScene,
      sceneStackDepth: this.sceneStack.length,
      isTransitioning: this.isTransitioning,
      sceneCount: this.sceneData.size
    };
  }

  /**
   * 检查场景是否有效
   * @param {string} sceneType - 场景类型
   * @returns {boolean} 是否有效
   */
  isValidScene(sceneType) {
    return Object.values(SCENE_TYPES).includes(sceneType);
  }

  /**
   * 获取所有可用场景
   * @returns {Array} 场景类型数组
   */
  getAvailableScenes() {
    return Object.values(SCENE_TYPES);
  }

  /**
   * 销毁场景管理器
   */
  destroy() {
    this.reset();
    this.removeAllListeners();
    this.gameEngine = null;
    
    console.log('场景管理器已销毁');
  }
} 