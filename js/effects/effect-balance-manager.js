/**
 * 特殊效果平衡管理器
 * 根据玩家等级和关卡难度动态调整正负面效果的比例
 */
import Emitter from '../libs/tinyemitter.js';
import { BLOCK_EFFECTS, EFFECT_CATEGORIES } from '../game/block.js';

// 效果稀有度等级
export const EFFECT_RARITY = {
  COMMON: 'common',       // 常见（基础效果）
  UNCOMMON: 'uncommon',   // 不常见
  RARE: 'rare',          // 稀有
  EPIC: 'epic',          // 史诗
  LEGENDARY: 'legendary'  // 传说
};

// 效果配置数据
export const EFFECT_CONFIG = {
  [BLOCK_EFFECTS.FROZEN]: {
    category: EFFECT_CATEGORIES.NEGATIVE,
    rarity: EFFECT_RARITY.COMMON,
    unlockLevel: 1,
    baseWeight: 1.0,
    description: '需要两次匹配才能消除',
    strategicValue: 0.3  // 策略价值（0-1）
  },
  [BLOCK_EFFECTS.MINE]: {
    category: EFFECT_CATEGORIES.POSITIVE,
    rarity: EFFECT_RARITY.COMMON,
    unlockLevel: 3,
    baseWeight: 0.8,
    description: '爆炸时消除周围3×3区域',
    strategicValue: 0.7
  },
  [BLOCK_EFFECTS.SHIELD]: {
    category: EFFECT_CATEGORIES.NEGATIVE,
    rarity: EFFECT_RARITY.UNCOMMON,
    unlockLevel: 10,
    baseWeight: 0.6,
    description: '免疫一次道具攻击',
    strategicValue: 0.4
  },
  [BLOCK_EFFECTS.MAGNET]: {
    category: EFFECT_CATEGORIES.POSITIVE,
    rarity: EFFECT_RARITY.UNCOMMON,
    unlockLevel: 15,
    baseWeight: 0.5,
    description: '消除时吸引相同颜色的方块',
    strategicValue: 0.8
  },
  [BLOCK_EFFECTS.RAINBOW]: {
    category: EFFECT_CATEGORIES.POSITIVE,
    rarity: EFFECT_RARITY.RARE,
    unlockLevel: 25,
    baseWeight: 0.3,
    description: '可以与任意颜色匹配',
    strategicValue: 0.9
  },
  [BLOCK_EFFECTS.VIRUS]: {
    category: EFFECT_CATEGORIES.NEGATIVE,
    rarity: EFFECT_RARITY.RARE,
    unlockLevel: 30,
    baseWeight: 0.4,
    description: '传播到相邻方块',
    strategicValue: 0.2
  },
  [BLOCK_EFFECTS.CRYSTAL]: {
    category: EFFECT_CATEGORIES.POSITIVE,
    rarity: EFFECT_RARITY.EPIC,
    unlockLevel: 40,
    baseWeight: 0.2,
    description: '提供3倍分数奖励',
    strategicValue: 0.6
  },
  [BLOCK_EFFECTS.ANCHOR]: {
    category: EFFECT_CATEGORIES.NEGATIVE,
    rarity: EFFECT_RARITY.EPIC,
    unlockLevel: 50,
    baseWeight: 0.15,
    description: '阻止方块下落',
    strategicValue: 0.1
  }
};

export default class EffectBalanceManager extends Emitter {
  constructor(progressionManager) {
    super();
    
    this.progressionManager = progressionManager;
    
    // 平衡配置
    this.balanceConfig = {
      // 基础正负面效果比例（正面:负面）
      baseRatio: {
        positive: 0.6,  // 60%正面效果
        negative: 0.4   // 40%负面效果
      },
      
      // 根据玩家等级调整的比例
      levelAdjustment: {
        // 新手期（1-15级）：更多正面效果
        newbie: { positive: 0.75, negative: 0.25 },
        // 进阶期（16-40级）：平衡
        intermediate: { positive: 0.6, negative: 0.4 },
        // 专家期（41+级）：更多负面效果增加挑战
        expert: { positive: 0.45, negative: 0.55 }
      },
      
      // 根据连续失败次数的动态调整
      failureAdjustment: {
        0: { positive: 1.0, negative: 1.0 },    // 无调整
        1: { positive: 1.1, negative: 0.9 },    // 轻微倾向正面
        2: { positive: 1.2, negative: 0.8 },    // 更多正面效果
        3: { positive: 1.4, negative: 0.6 },    // 显著增加正面效果
        4: { positive: 1.6, negative: 0.4 },    // 大幅增加正面效果
        5: { positive: 2.0, negative: 0.2 }     // 几乎全是正面效果
      }
    };
    
    // 当前效果权重缓存
    this.currentWeights = new Map();
    
    // 效果解锁状态
    this.unlockedEffects = new Set([BLOCK_EFFECTS.FROZEN]); // 默认解锁冰冻效果
    
    this._updateUnlockedEffects();
  }
  
  /**
   * 根据当前游戏状态生成效果分布
   * @param {Object} gameState - 游戏状态
   * @returns {Object} 效果分布配置
   */
  generateEffectDistribution(gameState) {
    const {
      playerLevel = 1,
      currentLevel = 1,
      consecutiveFailures = 0,
      stageTheme = 'tutorial',
      assistanceLevel = 'high'
    } = gameState;
    
    // 更新解锁的效果
    this._updateUnlockedEffects();
    
    // 获取基础比例
    const baseRatio = this._getBaseRatio(playerLevel, stageTheme);
    
    // 应用失败调整
    const adjustedRatio = this._applyFailureAdjustment(baseRatio, consecutiveFailures);
    
    // 应用辅助等级调整
    const finalRatio = this._applyAssistanceAdjustment(adjustedRatio, assistanceLevel);
    
    // 生成具体的效果权重
    const effectWeights = this._generateEffectWeights(finalRatio, currentLevel);
    
    // 缓存当前权重
    this.currentWeights = effectWeights;
    
    this.emit('distribution:updated', {
      ratio: finalRatio,
      weights: effectWeights,
      unlockedEffects: Array.from(this.unlockedEffects)
    });
    
    return {
      ratio: finalRatio,
      weights: effectWeights,
      unlockedEffects: Array.from(this.unlockedEffects)
    };
  }
  
  /**
   * 选择一个随机效果
   * @param {Object} distribution - 效果分布
   * @returns {string} 选中的效果类型
   */
  selectRandomEffect(distribution = null) {
    if (!distribution) {
      distribution = { weights: this.currentWeights };
    }
    
    const weights = distribution.weights;
    if (!weights || weights.size === 0) {
      return BLOCK_EFFECTS.NONE;
    }
    
    // 计算总权重
    let totalWeight = 0;
    for (const weight of weights.values()) {
      totalWeight += weight;
    }
    
    if (totalWeight === 0) {
      return BLOCK_EFFECTS.NONE;
    }
    
    // 随机选择
    let random = Math.random() * totalWeight;
    
    for (const [effect, weight] of weights.entries()) {
      random -= weight;
      if (random <= 0) {
        return effect;
      }
    }
    
    // 后备选择
    return Array.from(weights.keys())[0] || BLOCK_EFFECTS.NONE;
  }
  
  /**
   * 获取效果的策略提示
   * @param {string} effectType - 效果类型
   * @returns {Object} 策略提示
   */
  getEffectStrategy(effectType) {
    const config = EFFECT_CONFIG[effectType];
    if (!config) return null;
    
    const strategies = {
      [BLOCK_EFFECTS.FROZEN]: {
        tip: '优先用道具清理冰冻方块，或者先匹配一次解冻',
        priority: 'high'
      },
      [BLOCK_EFFECTS.MINE]: {
        tip: '将地雷方块放置在方块密集区域，最大化爆炸效果',
        priority: 'medium'
      },
      [BLOCK_EFFECTS.SHIELD]: {
        tip: '护盾方块需要先用普通匹配消除护盾，再用道具攻击',
        priority: 'high'
      },
      [BLOCK_EFFECTS.MAGNET]: {
        tip: '磁力方块消除时会吸引相同颜色，可以创造连锁反应',
        priority: 'low'
      },
      [BLOCK_EFFECTS.RAINBOW]: {
        tip: '彩虹方块是万能匹配，优先用于困难的匹配组合',
        priority: 'low'
      },
      [BLOCK_EFFECTS.VIRUS]: {
        tip: '病毒会传播，尽快消除以防扩散',
        priority: 'high'
      },
      [BLOCK_EFFECTS.CRYSTAL]: {
        tip: '水晶方块提供额外分数，优先保护并在合适时机消除',
        priority: 'medium'
      },
      [BLOCK_EFFECTS.ANCHOR]: {
        tip: '锚点阻止下落，会改变方块布局，需要策略性处理',
        priority: 'high'
      }
    };
    
    return strategies[effectType] || null;
  }
  
  /**
   * 获取基础比例
   * @param {number} playerLevel - 玩家等级
   * @param {string} stageTheme - 阶段主题
   * @returns {Object} 基础比例
   * @private
   */
  _getBaseRatio(playerLevel, stageTheme) {
    // 根据阶段主题调整
    const themeAdjustment = {
      'tutorial': { positive: 0.8, negative: 0.2 },
      'practice': { positive: 0.7, negative: 0.3 },
      'adaptation': { positive: 0.6, negative: 0.4 },
      'improvement': { positive: 0.55, negative: 0.45 },
      'challenge': { positive: 0.45, negative: 0.55 },
      'expert': { positive: 0.4, negative: 0.6 },
      'master': { positive: 0.35, negative: 0.65 }
    };
    
    return themeAdjustment[stageTheme] || this.balanceConfig.baseRatio;
  }
  
  /**
   * 应用失败调整
   * @param {Object} baseRatio - 基础比例
   * @param {number} consecutiveFailures - 连续失败次数
   * @returns {Object} 调整后的比例
   * @private
   */
  _applyFailureAdjustment(baseRatio, consecutiveFailures) {
    const adjustment = this.balanceConfig.failureAdjustment[Math.min(consecutiveFailures, 5)] || 
                      this.balanceConfig.failureAdjustment[0];
    
    return {
      positive: baseRatio.positive * adjustment.positive,
      negative: baseRatio.negative * adjustment.negative
    };
  }
  
  /**
   * 应用辅助等级调整
   * @param {Object} ratio - 当前比例
   * @param {string} assistanceLevel - 辅助等级
   * @returns {Object} 最终比例
   * @private
   */
  _applyAssistanceAdjustment(ratio, assistanceLevel) {
    const assistanceMultiplier = {
      'high': { positive: 1.3, negative: 0.7 },
      'medium': { positive: 1.1, negative: 0.9 },
      'low': { positive: 1.0, negative: 1.0 },
      'minimal': { positive: 0.9, negative: 1.1 },
      'none': { positive: 0.8, negative: 1.2 }
    };
    
    const multiplier = assistanceMultiplier[assistanceLevel] || assistanceMultiplier['medium'];
    
    return {
      positive: ratio.positive * multiplier.positive,
      negative: ratio.negative * multiplier.negative
    };
  }
  
  /**
   * 生成效果权重
   * @param {Object} ratio - 正负面比例
   * @param {number} currentLevel - 当前关卡
   * @returns {Map} 效果权重映射
   * @private
   */
  _generateEffectWeights(ratio, currentLevel) {
    const weights = new Map();
    
    // 分别处理正面和负面效果
    const positiveEffects = [];
    const negativeEffects = [];
    
    for (const [effect, config] of Object.entries(EFFECT_CONFIG)) {
      if (!this.unlockedEffects.has(effect)) continue;
      
      if (config.category === EFFECT_CATEGORIES.POSITIVE) {
        positiveEffects.push({ effect, config });
      } else if (config.category === EFFECT_CATEGORIES.NEGATIVE) {
        negativeEffects.push({ effect, config });
      }
    }
    
    // 计算正面效果权重
    const positiveWeight = ratio.positive / Math.max(positiveEffects.length, 1);
    positiveEffects.forEach(({ effect, config }) => {
      const levelBonus = Math.max(0, currentLevel - config.unlockLevel) * 0.01;
      weights.set(effect, config.baseWeight * positiveWeight * (1 + levelBonus));
    });
    
    // 计算负面效果权重
    const negativeWeight = ratio.negative / Math.max(negativeEffects.length, 1);
    negativeEffects.forEach(({ effect, config }) => {
      const levelBonus = Math.max(0, currentLevel - config.unlockLevel) * 0.01;
      weights.set(effect, config.baseWeight * negativeWeight * (1 + levelBonus));
    });
    
    return weights;
  }
  
  /**
   * 更新解锁的效果
   * @private
   */
  _updateUnlockedEffects() {
    if (!this.progressionManager || !this.progressionManager.playerData) return;

    const playerLevel = this.progressionManager.playerData.level || 1;

    // 根据玩家等级解锁效果
    for (const [effect, config] of Object.entries(EFFECT_CONFIG)) {
      if (playerLevel >= config.unlockLevel) {
        this.unlockedEffects.add(effect);
      }
    }
  }
  
  /**
   * 获取效果统计信息
   * @returns {Object} 统计信息
   */
  getEffectStats() {
    const stats = {
      total: this.unlockedEffects.size,
      positive: 0,
      negative: 0,
      byRarity: {}
    };
    
    for (const effect of this.unlockedEffects) {
      const config = EFFECT_CONFIG[effect];
      if (!config) continue;
      
      if (config.category === EFFECT_CATEGORIES.POSITIVE) {
        stats.positive++;
      } else if (config.category === EFFECT_CATEGORIES.NEGATIVE) {
        stats.negative++;
      }
      
      stats.byRarity[config.rarity] = (stats.byRarity[config.rarity] || 0) + 1;
    }
    
    return stats;
  }
  
  /**
   * 强制解锁效果（用于测试或特殊奖励）
   * @param {string} effectType - 效果类型
   */
  unlockEffect(effectType) {
    if (EFFECT_CONFIG[effectType]) {
      this.unlockedEffects.add(effectType);
      this.emit('effect:unlocked', { effect: effectType });
    }
  }
}
