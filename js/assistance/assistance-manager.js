/**
 * 辅助功能管理器
 * 提供降低游戏门槛的各种辅助功能
 */
import Emitter from '../libs/tinyemitter.js';

// 辅助功能类型
export const ASSISTANCE_FEATURES = {
  PAUSE_ON_DANGER: 'pause_on_danger',
  GHOST_PIECE: 'ghost_piece',
  MATCH_HIGHLIGHT: 'match_highlight',
  SLOW_MODE: 'slow_mode',
  UNDO_MOVE: 'undo_move',
  HINT_SYSTEM: 'hint_system',
  AUTO_PAUSE: 'auto_pause',
  EXTENDED_LOCK_DELAY: 'extended_lock_delay'
};

export default class AssistanceManager extends Emitter {
  constructor(gameController, grid) {
    super();
    
    this.gameController = gameController;
    this.grid = grid;
    
    // 辅助功能状态
    this.features = {
      [ASSISTANCE_FEATURES.PAUSE_ON_DANGER]: false,
      [ASSISTANCE_FEATURES.GHOST_PIECE]: true,
      [ASSISTANCE_FEATURES.MATCH_HIGHLIGHT]: true,
      [ASSISTANCE_FEATURES.SLOW_MODE]: false,
      [ASSISTANCE_FEATURES.UNDO_MOVE]: false,
      [ASSISTANCE_FEATURES.HINT_SYSTEM]: true,
      [ASSISTANCE_FEATURES.AUTO_PAUSE]: false,
      [ASSISTANCE_FEATURES.EXTENDED_LOCK_DELAY]: false
    };
    
    // 撤销系统
    this.undoHistory = [];
    this.maxUndoSteps = 3;
    this.undoUsesLeft = 3;
    
    // 慢速模式
    this.slowModeMultiplier = 0.5;
    this.originalSpeed = null;
    
    // 危险检测
    this.dangerThreshold = 5; // 顶部5行
    this.lastDangerCheck = 0;
    
    // 自动暂停
    this.autoPauseTimer = 0;
    this.autoPauseDelay = 1800; // 30秒无操作自动暂停
    this.lastActionTime = Date.now();
    
    // 扩展锁定延迟
    this.extendedLockDelay = 60; // 1秒
    this.originalLockDelay = null;
    
    // 匹配高亮
    this.highlightedBlocks = new Set();
    this.highlightTimer = 0;
    
    this._loadSettings();
  }
  
  /**
   * 启用辅助功能
   * @param {string} feature - 功能名称
   */
  enableFeature(feature) {
    if (this.features.hasOwnProperty(feature)) {
      this.features[feature] = true;
      this._applyFeature(feature, true);
      this._saveSettings();
      
      this.emit('assistance:feature_enabled', { feature });
    }
  }
  
  /**
   * 禁用辅助功能
   * @param {string} feature - 功能名称
   */
  disableFeature(feature) {
    if (this.features.hasOwnProperty(feature)) {
      this.features[feature] = false;
      this._applyFeature(feature, false);
      this._saveSettings();
      
      this.emit('assistance:feature_disabled', { feature });
    }
  }
  
  /**
   * 切换辅助功能
   * @param {string} feature - 功能名称
   */
  toggleFeature(feature) {
    if (this.features[feature]) {
      this.disableFeature(feature);
    } else {
      this.enableFeature(feature);
    }
  }
  
  /**
   * 应用辅助功能
   * @param {string} feature - 功能名称
   * @param {boolean} enabled - 是否启用
   * @private
   */
  _applyFeature(feature, enabled) {
    switch (feature) {
      case ASSISTANCE_FEATURES.SLOW_MODE:
        this._applySlowMode(enabled);
        break;
      case ASSISTANCE_FEATURES.EXTENDED_LOCK_DELAY:
        this._applyExtendedLockDelay(enabled);
        break;
      case ASSISTANCE_FEATURES.MATCH_HIGHLIGHT:
        if (!enabled) {
          this.highlightedBlocks.clear();
        }
        break;
    }
  }
  
  /**
   * 应用慢速模式
   * @param {boolean} enabled - 是否启用
   * @private
   */
  _applySlowMode(enabled) {
    if (!this.gameController) return;
    
    if (enabled) {
      if (this.originalSpeed === null) {
        this.originalSpeed = this.gameController.options.speedFactor || 1.0;
      }
      this.gameController.options.speedFactor = this.originalSpeed * this.slowModeMultiplier;
    } else {
      if (this.originalSpeed !== null) {
        this.gameController.options.speedFactor = this.originalSpeed;
        this.originalSpeed = null;
      }
    }
  }
  
  /**
   * 应用扩展锁定延迟
   * @param {boolean} enabled - 是否启用
   * @private
   */
  _applyExtendedLockDelay(enabled) {
    if (!this.gameController) return;
    
    if (enabled) {
      if (this.originalLockDelay === null) {
        this.originalLockDelay = this.gameController.lockDelay || 30;
      }
      this.gameController.lockDelay = this.extendedLockDelay;
    } else {
      if (this.originalLockDelay !== null) {
        this.gameController.lockDelay = this.originalLockDelay;
        this.originalLockDelay = null;
      }
    }
  }
  
  /**
   * 保存游戏状态用于撤销
   */
  saveStateForUndo() {
    if (!this.features[ASSISTANCE_FEATURES.UNDO_MOVE]) return;
    
    const gameState = this._captureGameState();
    this.undoHistory.push(gameState);
    
    // 限制历史记录数量
    if (this.undoHistory.length > this.maxUndoSteps) {
      this.undoHistory.shift();
    }
  }
  
  /**
   * 撤销上一步操作
   * @returns {boolean} 是否成功撤销
   */
  undoLastMove() {
    if (!this.features[ASSISTANCE_FEATURES.UNDO_MOVE]) return false;
    if (this.undoUsesLeft <= 0) return false;
    if (this.undoHistory.length === 0) return false;
    
    const previousState = this.undoHistory.pop();
    this._restoreGameState(previousState);
    
    this.undoUsesLeft--;
    
    this.emit('assistance:undo_used', {
      usesLeft: this.undoUsesLeft
    });
    
    return true;
  }
  
  /**
   * 重置撤销次数
   * @param {number} uses - 撤销次数
   */
  resetUndoUses(uses = 3) {
    this.undoUsesLeft = uses;
    this.emit('assistance:undo_reset', { uses });
  }
  
  /**
   * 检查危险状态
   */
  checkDangerState() {
    if (!this.features[ASSISTANCE_FEATURES.PAUSE_ON_DANGER]) return;
    if (!this.grid) return;
    
    let dangerLevel = 0;
    
    // 检查顶部几行是否有方块
    for (let row = 0; row < this.dangerThreshold; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.getBlock(row, col)) {
          dangerLevel++;
        }
      }
    }
    
    // 如果危险等级过高，暂停游戏
    if (dangerLevel > this.grid.cols * 2) { // 超过2行的方块数量
      this._pauseForDanger();
    }
  }
  
  /**
   * 因危险状态暂停游戏
   * @private
   */
  _pauseForDanger() {
    if (this.gameController && this.gameController.state === 'playing') {
      this.gameController.pause();
      
      this.emit('assistance:danger_pause', {
        message: '检测到危险状态，游戏已自动暂停。请仔细考虑下一步操作。'
      });
    }
  }
  
  /**
   * 高亮可能的匹配
   */
  highlightPossibleMatches() {
    if (!this.features[ASSISTANCE_FEATURES.MATCH_HIGHLIGHT]) return;
    if (!this.grid) return;
    
    this.highlightedBlocks.clear();
    
    // 查找可能的三消匹配
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols - 2; col++) {
        const block1 = this.grid.getBlock(row, col);
        const block2 = this.grid.getBlock(row, col + 1);
        const block3 = this.grid.getBlock(row, col + 2);
        
        if (block1 && block2 && block3 && 
            block1.color === block2.color && block2.color === block3.color) {
          this.highlightedBlocks.add(block1);
          this.highlightedBlocks.add(block2);
          this.highlightedBlocks.add(block3);
        }
      }
    }
    
    // 垂直匹配
    for (let row = 0; row < this.grid.rows - 2; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block1 = this.grid.getBlock(row, col);
        const block2 = this.grid.getBlock(row + 1, col);
        const block3 = this.grid.getBlock(row + 2, col);
        
        if (block1 && block2 && block3 && 
            block1.color === block2.color && block2.color === block3.color) {
          this.highlightedBlocks.add(block1);
          this.highlightedBlocks.add(block2);
          this.highlightedBlocks.add(block3);
        }
      }
    }
  }
  
  /**
   * 记录玩家操作
   */
  recordPlayerAction() {
    this.lastActionTime = Date.now();
    this.autoPauseTimer = 0;
  }
  
  /**
   * 更新辅助系统
   */
  update() {
    // 检查危险状态
    if (this.features[ASSISTANCE_FEATURES.PAUSE_ON_DANGER]) {
      this.checkDangerState();
    }
    
    // 自动暂停检查
    if (this.features[ASSISTANCE_FEATURES.AUTO_PAUSE]) {
      this.autoPauseTimer++;
      if (this.autoPauseTimer >= this.autoPauseDelay) {
        this._autoPause();
      }
    }
    
    // 匹配高亮更新
    if (this.features[ASSISTANCE_FEATURES.MATCH_HIGHLIGHT]) {
      this.highlightTimer++;
      if (this.highlightTimer >= 60) { // 每秒更新一次
        this.highlightTimer = 0;
        this.highlightPossibleMatches();
      }
    }
  }
  
  /**
   * 自动暂停
   * @private
   */
  _autoPause() {
    if (this.gameController && this.gameController.state === 'playing') {
      this.gameController.pause();
      
      this.emit('assistance:auto_pause', {
        message: '长时间无操作，游戏已自动暂停。'
      });
    }
  }
  
  /**
   * 捕获游戏状态
   * @returns {Object} 游戏状态
   * @private
   */
  _captureGameState() {
    // 这里需要根据实际的游戏状态结构来实现
    return {
      grid: this._cloneGrid(),
      score: this.gameController ? this.gameController.score : 0,
      currentTetromino: this.gameController ? this._cloneTetromino(this.gameController.currentTetromino) : null,
      timestamp: Date.now()
    };
  }
  
  /**
   * 恢复游戏状态
   * @param {Object} state - 游戏状态
   * @private
   */
  _restoreGameState(state) {
    // 这里需要根据实际的游戏状态结构来实现
    if (this.gameController) {
      this.gameController.score = state.score;
      // 恢复网格状态
      // 恢复当前方块状态
    }
  }
  
  /**
   * 克隆网格
   * @returns {Object} 网格副本
   * @private
   */
  _cloneGrid() {
    // 简化的网格克隆实现
    return {
      // 这里需要深度克隆网格数据
    };
  }
  
  /**
   * 克隆俄罗斯方块
   * @param {Object} tetromino - 俄罗斯方块
   * @returns {Object} 方块副本
   * @private
   */
  _cloneTetromino(tetromino) {
    if (!tetromino) return null;
    
    // 简化的方块克隆实现
    return {
      // 这里需要克隆方块数据
    };
  }
  
  /**
   * 渲染辅助功能
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    // 渲染匹配高亮
    if (this.features[ASSISTANCE_FEATURES.MATCH_HIGHLIGHT]) {
      this._renderMatchHighlight(ctx);
    }
    
    // 渲染幽灵方块
    if (this.features[ASSISTANCE_FEATURES.GHOST_PIECE]) {
      this._renderGhostPiece(ctx);
    }
  }
  
  /**
   * 渲染匹配高亮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderMatchHighlight(ctx) {
    for (const block of this.highlightedBlocks) {
      if (block && typeof block.row === 'number' && typeof block.col === 'number') {
        const pos = this.grid.gridToScreen(block.row, block.col);
        
        ctx.save();
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 3;
        ctx.strokeRect(pos.x, pos.y, this.grid.blockSize, this.grid.blockSize);
        ctx.restore();
      }
    }
  }
  
  /**
   * 渲染幽灵方块
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderGhostPiece(ctx) {
    // 这里需要根据实际的方块渲染逻辑来实现
    if (this.gameController && this.gameController.currentTetromino) {
      // 渲染半透明的预览方块
    }
  }
  
  /**
   * 保存设置
   * @private
   */
  _saveSettings() {
    try {
      const settings = {
        features: this.features,
        undoUsesLeft: this.undoUsesLeft,
        slowModeMultiplier: this.slowModeMultiplier,
        dangerThreshold: this.dangerThreshold
      };

      // 验证数据是否可以序列化
      const jsonString = this._safeStringify(settings);
      if (jsonString === null) {
        console.error('辅助功能设置序列化失败，跳过保存');
        return;
      }

      wx.setStorageSync('assistanceSettings', jsonString);
      console.log('辅助功能设置保存成功');
    } catch (e) {
      console.error('保存辅助功能设置失败:', e);
    }
  }

  /**
   * 安全的JSON序列化
   * @param {Object} obj - 要序列化的对象
   * @returns {string|null} 序列化后的字符串，失败返回null
   * @private
   */
  _safeStringify(obj) {
    try {
      return JSON.stringify(obj, (key, value) => {
        // 过滤掉函数、Symbol、undefined等不可序列化的值
        if (typeof value === 'function' || typeof value === 'symbol' || value === undefined) {
          return null;
        }
        return value;
      });
    } catch (e) {
      console.error('JSON序列化失败:', e);
      return null;
    }
  }
  
  /**
   * 加载设置
   * @private
   */
  _loadSettings() {
    try {
      const settings = wx.getStorageSync('assistanceSettings');
      
      if (settings) {
        const parsed = JSON.parse(settings);
        
        this.features = { ...this.features, ...parsed.features };
        this.undoUsesLeft = parsed.undoUsesLeft || 3;
        this.slowModeMultiplier = parsed.slowModeMultiplier || 0.5;
        this.dangerThreshold = parsed.dangerThreshold || 5;
        
        // 应用已启用的功能
        Object.keys(this.features).forEach(feature => {
          if (this.features[feature]) {
            this._applyFeature(feature, true);
          }
        });
      }
    } catch (e) {
      console.error('加载辅助功能设置失败:', e);
    }
  }
}
