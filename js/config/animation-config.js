/**
 * 动画配置文件
 * 管理游戏中各种动画的参数设置
 */

// 下落动画配置
export const FALLING_ANIMATION_CONFIG = {
  // 速度配置
  FRAMES_PER_ROW: 6,        // 每行下落需要的帧数（原来是15，现在是6，提升5倍速度）
  MIN_DURATION: 3,          // 最小动画持续时间（帧）
  MAX_DURATION: 30,         // 最大动画持续时间（帧）
  
  // 虚影效果配置
  GHOST_ENABLED: true,      // 是否启用虚影效果
  GHOST_ALPHA: 0.4,         // 虚影透明度（0-1）
  GHOST_MIN_ALPHA: 0.2,     // 最小虚影透明度
  GHOST_MAX_ALPHA: 0.6,     // 最大虚影透明度
  
  // 动画曲线配置
  EASING_TYPE: 'easeInQuad', // 缓动类型
  USE_PHYSICS: false,       // 是否使用物理模拟（重力加速度）
  GRAVITY: 0.5,             // 重力加速度（仅在USE_PHYSICS为true时生效）
  
  // 视觉效果配置
  MOTION_BLUR: false,       // 是否启用运动模糊效果
  TRAIL_LENGTH: 6,          // 拖尾长度（帧数）
  
  // 性能配置
  MAX_CONCURRENT_ANIMATIONS: 50, // 最大同时进行的下落动画数量
  SKIP_FRAMES_WHEN_BUSY: true,   // 当动画过多时是否跳帧
};

// 消除动画配置
export const DESTROY_ANIMATION_CONFIG = {
  DURATION: 20,             // 消除动画持续时间（帧）
  SCALE_EFFECT: true,       // 是否启用缩放效果
  FADE_EFFECT: true,        // 是否启用淡出效果
  PARTICLE_EFFECT: false,   // 是否启用粒子效果
};

// 爆炸动画配置
export const EXPLOSION_ANIMATION_CONFIG = {
  DURATION: 15,             // 爆炸动画持续时间（帧）
  MAX_RADIUS_MULTIPLIER: 1.5, // 最大半径倍数
  COLOR_PRIMARY: 'rgba(255, 200, 0, 0.8)',
  COLOR_SECONDARY: 'rgba(255, 100, 0, 0.6)',
};

// 特效动画配置
export const EFFECT_ANIMATION_CONFIG = {
  FIREBALL: {
    DURATION: 30,
    PARTICLE_COUNT: 20,
    FADE_SPEED: 0.05,
  },
  LIGHTNING: {
    DURATION: 20,
    BRANCH_COUNT: 3,
    FLICKER_SPEED: 0.1,
  },
  TORRENT: {
    DURATION: 25,
    WAVE_HEIGHT: 20,
    WAVE_SPEED: 0.2,
  }
};

/**
 * 缓动函数
 */
export const EASING_FUNCTIONS = {
  linear: (t) => t,
  easeInQuad: (t) => t * t,
  easeOutQuad: (t) => t * (2 - t),
  easeInOutQuad: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeInCubic: (t) => t * t * t,
  easeOutCubic: (t) => (--t) * t * t + 1,
  easeInOutCubic: (t) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
  easeInBounce: (t) => 1 - EASING_FUNCTIONS.easeOutBounce(1 - t),
  easeOutBounce: (t) => {
    if (t < 1 / 2.75) {
      return 7.5625 * t * t;
    } else if (t < 2 / 2.75) {
      return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
    } else if (t < 2.5 / 2.75) {
      return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
    } else {
      return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
    }
  }
};

/**
 * 根据下落距离计算动画持续时间
 * @param {number} fallDistance - 下落距离（行数）
 * @returns {number} 动画持续时间（帧数）
 */
export function calculateFallDuration(fallDistance) {
  const config = FALLING_ANIMATION_CONFIG;
  const baseDuration = Math.ceil(config.FRAMES_PER_ROW * fallDistance);
  return Math.max(config.MIN_DURATION, Math.min(config.MAX_DURATION, baseDuration));
}

/**
 * 根据动画进度计算缓动值
 * @param {number} progress - 动画进度（0-1）
 * @param {string} easingType - 缓动类型
 * @returns {number} 缓动后的进度值
 */
export function applyEasing(progress, easingType = 'linear') {
  const easingFunction = EASING_FUNCTIONS[easingType] || EASING_FUNCTIONS.linear;
  return easingFunction(Math.max(0, Math.min(1, progress)));
}

/**
 * 计算虚影透明度
 * @param {number} progress - 动画进度（0-1）
 * @returns {number} 虚影透明度
 */
export function calculateGhostAlpha(progress) {
  const config = FALLING_ANIMATION_CONFIG;
  // 动画开始时虚影较明显，随着进度增加逐渐变淡
  const alpha = config.GHOST_ALPHA * (1 - progress * 0.5);
  return Math.max(config.GHOST_MIN_ALPHA, Math.min(config.GHOST_MAX_ALPHA, alpha));
}

/**
 * 检查是否应该跳帧
 * @param {number} currentAnimationCount - 当前动画数量
 * @returns {boolean} 是否应该跳帧
 */
export function shouldSkipFrame(currentAnimationCount) {
  const config = FALLING_ANIMATION_CONFIG;
  return config.SKIP_FRAMES_WHEN_BUSY && 
         currentAnimationCount > config.MAX_CONCURRENT_ANIMATIONS;
}

/**
 * 获取动画配置的调试信息
 * @returns {Object} 调试信息
 */
export function getAnimationDebugInfo() {
  return {
    fallingAnimation: {
      speedImprovement: `${15 / FALLING_ANIMATION_CONFIG.FRAMES_PER_ROW}x faster`,
      framesPerRow: FALLING_ANIMATION_CONFIG.FRAMES_PER_ROW,
      ghostEnabled: FALLING_ANIMATION_CONFIG.GHOST_ENABLED,
      ghostAlpha: FALLING_ANIMATION_CONFIG.GHOST_ALPHA
    },
    performance: {
      maxConcurrentAnimations: FALLING_ANIMATION_CONFIG.MAX_CONCURRENT_ANIMATIONS,
      skipFramesWhenBusy: FALLING_ANIMATION_CONFIG.SKIP_FRAMES_WHEN_BUSY
    }
  };
}

// 预设配置
export const ANIMATION_PRESETS = {
  // 高性能模式：更快的动画，较少的特效
  PERFORMANCE: {
    FRAMES_PER_ROW: 2,
    GHOST_ENABLED: false,
    MOTION_BLUR: false,
    MAX_CONCURRENT_ANIMATIONS: 30,
    SKIP_FRAMES_WHEN_BUSY: true
  },
  
  // 视觉效果模式：更丰富的动画效果
  VISUAL: {
    FRAMES_PER_ROW: 4,
    GHOST_ENABLED: true,
    GHOST_ALPHA: 0.5,
    MOTION_BLUR: true,
    MAX_CONCURRENT_ANIMATIONS: 100,
    SKIP_FRAMES_WHEN_BUSY: false
  },
  
  // 平衡模式：默认设置
  BALANCED: {
    FRAMES_PER_ROW: 3,
    GHOST_ENABLED: true,
    GHOST_ALPHA: 0.4,
    MOTION_BLUR: false,
    MAX_CONCURRENT_ANIMATIONS: 50,
    SKIP_FRAMES_WHEN_BUSY: true
  }
};

/**
 * 应用动画预设
 * @param {string} presetName - 预设名称
 */
export function applyAnimationPreset(presetName) {
  const preset = ANIMATION_PRESETS[presetName];
  if (!preset) {
    console.warn(`未找到动画预设: ${presetName}`);
    return;
  }
  
  // 应用预设到配置
  Object.assign(FALLING_ANIMATION_CONFIG, preset);
  console.log(`已应用动画预设: ${presetName}`, preset);
}

// 导出默认配置
export default {
  FALLING_ANIMATION_CONFIG,
  DESTROY_ANIMATION_CONFIG,
  EXPLOSION_ANIMATION_CONFIG,
  EFFECT_ANIMATION_CONFIG,
  EASING_FUNCTIONS,
  calculateFallDuration,
  applyEasing,
  calculateGhostAlpha,
  shouldSkipFrame,
  getAnimationDebugInfo,
  ANIMATION_PRESETS,
  applyAnimationPreset
};
