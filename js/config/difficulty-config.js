/**
 * 难度系统配置
 * 定义新的基于垃圾生成的难度机制
 */

// 难度等级定义
export const DIFFICULTY_LEVELS = {
  EASY: 'easy',
  NORMAL: 'normal', 
  HARD: 'hard',
  EXPERT: 'expert',
  MASTER: 'master'
};

// 基础难度配置
export const BASE_DIFFICULTY_CONFIG = {
  // 下落速度配置（大幅降低速度提升）
  fallSpeed: {
    baseSpeed: 30,           // 基础速度（帧数）
    minSpeed: 10,            // 最快速度（帧数）
    levelDecrease: 2,        // 每级减少的帧数
    levelInterval: 10        // 每多少级减少一次
  },
  
  // 垃圾生成配置
  garbageGeneration: {
    enabled: true,           // 是否启用垃圾生成
    baseInterval: 1800,      // 基础生成间隔（30秒）
    minInterval: 300,        // 最小生成间隔（5秒）
    intervalDecrease: 60,    // 每级减少的间隔（1秒）
    
    // 生成数量配置
    baseRowCount: 1,         // 基础生成行数
    maxRowCount: 3,          // 最大生成行数
    rowIncreaseLevel: 10,    // 每多少级增加一行
    
    // 方块密度配置
    baseDensity: 0.6,        // 基础密度（60%）
    maxDensity: 0.9,         // 最大密度（90%）
    densityIncrease: 0.02,   // 每级增加的密度（2%）
    
    // 特效概率配置
    baseEffectProbability: 0.05,  // 基础特效概率（5%）
    maxEffectProbability: 0.25,   // 最大特效概率（25%）
    effectIncrease: 0.01,          // 每级增加的特效概率（1%）
    
    // 预警时间
    warningTime: 300,        // 预警时间（5秒）
    
    // 智能生成配置
    smartGeneration: true,   // 启用智能生成
    avoidanceStrength: 0.3,  // 避免相邻相同颜色的概率
    maxGenerationRetries: 3  // 最大重试次数
  }
};

// 不同难度等级的配置
export const DIFFICULTY_CONFIGS = {
  [DIFFICULTY_LEVELS.EASY]: {
    name: '简单',
    description: '适合新手，垃圾生成较慢',
    
    fallSpeed: {
      ...BASE_DIFFICULTY_CONFIG.fallSpeed,
      levelDecrease: 1,      // 更慢的速度提升
      levelInterval: 15      // 每15级才减少
    },
    
    garbageGeneration: {
      ...BASE_DIFFICULTY_CONFIG.garbageGeneration,
      baseInterval: 2400,    // 40秒基础间隔
      minInterval: 600,      // 10秒最小间隔
      intervalDecrease: 30,  // 每级减少0.5秒
      baseDensity: 0.5,      // 50%基础密度
      densityIncrease: 0.015, // 每级增加1.5%
      baseEffectProbability: 0.03, // 3%基础特效概率
      effectIncrease: 0.005   // 每级增加0.5%
    }
  },
  
  [DIFFICULTY_LEVELS.NORMAL]: {
    name: '普通',
    description: '标准难度，平衡的挑战',
    
    fallSpeed: BASE_DIFFICULTY_CONFIG.fallSpeed,
    garbageGeneration: BASE_DIFFICULTY_CONFIG.garbageGeneration
  },
  
  [DIFFICULTY_LEVELS.HARD]: {
    name: '困难',
    description: '更快的垃圾生成，更高密度',
    
    fallSpeed: {
      ...BASE_DIFFICULTY_CONFIG.fallSpeed,
      levelDecrease: 3,      // 更快的速度提升
      levelInterval: 8       // 每8级减少
    },
    
    garbageGeneration: {
      ...BASE_DIFFICULTY_CONFIG.garbageGeneration,
      baseInterval: 1200,    // 20秒基础间隔
      minInterval: 180,      // 3秒最小间隔
      intervalDecrease: 90,  // 每级减少1.5秒
      baseDensity: 0.7,      // 70%基础密度
      densityIncrease: 0.025, // 每级增加2.5%
      baseEffectProbability: 0.08, // 8%基础特效概率
      effectIncrease: 0.015   // 每级增加1.5%
    }
  },
  
  [DIFFICULTY_LEVELS.EXPERT]: {
    name: '专家',
    description: '高频垃圾生成，高密度方块',
    
    fallSpeed: {
      ...BASE_DIFFICULTY_CONFIG.fallSpeed,
      levelDecrease: 4,      // 更快的速度提升
      levelInterval: 6       // 每6级减少
    },
    
    garbageGeneration: {
      ...BASE_DIFFICULTY_CONFIG.garbageGeneration,
      baseInterval: 900,     // 15秒基础间隔
      minInterval: 120,      // 2秒最小间隔
      intervalDecrease: 120, // 每级减少2秒
      baseDensity: 0.8,      // 80%基础密度
      densityIncrease: 0.03, // 每级增加3%
      baseEffectProbability: 0.12, // 12%基础特效概率
      effectIncrease: 0.02,   // 每级增加2%
      baseRowCount: 2,       // 基础生成2行
      rowIncreaseLevel: 8,   // 每8级增加一行
      
      // 专家难度的智能生成配置
      smartGeneration: true,
      avoidanceStrength: 0.2, // 降低避免强度，增加随机性
      maxGenerationRetries: 2
    }
  },
  
  [DIFFICULTY_LEVELS.MASTER]: {
    name: '大师',
    description: '极限挑战，最高难度',
    
    fallSpeed: {
      ...BASE_DIFFICULTY_CONFIG.fallSpeed,
      levelDecrease: 5,      // 最快的速度提升
      levelInterval: 5       // 每5级减少
    },
    
    garbageGeneration: {
      ...BASE_DIFFICULTY_CONFIG.garbageGeneration,
      baseInterval: 600,     // 10秒基础间隔
      minInterval: 60,       // 1秒最小间隔
      intervalDecrease: 150, // 每级减少2.5秒
      baseDensity: 0.85,     // 85%基础密度
      maxDensity: 0.95,      // 95%最大密度
      densityIncrease: 0.035, // 每级增加3.5%
      baseEffectProbability: 0.15, // 15%基础特效概率
      maxEffectProbability: 0.35,  // 35%最大特效概率
      effectIncrease: 0.025,  // 每级增加2.5%
      baseRowCount: 2,       // 基础生成2行
      maxRowCount: 4,        // 最大生成4行
      rowIncreaseLevel: 6,   // 每6级增加一行
      warningTime: 180,      // 3秒预警时间
      
      // 大师难度的智能生成配置
      smartGeneration: true,
      avoidanceStrength: 0.1, // 进一步降低避免强度，更随机
      maxGenerationRetries: 1
    }
  }
};

// 关卡难度映射（根据关卡ID自动选择难度）
export const LEVEL_DIFFICULTY_MAPPING = {
  // 1-20关：简单
  getDifficultyForLevel: (levelId) => {
    if (levelId <= 20) {
      return DIFFICULTY_LEVELS.EASY;
    } else if (levelId <= 50) {
      return DIFFICULTY_LEVELS.NORMAL;
    } else if (levelId <= 80) {
      return DIFFICULTY_LEVELS.HARD;
    } else if (levelId <= 100) {
      return DIFFICULTY_LEVELS.EXPERT;
    } else {
      return DIFFICULTY_LEVELS.MASTER;
    }
  }
};

/**
 * 获取指定关卡的难度配置
 * @param {number} levelId - 关卡ID
 * @param {string} forceDifficulty - 强制指定难度（可选）
 * @returns {Object} 难度配置
 */
export function getDifficultyConfig(levelId, forceDifficulty = null) {
  const difficulty = forceDifficulty || LEVEL_DIFFICULTY_MAPPING.getDifficultyForLevel(levelId);
  const config = DIFFICULTY_CONFIGS[difficulty];
  
  if (!config) {
    console.warn(`未找到难度配置: ${difficulty}，使用普通难度`);
    return DIFFICULTY_CONFIGS[DIFFICULTY_LEVELS.NORMAL];
  }
  
  return {
    ...config,
    level: levelId,
    difficultyLevel: difficulty
  };
}

/**
 * 获取垃圾生成器配置
 * @param {number} levelId - 关卡ID
 * @param {string} difficulty - 难度等级（可选）
 * @returns {Object} 垃圾生成器配置
 */
export function getGarbageGeneratorConfig(levelId, difficulty = null) {
  const difficultyConfig = getDifficultyConfig(levelId, difficulty);
  
  return {
    enabled: difficultyConfig.garbageGeneration.enabled,
    level: levelId,
    baseInterval: difficultyConfig.garbageGeneration.baseInterval,
    minInterval: difficultyConfig.garbageGeneration.minInterval,
    intervalDecrease: difficultyConfig.garbageGeneration.intervalDecrease,
    baseRowCount: difficultyConfig.garbageGeneration.baseRowCount,
    maxRowCount: difficultyConfig.garbageGeneration.maxRowCount,
    rowIncreaseLevel: difficultyConfig.garbageGeneration.rowIncreaseLevel,
    baseDensity: difficultyConfig.garbageGeneration.baseDensity,
    maxDensity: difficultyConfig.garbageGeneration.maxDensity,
    densityIncrease: difficultyConfig.garbageGeneration.densityIncrease,
    baseEffectProbability: difficultyConfig.garbageGeneration.baseEffectProbability,
    maxEffectProbability: difficultyConfig.garbageGeneration.maxEffectProbability,
    effectIncrease: difficultyConfig.garbageGeneration.effectIncrease,
    warningTime: difficultyConfig.garbageGeneration.warningTime,
    
    // 智能生成配置
    smartGeneration: difficultyConfig.garbageGeneration.smartGeneration,
    avoidanceStrength: difficultyConfig.garbageGeneration.avoidanceStrength,
    maxGenerationRetries: difficultyConfig.garbageGeneration.maxGenerationRetries
  };
}

/**
 * 获取下落速度配置
 * @param {number} levelId - 关卡ID
 * @param {string} difficulty - 难度等级（可选）
 * @returns {Object} 下落速度配置
 */
export function getFallSpeedConfig(levelId, difficulty = null) {
  const difficultyConfig = getDifficultyConfig(levelId, difficulty);
  
  return {
    baseSpeed: difficultyConfig.fallSpeed.baseSpeed,
    minSpeed: difficultyConfig.fallSpeed.minSpeed,
    levelDecrease: difficultyConfig.fallSpeed.levelDecrease,
    levelInterval: difficultyConfig.fallSpeed.levelInterval
  };
}

// 导出所有配置
export default {
  DIFFICULTY_LEVELS,
  BASE_DIFFICULTY_CONFIG,
  DIFFICULTY_CONFIGS,
  LEVEL_DIFFICULTY_MAPPING,
  getDifficultyConfig,
  getGarbageGeneratorConfig,
  getFallSpeedConfig
};
