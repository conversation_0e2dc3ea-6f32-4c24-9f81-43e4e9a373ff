/**
 * 游戏全局配置文件
 * 包含调试模式、游戏平衡参数等配置项
 */

// 游戏配置对象
export const GAME_CONFIG = {
  // 调试模式配置
  debug: {
    // 是否开启调试模式
    enabled: true,

    // 调试模式下的分数调整倍数
    scoreMultiplier: 0.1, // 分数调整为原来的1/10
    
    // 调试模式下的其他配置
    fastLevelComplete: true,    // 快速关卡完成
    showDebugInfo: true,        // 显示调试信息
    unlockAllLevels: true,      // 解锁所有关卡
    infiniteItems: true,        // 无限道具
    noCooldown: true,           // 无冷却时间
    skipTutorial: false,        // 跳过教程
    
    // 调试日志级别
    logLevel: 'info' // 'debug', 'info', 'warn', 'error'
  },
  
  // 游戏平衡配置
  balance: {
    // 基础分数倍数
    baseScoreMultiplier: 1.0,
    
    // 难度曲线调整
    difficultyProgression: 1.0,
    
    // 道具平衡
    itemBalance: {
      cooldownMultiplier: 1.0,
      effectMultiplier: 1.0
    },
    
    // 特效平衡
    effectBalance: {
      probabilityMultiplier: 1.0,
      impactMultiplier: 1.0
    }
  },
  
  // 性能配置
  performance: {
    // 是否启用性能监控
    enableProfiling: false,
    
    // 帧率限制
    targetFPS: 60,
    
    // 渲染优化
    enableOptimizations: true
  },
  
  // 用户体验配置
  ux: {
    // 动画速度倍数
    animationSpeedMultiplier: 1.0,
    
    // 音效音量
    soundVolume: 1.0,
    
    // 音乐音量
    musicVolume: 0.7,
    
    // 触觉反馈
    hapticFeedback: true
  }
};

/**
 * 获取调试模式状态
 * @returns {boolean} 是否开启调试模式
 */
export function isDebugMode() {
  return GAME_CONFIG.debug.enabled;
}

/**
 * 设置调试模式
 * @param {boolean} enabled - 是否开启调试模式
 */
export function setDebugMode(enabled) {
  GAME_CONFIG.debug.enabled = enabled;
  console.log(`调试模式${enabled ? '已开启' : '已关闭'}`);
  
  if (enabled) {
    console.log(`分数调整倍数: ${GAME_CONFIG.debug.scoreMultiplier}`);
    console.log('所有关卡的目标分数将调整为原来的1/10');
  }
}

/**
 * 获取调试模式下的分数倍数
 * @returns {number} 分数倍数
 */
export function getDebugScoreMultiplier() {
  return isDebugMode() ? GAME_CONFIG.debug.scoreMultiplier : 1.0;
}

/**
 * 应用调试模式到分数
 * @param {number} originalScore - 原始分数
 * @returns {number} 调整后的分数
 */
export function applyDebugScore(originalScore) {
  const multiplier = getDebugScoreMultiplier();
  const adjustedScore = Math.floor(originalScore * multiplier);

  return adjustedScore;
}

/**
 * 切换调试模式
 */
export function toggleDebugMode() {
  setDebugMode(!isDebugMode());
}

/**
 * 获取配置值
 * @param {string} path - 配置路径，如 'debug.enabled' 或 'balance.baseScoreMultiplier'
 * @returns {any} 配置值
 */
export function getConfig(path) {
  const keys = path.split('.');
  let value = GAME_CONFIG;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return undefined;
    }
  }
  
  return value;
}

/**
 * 设置配置值
 * @param {string} path - 配置路径
 * @param {any} value - 配置值
 */
export function setConfig(path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  let target = GAME_CONFIG;
  
  for (const key of keys) {
    if (!target[key] || typeof target[key] !== 'object') {
      target[key] = {};
    }
    target = target[key];
  }
  
  target[lastKey] = value;
}

// 导出默认配置
export default GAME_CONFIG;
