/**
 * 新元素介绍管理器
 * 负责在特定关卡介绍新的游戏元素（道具、特效等）
 */

import Emitter from '../libs/tinyemitter.js';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render.js';

export default class ElementIntroduction extends Emitter {
  constructor() {
    super();
    
    // 新元素介绍配置
    this.introductions = {
      // 道具介绍
      2: {
        type: 'item',
        element: 'fireball',
        title: '🔥 火球术',
        description: [
          '恭喜解锁新道具：火球术！',
          '',
          '• 点击火球术按钮激活',
          '• 消除指定位置周围的方块',
          '• 范围随等级提升而扩大',
          '• 适合清理密集的方块区域'
        ],
        icon: '🔥',
        color: '#ff4444'
      },
      5: {
        type: 'item',
        element: 'lightning',
        title: '⚡ 闪电链',
        description: [
          '恭喜解锁新道具：闪电链！',
          '',
          '• 点击闪电链按钮激活',
          '• 消除整行相同颜色的方块',
          '• 可以连锁消除多行',
          '• 适合处理水平排列的方块'
        ],
        icon: '⚡',
        color: '#ffff00'
      },
      8: {
        type: 'item',
        element: 'torrent',
        title: '🌊 激流',
        description: [
          '恭喜解锁新道具：激流！',
          '',
          '• 点击激流按钮激活',
          '• 消除整列的方块',
          '• 可以同时影响多列',
          '• 适合清理垂直堆积的方块'
        ],
        icon: '🌊',
        color: '#4444ff'
      },
      19: {
        type: 'effect',
        element: 'mine',
        title: '💣 地雷方块',
        description: [
          '新的挑战：地雷方块！',
          '',
          '• 消除时会爆炸影响周围方块',
          '• 可以连锁引爆其他地雷',
          '• 合理利用可以快速清理大片区域',
          '• 小心不要意外触发！'
        ],
        icon: '💣',
        color: '#ff8800'
      },
      28: {
        type: 'effect',
        element: 'shield',
        title: '🛡️ 护盾方块',
        description: [
          '新元素：护盾方块！',
          '',
          '• 需要多次消除才能破坏',
          '• 每次消除会减少护盾层数',
          '• 完全破坏后可获得额外分数',
          '• 策略性地规划消除顺序'
        ],
        icon: '🛡️',
        color: '#00ff88'
      }
    };
    
    // 当前显示的介绍
    this.currentIntro = null;
    this.isShowing = false;
    this.animationFrame = 0;
    this.maxAnimationFrames = 60;
  }
  
  /**
   * 检查是否需要显示新元素介绍
   * @param {number} levelId - 关卡ID
   * @returns {boolean} 是否显示了介绍
   */
  checkAndShowIntroduction(levelId) {
    if (this.introductions[levelId]) {
      this.showIntroduction(levelId);
      return true;
    }
    return false;
  }
  
  /**
   * 显示新元素介绍
   * @param {number} levelId - 关卡ID
   */
  showIntroduction(levelId) {
    const intro = this.introductions[levelId];
    if (!intro) return;
    
    this.currentIntro = intro;
    this.isShowing = true;
    this.animationFrame = 0;
    
    console.log(`显示第${levelId}关新元素介绍: ${intro.title}`);
    
    // 触发介绍开始事件
    this.emit('introduction:start', { levelId, intro });
  }
  
  /**
   * 隐藏介绍
   */
  hideIntroduction() {
    if (!this.isShowing) return;
    
    this.isShowing = false;
    this.currentIntro = null;
    this.animationFrame = 0;
    
    // 触发介绍结束事件
    this.emit('introduction:end');
  }
  
  /**
   * 更新动画
   */
  update() {
    if (this.isShowing && this.animationFrame < this.maxAnimationFrames) {
      this.animationFrame++;
    }
  }
  
  /**
   * 渲染介绍界面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isShowing || !this.currentIntro) return;
    
    // 计算动画进度
    const progress = Math.min(this.animationFrame / this.maxAnimationFrames, 1);
    const easeProgress = this._easeInOut(progress);
    
    // 绘制半透明背景
    ctx.fillStyle = `rgba(0, 0, 0, ${0.8 * easeProgress})`;
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 计算面板位置和大小
    const panelWidth = Math.min(SCREEN_WIDTH - 40, 400);
    const panelHeight = Math.min(SCREEN_HEIGHT - 100, 350);
    const panelX = (SCREEN_WIDTH - panelWidth) / 2;
    const panelY = (SCREEN_HEIGHT - panelHeight) / 2;
    
    // 应用缩放动画
    const scale = 0.5 + 0.5 * easeProgress;
    ctx.save();
    ctx.translate(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2);
    ctx.scale(scale, scale);
    ctx.translate(-SCREEN_WIDTH / 2, -SCREEN_HEIGHT / 2);
    
    // 绘制面板背景
    this._drawRoundedRect(ctx, panelX, panelY, panelWidth, panelHeight, 20);
    ctx.fillStyle = '#2a2a2a';
    ctx.fill();
    ctx.strokeStyle = this.currentIntro.color;
    ctx.lineWidth = 3;
    ctx.stroke();
    
    // 绘制标题
    ctx.fillStyle = this.currentIntro.color;
    ctx.font = 'bold 28px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(this.currentIntro.title, SCREEN_WIDTH / 2, panelY + 60);
    
    // 绘制图标
    ctx.font = '48px Arial';
    ctx.fillText(this.currentIntro.icon, SCREEN_WIDTH / 2, panelY + 120);
    
    // 绘制描述文本
    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';
    
    const textStartY = panelY + 150;
    const lineHeight = 24;
    const textX = panelX + 30;
    
    this.currentIntro.description.forEach((line, index) => {
      const y = textStartY + index * lineHeight;
      if (line === '') return; // 空行
      
      // 高亮特殊符号
      if (line.startsWith('•')) {
        ctx.fillStyle = this.currentIntro.color;
        ctx.fillText('•', textX, y);
        ctx.fillStyle = '#ffffff';
        ctx.fillText(line.substring(1), textX + 15, y);
      } else {
        ctx.fillText(line, textX, y);
      }
    });
    
    // 绘制继续提示
    const continueY = panelY + panelHeight - 40;
    ctx.fillStyle = '#cccccc';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    
    // 闪烁效果
    const blinkAlpha = 0.5 + 0.5 * Math.sin(Date.now() / 500);
    ctx.fillStyle = `rgba(204, 204, 204, ${blinkAlpha})`;
    ctx.fillText('点击任意位置继续', SCREEN_WIDTH / 2, continueY);
    
    ctx.restore();
  }
  
  /**
   * 处理触摸事件
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   * @returns {boolean} 是否处理了事件
   */
  handleTouch(x, y) {
    if (this.isShowing) {
      this.hideIntroduction();
      return true;
    }
    return false;
  }
  
  /**
   * 绘制圆角矩形路径
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   * @private
   */
  _drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.arcTo(x + width, y, x + width, y + radius, radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius);
    ctx.lineTo(x + radius, y + height);
    ctx.arcTo(x, y + height, x, y + height - radius, radius);
    ctx.lineTo(x, y + radius);
    ctx.arcTo(x, y, x + radius, y, radius);
    ctx.closePath();
  }
  
  /**
   * 缓动函数
   * @param {number} t - 进度 (0-1)
   * @returns {number} 缓动后的进度
   * @private
   */
  _easeInOut(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }
  
  /**
   * 检查是否正在显示介绍
   * @returns {boolean} 是否正在显示
   */
  isShowingIntroduction() {
    return this.isShowing;
  }
  
  /**
   * 获取当前介绍信息
   * @returns {Object|null} 当前介绍信息
   */
  getCurrentIntroduction() {
    return this.currentIntro;
  }
}
