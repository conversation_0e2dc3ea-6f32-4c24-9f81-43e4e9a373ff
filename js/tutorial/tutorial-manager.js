/**
 * 教学系统管理器
 * 负责新手引导、提示系统和辅助功能
 */
import Emitter from '../libs/tinyemitter.js';

// 教学步骤类型
export const TUTORIAL_STEPS = {
  WELCOME: 'welcome',
  BASIC_CONTROL: 'basic_control',
  ROTATION: 'rotation',
  SOFT_DROP: 'soft_drop',
  MATCH_THREE: 'match_three',
  FROZEN_BLOCK: 'frozen_block',
  MINE_BLOCK: 'mine_block',
  ITEM_USAGE: 'item_usage',
  COMBO_SYSTEM: 'combo_system',
  COMPLETE: 'complete'
};

// 提示类型
export const HINT_TYPES = {
  MOVEMENT: 'movement',
  ROTATION: 'rotation',
  MATCH_OPPORTUNITY: 'match_opportunity',
  ITEM_SUGGESTION: 'item_suggestion',
  DANGER_WARNING: 'danger_warning'
};

export default class TutorialManager extends Emitter {
  constructor() {
    super();
    
    // 当前教学状态
    this.isActive = false;
    this.currentStep = null;
    this.stepProgress = 0;
    this.completedSteps = new Set();
    
    // 提示系统
    this.hintsEnabled = true;
    this.autoHintTimer = 0;
    this.lastHintTime = 0;
    this.hintCooldown = 300; // 5秒冷却
    
    // 辅助功能状态
    this.assistanceLevel = 'high'; // high, medium, low, minimal, none
    this.pauseOnDanger = true;
    this.showGhostPiece = true;
    this.highlightMatches = true;
    
    // 教学数据
    this.tutorialData = this._initTutorialData();
    
    // 加载已完成的教学进度
    this._loadProgress();
  }
  
  /**
   * 初始化教学数据
   * @private
   */
  _initTutorialData() {
    return {
      [TUTORIAL_STEPS.WELCOME]: {
        title: '欢迎来到俄罗斯方块三消！',
        description: '这是一个结合了俄罗斯方块和三消玩法的游戏',
        instructions: [
          '控制下落的方块组合',
          '让相同颜色的方块连成3个或以上',
          '消除方块获得分数'
        ],
        duration: 5000,
        skippable: true
      },
      [TUTORIAL_STEPS.BASIC_CONTROL]: {
        title: '基础控制',
        description: '学习如何移动方块',
        instructions: [
          '点击左右箭头移动方块',
          '点击下箭头加速下落',
          '尝试移动当前的方块'
        ],
        targetActions: ['move_left', 'move_right', 'soft_drop'],
        requiredActions: 2
      },
      [TUTORIAL_STEPS.ROTATION]: {
        title: '旋转方块',
        description: '学习旋转方块来适应空间',
        instructions: [
          '点击旋转按钮旋转方块',
          '找到合适的位置放置方块'
        ],
        targetActions: ['rotate'],
        requiredActions: 1
      },
      [TUTORIAL_STEPS.MATCH_THREE]: {
        title: '三消消除',
        description: '创造你的第一次三消',
        instructions: [
          '让3个或以上相同颜色的方块连在一起',
          '可以是水平或垂直连接',
          '消除方块获得分数'
        ],
        targetActions: ['match_three'],
        requiredActions: 1
      },
      [TUTORIAL_STEPS.FROZEN_BLOCK]: {
        title: '冰冻方块',
        description: '了解特殊方块的机制',
        instructions: [
          '蓝色边框的是冰冻方块',
          '需要两次匹配才能消除',
          '第一次匹配解除冰冻状态'
        ],
        targetActions: ['unfreeze_block'],
        requiredActions: 1
      }
    };
  }
  
  /**
   * 开始教学
   * @param {string} stepId - 教学步骤ID
   */
  startTutorial(stepId = TUTORIAL_STEPS.WELCOME) {
    this.isActive = true;
    this.currentStep = stepId;
    this.stepProgress = 0;
    
    const stepData = this.tutorialData[stepId];
    if (stepData) {
      this.emit('tutorial:start', {
        step: stepId,
        data: stepData
      });
    }
  }
  
  /**
   * 完成当前教学步骤
   */
  completeCurrentStep() {
    if (!this.currentStep) return;
    
    this.completedSteps.add(this.currentStep);
    this.emit('tutorial:step_complete', { step: this.currentStep });
    
    // 进入下一步
    const nextStep = this._getNextStep(this.currentStep);
    if (nextStep) {
      this.startTutorial(nextStep);
    } else {
      this._completeTutorial();
    }
    
    this._saveProgress();
  }
  
  /**
   * 跳过当前教学步骤
   */
  skipCurrentStep() {
    const stepData = this.tutorialData[this.currentStep];
    if (stepData && stepData.skippable) {
      this.completeCurrentStep();
    }
  }
  
  /**
   * 记录玩家行动
   * @param {string} action - 行动类型
   */
  recordAction(action) {
    if (!this.isActive || !this.currentStep) return;
    
    const stepData = this.tutorialData[this.currentStep];
    if (stepData && stepData.targetActions) {
      if (stepData.targetActions.includes(action)) {
        this.stepProgress++;
        
        this.emit('tutorial:progress', {
          step: this.currentStep,
          progress: this.stepProgress,
          required: stepData.requiredActions
        });
        
        // 检查是否完成当前步骤
        if (this.stepProgress >= stepData.requiredActions) {
          this.completeCurrentStep();
        }
      }
    }
  }
  
  /**
   * 显示提示
   * @param {string} hintType - 提示类型
   * @param {Object} context - 上下文信息
   */
  showHint(hintType, context = {}) {
    if (!this.hintsEnabled) return;
    
    const now = Date.now();
    if (now - this.lastHintTime < this.hintCooldown) return;
    
    this.lastHintTime = now;
    
    const hint = this._generateHint(hintType, context);
    if (hint) {
      this.emit('tutorial:hint', hint);
    }
  }
  
  /**
   * 更新辅助等级
   * @param {string} level - 辅助等级
   */
  setAssistanceLevel(level) {
    this.assistanceLevel = level;
    
    // 根据辅助等级调整功能
    switch (level) {
      case 'high':
        this.hintsEnabled = true;
        this.pauseOnDanger = true;
        this.showGhostPiece = true;
        this.highlightMatches = true;
        this.hintCooldown = 180; // 3秒
        break;
      case 'medium':
        this.hintsEnabled = true;
        this.pauseOnDanger = false;
        this.showGhostPiece = true;
        this.highlightMatches = true;
        this.hintCooldown = 300; // 5秒
        break;
      case 'low':
        this.hintsEnabled = true;
        this.pauseOnDanger = false;
        this.showGhostPiece = false;
        this.highlightMatches = false;
        this.hintCooldown = 600; // 10秒
        break;
      case 'minimal':
        this.hintsEnabled = false;
        this.pauseOnDanger = false;
        this.showGhostPiece = false;
        this.highlightMatches = false;
        break;
      case 'none':
        this.hintsEnabled = false;
        this.pauseOnDanger = false;
        this.showGhostPiece = false;
        this.highlightMatches = false;
        break;
    }
    
    this.emit('tutorial:assistance_change', {
      level,
      features: {
        hints: this.hintsEnabled,
        pauseOnDanger: this.pauseOnDanger,
        ghostPiece: this.showGhostPiece,
        highlightMatches: this.highlightMatches
      }
    });
  }
  
  /**
   * 更新教学系统
   */
  update() {
    if (!this.isActive) return;
    
    // 自动提示计时器
    if (this.hintsEnabled) {
      this.autoHintTimer++;
      
      // 每10秒检查是否需要提示
      if (this.autoHintTimer >= 600) {
        this.autoHintTimer = 0;
        this._checkAutoHint();
      }
    }
  }
  
  /**
   * 获取下一个教学步骤
   * @param {string} currentStep - 当前步骤
   * @returns {string|null} 下一步骤ID
   * @private
   */
  _getNextStep(currentStep) {
    const steps = Object.keys(TUTORIAL_STEPS);
    const currentIndex = steps.indexOf(currentStep);
    
    if (currentIndex >= 0 && currentIndex < steps.length - 1) {
      return steps[currentIndex + 1];
    }
    
    return null;
  }
  
  /**
   * 完成整个教学
   * @private
   */
  _completeTutorial() {
    this.isActive = false;
    this.currentStep = null;
    
    this.emit('tutorial:complete');
    this._saveProgress();
  }
  
  /**
   * 生成提示内容
   * @param {string} hintType - 提示类型
   * @param {Object} context - 上下文
   * @returns {Object|null} 提示对象
   * @private
   */
  _generateHint(hintType, context) {
    const hints = {
      [HINT_TYPES.MOVEMENT]: {
        title: '移动提示',
        message: '尝试左右移动方块找到更好的位置',
        icon: 'arrow'
      },
      [HINT_TYPES.ROTATION]: {
        title: '旋转提示',
        message: '旋转方块可能会找到更好的放置位置',
        icon: 'rotate'
      },
      [HINT_TYPES.MATCH_OPPORTUNITY]: {
        title: '消除机会',
        message: '注意相同颜色的方块，尝试让它们连成一线',
        icon: 'match'
      },
      [HINT_TYPES.ITEM_SUGGESTION]: {
        title: '道具建议',
        message: '考虑使用道具来清理困难的区域',
        icon: 'item'
      },
      [HINT_TYPES.DANGER_WARNING]: {
        title: '危险警告',
        message: '方块堆积过高，小心游戏结束！',
        icon: 'warning'
      }
    };
    
    return hints[hintType] || null;
  }
  
  /**
   * 检查是否需要自动提示
   * @private
   */
  _checkAutoHint() {
    // 这里可以根据游戏状态决定显示什么提示
    // 例如：检查是否有消除机会、是否需要使用道具等
  }
  
  /**
   * 保存教学进度
   * @private
   */
  _saveProgress() {
    try {
      const progressData = {
        completedSteps: Array.from(this.completedSteps),
        assistanceLevel: this.assistanceLevel,
        hintsEnabled: this.hintsEnabled
      };

      // 验证数据是否可以序列化
      const jsonString = this._safeStringify(progressData);
      if (jsonString === null) {
        console.error('教学进度数据序列化失败，跳过保存');
        return;
      }

      wx.setStorageSync('tutorialProgress', jsonString);
      console.log('教学进度保存成功');
    } catch (e) {
      console.error('保存教学进度失败:', e);
    }
  }

  /**
   * 安全的JSON序列化
   * @param {Object} obj - 要序列化的对象
   * @returns {string|null} 序列化后的字符串，失败返回null
   * @private
   */
  _safeStringify(obj) {
    try {
      return JSON.stringify(obj, (key, value) => {
        // 过滤掉函数、Symbol、undefined等不可序列化的值
        if (typeof value === 'function' || typeof value === 'symbol' || value === undefined) {
          return null;
        }
        return value;
      });
    } catch (e) {
      console.error('JSON序列化失败:', e);
      return null;
    }
  }
  
  /**
   * 加载教学进度
   * @private
   */
  _loadProgress() {
    try {
      const progressData = wx.getStorageSync('tutorialProgress');
      
      if (progressData) {
        const progress = JSON.parse(progressData);
        
        this.completedSteps = new Set(progress.completedSteps || []);
        this.assistanceLevel = progress.assistanceLevel || 'high';
        this.hintsEnabled = progress.hintsEnabled !== false;
        
        // 应用辅助等级设置
        this.setAssistanceLevel(this.assistanceLevel);
      }
    } catch (e) {
      console.error('加载教学进度失败:', e);
    }
  }
}
