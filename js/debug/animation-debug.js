/**
 * 动画调试工具
 * 用于测试和调试新的下落动画系统
 */

import { 
  getAnimationDebugInfo, 
  applyAnimationPreset,
  FALLING_ANIMATION_CONFIG,
  ANIMATION_PRESETS 
} from '../config/animation-config.js';

export default class AnimationDebug {
  constructor() {
    this.isEnabled = false;
    this.debugPanel = null;
    this.animationStats = {
      totalAnimations: 0,
      fallingAnimations: 0,
      averageDuration: 0,
      maxConcurrent: 0
    };
    
    console.log('动画调试工具初始化');
  }
  
  /**
   * 启用调试模式
   */
  enable() {
    this.isEnabled = true;
    this.createDebugPanel();
    console.log('动画调试模式已启用');
  }
  
  /**
   * 禁用调试模式
   */
  disable() {
    this.isEnabled = false;
    this.removeDebugPanel();
    console.log('动画调试模式已禁用');
  }
  
  /**
   * 创建调试面板
   */
  createDebugPanel() {
    if (this.debugPanel) return;
    
    // 创建调试面板DOM元素（如果在支持DOM的环境中）
    if (typeof document !== 'undefined') {
      this.debugPanel = document.createElement('div');
      this.debugPanel.id = 'animation-debug-panel';
      this.debugPanel.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        width: 300px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10000;
        pointer-events: none;
      `;
      document.body.appendChild(this.debugPanel);
    }
  }
  
  /**
   * 移除调试面板
   */
  removeDebugPanel() {
    if (this.debugPanel && this.debugPanel.parentNode) {
      this.debugPanel.parentNode.removeChild(this.debugPanel);
      this.debugPanel = null;
    }
  }
  
  /**
   * 更新调试信息
   * @param {Array} animations - 当前动画数组
   */
  update(animations = []) {
    if (!this.isEnabled) return;
    
    // 统计动画信息
    this.updateAnimationStats(animations);
    
    // 更新调试面板
    this.updateDebugPanel();
  }
  
  /**
   * 更新动画统计信息
   * @param {Array} animations - 动画数组
   */
  updateAnimationStats(animations) {
    this.animationStats.totalAnimations = animations.length;
    this.animationStats.fallingAnimations = animations.filter(anim => anim.type === 'falling').length;
    this.animationStats.maxConcurrent = Math.max(this.animationStats.maxConcurrent, animations.length);
    
    // 计算平均持续时间
    const fallingAnims = animations.filter(anim => anim.type === 'falling');
    if (fallingAnims.length > 0) {
      const totalDuration = fallingAnims.reduce((sum, anim) => sum + anim.duration, 0);
      this.animationStats.averageDuration = Math.round(totalDuration / fallingAnims.length);
    }
  }
  
  /**
   * 更新调试面板显示
   */
  updateDebugPanel() {
    if (!this.debugPanel) return;
    
    const debugInfo = getAnimationDebugInfo();
    
    this.debugPanel.innerHTML = `
      <h3>🎬 动画调试信息</h3>
      
      <h4>📊 当前统计</h4>
      <div>总动画数: ${this.animationStats.totalAnimations}</div>
      <div>下落动画: ${this.animationStats.fallingAnimations}</div>
      <div>平均时长: ${this.animationStats.averageDuration}帧</div>
      <div>最大并发: ${this.animationStats.maxConcurrent}</div>
      
      <h4>⚙️ 配置信息</h4>
      <div>速度提升: ${debugInfo.fallingAnimation.speedImprovement}</div>
      <div>帧/行: ${debugInfo.fallingAnimation.framesPerRow}</div>
      <div>虚影启用: ${debugInfo.fallingAnimation.ghostEnabled ? '✅' : '❌'}</div>
      <div>虚影透明度: ${Math.round(debugInfo.fallingAnimation.ghostAlpha * 100)}%</div>
      
      <h4>🚀 性能</h4>
      <div>最大并发: ${debugInfo.performance.maxConcurrentAnimations}</div>
      <div>跳帧模式: ${debugInfo.performance.skipFramesWhenBusy ? '✅' : '❌'}</div>
      
      <h4>🎛️ 快捷操作</h4>
      <div>按 1: 高性能模式</div>
      <div>按 2: 平衡模式</div>
      <div>按 3: 视觉效果模式</div>
    `;
  }
  
  /**
   * 测试下落动画
   * @param {Grid} grid - 游戏网格
   * @param {number} count - 测试动画数量
   */
  testFallingAnimations(grid, count = 5) {
    if (!grid) {
      console.warn('需要提供Grid实例来测试动画');
      return;
    }
    
    console.log(`开始测试${count}个下落动画`);
    
    for (let i = 0; i < count; i++) {
      // 创建测试方块
      const testBlock = {
        color: ['red', 'blue', 'green', 'yellow', 'purple'][i % 5],
        render: function(ctx, x, y, size) {
          ctx.fillStyle = this.color;
          ctx.fillRect(x, y, size, size);
          ctx.strokeStyle = 'white';
          ctx.strokeRect(x, y, size, size);
        }
      };
      
      // 随机起始和结束位置
      const startRow = Math.floor(Math.random() * 5);
      const endRow = startRow + Math.floor(Math.random() * 10) + 3;
      const col = Math.floor(Math.random() * grid.cols);
      
      // 添加测试动画
      grid.addFallingAnimation(testBlock, startRow, col, Math.min(endRow, grid.rows - 1), col);
    }
    
    console.log(`已添加${count}个测试下落动画`);
  }
  
  /**
   * 切换动画预设
   * @param {string} presetName - 预设名称
   */
  switchPreset(presetName) {
    if (!ANIMATION_PRESETS[presetName]) {
      console.warn(`未知的动画预设: ${presetName}`);
      return;
    }
    
    applyAnimationPreset(presetName);
    console.log(`已切换到动画预设: ${presetName}`);
    
    // 更新调试面板
    this.updateDebugPanel();
  }
  
  /**
   * 获取当前配置信息
   * @returns {Object} 配置信息
   */
  getCurrentConfig() {
    return {
      ...FALLING_ANIMATION_CONFIG,
      debugInfo: getAnimationDebugInfo(),
      stats: this.animationStats
    };
  }
  
  /**
   * 导出调试数据
   * @returns {string} JSON格式的调试数据
   */
  exportDebugData() {
    const data = {
      timestamp: new Date().toISOString(),
      config: this.getCurrentConfig(),
      stats: this.animationStats,
      presets: ANIMATION_PRESETS
    };
    
    const jsonData = JSON.stringify(data, null, 2);
    console.log('调试数据导出:', jsonData);
    return jsonData;
  }
  
  /**
   * 重置统计信息
   */
  resetStats() {
    this.animationStats = {
      totalAnimations: 0,
      fallingAnimations: 0,
      averageDuration: 0,
      maxConcurrent: 0
    };
    console.log('动画统计信息已重置');
  }
  
  /**
   * 监听键盘事件（用于快捷操作）
   */
  setupKeyboardShortcuts() {
    if (typeof document === 'undefined') return;
    
    document.addEventListener('keydown', (event) => {
      if (!this.isEnabled) return;
      
      // 只在按住Ctrl+Shift时响应
      if (!event.ctrlKey || !event.shiftKey) return;
      
      switch (event.key) {
        case '1':
          this.switchPreset('PERFORMANCE');
          event.preventDefault();
          break;
        case '2':
          this.switchPreset('BALANCED');
          event.preventDefault();
          break;
        case '3':
          this.switchPreset('VISUAL');
          event.preventDefault();
          break;
        case 'r':
        case 'R':
          this.resetStats();
          event.preventDefault();
          break;
        case 'e':
        case 'E':
          this.exportDebugData();
          event.preventDefault();
          break;
      }
    });
    
    console.log('动画调试快捷键已设置:');
    console.log('Ctrl+Shift+1: 高性能模式');
    console.log('Ctrl+Shift+2: 平衡模式');
    console.log('Ctrl+Shift+3: 视觉效果模式');
    console.log('Ctrl+Shift+R: 重置统计');
    console.log('Ctrl+Shift+E: 导出数据');
  }
}

// 创建全局调试实例
const animationDebug = new AnimationDebug();

// 导出调试工具
export { animationDebug };

// 在开发环境中自动启用调试模式
if (typeof window !== 'undefined' && window.location && window.location.hostname === 'localhost') {
  animationDebug.enable();
  animationDebug.setupKeyboardShortcuts();
  console.log('🎬 动画调试模式已在开发环境中自动启用');
}
