import RenderAdapter from './render-adapter.js';

/**
 * Web平台渲染适配器
 * 实现Web浏览器平台的渲染接口
 */
export default class WebRenderAdapter extends RenderAdapter {
  constructor() {
    super();
    
    // 检查Web环境
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      throw new Error('WebRenderAdapter requires Web browser environment');
    }
  }

  /**
   * 创建画布
   * @returns {HTMLCanvasElement} HTML Canvas元素
   */
  createCanvas() {
    return document.createElement('canvas');
  }

  /**
   * 获取屏幕尺寸
   * @returns {Object} { width: number, height: number }
   */
  getScreenSize() {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  }

  /**
   * 获取画布2D上下文
   * @param {HTMLCanvasElement} canvas - HTML Canvas元素
   * @returns {CanvasRenderingContext2D} 2D渲染上下文
   */
  getContext2D(canvas) {
    return canvas.getContext('2d');
  }

  /**
   * 加载图片
   * @param {string} path - 图片路径
   * @returns {Promise<HTMLImageElement>} 图片元素
   */
  loadImage(path) {
    return new Promise((resolve, reject) => {
      const image = new Image();
      image.onload = () => resolve(image);
      image.onerror = (error) => reject(error);
      image.src = path;
    });
  }

  /**
   * 设置画布尺寸
   * @param {HTMLCanvasElement} canvas - HTML Canvas元素
   * @param {number} width - 宽度
   * @param {number} height - 高度
   */
  setCanvasSize(canvas, width, height) {
    canvas.width = width;
    canvas.height = height;
    
    // 设置CSS样式以适应高DPI显示
    const devicePixelRatio = this.getDevicePixelRatio();
    canvas.style.width = (width / devicePixelRatio) + 'px';
    canvas.style.height = (height / devicePixelRatio) + 'px';
  }

  /**
   * 获取设备像素比
   * @returns {number} 设备像素比
   */
  getDevicePixelRatio() {
    return window.devicePixelRatio || 1;
  }

  /**
   * 请求动画帧
   * @param {Function} callback - 回调函数
   * @returns {number} 动画帧ID
   */
  requestAnimationFrame(callback) {
    return window.requestAnimationFrame(callback);
  }

  /**
   * 取消动画帧
   * @param {number} id - 动画帧ID
   */
  cancelAnimationFrame(id) {
    window.cancelAnimationFrame(id);
  }

  /**
   * 获取用户代理信息
   * @returns {string} 用户代理字符串
   */
  getUserAgent() {
    return navigator.userAgent;
  }

  /**
   * 检查是否为移动设备
   * @returns {boolean} 是否为移动设备
   */
  isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件类型
   * @param {Function} handler - 事件处理函数
   */
  addEventListener(event, handler) {
    document.addEventListener(event, handler);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件类型
   * @param {Function} handler - 事件处理函数
   */
  removeEventListener(event, handler) {
    document.removeEventListener(event, handler);
  }

  /**
   * 进入全屏模式
   * @param {HTMLElement} element - 要全屏的元素，默认为整个文档
   */
  requestFullscreen(element = document.documentElement) {
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen();
    }
  }

  /**
   * 退出全屏模式
   */
  exitFullscreen() {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
  }
} 