import RenderAdapter from './render-adapter.js';

/**
 * 微信小游戏渲染适配器
 * 实现微信小游戏平台的渲染接口
 */
export default class WeChatRenderAdapter extends RenderAdapter {
  constructor() {
    super();
    
    // 检查微信环境
    if (typeof wx === 'undefined') {
      throw new Error('WeChatRenderAdapter requires WeChat Mini Game environment');
    }
  }

  /**
   * 创建画布
   * @returns {Object} 微信画布对象
   */
  createCanvas() {
    return (wx.createCanvas ? wx.createCanvas() : this.createMockCanvas());
  }

  /**
   * 获取屏幕尺寸
   * @returns {Object} { width: number, height: number }
   */
  getScreenSize() {
    try {
      const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : wx.getSystemInfoSync();
      return {
        width: windowInfo.screenWidth || 375,
        height: windowInfo.screenHeight || 667
      };
    } catch (error) {
      console.warn('⚠️ 获取屏幕尺寸失败，使用默认值:', error);
      return {
        width: 375,
        height: 667
      };
    }
  }

  /**
   * 获取画布2D上下文
   * @param {Object} canvas - 微信画布对象
   * @returns {Object} 2D渲染上下文
   */
  getContext2D(canvas) {
    return canvas.getContext('2d');
  }

  /**
   * 加载图片
   * @param {string} path - 图片路径
   * @returns {Promise<Object>} 图片对象
   */
  loadImage(path) {
    return new Promise((resolve, reject) => {
      const image = wx.createImage();
      image.onload = () => resolve(image);
      image.onerror = (error) => reject(error);
      image.src = path;
    });
  }

  /**
   * 设置画布尺寸
   * @param {Object} canvas - 微信画布对象
   * @param {number} width - 宽度
   * @param {number} height - 高度
   */
  setCanvasSize(canvas, width, height) {
    canvas.width = width;
    canvas.height = height;
  }

  /**
   * 获取设备像素比
   * @returns {number} 设备像素比
   */
  getDevicePixelRatio() {
    try {
      const systemInfo = wx.getSystemInfoSync ? wx.getSystemInfoSync() : {};
      return systemInfo.pixelRatio || 1;
    } catch (error) {
      console.warn('⚠️ 获取设备像素比失败，使用默认值:', error);
      return 1;
    }
  }

  /**
   * 请求动画帧
   * @param {Function} callback - 回调函数
   * @returns {number} 动画帧ID
   */
  requestAnimationFrame(callback) {
    return wx.requestAnimationFrame(callback);
  }

  /**
   * 取消动画帧
   * @param {number} id - 动画帧ID
   */
  cancelAnimationFrame(id) {
    if (wx.cancelAnimationFrame) {
      wx.cancelAnimationFrame(id);
    }
  }

  /**
   * 获取系统信息
   * @returns {Object} 系统信息
   */
  getSystemInfo() {
    try {
      return wx.getSystemInfoSync ? wx.getSystemInfoSync() : {};
    } catch (error) {
      console.warn('⚠️ 获取系统信息失败，返回空对象:', error);
      return {
        brand: 'devtools',
        model: 'devtools',
        pixelRatio: 2,
        screenWidth: 375,
        screenHeight: 667,
        windowWidth: 375,
        windowHeight: 667,
        statusBarHeight: 20,
        language: 'zh_CN',
        version: '8.0.5',
        system: 'iOS 16.0',
        platform: 'devtools'
      };
    }
  }

  /**
   * 显示加载提示
   * @param {string} title - 提示文字
   */
  showLoading(title = '加载中...') {
    if (wx.showLoading) {
      wx.showLoading({ title });
    }
  }

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    if (wx.hideLoading) {
      wx.hideLoading();
    }
  }

  createMockCanvas() {
    return {
      width: 375,
      height: 667,
      getContext: (type) => ({
        scale: () => {},
        clearRect: () => {},
        fillRect: () => {},
        strokeRect: () => {},
        drawImage: () => {},
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high',
        canvas: { width: 375, height: 667 }
      })
    };
  }
} 