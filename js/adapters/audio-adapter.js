/**
 * 音频适配器抽象基类
 * 定义所有平台音频处理器必须实现的接口
 */

// 音频类型
export const AUDIO_TYPES = {
  MUSIC: 'music',
  SOUND_EFFECT: 'sound_effect',
  VOICE: 'voice'
};

// 音频状态
export const AUDIO_STATES = {
  LOADING: 'loading',
  READY: 'ready',
  PLAYING: 'playing',
  PAUSED: 'paused',
  STOPPED: 'stopped',
  ERROR: 'error'
};

export default class AudioAdapter {
  constructor() {
    this.audioCache = new Map();
    this.globalVolume = 1.0;
    this.musicVolume = 1.0;
    this.soundEffectVolume = 1.0;
    this.muted = false;
  }

  /**
   * 加载音频文件
   * @param {string} path - 音频文件路径
   * @param {string} type - 音频类型 (AUDIO_TYPES中的值)
   * @returns {Promise<Object>} 音频对象
   */
  loadAudio(path, type = AUDIO_TYPES.SOUND_EFFECT) {
    throw new Error('AudioAdapter.loadAudio() must be implemented');
  }

  /**
   * 播放音频
   * @param {string} path - 音频文件路径
   * @param {Object} options - 播放选项
   * @returns {Promise<Object>} 播放实例
   */
  play(path, options = {}) {
    throw new Error('AudioAdapter.play() must be implemented');
  }

  /**
   * 暂停音频
   * @param {string} path - 音频文件路径
   */
  pause(path) {
    throw new Error('AudioAdapter.pause() must be implemented');
  }

  /**
   * 停止音频
   * @param {string} path - 音频文件路径
   */
  stop(path) {
    throw new Error('AudioAdapter.stop() must be implemented');
  }

  /**
   * 恢复播放
   * @param {string} path - 音频文件路径
   */
  resume(path) {
    throw new Error('AudioAdapter.resume() must be implemented');
  }

  /**
   * 获取音频状态
   * @param {string} path - 音频文件路径
   * @returns {string} 音频状态 (AUDIO_STATES中的值)
   */
  getState(path) {
    throw new Error('AudioAdapter.getState() must be implemented');
  }

  /**
   * 设置全局音量
   * @param {number} volume - 音量值 (0-1)
   */
  setGlobalVolume(volume) {
    this.globalVolume = Math.max(0, Math.min(1, volume));
    this._updateAllVolumes();
  }

  /**
   * 获取全局音量
   * @returns {number} 音量值
   */
  getGlobalVolume() {
    return this.globalVolume;
  }

  /**
   * 设置音乐音量
   * @param {number} volume - 音量值 (0-1)
   */
  setMusicVolume(volume) {
    this.musicVolume = Math.max(0, Math.min(1, volume));
    this._updateVolumeByType(AUDIO_TYPES.MUSIC);
  }

  /**
   * 获取音乐音量
   * @returns {number} 音量值
   */
  getMusicVolume() {
    return this.musicVolume;
  }

  /**
   * 设置音效音量
   * @param {number} volume - 音量值 (0-1)
   */
  setSoundEffectVolume(volume) {
    this.soundEffectVolume = Math.max(0, Math.min(1, volume));
    this._updateVolumeByType(AUDIO_TYPES.SOUND_EFFECT);
  }

  /**
   * 获取音效音量
   * @returns {number} 音量值
   */
  getSoundEffectVolume() {
    return this.soundEffectVolume;
  }

  /**
   * 设置静音状态
   * @param {boolean} muted - 是否静音
   */
  setMuted(muted) {
    this.muted = muted;
    this._updateAllVolumes();
  }

  /**
   * 获取静音状态
   * @returns {boolean} 是否静音
   */
  isMuted() {
    return this.muted;
  }

  /**
   * 切换静音状态
   */
  toggleMute() {
    this.setMuted(!this.muted);
  }

  /**
   * 停止所有音频
   */
  stopAll() {
    for (const path of this.audioCache.keys()) {
      this.stop(path);
    }
  }

  /**
   * 暂停所有音频
   */
  pauseAll() {
    for (const path of this.audioCache.keys()) {
      this.pause(path);
    }
  }

  /**
   * 恢复所有音频
   */
  resumeAll() {
    for (const path of this.audioCache.keys()) {
      this.resume(path);
    }
  }

  /**
   * 预加载音频文件列表
   * @param {Array<Object>} audioList - 音频文件列表 [{path, type}, ...]
   * @returns {Promise<Array>} 加载结果
   */
  async preloadAudios(audioList) {
    const promises = audioList.map(({ path, type }) => 
      this.loadAudio(path, type).catch(error => {
        console.warn(`Failed to preload audio: ${path}`, error);
        return null;
      })
    );
    
    return Promise.all(promises);
  }

  /**
   * 清理音频缓存
   */
  clearCache() {
    this.audioCache.clear();
  }

  /**
   * 获取缓存统计
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    return {
      totalCount: this.audioCache.size,
      audioList: Array.from(this.audioCache.keys())
    };
  }

  /**
   * 计算实际音量
   * @param {string} type - 音频类型
   * @returns {number} 实际音量值
   */
  _calculateVolume(type) {
    if (this.muted) return 0;
    
    let typeVolume = 1.0;
    switch (type) {
      case AUDIO_TYPES.MUSIC:
        typeVolume = this.musicVolume;
        break;
      case AUDIO_TYPES.SOUND_EFFECT:
        typeVolume = this.soundEffectVolume;
        break;
      default:
        typeVolume = 1.0;
    }
    
    return this.globalVolume * typeVolume;
  }

  /**
   * 更新所有音频的音量
   * @private
   */
  _updateAllVolumes() {
    // 子类实现
  }

  /**
   * 根据类型更新音频音量
   * @param {string} type - 音频类型
   * @private
   */
  _updateVolumeByType(type) {
    // 子类实现
  }

  /**
   * 销毁适配器
   */
  destroy() {
    this.stopAll();
    this.clearCache();
  }
} 