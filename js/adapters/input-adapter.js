/**
 * 输入适配器抽象基类
 * 定义所有平台输入处理器必须实现的接口
 */

// 输入事件类型
export const INPUT_EVENTS = {
  MOVE_LEFT: 'move_left',
  MOVE_RIGHT: 'move_right',
  ROTATE: 'rotate',
  SOFT_DROP: 'soft_drop',
  HARD_DROP: 'hard_drop',
  PAUSE: 'pause',
  USE_ITEM: 'use_item'
};

// 触摸手势类型
export const GESTURE_TYPES = {
  TAP: 'tap',
  SWIPE_LEFT: 'swipe_left',
  SWIPE_RIGHT: 'swipe_right',
  SWIPE_UP: 'swipe_up',
  SWIPE_DOWN: 'swipe_down',
  LONG_PRESS: 'long_press'
};

export default class InputAdapter {
  constructor() {
    this.listeners = new Map();
  }

  /**
   * 添加输入事件监听器
   * @param {string} event - 事件类型 (INPUT_EVENTS中的值)
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * 移除输入事件监听器
   * @param {string} event - 事件类型
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件类型
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in input event handler for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 初始化输入处理
   * 子类必须实现此方法来设置平台特定的输入监听
   */
  initialize() {
    throw new Error('InputAdapter.initialize() must be implemented');
  }

  /**
   * 销毁输入处理
   * 子类应该实现此方法来清理资源
   */
  destroy() {
    this.listeners.clear();
  }

  /**
   * 启用输入处理
   */
  enable() {
    throw new Error('InputAdapter.enable() must be implemented');
  }

  /**
   * 禁用输入处理
   */
  disable() {
    throw new Error('InputAdapter.disable() must be implemented');
  }

  /**
   * 检查当前是否启用
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    throw new Error('InputAdapter.isEnabled() must be implemented');
  }

  /**
   * 设置触摸灵敏度
   * @param {number} sensitivity - 灵敏度值 (0-1)
   */
  setSensitivity(sensitivity) {
    this.sensitivity = Math.max(0, Math.min(1, sensitivity));
  }

  /**
   * 获取触摸灵敏度
   * @returns {number} 灵敏度值
   */
  getSensitivity() {
    return this.sensitivity || 0.5;
  }
} 