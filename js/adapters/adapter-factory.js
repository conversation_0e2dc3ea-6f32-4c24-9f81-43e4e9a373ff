import WeChatRenderAdapter from './wechat-render-adapter.js';
import WebRenderAdapter from './web-render-adapter.js';
import WeChatInputAdapter from './wechat-input-adapter.js';
/**
 * 适配器工厂
 * 统一管理所有平台适配器的创建和配置
 */

// 平台类型
export const PLATFORM_TYPES = {
  WECHAT: 'wechat',
  WEB: 'web',
  UNKNOWN: 'unknown'
};

export default class AdapterFactory {
  constructor() {
    this.platform = this._detectPlatform();
    this.renderAdapter = null;
    this.inputAdapter = null;
    this.audioAdapter = null;
  }

  /**
   * 检测当前运行平台
   * @returns {string} 平台类型
   * @private
   */
  _detectPlatform() {
    if (typeof wx !== 'undefined') {
      return PLATFORM_TYPES.WECHAT;
    } else if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      return PLATFORM_TYPES.WEB;
    } else {
      return PLATFORM_TYPES.UNKNOWN;
    }
  }

  /**
   * 获取当前平台类型
   * @returns {string} 平台类型
   */
  getPlatform() {
    return this.platform;
  }

  /**
   * 创建渲染适配器
   * @returns {RenderAdapter} 渲染适配器实例
   */
  createRenderAdapter() {
    if (this.renderAdapter) {
      return this.renderAdapter;
    }

    switch (this.platform) {
      case PLATFORM_TYPES.WECHAT:
        this.renderAdapter = new WeChatRenderAdapter();
        break;
      case PLATFORM_TYPES.WEB:
        this.renderAdapter = new WebRenderAdapter();
        break;
      default:
        throw new Error(`不支持的平台: ${this.platform}`);
    }

    console.log(`创建${this.platform}渲染适配器`);
    return this.renderAdapter;
  }

  /**
   * 创建输入适配器
   * @returns {InputAdapter} 输入适配器实例
   */
  createInputAdapter() {
    if (this.inputAdapter) {
      return this.inputAdapter;
    }

    switch (this.platform) {
      case PLATFORM_TYPES.WECHAT:
        this.inputAdapter = new WeChatInputAdapter();
        break;
      case PLATFORM_TYPES.WEB:
        // TODO: 实现Web输入适配器
        console.warn('Web输入适配器尚未实现');
        return null;
      default:
        throw new Error(`不支持的平台: ${this.platform}`);
    }

    console.log(`创建${this.platform}输入适配器`);
    return this.inputAdapter;
  }

  /**
   * 创建音频适配器
   * @returns {AudioAdapter} 音频适配器实例
   */
  createAudioAdapter() {
    if (this.audioAdapter) {
      return this.audioAdapter;
    }

    switch (this.platform) {
      case PLATFORM_TYPES.WECHAT:
        // TODO: 实现微信音频适配器
        console.warn('微信音频适配器尚未实现');
        return null;
      case PLATFORM_TYPES.WEB:
        // TODO: 实现Web音频适配器
        console.warn('Web音频适配器尚未实现');
        return null;
      default:
        throw new Error(`不支持的平台: ${this.platform}`);
    }
  }

  /**
   * 创建所有适配器
   * @returns {Object} 包含所有适配器的对象
   */
  createAllAdapters() {
    return {
      render: this.createRenderAdapter(),
      input: this.createInputAdapter(),
      audio: this.createAudioAdapter(),
      platform: this.platform
    };
  }

  /**
   * 获取平台信息
   * @returns {Object} 平台详细信息
   */
  getPlatformInfo() {
    const info = {
      platform: this.platform,
      supportedFeatures: []
    };

    switch (this.platform) {
      case PLATFORM_TYPES.WECHAT:
        info.supportedFeatures = [
          'canvas',
          'touch',
          'keyboard',
          'audio',
          'storage',
          'network'
        ];
        info.environment = 'WeChat Mini Game';
        break;
      case PLATFORM_TYPES.WEB:
        info.supportedFeatures = [
          'canvas',
          'mouse',
          'keyboard',
          'audio',
          'storage',
          'network',
          'fullscreen'
        ];
        info.environment = 'Web Browser';
        break;
      default:
        info.supportedFeatures = [];
        info.environment = 'Unknown';
    }

    return info;
  }

  /**
   * 检查功能支持
   * @param {string} feature - 功能名称
   * @returns {boolean} 是否支持该功能
   */
  isFeatureSupported(feature) {
    const platformInfo = this.getPlatformInfo();
    return platformInfo.supportedFeatures.includes(feature);
  }

  /**
   * 销毁所有适配器
   */
  destroyAll() {
    if (this.inputAdapter) {
      this.inputAdapter.destroy();
      this.inputAdapter = null;
    }

    if (this.audioAdapter) {
      this.audioAdapter.destroy();
      this.audioAdapter = null;
    }

    // 渲染适配器通常不需要特殊的销毁逻辑
    this.renderAdapter = null;

    console.log('所有适配器已销毁');
  }

  /**
   * 重新初始化适配器
   */
  reinitialize() {
    this.destroyAll();
    this.platform = this._detectPlatform();
    console.log(`重新初始化适配器 - 平台: ${this.platform}`);
  }
}

// 创建单例实例
const adapterFactory = new AdapterFactory();

// 导出单例和类
export { adapterFactory }; 