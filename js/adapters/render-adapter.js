/**
 * 渲染适配器抽象基类
 * 定义所有平台渲染器必须实现的接口
 */
export default class RenderAdapter {
  /**
   * 创建画布
   * @returns {Object} 画布对象
   */
  createCanvas() {
    throw new Error('RenderAdapter.createCanvas() must be implemented');
  }

  /**
   * 获取屏幕尺寸
   * @returns {Object} { width: number, height: number }
   */
  getScreenSize() {
    throw new Error('RenderAdapter.getScreenSize() must be implemented');
  }

  /**
   * 获取画布2D上下文
   * @param {Object} canvas - 画布对象
   * @returns {Object} 2D渲染上下文
   */
  getContext2D(canvas) {
    throw new Error('RenderAdapter.getContext2D() must be implemented');
  }

  /**
   * 加载图片
   * @param {string} path - 图片路径
   * @returns {Promise<Object>} 图片对象
   */
  loadImage(path) {
    throw new Error('RenderAdapter.loadImage() must be implemented');
  }

  /**
   * 设置画布尺寸
   * @param {Object} canvas - 画布对象
   * @param {number} width - 宽度
   * @param {number} height - 高度
   */
  setCanvasSize(canvas, width, height) {
    throw new Error('RenderAdapter.setCanvasSize() must be implemented');
  }

  /**
   * 获取设备像素比
   * @returns {number} 设备像素比
   */
  getDevicePixelRatio() {
    return 1; // 默认值，子类可覆盖
  }

  /**
   * 请求动画帧
   * @param {Function} callback - 回调函数
   * @returns {number} 动画帧ID
   */
  requestAnimationFrame(callback) {
    throw new Error('RenderAdapter.requestAnimationFrame() must be implemented');
  }

  /**
   * 取消动画帧
   * @param {number} id - 动画帧ID
   */
  cancelAnimationFrame(id) {
    throw new Error('RenderAdapter.cancelAnimationFrame() must be implemented');
  }
} 