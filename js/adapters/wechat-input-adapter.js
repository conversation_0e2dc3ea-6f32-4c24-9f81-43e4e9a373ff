import InputAdapter, { INPUT_EVENTS, GESTURE_TYPES } from './input-adapter.js';

/**
 * 微信小游戏输入适配器
 * 处理微信小游戏平台的触摸和键盘输入
 */
export default class WeChatInputAdapter extends InputAdapter {
  constructor() {
    super();
    
    // 检查微信环境
    if (typeof wx === 'undefined') {
      throw new Error('WeChatInputAdapter requires WeChat Mini Game environment');
    }

    this.enabled = false;
    this.touchStartTime = 0;
    this.touchStartPos = { x: 0, y: 0 };
    this.longPressTimer = null;
    this.longPressDelay = 500; // 长按延迟时间(ms)
    this.swipeThreshold = 30; // 滑动阈值(px)
    
    // 绑定事件处理函数
    this._onKeyDown = this._onKeyDown.bind(this);
    this._onKeyUp = this._onKeyUp.bind(this);
    this._onTouchStart = this._onTouchStart.bind(this);
    this._onTouchMove = this._onTouchMove.bind(this);
    this._onTouchEnd = this._onTouchEnd.bind(this);
  }

  /**
   * 初始化输入处理
   */
  initialize() {
    this.enable();
  }

  /**
   * 启用输入处理
   */
  enable() {
    if (this.enabled) return;

    // 注册键盘事件
    if (wx.onKeyDown) {
      wx.onKeyDown(this._onKeyDown);
    }
    if (wx.onKeyUp) {
      wx.onKeyUp(this._onKeyUp);
    }

    // 注册触摸事件
    if (wx.onTouchStart) {
      wx.onTouchStart(this._onTouchStart);
    }
    if (wx.onTouchMove) {
      wx.onTouchMove(this._onTouchMove);
    }
    if (wx.onTouchEnd) {
      wx.onTouchEnd(this._onTouchEnd);
    }

    this.enabled = true;
    console.log('微信输入适配器已启用');
  }

  /**
   * 禁用输入处理
   */
  disable() {
    if (!this.enabled) return;

    // 移除键盘事件
    if (wx.offKeyDown) {
      wx.offKeyDown(this._onKeyDown);
    }
    if (wx.offKeyUp) {
      wx.offKeyUp(this._onKeyUp);
    }

    // 移除触摸事件
    if (wx.offTouchStart) {
      wx.offTouchStart(this._onTouchStart);
    }
    if (wx.offTouchMove) {
      wx.offTouchMove(this._onTouchMove);
    }
    if (wx.offTouchEnd) {
      wx.offTouchEnd(this._onTouchEnd);
    }

    this._clearLongPressTimer();
    this.enabled = false;
    console.log('微信输入适配器已禁用');
  }

  /**
   * 检查当前是否启用
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * 销毁输入处理
   */
  destroy() {
    this.disable();
    super.destroy();
  }

  /**
   * 键盘按下事件处理
   * @param {Object} res - 按键事件数据
   */
  _onKeyDown(res) {
    const { key } = res;
    
    switch (key) {
      case 'ArrowLeft':
        this.emit(INPUT_EVENTS.MOVE_LEFT);
        break;
      case 'ArrowRight':
        this.emit(INPUT_EVENTS.MOVE_RIGHT);
        break;
      case 'ArrowUp':
      case 'w':
      case 'W':
        this.emit(INPUT_EVENTS.ROTATE);
        break;
      case 'ArrowDown':
      case 's':
      case 'S':
        this.emit(INPUT_EVENTS.SOFT_DROP, { pressed: true });
        break;
      case ' ':
      case 'Space':
        this.emit(INPUT_EVENTS.HARD_DROP);
        break;
      case 'p':
      case 'P':
      case 'Escape':
        this.emit(INPUT_EVENTS.PAUSE);
        break;
      case '1':
      case '2':
      case '3':
        this.emit(INPUT_EVENTS.USE_ITEM, { itemIndex: parseInt(key) - 1 });
        break;
    }
  }

  /**
   * 键盘释放事件处理
   * @param {Object} res - 按键事件数据
   */
  _onKeyUp(res) {
    const { key } = res;
    
    if (key === 'ArrowDown' || key === 's' || key === 'S') {
      this.emit(INPUT_EVENTS.SOFT_DROP, { pressed: false });
    }
  }

  /**
   * 触摸开始事件处理
   * @param {Object} e - 触摸事件数据
   */
  _onTouchStart(e) {
    if (!e.touches || e.touches.length === 0) return;

    const touch = e.touches[0];
    this.touchStartTime = Date.now();
    this.touchStartPos = { x: touch.clientX, y: touch.clientY };

    // 设置长按计时器
    this._clearLongPressTimer();
    this.longPressTimer = setTimeout(() => {
      this.emit(INPUT_EVENTS.HARD_DROP);
      this._clearLongPressTimer();
    }, this.longPressDelay);
  }

  /**
   * 触摸移动事件处理
   * @param {Object} e - 触摸事件数据
   */
  _onTouchMove(e) {
    // 如果有移动，取消长按
    this._clearLongPressTimer();
  }

  /**
   * 触摸结束事件处理
   * @param {Object} e - 触摸事件数据
   */
  _onTouchEnd(e) {
    this._clearLongPressTimer();

    if (!e.changedTouches || e.changedTouches.length === 0) return;

    const touch = e.changedTouches[0];
    const endPos = { x: touch.clientX, y: touch.clientY };
    const touchDuration = Date.now() - this.touchStartTime;
    
    // 计算移动距离
    const deltaX = endPos.x - this.touchStartPos.x;
    const deltaY = endPos.y - this.touchStartPos.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // 判断手势类型
    if (distance < this.swipeThreshold) {
      // 短按 - 旋转
      if (touchDuration < 200) {
        this.emit(INPUT_EVENTS.ROTATE);
      }
    } else {
      // 滑动手势
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // 水平滑动
        if (deltaX > 0) {
          this.emit(INPUT_EVENTS.MOVE_RIGHT);
        } else {
          this.emit(INPUT_EVENTS.MOVE_LEFT);
        }
      } else {
        // 垂直滑动
        if (deltaY > 0) {
          this.emit(INPUT_EVENTS.SOFT_DROP, { pressed: true });
          // 模拟释放
          setTimeout(() => {
            this.emit(INPUT_EVENTS.SOFT_DROP, { pressed: false });
          }, 100);
        } else {
          // 向上滑动 - 旋转
          this.emit(INPUT_EVENTS.ROTATE);
        }
      }
    }
  }

  /**
   * 清除长按计时器
   */
  _clearLongPressTimer() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * 设置长按延迟时间
   * @param {number} delay - 延迟时间(ms)
   */
  setLongPressDelay(delay) {
    this.longPressDelay = Math.max(100, delay);
  }

  /**
   * 设置滑动阈值
   * @param {number} threshold - 阈值(px)
   */
  setSwipeThreshold(threshold) {
    this.swipeThreshold = Math.max(10, threshold);
  }

  /**
   * 获取触摸配置
   * @returns {Object} 触摸配置
   */
  getTouchConfig() {
    return {
      longPressDelay: this.longPressDelay,
      swipeThreshold: this.swipeThreshold,
      sensitivity: this.getSensitivity()
    };
  }

  /**
   * 设置触摸配置
   * @param {Object} config - 配置对象
   */
  setTouchConfig(config) {
    if (config.longPressDelay !== undefined) {
      this.setLongPressDelay(config.longPressDelay);
    }
    if (config.swipeThreshold !== undefined) {
      this.setSwipeThreshold(config.swipeThreshold);
    }
    if (config.sensitivity !== undefined) {
      this.setSensitivity(config.sensitivity);
    }
  }
} 