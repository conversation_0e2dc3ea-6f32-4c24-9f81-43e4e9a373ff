/**
 * 关卡管理器
 * 负责处理游戏关卡的加载、切换和状态
 */
import { getLevelConfig, getTotalLevels, getStageInfo } from './level-config.js';
import { BLOCK_COLORS, BLOCK_EFFECTS } from '../game/block.js';
import Block from '../game/block.js';
import { isDebugMode } from '../config/game-config.js';
import Emitter from '../libs/tinyemitter.js';

export default class LevelManager extends Emitter {
  /**
   * 创建关卡管理器
   */
  constructor() {
    super();
    
    // 当前关卡ID
    this.currentLevelId = 1;
    
    // 已解锁的最高关卡
    this.maxUnlockedLevel = 1;
    
    // 关卡评星
    this.levelStars = {};
    
    // 关卡计时器（以帧为单位）
    this.timer = 0;
    
    // 关卡是否开始
    this.isLevelStarted = false;
    
    // 关卡是否完成
    this.isLevelCompleted = false;
    
    // 加载已保存的进度
    this._loadProgress();

    // 在开发模式下解锁更多关卡用于测试
    this._setupDevelopmentMode();
  }
  
  /**
   * 加载游戏进度
   * @private
   */
  _loadProgress() {
    try {
      // 尝试从本地存储加载进度
      const progressData = wx.getStorageSync('levelProgress');

      if (progressData) {
        const progress = JSON.parse(progressData);

        this.maxUnlockedLevel = progress.maxUnlockedLevel || 1;
        this.levelStars = progress.levelStars || {};
      }
    } catch (e) {
      console.error('加载游戏进度失败:', e);
    }
  }

  /**
   * 设置开发模式
   * @private
   */
  _setupDevelopmentMode() {
    // 在开发模式下解锁更多关卡用于测试
    if (isDebugMode()) {
      const totalLevels = getTotalLevels();
      // 解锁前27关（3个阶段）用于测试滚动功能
      this.maxUnlockedLevel = Math.max(this.maxUnlockedLevel, Math.min(27, totalLevels));
      console.log(`🔧 开发模式：已解锁前 ${this.maxUnlockedLevel} 关用于测试`);
    }
  }
  
  /**
   * 保存游戏进度
   * @private
   */
  _saveProgress() {
    try {
      const progressData = {
        maxUnlockedLevel: this.maxUnlockedLevel,
        levelStars: this.levelStars
      };

      // 验证数据是否可以序列化
      const jsonString = this._safeStringify(progressData);
      if (jsonString === null) {
        console.error('关卡进度数据序列化失败，跳过保存');
        return;
      }

      wx.setStorageSync('levelProgress', jsonString);
      console.log('关卡进度保存成功');
    } catch (e) {
      console.error('保存游戏进度失败:', e);
    }
  }

  /**
   * 安全的JSON序列化
   * @param {Object} obj - 要序列化的对象
   * @returns {string|null} 序列化后的字符串，失败返回null
   * @private
   */
  _safeStringify(obj) {
    try {
      return JSON.stringify(obj, (key, value) => {
        // 过滤掉函数、Symbol、undefined等不可序列化的值
        if (typeof value === 'function' || typeof value === 'symbol' || value === undefined) {
          return null;
        }
        return value;
      });
    } catch (e) {
      console.error('JSON序列化失败:', e);
      return null;
    }
  }
  
  /**
   * 加载关卡
   * @param {number} levelId - 关卡ID
   * @returns {Object} 关卡配置
   */
  loadLevel(levelId) {
    if (levelId > this.maxUnlockedLevel) {
      console.warn(`关卡 ${levelId} 尚未解锁`);
      return null;
    }
    
    this.currentLevelId = levelId;
    const levelConfig = getLevelConfig(levelId);
    
    // 重置关卡状态
    this.timer = 0;
    this.isLevelStarted = false;
    this.isLevelCompleted = false;
    
    // 触发关卡加载事件
    this.emit('level:load', { levelId, config: levelConfig });
    
    return levelConfig;
  }
  
  /**
   * 开始当前关卡
   */
  startLevel() {
    this.isLevelStarted = true;
    this.timer = 0;
    
    // 触发关卡开始事件
    this.emit('level:start', { levelId: this.currentLevelId });
  }
  
  /**
   * 更新关卡状态
   * @param {Object} gameState - 游戏状态数据
   */
  update(gameState) {
    if (!this.isLevelStarted || this.isLevelCompleted) {
      return;
    }
    
    const levelConfig = getLevelConfig(this.currentLevelId);
    
    // 更新计时器
    this.timer++;
    
    // 检查时间限制
    if (levelConfig.timeLimit > 0) {
      const timeElapsed = Math.floor(this.timer / 60); // 假设60帧/秒
      
      if (timeElapsed >= levelConfig.timeLimit) {
        this._failLevel('超时');
        return;
      }
    }
    
    // 检查是否达到目标分数
    if (gameState.score >= levelConfig.targetScore) {
      this._completeLevel(gameState.score);
    }
  }
  
  /**
   * 完成关卡
   * @param {number} score - 最终分数
   * @private
   */
  _completeLevel(score) {
    if (this.isLevelCompleted) {
      return;
    }
    
    this.isLevelCompleted = true;
    
    // 计算星级
    const levelConfig = getLevelConfig(this.currentLevelId);
    let stars = 0;
    
    for (let i = 0; i < levelConfig.starThresholds.length; i++) {
      if (score >= levelConfig.starThresholds[i]) {
        stars = i + 1;
      } else {
        break;
      }
    }
    
    // 更新最高星级
    const currentStars = this.levelStars[this.currentLevelId] || 0;
    if (stars > currentStars) {
      this.levelStars[this.currentLevelId] = stars;
    }
    
    // 解锁下一关
    if (this.currentLevelId === this.maxUnlockedLevel && this.currentLevelId < getTotalLevels()) {
      this.maxUnlockedLevel++;
    }
    
    // 保存进度
    this._saveProgress();
    
    // 触发关卡完成事件
    this.emit('level:complete', {
      levelId: this.currentLevelId,
      score,
      stars,
      timeElapsed: Math.floor(this.timer / 60)
    });
  }
  
  /**
   * 关卡失败
   * @param {string} reason - 失败原因
   * @private
   */
  _failLevel(reason) {
    if (this.isLevelCompleted) {
      return;
    }
    
    // 触发关卡失败事件
    this.emit('level:fail', {
      levelId: this.currentLevelId,
      reason
    });
  }
  
  /**
   * 创建初始方块
   * @param {Grid} grid - 游戏网格
   */
  setupInitialBlocks(grid) {
    const levelConfig = getLevelConfig(this.currentLevelId);
    
    // 清空网格
    for (let row = 0; row < grid.rows; row++) {
      for (let col = 0; col < grid.cols; col++) {
        grid.removeBlock(row, col);
      }
    }
    
    // 放置初始方块
    if (levelConfig.initialBlocks && levelConfig.initialBlocks.length > 0) {
      levelConfig.initialBlocks.forEach(blockData => {
        const { row, col, color, effect } = blockData;
        
        // 创建方块
        const block = new Block(
          BLOCK_COLORS[color.toUpperCase()] || color, 
          BLOCK_EFFECTS[effect.toUpperCase()] || BLOCK_EFFECTS.NONE
        );
        
        // 放置到网格
        grid.placeBlock(block, row, col);
      });
    }
  }
  
  /**
   * 获取关卡时间信息
   * @returns {Object} 时间信息
   */
  getTimeInfo() {
    const levelConfig = getLevelConfig(this.currentLevelId);
    const timeElapsed = Math.floor(this.timer / 60); // 假设60帧/秒
    const timeLeft = levelConfig.timeLimit > 0 ? levelConfig.timeLimit - timeElapsed : Infinity;
    
    return {
      timeElapsed,
      timeLeft,
      hasTimeLimit: levelConfig.timeLimit > 0
    };
  }
  
  /**
   * 获取关卡目标信息
   * @returns {Object} 目标信息
   */
  getTargetInfo() {
    const levelConfig = getLevelConfig(this.currentLevelId);
    
    return {
      targetScore: levelConfig.targetScore,
      starThresholds: levelConfig.starThresholds
    };
  }
  
  /**
   * 获取解锁的关卡列表
   * @returns {Array} 关卡列表
   */
  getUnlockedLevels() {
    const levels = [];

    for (let i = 1; i <= this.maxUnlockedLevel; i++) {
      const levelConfig = getLevelConfig(i);
      const stageInfo = getStageInfo(i);

      levels.push({
        id: i,
        name: levelConfig.name,
        stars: this.levelStars[i] || 0,
        stageInfo: stageInfo,
        unlocked: true // 所有返回的关卡都是已解锁的
      });
    }

    return levels;
  }

  /**
   * 获取分阶段的关卡列表
   * @returns {Array} 阶段列表，每个阶段包含关卡数组
   */
  getUnlockedLevelsByStage() {
    const stages = [];
    const levels = this.getUnlockedLevels();

    // 按阶段分组
    const stageMap = new Map();

    levels.forEach(level => {
      const stageNum = level.stageInfo.stageNumber;

      if (!stageMap.has(stageNum)) {
        stageMap.set(stageNum, {
          stageNumber: stageNum,
          stageName: level.stageInfo.stageName,
          stageDescription: level.stageInfo.stageDescription,
          stageRange: level.stageInfo.stageRange,
          levels: [],
          isUnlocked: true,
          totalStars: 0,
          maxStars: 0
        });
      }

      const stage = stageMap.get(stageNum);
      stage.levels.push(level);
      stage.totalStars += level.stars;
      stage.maxStars += 3; // 每关最多3星
    });

    // 转换为数组并排序
    const sortedStages = Array.from(stageMap.values()).sort((a, b) => a.stageNumber - b.stageNumber);

    return sortedStages;
  }
  
  /**
   * 重置所有进度
   */
  resetAllProgress() {
    this.maxUnlockedLevel = 1;
    this.levelStars = {};
    this._saveProgress();
  }

  /**
   * 解锁指定数量的关卡（用于测试）
   * @param {number} levelCount - 要解锁的关卡数量
   */
  unlockLevelsForTesting(levelCount) {
    const totalLevels = getTotalLevels();
    this.maxUnlockedLevel = Math.min(levelCount, totalLevels);
    this._saveProgress();
    console.log(`🔓 已解锁前 ${this.maxUnlockedLevel} 关用于测试`);
  }

  /**
   * 获取当前最大解锁关卡
   * @returns {number} 最大解锁关卡ID
   */
  getMaxUnlockedLevel() {
    return this.maxUnlockedLevel;
  }
  
  /**
   * 渲染关卡信息
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    // 可以在这里添加渲染关卡信息的代码，例如时间、目标等
  }
} 