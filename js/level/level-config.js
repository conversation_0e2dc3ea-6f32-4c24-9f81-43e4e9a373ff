/**
 * 游戏关卡配置
 * 定义不同关卡的参数和目标
 * 
 * 更新内容：
 * 1. 增加关卡类型枚举
 * 2. 支持阶段性配置递增
 * 3. 更灵活的方块效果配置
 * 4. 支持多种关卡目标类型
 */

import { applyDebugScore } from '../config/game-config.js';

// 初始方块布局模板（每阶段9关通用布局）
export const INITIAL_LAYOUTS = {
  1: [], // 第1关：空场开局
  2: [ // 第2关：单点障碍
    { row: 19, col: 4, color: 'blue', effect: 'frozen' }
  ],
  3: [ // 第3关：双点对称
    { row: 19, col: 3, color: 'blue', effect: 'frozen' },
    { row: 19, col: 6, color: 'red', effect: 'frozen' }
  ],
  4: [ // 第4关：一字横排
    { row: 19, col: 1, color: 'red', effect: 'frozen' },
    { row: 19, col: 2, color: 'blue', effect: 'frozen' },
    { row: 19, col: 3, color: 'green', effect: 'frozen' },
    { row: 19, col: 4, color: 'red', effect: 'frozen' },
    { row: 19, col: 5, color: 'blue', effect: 'frozen' },
    { row: 19, col: 6, color: 'green', effect: 'frozen' }
  ],
  5: [ // 第5关：十字布局
    { row: 17, col: 4, color: 'blue', effect: 'frozen' },
    { row: 18, col: 3, color: 'red', effect: 'frozen' },
    { row: 18, col: 4, color: 'green', effect: 'frozen' },
    { row: 18, col: 5, color: 'blue', effect: 'frozen' },
    { row: 19, col: 4, color: 'red', effect: 'frozen' }
  ],
  6: [ // 第6关：L型角落
    { row: 17, col: 1, color: 'red', effect: 'frozen' },
    { row: 18, col: 1, color: 'blue', effect: 'frozen' },
    { row: 19, col: 1, color: 'green', effect: 'frozen' },
    { row: 19, col: 2, color: 'red', effect: 'frozen' },
    { row: 19, col: 3, color: 'blue', effect: 'frozen' },
    { row: 19, col: 4, color: 'green', effect: 'frozen' }
  ],
  7: [ // 第7关：井字格局
    { row: 18, col: 1, color: 'red', effect: 'frozen' },
    { row: 18, col: 3, color: 'blue', effect: 'frozen' },
    { row: 18, col: 5, color: 'green', effect: 'frozen' }
  ],
  8: [ // 第8关：阶梯上升
    { row: 19, col: 1, color: 'red', effect: 'frozen' },
    { row: 19, col: 2, color: 'blue', effect: 'frozen' },
    { row: 18, col: 3, color: 'green', effect: 'frozen' },
    { row: 18, col: 4, color: 'red', effect: 'frozen' },
    { row: 17, col: 5, color: 'blue', effect: 'frozen' }
  ],
  9: [ // 第9关：复合挑战（Boss关卡）
    { row: 17, col: 2, color: 'red', effect: 'frozen' },
    { row: 17, col: 6, color: 'blue', effect: 'frozen' },
    { row: 18, col: 1, color: 'green', effect: 'frozen' },
    { row: 18, col: 2, color: 'red', effect: 'frozen' },
    { row: 18, col: 3, color: 'blue', effect: 'frozen' },
    { row: 18, col: 5, color: 'green', effect: 'frozen' },
    { row: 18, col: 6, color: 'red', effect: 'frozen' },
    { row: 18, col: 7, color: 'blue', effect: 'frozen' },
    { row: 19, col: 2, color: 'green', effect: 'frozen' },
    { row: 19, col: 4, color: 'red', effect: 'frozen' },
    { row: 19, col: 6, color: 'blue', effect: 'frozen' }
  ]
};

// 关卡类型定义
export const LEVEL_TYPES = {
  CLASSIC: 'classic',           // 经典模式：清除指定行数或达到目标分数
  TARGET_SCORE: 'target_score', // 目标分数：达到指定分数
  TIME_ATTACK: 'time_attack',   // 时间挑战：限时内获得最高分
  SURVIVAL: 'survival',         // 生存模式：坚持指定时间不失败
  CLEAR_BLOCKS: 'clear_blocks', // 清除特定：消除指定数量的特殊方块
  NO_ITEMS: 'no_items',        // 禁用道具：不能使用道具完成关卡
  CHAIN_COMBO: 'chain_combo',   // 连击挑战：达到指定连击数
  TUTORIAL: 'tutorial',         // 教学关卡：带有引导和提示
  PRACTICE: 'practice',         // 练习关卡：可以无限重试
  ASSISTED: 'assisted'          // 辅助关卡：提供额外帮助
};

// 阶段名称配置
export const STAGE_NAMES = {
  stage1: '初出茅庐',
  stage2: '渐入佳境',
  stage3: '小试牛刀',
  stage4: '游刃有余',
  stage5: '炉火纯青',
  stage6: '登峰造极',
  stage7: '出神入化'
};

// 关卡阶段配置（每阶段9关）
export const STAGE_CONFIG = {
  // 第1阶段：初出茅庐 (1-9关)
  stage1: {
    name: '初出茅庐',
    description: '初学乍练，掌握基础',
    colorCount: 3,
    effectProbability: 0.02,  // 降低特效概率
    speedFactor: 0.6,         // 降低初始速度
    targetMultiplier: 0.8,    // 降低分数要求
    allowedEffects: ['frozen'],
    stageTheme: 'tutorial',
    assistanceLevel: 'high',  // 高辅助等级
    itemBonusMultiplier: 2.0, // 道具奖励翻倍
    effectBalance: { positive: 0.8, negative: 0.2 }, // 80%正面效果
    maxEffectsPerTetromino: 1 // 每个俄罗斯方块最多1个特效
  },
  // 第2阶段：渐入佳境 (10-18关)
  stage2: {
    name: '渐入佳境',
    description: '技艺渐熟，稳步提升',
    colorCount: 3,            // 保持3色，延缓复杂度增长
    effectProbability: 0.05,
    speedFactor: 0.75,        // 更平缓的速度增长
    targetMultiplier: 1.0,
    allowedEffects: ['frozen'],
    stageTheme: 'practice',
    assistanceLevel: 'medium',
    itemBonusMultiplier: 1.5,
    effectBalance: { positive: 0.7, negative: 0.3 },
    maxEffectsPerTetromino: 1
  },
  // 第3阶段：小试牛刀 (19-27关)
  stage3: {
    name: '小试牛刀',
    description: '初显身手，迎接挑战',
    colorCount: 4,            // 现在才增加到4色
    effectProbability: 0.08,  // 更平缓的特效增长
    speedFactor: 0.9,
    targetMultiplier: 1.1,    // 更小的分数增长
    allowedEffects: ['frozen', 'mine'],
    stageTheme: 'adaptation',
    assistanceLevel: 'low',
    itemBonusMultiplier: 1.2,
    effectBalance: { positive: 0.6, negative: 0.4 },
    maxEffectsPerTetromino: 1
  },
  // 第4阶段：游刃有余 (28-36关)
  stage4: {
    name: '游刃有余',
    description: '技法娴熟，应对自如',
    colorCount: 4,
    effectProbability: 0.12,
    speedFactor: 1.0,
    targetMultiplier: 1.25,
    allowedEffects: ['frozen', 'mine', 'shield'],
    stageTheme: 'improvement',
    assistanceLevel: 'minimal',
    itemBonusMultiplier: 1.0,
    effectBalance: { positive: 0.55, negative: 0.45 },
    maxEffectsPerTetromino: 1
  },
  // 第5阶段：炉火纯青 (37-45关)
  stage5: {
    name: '炉火纯青',
    description: '技艺精湛，挥洒自如',
    colorCount: 5, // 红蓝绿黄橙
    effectProbability: 0.15,
    speedFactor: 1.15,
    targetMultiplier: 1.4,
    allowedEffects: ['frozen', 'mine', 'shield', 'magnet'],
    stageTheme: 'challenge',
    assistanceLevel: 'none',
    itemBonusMultiplier: 1.0,
    effectBalance: { positive: 0.5, negative: 0.5 },
    maxEffectsPerTetromino: 2
  },
  // 第6阶段：登峰造极 (46-54关)
  stage6: {
    name: '登峰造极',
    description: '技压群雄，独步天下',
    colorCount: 6, // 红蓝绿黄橙灰
    effectProbability: 0.2,
    speedFactor: 1.3,
    targetMultiplier: 1.6,
    allowedEffects: ['frozen', 'mine', 'shield', 'magnet', 'rainbow', 'virus'],
    stageTheme: 'expert',
    assistanceLevel: 'none',
    itemBonusMultiplier: 1.0,
    effectBalance: { positive: 0.45, negative: 0.55 },
    maxEffectsPerTetromino: 2
  },
  // 第7阶段：出神入化 (55+关)
  stage7: {
    name: '出神入化',
    description: '超凡入圣，无人能及',
    colorCount: 7, // 全部7种颜色：红蓝绿黄橙灰黑
    effectProbability: 0.25,
    speedFactor: 1.5,
    targetMultiplier: 1.8,
    allowedEffects: ['frozen', 'mine', 'shield', 'magnet', 'rainbow', 'virus', 'crystal', 'anchor'],
    stageTheme: 'master',
    assistanceLevel: 'none',
    itemBonusMultiplier: 1.0,
    effectBalance: { positive: 0.4, negative: 0.6 },
    maxEffectsPerTetromino: 3
  }
};

/**
 * 根据关卡ID获取阶段配置
 * @param {number} levelId - 关卡ID
 * @returns {Object} 阶段配置
 */
function getStageConfig(levelId) {
  // 新的阶段划分：1-9, 10-18, 19-27, 28-36, 37-45, 46-54, 55+
  let stageNum;
  if (levelId <= 9) stageNum = 1;
  else if (levelId <= 18) stageNum = 2;
  else if (levelId <= 27) stageNum = 3;
  else if (levelId <= 36) stageNum = 4;
  else if (levelId <= 45) stageNum = 5;
  else if (levelId <= 54) stageNum = 6;
  else stageNum = 7;

  return STAGE_CONFIG[`stage${stageNum}`];
}

/**
 * 根据关卡ID生成初始方块布局
 * @param {number} levelId - 关卡ID
 * @returns {Array} 初始方块数组
 */
function generateInitialBlocks(levelId) {
  // 计算在阶段内的关卡位置 (1-9)
  const levelInStage = ((levelId - 1) % 9) + 1;

  // 获取基础布局模板
  const baseLayout = INITIAL_LAYOUTS[levelInStage] || [];

  // 如果是空布局，直接返回
  if (baseLayout.length === 0) {
    return [];
  }

  // 获取当前阶段配置
  const stageConfig = getStageConfig(levelId);

  // 复制布局并根据阶段特点调整
  const initialBlocks = baseLayout.map(block => {
    const newBlock = { ...block };

    // 根据阶段调整特效类型
    if (stageConfig.allowedEffects.length > 1) {
      // 如果阶段允许多种特效，随机分配
      const effectIndex = Math.floor(Math.random() * stageConfig.allowedEffects.length);
      newBlock.effect = stageConfig.allowedEffects[effectIndex];
    } else {
      // 否则使用阶段的主要特效
      newBlock.effect = stageConfig.allowedEffects[0] || 'frozen';
    }

    // 根据阶段调整颜色（确保使用当前阶段允许的颜色）
    const colors = ['red', 'blue', 'green', 'yellow', 'orange', 'gray', 'black'];
    const allowedColors = colors.slice(0, stageConfig.colorCount);
    const colorIndex = Math.floor(Math.random() * allowedColors.length);
    newBlock.color = allowedColors[colorIndex];

    return newBlock;
  });

  return initialBlocks;
}

/**
 * 生成动态关卡配置
 * @param {number} levelId - 关卡ID
 * @param {Object} customConfig - 自定义配置覆盖
 * @returns {Object} 关卡配置
 */
function generateLevelConfig(levelId, customConfig = {}) {
  const stageConfig = getStageConfig(levelId);
  const baseScore = 1000;
  const levelType = LEVEL_TYPES.CLASSIC;

  // 动态计算目标分数（随关卡递增）
  let targetScore = Math.floor(baseScore * levelId * stageConfig.targetMultiplier);

  // 应用调试模式分数调整
  targetScore = applyDebugScore(targetScore);

  // 计算基础配置
  const config = {
    id: levelId,
    name: `第${levelId}关`,
    type: levelType,

    // 从阶段配置继承
    colorCount: stageConfig.colorCount,
    effectProbability: stageConfig.effectProbability,
    speedFactor: stageConfig.speedFactor,
    allowedEffects: [...stageConfig.allowedEffects],
    stageTheme: stageConfig.stageTheme,

    // 目标分数（已应用调试模式调整）
    targetScore: targetScore,

    // 时间限制（可选，0表示无限制）
    timeLimit: getTimeLimit(levelType, levelId),

    // 初始方块分布（使用新的布局系统）
    initialBlocks: generateInitialBlocks(levelId),

    // 星级评分阈值（基于目标分数）
    starThresholds: [
      Math.floor(targetScore * 0.6),  // 1星：60%目标分数（过关线）
      Math.floor(targetScore * 0.8),  // 2星：80%目标分数
      targetScore                     // 3星：100%目标分数（满分）
    ],

    // 关卡描述和目标
    description: generateLevelDescription(levelType, levelId, stageConfig),
    objectives: generateObjectives(levelType, targetScore, stageConfig),

    // 特殊目标
    specialTargets: getSpecialTargets(levelType, levelId),

    // 关卡奖励配置
    rewards: {
      completion: {
        coins: Math.floor(levelId * 50),
        fireball: Math.floor(levelId / 5) + 1,
        lightning: Math.floor(levelId / 7) + 1,
        waterflow: Math.floor(levelId / 10) + 1,
        earthquake: Math.floor(levelId / 12) + 1
      },
      stars: {
        1: { coins: Math.floor(levelId * 20) },
        2: { coins: Math.floor(levelId * 30), gems: 1 },
        3: { coins: Math.floor(levelId * 50), gems: 2 }
      }
    },

    // 特殊配置
    itemsAllowed: true,
    maxItems: -1, // -1表示无限制

    // 垃圾生成配置
    garbageEnabled: levelId > 3, // 从第4关开始启用垃圾生成
    garbageInterval: Math.max(600, 2400 - levelId * 60), // 根据关卡调整间隔
    garbageDensity: Math.min(0.9, 0.4 + levelId * 0.02), // 根据关卡调整密度

    // 特殊规则
    specialRules: []
  };

  // 应用自定义配置覆盖
  const finalConfig = Object.assign(config, customConfig);

  // 如果自定义配置修改了目标分数，重新计算星级阈值和目标
  if (finalConfig.targetScore !== targetScore) {
    // 对自定义目标分数也应用调试模式调整
    let newTargetScore = applyDebugScore(finalConfig.targetScore);
    finalConfig.targetScore = newTargetScore;

    finalConfig.starThresholds = [
      Math.floor(newTargetScore * 0.6),  // 1星：60%目标分数（过关线）
      Math.floor(newTargetScore * 0.8),  // 2星：80%目标分数
      newTargetScore                     // 3星：100%目标分数（满分）
    ];

    // 重新生成目标描述
    finalConfig.objectives = generateObjectives(levelType, newTargetScore, stageConfig);
  }

  return finalConfig;
}

// 预定义的特殊关卡配置
export const SPECIAL_LEVELS = {
  // Boss关卡（每10关）
  boss: {
    10: {
      name: '第一章 Boss',
      type: LEVEL_TYPES.SURVIVAL,
      timeLimit: 180,
      speedFactor: 1.5,
      effectProbability: 0.3,
      initialBlocks: [
        // 复杂的初始障碍
        { row: 19, col: 4, color: 'red', effect: 'mine' },
        { row: 19, col: 5, color: 'blue', effect: 'mine' },
        { row: 18, col: 3, color: 'green', effect: 'frozen' },
        { row: 18, col: 4, color: 'yellow', effect: 'frozen' },
        { row: 18, col: 5, color: 'purple', effect: 'frozen' },
        { row: 18, col: 6, color: 'cyan', effect: 'frozen' }
      ],
      rewards: {
        completion: { coins: 1000, gems: 5, allItems: 5 }
      }
    },
    20: {
      name: '第二章 Boss',
      type: LEVEL_TYPES.TARGET_SCORE,
      targetScore: 25000,
      timeLimit: 300,
      colorCount: 5,
      speedFactor: 1.8,
      effectProbability: 0.35,
      initialBlocks: [
        // V形障碍 + 地雷区
        ...Array(7).fill().map((_, i) => ({ 
          row: 19, col: i + 1, color: ['red', 'blue', 'green', 'yellow', 'purple'][i % 5], effect: 'frozen'
        })),
        { row: 18, col: 2, color: 'red', effect: 'mine' },
        { row: 18, col: 6, color: 'blue', effect: 'mine' },
        { row: 17, col: 4, color: 'green', effect: 'mine' }
      ],
      rewards: {
        completion: { coins: 2000, gems: 10, allItems: 10 }
      }
    }
  },
  
  // 特殊挑战关卡
  challenge: {
    15: {
      name: '无道具挑战',
      type: LEVEL_TYPES.NO_ITEMS,
      itemsAllowed: false,
      targetScore: 8000,
      specialRules: ['no_items'],
      rewards: {
        completion: { coins: 1500, gems: 3 }
      }
    },
    25: {
      name: '时间竞速',
      type: LEVEL_TYPES.TIME_ATTACK,
      timeLimit: 120,
      speedFactor: 2.0,
      targetScore: 15000,
      specialRules: ['time_pressure'],
      rewards: {
        completion: { coins: 2000, gems: 5 }
      }
    }
  }
};

// 关卡配置数据（现在使用统一的布局系统）
export const LEVEL_CONFIG = [
  // 关卡1：空场开局
  generateLevelConfig(1, {
    name: '空场开局',
    type: LEVEL_TYPES.TUTORIAL
  }),

  // 关卡2：单点障碍
  generateLevelConfig(2, {
    name: '单点障碍',
    type: LEVEL_TYPES.TUTORIAL
  }),

  // 关卡3：双点对称
  generateLevelConfig(3, {
    name: '双点对称',
    type: LEVEL_TYPES.TUTORIAL
  }),

  // 关卡4：一字横排
  generateLevelConfig(4, {
    name: '一字横排',
    type: LEVEL_TYPES.PRACTICE
  }),

  // 关卡5：十字布局
  generateLevelConfig(5, {
    name: '十字布局',
    type: LEVEL_TYPES.PRACTICE
  }),

  // 关卡6：L型角落
  generateLevelConfig(6, {
    name: 'L型角落',
    type: LEVEL_TYPES.PRACTICE
  }),

  // 关卡7：井字格局
  generateLevelConfig(7, {
    name: '井字格局',
    type: LEVEL_TYPES.PRACTICE
  }),

  // 关卡8：阶梯上升
  generateLevelConfig(8, {
    name: '阶梯上升',
    type: LEVEL_TYPES.PRACTICE
  }),

  // 关卡9：复合挑战
  generateLevelConfig(9, {
    name: '复合挑战',
    type: LEVEL_TYPES.CLASSIC
  })
];

/**
 * 动态生成更多关卡（用于无限关卡）
 */
function generateMoreLevels(startLevel, count) {
  const levels = [];
  
  for (let i = 0; i < count; i++) {
    const levelId = startLevel + i;
    let config = generateLevelConfig(levelId);
    
    // 检查是否有特殊关卡配置
    if (SPECIAL_LEVELS.boss[levelId]) {
      config = Object.assign(config, SPECIAL_LEVELS.boss[levelId]);
    } else if (SPECIAL_LEVELS.challenge[levelId]) {
      config = Object.assign(config, SPECIAL_LEVELS.challenge[levelId]);
    }
    
    // 每5关增加一些初始障碍
    if (levelId % 5 === 0 && levelId > 5) {
      const obstacleCount = Math.min(Math.floor(levelId / 5), 8);
      config.initialBlocks = generateRandomObstacles(obstacleCount, config.allowedEffects);
    }
    
    levels.push(config);
  }
  
  return levels;
}

/**
 * 生成随机障碍方块
 * @param {number} count - 障碍数量
 * @param {Array} allowedEffects - 允许的效果
 * @returns {Array} 障碍方块配置
 */
function generateRandomObstacles(count, allowedEffects) {
  const obstacles = [];
  const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'cyan'];
  const safeEffects = Array.isArray(allowedEffects) && allowedEffects.length
  ? allowedEffects
  : ['frozen','mine'];
  
  for (let i = 0; i < count; i++) {
    obstacles.push({
      row: Math.floor(Math.random() * 5) + 15, // 底部5行
      col: Math.floor(Math.random() * 10),
      color: colors[Math.floor(Math.random() * colors.length)],
      effect: safeEffects[Math.floor(Math.random() * safeEffects.length)]
    });
  }
  
  return obstacles;
}

/**
 * 获取关卡配置
 * @param {number} levelId - 关卡ID
 * @returns {Object} 关卡配置对象
 */
export function getLevelConfig(levelId) {
  // 如果预定义的关卡存在，直接返回
  if (levelId <= LEVEL_CONFIG.length) {
    return LEVEL_CONFIG[levelId - 1];
  }
  
  // 否则动态生成关卡
  return generateLevelConfig(levelId);
}

/**
 * 获取总关卡数（无限关卡模式）
 * @returns {number} 关卡总数（返回一个大数表示无限）
 */
export function getTotalLevels() {
  return 9999; // 表示无限关卡
}

/**
 * 获取关卡阶段信息
 * @param {number} levelId - 关卡ID
 * @returns {Object} 阶段信息
 */
export function getStageInfo(levelId) {
  // 新的阶段划分：1-9, 10-18, 19-27, 28-36, 37-45, 46-54, 55+
  let stageNum;
  if (levelId <= 9) stageNum = 1;
  else if (levelId <= 18) stageNum = 2;
  else if (levelId <= 27) stageNum = 3;
  else if (levelId <= 36) stageNum = 4;
  else if (levelId <= 45) stageNum = 5;
  else if (levelId <= 54) stageNum = 6;
  else stageNum = 7;

  const stageConfig = getStageConfig(levelId);
  const levelInStage = ((levelId - 1) % 9) + 1;

  return {
    stageNumber: stageNum,
    stageName: stageConfig.name,
    stageDescription: stageConfig.description,
    levelInStage: levelInStage,
    stageConfig: stageConfig,
    isBossLevel: levelInStage === 9, // 每阶段第9关是Boss关
    isSpecialLevel: SPECIAL_LEVELS.challenge[levelId] !== undefined,
    stageRange: {
      start: (stageNum - 1) * 9 + 1,
      end: stageNum === 7 ? 999 : stageNum * 9
    }
  };
}

/**
 * 获取下一阶段预览
 * @param {number} currentLevel - 当前关卡
 * @returns {Object} 下一阶段信息
 */
export function getNextStagePreview(currentLevel) {
  const nextStageLevel = Math.ceil(currentLevel / 10) * 10 + 1;
  const nextStageConfig = getStageConfig(nextStageLevel);

  return {
    level: nextStageLevel,
    config: nextStageConfig,
    improvements: {
      colorIncrease: nextStageConfig.colorCount > getStageConfig(currentLevel).colorCount,
      speedIncrease: nextStageConfig.speedFactor > getStageConfig(currentLevel).speedFactor,
      newEffects: nextStageConfig.allowedEffects.filter(
        effect => !getStageConfig(currentLevel).allowedEffects.includes(effect)
      )
    }
  };
}

/**
 * 生成关卡描述
 * @param {string} levelType - 关卡类型
 * @param {number} levelId - 关卡ID
 * @param {Object} stageConfig - 阶段配置
 * @returns {string} 关卡描述
 */
export function generateLevelDescription(levelType, levelId, stageConfig) {
  const stageThemes = {
    'tutorial': '新手教学阶段，学习基础操作',
    'practice': '基础练习阶段，熟悉游戏机制',
    'adaptation': '进阶适应阶段，掌握策略技巧',
    'improvement': '技能提升阶段，挑战更高难度',
    'challenge': '挑战阶段，考验综合实力',
    'expert': '专家阶段，需要精湛技巧',
    'master': '大师阶段，终极挑战'
  };

  const baseDescription = stageThemes[stageConfig.stageTheme] || '挑战关卡';

  // 根据关卡类型添加特殊说明
  const typeDescriptions = {
    [LEVEL_TYPES.CLASSIC]: '经典模式',
    [LEVEL_TYPES.TARGET_SCORE]: '目标分数挑战',
    [LEVEL_TYPES.TIME_ATTACK]: '限时挑战',
    [LEVEL_TYPES.SURVIVAL]: '生存模式',
    [LEVEL_TYPES.NO_ITEMS]: '无道具挑战',
    [LEVEL_TYPES.CLEAR_BLOCKS]: '清除特定方块',
    [LEVEL_TYPES.CHAIN_COMBO]: '连击挑战'
  };

  const typeDesc = typeDescriptions[levelType] || '特殊挑战';

  // 添加特殊关卡标识
  let specialNote = '';
  if (levelId % 10 === 0) {
    specialNote = ' - Boss关卡';
  } else if (SPECIAL_LEVELS.challenge[levelId]) {
    specialNote = ' - 特殊挑战';
  }

  return `${baseDescription} - ${typeDesc}${specialNote}`;
}

/**
 * 生成过关目标说明
 * @param {string} levelType - 关卡类型
 * @param {number} targetScore - 目标分数
 * @param {Object} stageConfig - 阶段配置
 * @returns {Array} 目标列表
 */
export function generateObjectives(levelType, targetScore, stageConfig) {
  const objectives = [];

  // 基础目标 - 只显示最低过关要求（1星分数）
  const oneStarScore = Math.floor(targetScore * 0.6);
  objectives.push(`达到 ${oneStarScore} 分过关`);

  // 根据关卡类型添加特殊目标
  switch (levelType) {
    case LEVEL_TYPES.TIME_ATTACK:
      objectives.push('在限定时间内完成');
      break;
    case LEVEL_TYPES.SURVIVAL:
      objectives.push('坚持到时间结束');
      break;
    case LEVEL_TYPES.NO_ITEMS:
      objectives.push('不使用任何道具');
      break;
    case LEVEL_TYPES.CLEAR_BLOCKS:
      objectives.push('清除所有特殊方块');
      break;
    case LEVEL_TYPES.CHAIN_COMBO:
      objectives.push('达到指定连击数');
      break;
  }

  // 添加额外挑战目标
  if (levelType === LEVEL_TYPES.CLASSIC) {
    objectives.push('尝试获得更高星级');
    objectives.push('使用道具提升分数');
  }

  return objectives;
}

/**
 * 获取时间限制
 * @param {string} levelType - 关卡类型
 * @param {number} levelId - 关卡ID
 * @returns {number} 时间限制（秒，0表示无限制）
 */
export function getTimeLimit(levelType, levelId) {
  switch (levelType) {
    case LEVEL_TYPES.TIME_ATTACK:
      return Math.max(60, 180 - Math.floor(levelId / 10) * 10); // 随关卡递减
    case LEVEL_TYPES.SURVIVAL:
      return 120 + Math.floor(levelId / 5) * 30; // 随关卡递增
    default:
      return 0; // 无时间限制
  }
}

/**
 * 获取特殊目标
 * @param {string} levelType - 关卡类型
 * @param {number} levelId - 关卡ID
 * @returns {Object} 特殊目标配置
 */
export function getSpecialTargets(levelType, levelId) {
  const targets = {};

  switch (levelType) {
    case LEVEL_TYPES.CLEAR_BLOCKS:
      targets.blocksToCllear = Math.min(10, Math.floor(levelId / 3) + 3);
      break;
    case LEVEL_TYPES.CHAIN_COMBO:
      targets.requiredCombo = Math.min(15, Math.floor(levelId / 2) + 5);
      break;
  }

  return targets;
}