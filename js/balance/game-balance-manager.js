/**
 * 游戏平衡管理器
 * 确保道具系统既能帮助卡关玩家又不会让游戏失去挑战性
 */
import Emitter from '../libs/tinyemitter.js';
import { ITEM_TYPES } from '../item/refactored-item-manager.js';

// 平衡策略类型
export const BALANCE_STRATEGIES = {
  ADAPTIVE: 'adaptive',       // 自适应平衡
  PROGRESSIVE: 'progressive', // 渐进式平衡
  DYNAMIC: 'dynamic',        // 动态平衡
  STATIC: 'static'           // 静态平衡
};

// 玩家类型分类
export const PLAYER_TYPES = {
  CASUAL: 'casual',           // 休闲玩家
  CORE: 'core',              // 核心玩家
  HARDCORE: 'hardcore',       // 硬核玩家
  STRUGGLING: 'struggling',   // 困难玩家
  EXPERT: 'expert'           // 专家玩家
};

export default class GameBalanceManager extends Emitter {
  constructor(progressionManager, itemProgressionManager, retryManager) {
    super();
    
    this.progressionManager = progressionManager;
    this.itemProgressionManager = itemProgressionManager;
    this.retryManager = retryManager;
    
    // 平衡配置
    this.balanceConfig = {
      // 道具效果强度调整
      itemPowerScaling: {
        [PLAYER_TYPES.STRUGGLING]: 1.3,  // 困难玩家道具效果增强30%
        [PLAYER_TYPES.CASUAL]: 1.1,      // 休闲玩家道具效果增强10%
        [PLAYER_TYPES.CORE]: 1.0,        // 核心玩家标准效果
        [PLAYER_TYPES.HARDCORE]: 0.9,    // 硬核玩家道具效果减弱10%
        [PLAYER_TYPES.EXPERT]: 0.8       // 专家玩家道具效果减弱20%
      },
      
      // 道具获取频率调整
      acquisitionRateModifier: {
        [PLAYER_TYPES.STRUGGLING]: 1.5,  // 困难玩家获得更多道具
        [PLAYER_TYPES.CASUAL]: 1.2,
        [PLAYER_TYPES.CORE]: 1.0,
        [PLAYER_TYPES.HARDCORE]: 0.8,
        [PLAYER_TYPES.EXPERT]: 0.7
      },
      
      // 道具冷却时间调整
      cooldownModifier: {
        [PLAYER_TYPES.STRUGGLING]: 0.7,  // 困难玩家冷却时间减少30%
        [PLAYER_TYPES.CASUAL]: 0.85,
        [PLAYER_TYPES.CORE]: 1.0,
        [PLAYER_TYPES.HARDCORE]: 1.15,
        [PLAYER_TYPES.EXPERT]: 1.3
      },
      
      // 挑战性保持阈值
      challengeThresholds: {
        minDifficulty: 0.3,    // 最低难度保持30%
        maxAssistance: 0.7,    // 最大辅助不超过70%
        balancePoint: 0.5      // 理想平衡点50%
      }
    };
    
    // 玩家行为数据
    this.playerBehavior = {
      averageSessionTime: 0,
      itemUsageFrequency: {},
      levelCompletionRate: 0,
      retryFrequency: 0,
      progressionSpeed: 0,
      lastAnalysisTime: 0
    };
    
    // 当前玩家类型
    this.currentPlayerType = PLAYER_TYPES.CASUAL;
    
    // 平衡调整历史
    this.adjustmentHistory = [];
    
    this._loadData();
    this._analyzePlayerBehavior();
  }
  
  /**
   * 分析玩家行为并分类
   */
  _analyzePlayerBehavior() {
    const playerData = this.progressionManager.playerData;

    // 安全获取重试统计，如果retryManager为null则使用默认值
    const retryStats = this.retryManager ? this.retryManager.getRetryStats() : {
      totalFailures: 0,
      consecutiveFailures: 0,
      revivesUsed: 0,
      skipsUsed: 0,
      skipsRemaining: 5
    };

    // 计算关键指标
    const avgSessionTime = playerData.totalPlayTime / Math.max(playerData.gamesPlayed, 1);
    const completionRate = this._calculateCompletionRate();
    const itemUsageRate = this._calculateItemUsageRate();
    const progressionSpeed = this._calculateProgressionSpeed();

    // 更新行为数据
    this.playerBehavior = {
      averageSessionTime: avgSessionTime,
      itemUsageFrequency: itemUsageRate,
      levelCompletionRate: completionRate,
      retryFrequency: retryStats.consecutiveFailures,
      progressionSpeed: progressionSpeed,
      lastAnalysisTime: Date.now()
    };
    
    // 分类玩家类型
    this.currentPlayerType = this._classifyPlayer();
    
    // 应用平衡调整
    this._applyBalanceAdjustments();
    
    this.emit('player:analyzed', {
      playerType: this.currentPlayerType,
      behavior: this.playerBehavior
    });
  }
  
  /**
   * 分类玩家类型
   * @returns {string} 玩家类型
   * @private
   */
  _classifyPlayer() {
    const behavior = this.playerBehavior;
    const playerData = this.progressionManager.playerData;
    
    // 困难玩家：低完成率，高重试频率，低进度速度
    if (behavior.levelCompletionRate < 0.3 && 
        behavior.retryFrequency > 3 && 
        behavior.progressionSpeed < 0.5) {
      return PLAYER_TYPES.STRUGGLING;
    }
    
    // 专家玩家：高完成率，低重试频率，高进度速度，低道具使用
    if (behavior.levelCompletionRate > 0.8 && 
        behavior.retryFrequency < 1 && 
        behavior.progressionSpeed > 1.5 &&
        Object.values(behavior.itemUsageFrequency).every(rate => rate < 0.3)) {
      return PLAYER_TYPES.EXPERT;
    }
    
    // 硬核玩家：高完成率，中等重试频率，高进度速度
    if (behavior.levelCompletionRate > 0.7 && 
        behavior.retryFrequency <= 2 && 
        behavior.progressionSpeed > 1.2) {
      return PLAYER_TYPES.HARDCORE;
    }
    
    // 休闲玩家：中等完成率，短游戏时间，中等道具使用
    if (behavior.averageSessionTime < 300 && // 5分钟以下
        behavior.levelCompletionRate >= 0.4 && 
        behavior.levelCompletionRate <= 0.7) {
      return PLAYER_TYPES.CASUAL;
    }
    
    // 默认为核心玩家
    return PLAYER_TYPES.CORE;
  }
  
  /**
   * 应用平衡调整
   * @private
   */
  _applyBalanceAdjustments() {
    const playerType = this.currentPlayerType;
    
    // 调整道具效果强度
    this._adjustItemPower(playerType);
    
    // 调整道具获取频率
    this._adjustAcquisitionRate(playerType);
    
    // 调整道具冷却时间
    this._adjustCooldowns(playerType);
    
    // 记录调整历史
    this.adjustmentHistory.push({
      timestamp: Date.now(),
      playerType,
      adjustments: {
        powerScaling: this.balanceConfig.itemPowerScaling[playerType],
        acquisitionRate: this.balanceConfig.acquisitionRateModifier[playerType],
        cooldownModifier: this.balanceConfig.cooldownModifier[playerType]
      }
    });
    
    // 限制历史记录数量
    if (this.adjustmentHistory.length > 50) {
      this.adjustmentHistory = this.adjustmentHistory.slice(-50);
    }
    
    this.emit('balance:adjusted', {
      playerType,
      adjustments: this.adjustmentHistory[this.adjustmentHistory.length - 1].adjustments
    });
  }
  
  /**
   * 调整道具效果强度
   * @param {string} playerType - 玩家类型
   * @private
   */
  _adjustItemPower(playerType) {
    const powerModifier = this.balanceConfig.itemPowerScaling[playerType];
    
    // 这里可以调整道具管理器中的效果参数
    // 例如：增加火球术的范围、闪电链的连锁数量等
    if (this.itemProgressionManager.itemManager) {
      const itemManager = this.itemProgressionManager.itemManager;
      
      // 调整火球术范围
      if (itemManager.itemEffects[ITEM_TYPES.FIREBALL]) {
        const originalRange = itemManager.itemEffects[ITEM_TYPES.FIREBALL].range || [1, 2, 3];
        itemManager.itemEffects[ITEM_TYPES.FIREBALL].range = originalRange.map(r => r * powerModifier);
      }
      
      // 调整闪电链连锁数量
      if (itemManager.itemEffects[ITEM_TYPES.LIGHTNING]) {
        const originalChain = itemManager.itemEffects[ITEM_TYPES.LIGHTNING].chainCount || [5, 8, 12];
        itemManager.itemEffects[ITEM_TYPES.LIGHTNING].chainCount = 
          originalChain.map(c => Math.floor(c * powerModifier));
      }
      
      // 调整激流行数
      if (itemManager.itemEffects[ITEM_TYPES.WATERFLOW]) {
        const originalRows = itemManager.itemEffects[ITEM_TYPES.WATERFLOW].rowCount || [1, 2, 3];
        itemManager.itemEffects[ITEM_TYPES.WATERFLOW].rowCount = 
          originalRows.map(r => Math.min(5, Math.floor(r * powerModifier))); // 最多5行
      }
    }
  }
  
  /**
   * 调整道具获取频率
   * @param {string} playerType - 玩家类型
   * @private
   */
  _adjustAcquisitionRate(playerType) {
    const rateModifier = this.balanceConfig.acquisitionRateModifier[playerType];
    
    // 调整关卡奖励中的道具数量
    if (this.progressionManager) {
      // 这里可以动态调整关卡完成奖励
      const baseRewards = {
        [ITEM_TYPES.FIREBALL]: 3,
        [ITEM_TYPES.LIGHTNING]: 2,
        [ITEM_TYPES.WATERFLOW]: 1
      };
      
      for (const [itemType, baseAmount] of Object.entries(baseRewards)) {
        const adjustedAmount = Math.floor(baseAmount * rateModifier);
        // 可以在这里更新奖励配置
      }
    }
  }
  
  /**
   * 调整道具冷却时间
   * @param {string} playerType - 玩家类型
   * @private
   */
  _adjustCooldowns(playerType) {
    const cooldownModifier = this.balanceConfig.cooldownModifier[playerType];
    
    if (this.itemProgressionManager.itemManager) {
      const itemManager = this.itemProgressionManager.itemManager;
      
      // 调整所有道具的冷却时间
      for (const itemType of Object.values(ITEM_TYPES)) {
        if (itemManager.maxCooldowns[itemType]) {
          const originalCooldown = itemManager.maxCooldowns[itemType];
          itemManager.maxCooldowns[itemType] = Math.floor(originalCooldown * cooldownModifier);
        }
      }
    }
  }
  
  /**
   * 计算关卡完成率
   * @returns {number} 完成率（0-1）
   * @private
   */
  _calculateCompletionRate() {
    const playerData = this.progressionManager.playerData;

    if (playerData.gamesPlayed === 0) return 0;

    // 安全获取重试统计
    const retryStats = this.retryManager ? this.retryManager.getRetryStats() : {
      totalFailures: 0
    };

    // 简化计算：成功次数 / 总尝试次数
    const successfulGames = playerData.gamesPlayed - retryStats.totalFailures;
    return Math.max(0, successfulGames / playerData.gamesPlayed);
  }
  
  /**
   * 计算道具使用率
   * @returns {Object} 各道具的使用率
   * @private
   */
  _calculateItemUsageRate() {
    const usageStats = this.itemProgressionManager.itemUsageStats;
    const playerData = this.progressionManager.playerData;
    
    const rates = {};
    for (const [itemType, usageCount] of Object.entries(usageStats)) {
      rates[itemType] = playerData.gamesPlayed > 0 ? usageCount / playerData.gamesPlayed : 0;
    }
    
    return rates;
  }
  
  /**
   * 计算进度速度
   * @returns {number} 进度速度（关卡/小时）
   * @private
   */
  _calculateProgressionSpeed() {
    const playerData = this.progressionManager.playerData;
    
    if (playerData.totalPlayTime === 0) return 0;
    
    // 关卡数 / 游戏时间（小时）
    const hoursPlayed = playerData.totalPlayTime / 3600;
    return playerData.level / hoursPlayed;
  }
  
  /**
   * 检查挑战性是否保持
   * @returns {Object} 挑战性分析结果
   */
  checkChallengeLevel() {
    const behavior = this.playerBehavior;
    const thresholds = this.balanceConfig.challengeThresholds;
    
    const analysis = {
      currentDifficulty: 1 - behavior.levelCompletionRate,
      assistanceLevel: this._calculateAssistanceLevel(),
      isBalanced: false,
      recommendations: []
    };
    
    // 检查是否过于简单
    if (analysis.currentDifficulty < thresholds.minDifficulty) {
      analysis.recommendations.push('increase_difficulty');
    }
    
    // 检查是否辅助过多
    if (analysis.assistanceLevel > thresholds.maxAssistance) {
      analysis.recommendations.push('reduce_assistance');
    }
    
    // 检查是否在理想平衡点附近
    const balanceDistance = Math.abs(analysis.currentDifficulty - thresholds.balancePoint);
    analysis.isBalanced = balanceDistance < 0.1;
    
    if (!analysis.isBalanced) {
      if (analysis.currentDifficulty > thresholds.balancePoint) {
        analysis.recommendations.push('provide_more_help');
      } else {
        analysis.recommendations.push('increase_challenge');
      }
    }
    
    return analysis;
  }
  
  /**
   * 计算当前辅助水平
   * @returns {number} 辅助水平（0-1）
   * @private
   */
  _calculateAssistanceLevel() {
    const playerType = this.currentPlayerType;
    const powerModifier = this.balanceConfig.itemPowerScaling[playerType];
    const rateModifier = this.balanceConfig.acquisitionRateModifier[playerType];
    const cooldownModifier = this.balanceConfig.cooldownModifier[playerType];
    
    // 综合计算辅助水平
    const assistanceScore = (powerModifier + rateModifier + (2 - cooldownModifier)) / 4;
    return Math.max(0, Math.min(1, assistanceScore));
  }
  
  /**
   * 获取平衡建议
   * @returns {Array} 建议列表
   */
  getBalanceRecommendations() {
    const challengeAnalysis = this.checkChallengeLevel();
    const recommendations = [];
    
    challengeAnalysis.recommendations.forEach(rec => {
      switch (rec) {
        case 'increase_difficulty':
          recommendations.push({
            type: 'difficulty',
            action: 'increase',
            description: '游戏过于简单，建议增加难度或减少辅助'
          });
          break;
        case 'reduce_assistance':
          recommendations.push({
            type: 'assistance',
            action: 'reduce',
            description: '辅助过多，建议减少道具效果或增加冷却时间'
          });
          break;
        case 'provide_more_help':
          recommendations.push({
            type: 'help',
            action: 'increase',
            description: '玩家遇到困难，建议增加辅助或道具奖励'
          });
          break;
        case 'increase_challenge':
          recommendations.push({
            type: 'challenge',
            action: 'increase',
            description: '可以适当增加挑战性，保持游戏趣味'
          });
          break;
      }
    });
    
    return recommendations;
  }
  
  /**
   * 强制重新分析（用于测试或特殊情况）
   */
  forceReanalysis() {
    this._analyzePlayerBehavior();
  }
  
  /**
   * 获取当前平衡状态
   * @returns {Object} 平衡状态信息
   */
  getBalanceStatus() {
    return {
      playerType: this.currentPlayerType,
      behavior: this.playerBehavior,
      challengeLevel: this.checkChallengeLevel(),
      recommendations: this.getBalanceRecommendations(),
      lastAdjustment: this.adjustmentHistory[this.adjustmentHistory.length - 1] || null
    };
  }
  
  /**
   * 保存数据
   * @private
   */
  _saveData() {
    try {
      const data = {
        playerBehavior: this.playerBehavior,
        currentPlayerType: this.currentPlayerType,
        adjustmentHistory: this.adjustmentHistory
      };

      // 验证数据是否可以序列化
      const jsonString = this._safeStringify(data);
      if (jsonString === null) {
        console.error('游戏平衡数据序列化失败，跳过保存');
        return;
      }

      wx.setStorageSync('gameBalanceData', jsonString);
      console.log('游戏平衡数据保存成功');
    } catch (e) {
      console.error('保存游戏平衡数据失败:', e);
    }
  }

  /**
   * 安全的JSON序列化
   * @param {Object} obj - 要序列化的对象
   * @returns {string|null} 序列化后的字符串，失败返回null
   * @private
   */
  _safeStringify(obj) {
    try {
      return JSON.stringify(obj, (key, value) => {
        // 过滤掉函数、Symbol、undefined等不可序列化的值
        if (typeof value === 'function' || typeof value === 'symbol' || value === undefined) {
          return null;
        }
        return value;
      });
    } catch (e) {
      console.error('JSON序列化失败:', e);
      return null;
    }
  }
  
  /**
   * 加载数据
   * @private
   */
  _loadData() {
    try {
      const data = wx.getStorageSync('gameBalanceData');
      
      if (data) {
        const parsed = JSON.parse(data);
        
        this.playerBehavior = { ...this.playerBehavior, ...parsed.playerBehavior };
        this.currentPlayerType = parsed.currentPlayerType || PLAYER_TYPES.CASUAL;
        this.adjustmentHistory = parsed.adjustmentHistory || [];
      }
    } catch (e) {
      console.error('加载游戏平衡数据失败:', e);
    }
  }
}
