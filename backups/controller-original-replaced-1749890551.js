/**
 * 游戏控制器
 * 负责管理游戏流程和状态
 */
import Grid from './grid.js';
import Tetromino, { TETROMINO_SHAPES } from './tetromino.js';
import <PERSON><PERSON><PERSON><PERSON> from './match-checker.js';
import GarbageGenerator from './garbage-generator.js';
import ComboSystem from './combo-system.js';
import ComboDisplay from '../ui/combo-display.js';
import ComboNotification from '../ui/combo-notification.js';
import { BLOCK_EFFECTS } from './block.js';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render.js';
import Emitter from '../libs/tinyemitter.js';
// 游戏状态
export const GAME_STATE = {
  READY: 'ready',         // 准备开始
  PLAYING: 'playing',     // 游戏中
  CHECKING: 'checking',   // 检查匹配
  ANIMATING: 'animating', // 动画中
  PAUSED: 'paused',       // 暂停
  GAME_OVER: 'gameover',  // 游戏结束
  LEVEL_CLEAR: 'levelclear' // 关卡通关
};

// 游戏配置
const SOFT_DROP_SPEED = 20;  // 软下落的速度倍数
const BASE_SPEED = 30;       // 基础下落间隔帧数
const LOCK_DELAY = 30;       // 锁定延迟帧数（恢复为30，但现在每帧都会增加）
const EFFECTS_DELAY = 15;    // 特效延迟帧数
const TETROMINO_COLOR_DISTRIBUTION = 'random';  // 方块颜色分布随机
// const TETROMINO_COLOR_DISTRIBUTION = 'single';  // 方块颜色分布单色

export default class GameController extends Emitter {
  /**
   * 创建游戏控制器
   * @param {Object} options - 游戏配置
   */
  constructor(options = {}) {
    super();
    
    // 合并默认选项和提供的选项
    this.options = Object.assign({
      level: 1,
      colorCount: 4,
      effectProbability: 0.1,
    }, options);
    
    // 创建游戏网格
    this.grid = new Grid();
    
    // 创建匹配检测器
    this.matchChecker = new MatchChecker(this.grid);

    // 创建连击系统
    this.comboSystem = new ComboSystem();

    // 创建连击显示UI（调整位置避免与其他UI重叠）
    this.comboDisplay = new ComboDisplay({
      x: 20,   // 移到左侧，避免与右侧道具UI冲突
      y: 120,  // 下移避免与顶部UI冲突
      width: 200,
      height: 100
    });

    // 创建连击通知系统
    this.comboNotification = new ComboNotification();

    // 设置连击系统事件监听
    this._setupComboSystemEvents();

    // 创建垃圾生成器
    this.garbageGenerator = new GarbageGenerator({
      enabled: this.options.garbageEnabled !== false,
      level: this.options.level,
      colorCount: this.options.colorCount,
      allowedEffects: this.options.allowedEffects || ['frozen'],
      baseInterval: this.options.garbageInterval || 1800,
      baseDensity: this.options.garbageDensity || 0.6
    });
    this.garbageGenerator.setGrid(this.grid);

    // 游戏状态
    this.state = GAME_STATE.READY;
    
    // 当前方块
    this.currentTetromino = null;
    
    // 下一个方块
    this.nextTetromino = null;
    
    // 保存最后锁定的方块位置
    this.lastTetrominoPositions = null;
    
    // 计时器
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.animationTimer = 0;
    this.effectsTimer = 0;
    
    // 锁定重置计数
    this.lockResetCount = 0;
    this.maxLockResets = 15; // 最多允许重置15次
    
    // 得分
    this.score = 0;
    this.combo = 0;
    
    // 游戏控制
    this.isPaused = false;
    this.isSoftDropping = false;
    
    // 动画状态
    this.hasAnimations = false;
    
    // 检测状态
    this.hasMatches = false;
    this.effectsToApply = [];
    this.affectedBlocks = new Set();
    
    // 跟踪最后锁定的方块组合
    this.lastLockedBlocks = new Set();
    
    // 初始化输入处理
    this._initInput();
    
    this.destroyDuration = 20; // 消除动画持续时间
    
    // 记录被消除的方块位置（用于下落逻辑）
    this.lastRemovedPositions = [];
    
    // 动画完成后的检查标记
    this._pendingAfterAnimationCheck = false;
    
    // 道具管理器将在Main类中初始化

    // 🔍 调试：检查构造函数结束时的分数
    console.log(`🎮 [${Date.now()}] GameController构造函数完成 - score=${this.score}`);
  }
  
  /**
   * 初始化输入处理
   * @private
   */
  _initInput() {
    console.log('initInput', wx);
    
    // 保存事件处理函数的引用，以便后续移除
    this._keyDownHandler = (res) => {
      console.log('key', res);
      const { key } = res;
      if (this.state !== GAME_STATE.PLAYING) return;
      
      switch (key) {
        case 'ArrowLeft':
          this._handleLeft();
          break;
        case 'ArrowRight':
          this._handleRight();
          break;
        case 'ArrowDown':
          this._handleSoftDrop(true);
          break;
        case 'ArrowUp':
        case 'w':
          this._handleRotate(); // 添加旋转按键 - 上箭头或W键
          break;
        case ' ': // 空格键
          this._handleHardDrop();
          break;
      }
    };
    
    this._keyUpHandler = (res) => {
      const { key } = res;
      
      if (key === 'ArrowDown') {
        this._handleSoftDrop(false);
      }
    };
    
    // 注册键盘事件
    wx.onKeyDown(this._keyDownHandler);
    wx.onKeyUp(this._keyUpHandler);
    
    // 触摸输入（已移除直接的wx.onTouch监听，改为通过main.js统一处理）
    this.touchStartX = 0;
    this.touchStartY = 0;
    this.isTouchHold = false;
    this.touchHoldTimer = 0;

    console.log('Controller: 触摸事件将由 main.js 统一处理，通过GameInfo转发');
  }
  
  /**
   * 开始游戏
   */
  start() {
    console.log(`🎮 [${Date.now()}] GameController.start() 开始 - score=${this.score}`);

    this.state = GAME_STATE.PLAYING;
    this.score = 0;
    this.combo = 0;

    console.log(`🎮 [${Date.now()}] GameController.start() 重置分数后 - score=${this.score}`);

    // 启动垃圾生成器
    this.garbageGenerator.start();

    // 生成第一个方块作为当前方块
    this._generateRandomTetromino();

    // 移除了自动触发的预览测试功能

    this.emit('gamestart');
    console.log(`🎮 [${Date.now()}] GameController.start() 完成 - score=${this.score}`);
  }
  
  /**
   * 暂停游戏
   */
  pause() {
    // 修改暂停逻辑，存储之前的状态
    if (this.state === GAME_STATE.PLAYING || this.state === GAME_STATE.ANIMATING || 
        this.state === GAME_STATE.CHECKING) {
      // 保存当前状态，以便恢复时知道之前是什么状态
      this.previousState = this.state;
      
      // 保存当前下落方块的状态
      if (this.currentTetromino) {
        this.savedTetromino = {
          shape: this.currentTetromino.shape,
          position: { ...this.currentTetromino.position },
          rotation: this.currentTetromino.rotation,
          blocks: this.currentTetromino.blocks.map(block => ({
            row: block.row,
            col: block.col,
            color: block.color,
            effect: block.effect
          }))
        };
        
        // 保存下落计时器状态
        this.savedFallTimer = this.fallTimer;
        this.savedLockTimer = this.lockTimer;
      }
      
      this.state = GAME_STATE.PAUSED;

      // 暂停垃圾生成器
      this.garbageGenerator.pause();

      console.log('游戏暂停，之前状态：', this.previousState, this.state);
      this.emit('gamepause');
    }
  }
  
  /**
   * 继续游戏
   */
  resume() {
    if (this.state === GAME_STATE.PAUSED) {
      // 恢复之前的状态，如果没有保存则默认为PLAYING
      this.state = this.previousState || GAME_STATE.PLAYING;
      this.previousState = null;
      
      // 恢复保存的方块状态
      if (this.savedTetromino) {
        // 如果当前没有方块或生成了新方块，恢复之前保存的方块
        if (!this.currentTetromino || 
            this.currentTetromino.shape !== this.savedTetromino.shape ||
            this.currentTetromino.position.row !== this.savedTetromino.position.row ||
            this.currentTetromino.position.col !== this.savedTetromino.position.col) {
            
          // 重新创建方块对象
          this.currentTetromino = new Tetromino(this.savedTetromino.shape);
          this.currentTetromino.position = { ...this.savedTetromino.position };
          this.currentTetromino.rotation = this.savedTetromino.rotation;
          
          // 如果需要，可以进一步还原方块的颜色和效果
          if (this.savedTetromino.blocks && this.savedTetromino.blocks.length > 0) {
            // 确保方块数量匹配
            if (this.currentTetromino.blocks.length === this.savedTetromino.blocks.length) {
              for (let i = 0; i < this.currentTetromino.blocks.length; i++) {
                const savedBlock = this.savedTetromino.blocks[i];
                if (savedBlock) {
                  this.currentTetromino.blocks[i].color = savedBlock.color;
                  this.currentTetromino.blocks[i].effect = savedBlock.effect;
                }
              }
            }
          }
        }
        
        // 恢复计时器状态
        if (typeof this.savedFallTimer === 'number') {
          this.fallTimer = this.savedFallTimer;
        }
        if (typeof this.savedLockTimer === 'number') {
          this.lockTimer = this.savedLockTimer;
        }
        
        // 清除保存的状态
        this.savedTetromino = null;
        this.savedFallTimer = null;
        this.savedLockTimer = null;
      }
      
      // 恢复垃圾生成器
      this.garbageGenerator.resume();

      // 设置恢复标志，防止立即生成新方块
      this.justResumed = true;

      console.log('游戏继续，恢复状态：', this.state);
      this.emit('gameresume');
    }
  }
  
  /**
   * 重置游戏
   */
  reset() {
    // 重置游戏网格
    this.grid = new Grid();
    this.matchChecker = new MatchChecker(this.grid);

    // 重置垃圾生成器
    this.garbageGenerator.stop();
    this.garbageGenerator.setGrid(this.grid);

    // 重置游戏状态
    this.state = GAME_STATE.READY;
    this.currentTetromino = null;
    this.nextTetromino = null;
    this.score = 0;
    this.combo = 0;

    // 重置计时器
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.animationTimer = 0;

    this.emit('gamereset');
  }
  
  /**
   * 游戏结束
   */
  gameOver() {
    console.log('游戏结束');

    // 设置游戏状态为结束
    this.state = GAME_STATE.GAME_OVER;

    // 停止垃圾生成器
    if (this.garbageGenerator) {
      this.garbageGenerator.stop();
      // 🔧 确保垃圾生成器完全停止
      this.garbageGenerator.isActive = false;
      this.garbageGenerator.timer = 0;
      this.garbageGenerator.isWarning = false;
      this.garbageGenerator.warningTimer = 0;
    }

    // 清除当前方块和下一个方块
    this.currentTetromino = null;
    this.nextTetromino = null;

    // 重置各种计时器和标记
    this.lockTimer = 0;
    this.fallTimer = 0;
    this._pendingAfterAnimationCheck = false;

    // 清除临时数据
    this.lastTetrominoPositions = null;
    this.lastRemovedPositions = [];

    // 发出游戏结束事件
    this.emit('gameover', { score: this.score });
  }
  
  /**
   * 生成随机方块
   * @private
   */
  _generateRandomTetromino() {
    // 首先检查游戏是否已经结束，如果是则不生成新方块
    if (this.state === GAME_STATE.GAME_OVER) {
      console.log('游戏已结束，停止生成新方块');
      return;
    }
    
    console.log('🎲 _generateRandomTetromino() 开始执行');
    
    // 使用下一个方块（如果有）
    if (this.nextTetromino) {
      console.log(`✅ 使用已存在的nextTetromino作为currentTetromino: 形状=${this.nextTetromino.shape}, 效果=${this.nextTetromino.effect || '无'}`);
      this.currentTetromino = this.nextTetromino;
    } else {
      // 生成一个随机方块
      const shapes = Object.keys(TETROMINO_SHAPES);
      const randomShape = shapes[Math.floor(Math.random() * shapes.length)];
      
      console.log(`🆕 生成新的currentTetromino: 形状=${randomShape}`);
      this.currentTetromino = new Tetromino(randomShape, {
        colorDistribution: TETROMINO_COLOR_DISTRIBUTION,
        effectProbability: this.options.effectProbability,
        colorCount: this.options.colorCount,
        allowedEffects: this.options.allowedEffects || ['frozen', 'mine']
      });
    }
    
    // 重置方块位置
    this.currentTetromino.position = {
      row: 0,
      col: Math.floor(this.grid.cols / 2) - 2
    };

    // 生成下一个方块
    const nextShapes = Object.keys(TETROMINO_SHAPES);
    const nextRandomShape = nextShapes[Math.floor(Math.random() * nextShapes.length)];

    console.log(`🔮 生成新的nextTetromino: 形状=${nextRandomShape}`);
    this.nextTetromino = new Tetromino(nextRandomShape, {
      colorDistribution: TETROMINO_COLOR_DISTRIBUTION,
      effectProbability: this.options.effectProbability,
      colorCount: this.options.colorCount,
      allowedEffects: this.options.allowedEffects || ['frozen', 'mine']
    });

    // 重置锁定计时器和重置次数
    this.lockTimer = 0;
    this.lockResetCount = 0;

    // 重置快速下落状态，防止传播到新方块
    this.isSoftDropping = false;
    
    // 检查游戏是否结束
    if (!this.currentTetromino.isValidPosition(this.grid)) {
      this.state = GAME_STATE.GAME_OVER;
      this.emit('gameover', { score: this.score });
      // 清除当前方块，确保不再处理它
      this.currentTetromino = null;
      this.nextTetromino = null;
      return;
    }
    
    // 进入游戏状态
    this.state = GAME_STATE.PLAYING;
    
    console.log(`✅ _generateRandomTetromino() 完成: currentTetromino形状=${this.currentTetromino.shape}, nextTetromino形状=${this.nextTetromino.shape}`);
  }
  
  // 重置锁定计时器的方法，集中处理重置逻辑
  _resetLockTimerIfPossible() {
    // 只有在方块还能下落或者还没达到最大重置次数时才重置
    if (this.currentTetromino.canMoveDown(this.grid)) {
      this.lockTimer = 0;
      this.lockResetCount = 0; // 如果方块可以下落，重置计数
      return true;
    } else if (this.lockResetCount < this.maxLockResets) {
      this.lockTimer = 0;
      this.lockResetCount++;
      console.log(`锁定计时器重置 (${this.lockResetCount}/${this.maxLockResets})`);
      return true;
    }
    return false;
  }
  
  /**
   * 处理左移操作
   * @private
   */
  _handleLeft() {
    if (!this.currentTetromino || this.state !== GAME_STATE.PLAYING) return;

    // 尝试左移
    this.currentTetromino.moveLeft();

    // 如果新位置无效，则还原
    if (!this.currentTetromino.isValidPosition(this.grid)) {
      this.currentTetromino.moveRight();
      return;
    }

    // 尝试重置锁定计时器
    this._resetLockTimerIfPossible();

    // 播放移动音效
    GameGlobal.musicManager.playMove();
  }
  
  /**
   * 处理右移操作
   * @private
   */
  _handleRight() {
    if (!this.currentTetromino || this.state !== GAME_STATE.PLAYING) return;

    // 尝试右移
    this.currentTetromino.moveRight();

    // 如果新位置无效，则还原
    if (!this.currentTetromino.isValidPosition(this.grid)) {
      this.currentTetromino.moveLeft();
      return;
    }

    // 尝试重置锁定计时器
    this._resetLockTimerIfPossible();

    // 播放移动音效
    GameGlobal.musicManager.playMove();
  }
  
  /**
   * 处理旋转操作
   * @private
   */
  _handleRotate() {
    if (!this.currentTetromino || this.state !== GAME_STATE.PLAYING) return;
    
    // 保存原始状态
    const originalRotation = this.currentTetromino.rotation;
    const originalPosition = { ...this.currentTetromino.position };
    
    // 尝试旋转
    this.currentTetromino.rotate();
    
    // 如果新位置无效，尝试墙踢
    if (!this.currentTetromino.isValidPosition(this.grid)) {
      // 不同形状有不同的墙踢策略
      const shape = this.currentTetromino.shape;
      let validPositionFound = false;
      
      // I型方块特殊处理
      if (shape === 'I') {
        // I型方块的特殊墙踢表（简化版SRS系统）
        const kickTable = [
          [-1, 0], [1, 0], [-2, 0], [2, 0],  // 水平移动尝试
          [0, -1], [0, 1],                   // 垂直移动尝试
          [-1, -1], [1, -1], [-1, 1], [1, 1] // 对角移动尝试
        ];
        
        // 尝试所有可能的墙踢位置
        for (const [dx, dy] of kickTable) {
          // 保存当前位置
          const currentPos = { ...this.currentTetromino.position };
          
          // 应用偏移
          this.currentTetromino.position.col += dx;
          this.currentTetromino.position.row += dy;
          
          // 检查新位置是否有效
          if (this.currentTetromino.isValidPosition(this.grid)) {
            validPositionFound = true;
            break;
          }
          
          // 恢复位置
          this.currentTetromino.position = { ...currentPos };
        }
      } else {
        // 其他形状的墙踢逻辑
        // 尝试左移一格
        this.currentTetromino.moveLeft();
        
        if (!this.currentTetromino.isValidPosition(this.grid)) {
          // 尝试右移两格
          this.currentTetromino.moveRight();
          this.currentTetromino.moveRight();
          
          if (!this.currentTetromino.isValidPosition(this.grid)) {
            // 尝试左移一格并上移一格
            this.currentTetromino.moveLeft();
            this.currentTetromino.position.row -= 1;
            
            if (!this.currentTetromino.isValidPosition(this.grid)) {
              // 尝试下移一格
              this.currentTetromino.position.row += 2;
              
              if (!this.currentTetromino.isValidPosition(this.grid)) {
                // 所有尝试都失败，恢复原始状态
                this.currentTetromino.position.row -= 1;
                validPositionFound = false;
              } else {
                validPositionFound = true;
              }
            } else {
              validPositionFound = true;
            }
          } else {
            validPositionFound = true;
          }
        } else {
          validPositionFound = true;
        }
      }
      
      // 如果所有尝试都失败，恢复原始旋转状态
      if (!validPositionFound) {
        this.currentTetromino.position = originalPosition;
        this.currentTetromino.rotation = originalRotation;
        return; // 旋转失败，不重置锁定计时器
      }
    }

    // 尝试重置锁定计时器
    this._resetLockTimerIfPossible();

    // 播放旋转音效
    GameGlobal.musicManager.playRotate();
  }
  
  /**
   * 处理软下落操作
   * @param {boolean} isDown - 是否按下软下落键
   * @private
   */
  _handleSoftDrop(isDown) {
    if (!this.currentTetromino || this.state !== GAME_STATE.PLAYING) {
      return;
    }

    // 更新软下落状态
    this.isSoftDropping = isDown;

    // 如果按下了软下落键，尝试立即下落一格
    if (isDown && this.currentTetromino.canMoveDown(this.grid)) {
      this.currentTetromino.moveDown();
      // 重置锁定计时器
      this.lockTimer = 0;
    } else if (!isDown) {
      // 如果软下落键松开，重置下落计时器，恢复正常下落速度
      this.fallTimer = 0;
    }
  }
  
  /**
   * 处理硬下落操作（直接下落到底部）
   * @private
   */
  _handleHardDrop() {
    if (!this.currentTetromino || this.state !== GAME_STATE.PLAYING) return;

    // 获取可以下落的距离
    const { dropDistance } = this.currentTetromino.getGhostPosition(this.grid);

    if (dropDistance > 0) {
      // 立即下落
      this.currentTetromino.hardDrop(dropDistance);

      // 🎯 选项1：完全移除硬降分数，只保留快速放置功能
      console.log(`⬇️ 硬降: 下落${dropDistance}格，立即锁定`);

      // 立即锁定
      this._lockTetromino();
    }
  }
  
  /**
   * 锁定当前方块
   * @private
   */
  _lockTetromino() {
    console.log('锁定方块');
    
    if (!this.currentTetromino) {
      console.error('尝试锁定不存在的方块');
      return;
    }
    
    // 清空上一次锁定的方块集合
    if (this.lastLockedBlocks) {
      this.lastLockedBlocks.clear();
    }
    
    // 获取当前方块组合中所有方块的位置
    const positions = this.currentTetromino.getBlockPositions();
    
    // 保存当前方块的位置和形状信息，以便后续处理
    this.lastTetrominoPositions = positions.map(({ row, col, block }) => {
      return {
        row,
        col,
        block // 保存实际方块引用，不再使用克隆
      };
    });
    
    // 放置方块到网格
    this.currentTetromino.placeOnGrid(this.grid);
    
    // 记录刚刚锁定的方块
    if (this.lastLockedBlocks) {
      for (const { row, col } of positions) {
        // 这里的row和col已经是绝对位置，不需要再加上currentTetromino的position
        const block = this.grid.getBlock(row, col);
        if (block) {
          this.lastLockedBlocks.add(block);
        }
      }
    }
    
    // 确保锁定状态
    this.currentTetromino.lock();
    
    // 🔧 优化游戏结束检测：更精确的判断条件
    // 只有当锁定的方块位置在最顶行（row = 0）时才判断游戏结束
    // 这避免了垃圾行推移导致的误判
    const isGameOver = positions.some(({ row }) => row <= 0);
    
    if (isGameOver) {
      console.log('方块锁定在顶行（row ≤ 0），游戏结束');
      this.gameOver();
      return;
    }
    
    // 确保任何上一轮地雷爆炸的数据不会干扰新的检查
    // 这是关键修复：防止地雷爆炸数据影响新锁定的方块
    this.lastRemovedPositions = [];
    
    // 切换到检查状态
    this.state = GAME_STATE.CHECKING;
    
    // 重置锁定相关计时器和计数
    this.lockTimer = 0;
    this.lockResetCount = 0;
    
    this.currentTetromino = null;
    
    // 发出锁定事件
    this.emit('tetromino:lock', { positions });
  }
  
  /**
   * 检查和处理满行
   * @private
   * @returns {boolean} 如果有满行被消除则返回true
   */
  _checkAndClearFullRows() {
    const fullRows = [];
    
    // 检查哪些行是满的
    for (let row = 0; row < this.grid.rows; row++) {
      if (this.grid.isRowFull(row)) {
        fullRows.push(row);
      }
    }
    
    // 如果没有满行，返回false
    if (fullRows.length === 0) {
      return false;
    }
    
    // 记录每一行的方块，用于动画和消除
    for (const row of fullRows) {
      console.log(`🔍 处理满行 ${row}，开始检查每个方块:`);
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          console.log(`  ➤ 方块[${row},${col}]: 颜色=${block.color}, 特效=${block.effect}, 冰冻=${block.isFrozen}`);
          
          // 特殊处理冰冻方块
          if (block.effect === BLOCK_EFFECTS.FROZEN && block.isFrozen) {
            // 冰冻方块第一次匹配只解除冰冻效果，不消除
            block.setEffect(BLOCK_EFFECTS.NONE);
            console.log(`❄️ 冰冻方块在行${row}列${col}解除冰冻效果但不消除`);
          } else {
            // 对于非冰冻方块或已解冻的方块，正常消除
            this.matchChecker.matchedBlocks.add(block);
            // 标记方块开始消除动画
            const animStarted = block.startDestroyAnimation();
            console.log(`🔥 普通方块[${row},${col}]加入消除列表，动画启动结果: ${animStarted}`);
          }
        } else {
          console.log(`  ➤ 方块[${row},${col}]: 空位置！这不应该发生在满行中`);
        }
      }
    }
    
    // 计算消除的方块数量（用于连击系统）
    const matchCount = fullRows.length * this.grid.cols;

    // 增加连击
    this.combo++;

    // 🎯 重要修复：满行消除也要添加到连击系统
    this.comboSystem.addCombo(matchCount, []);

    // 计算分数（使用连击系统的倍数）
    const comboMultiplier = this.comboSystem.getComboMultiplier();
    const lineScore = [100, 300, 500, 800]; // 1行、2行、3行、4行的分数
    const scoreIndex = Math.min(fullRows.length - 1, lineScore.length - 1);

    // 🎯 修复：使用更合理的关卡倍数，避免分数过度膨胀
    const levelMultiplier = 1 + Math.floor(this.options.level / 10) * 0.5; // 每10关增加0.5倍
    const baseRowScore = lineScore[scoreIndex] * levelMultiplier;
    const rowScore = Math.floor(baseRowScore * comboMultiplier);

    const oldScore = this.score;
    this.score += rowScore;

    // 🎯 重要修复：立即同步到全局分数
    GameGlobal.databus.score = this.score;

    // 满行消除日志
    console.log(`📏 满行消除! 行数: ${fullRows.length}, 连击: ${this.comboSystem.combo}, 关卡倍数: ${levelMultiplier.toFixed(1)}x, 分数: ${baseRowScore} × ${comboMultiplier.toFixed(1)}x = ${rowScore} (${oldScore} → ${this.score})`);

    // 发出满行清除事件
    this.emit('rows:clear', { rowCount: fullRows.length, score: rowScore, combo: this.combo });

    // 🔍 添加满行消除后的网格状态调试
    console.log(`🔍 ===== 满行消除标记完成后的网格状态 =====`);
    if (typeof debugGridState === 'function') {
      debugGridState();
    } else {
      console.log('❌ debugGridState函数不可用');
    }
    console.log(`🔍 ===== 满行消除标记完成后的网格状态结束 =====`);

    // 切换到动画状态
    this.state = GAME_STATE.ANIMATING;
    this.animationTimer = 0;

    return true;
  }
  
  /**
   * 处理行被消除后的下落逻辑
   * @private
   */
  _handleRowClear() {
    console.log('处理行消除后的下落');
    
    const removedPositions = this.lastRemovedPositions || [];
    console.log('行消除使用保存的位置信息:', removedPositions);
    
    // 🎯 详细调试：打印当前网格状态
    console.log('=== 当前网格状态分析 ===');
    for (let row = 0; row < this.grid.rows; row++) {
      let blockCount = 0;
      for (let col = 0; col < this.grid.cols; col++) {
        if (this.grid.getBlock(row, col)) {
          blockCount++;
        }
      }
      if (blockCount > 0) {
        console.log(`行${row}: ${blockCount}个方块`);
      } else {
        console.log(`行${row}: 完全为空`);
      }
    }
    console.log('=== 网格状态分析结束 ===');
    
    // 检查当前是否已经有完全空的行（被消除的行）
    let hasFullyEmptyRows = false;
    let fullyEmptyRows = [];
    
    for (let row = 0; row < this.grid.rows; row++) {
      if (this.grid.isRowEmpty(row)) {
        hasFullyEmptyRows = true;
        fullyEmptyRows.push(row);
        console.log(`检测到完全空行: ${row}`);
      }
    }
    
    // 如果有完全空的行，使用传统的整行下移逻辑
    if (hasFullyEmptyRows) {
      console.log(`需要处理${fullyEmptyRows.length}个完全空行的下落`);
      
      // 先清空所有空行，确保没有残留方块
      for (const row of fullyEmptyRows) {
        for (let col = 0; col < this.grid.cols; col++) {
          const block = this.grid.getBlock(row, col);
          if (block) {
            console.log(`强制移除残留方块 [${row}, ${col}]`);
            this.grid.removeBlock(row, col);
          }
        }
      }
      
      // 使用新的多行下移逻辑
      const hasFallen = this._handleMultipleRowClear(fullyEmptyRows);
      console.log(`多行下移结果: ${hasFallen ? '有方块下落' : '没有方块下落'}`);
      
      // 🎯 新增：如果主算法失败，尝试使用备用V2算法
      if (!hasFallen && fullyEmptyRows.length > 0) {
        console.log('主算法未检测到下落，尝试V2备用算法');
        const hasFailedV2 = this._handleMultipleRowClearV2(fullyEmptyRows);
        console.log(`V2算法结果: ${hasFailedV2 ? '有方块下落' : '没有方块下落'}`);
      }
    } 
    // 如果没有完全空行但有被消除的位置，说明是部分消除（冰冻方块情况）
    else if (removedPositions.length > 0) {
      console.log('处理部分消除（含冰冻方块）的下落');
      
      // 按列处理下落，类似激流道具的逻辑
      const hasFallen = this._handlePartialRowClear(removedPositions);
      console.log(`部分消除下移结果: ${hasFallen ? '有方块下落' : '没有方块下落'}`);
    }
    
    // 清除被移动方块的标记，防止重复检查
    this.lastRemovedPositions = [];
  }

  /**
   * 处理部分行消除后的下落逻辑（当行中有冰冻方块时）
   * @param {Array} removedPositions - 被移除的方块位置
   * @returns {boolean} 是否有方块下落
   * @private
   */
  _handlePartialRowClear(removedPositions) {
    console.log('处理部分行消除，被移除位置:', removedPositions);
    
    // 按列分组被移除的方块
    const removedByColumn = {};
    for (const position of removedPositions) {
      if (position && typeof position.col === 'number' && typeof position.row === 'number') {
        if (!removedByColumn[position.col]) {
          removedByColumn[position.col] = [];
        }
        removedByColumn[position.col].push(position.row);
      }
    }
    
    let hasFallen = false;
    
    // 对每个受影响的列进行处理
    for (const colStr of Object.keys(removedByColumn)) {
      const col = parseInt(colStr);
      const removedRows = removedByColumn[col];
      
      console.log(`处理列${col}，被移除的行:`, removedRows);
      
      // 对这列进行下落处理
      const columnHasFallen = this._handleColumnDrop(col, removedRows);
      if (columnHasFallen) {
        hasFallen = true;
      }
    }
    
    return hasFallen;
  }

  /**
   * 处理单列的方块下落
   * @param {number} col - 列索引
   * @param {Array<number>} removedRows - 该列中被移除的行
   * @returns {boolean} 是否有方块下落
   * @private
   */
  _handleColumnDrop(col, removedRows) {
    console.log(`处理列${col}下落，移除的行:`, removedRows);
    
    // 对移除的行进行排序，从下到上处理
    const sortedRemovedRows = [...removedRows].sort((a, b) => b - a);
    let hasFallen = false;
    
    // 对每个被移除的位置，让其上方的方块下落
    for (const removedRow of sortedRemovedRows) {
      // 从被移除位置的上方开始，向上查找需要下落的方块
      for (let sourceRow = removedRow - 1; sourceRow >= 0; sourceRow--) {
        const block = this.grid.getBlock(sourceRow, col);
        
        if (block) {
          // 找到这个方块可以下落到的最低位置
          const targetRow = this._findLowestAvailableRowInColumn(col, sourceRow);
          
          if (targetRow > sourceRow) {
            console.log(`列${col}下落：第${sourceRow}行 -> 第${targetRow}行`);
            
            // 移除原位置
            this.grid.removeBlock(sourceRow, col);
            
            // 放置到新位置
            this.grid.placeBlock(block, targetRow, col);
            
            // 创建下落动画
            this.grid.addFallingAnimation(block, sourceRow, col, targetRow, col);
            
            hasFallen = true;
          }
        }
      }
    }
    
    return hasFallen;
  }

  /**
   * 在指定列中找到方块可以下落到的最低位置
   * @param {number} col - 列索引
   * @param {number} sourceRow - 源行位置
   * @returns {number} 目标行位置
   * @private
   */
  _findLowestAvailableRowInColumn(col, sourceRow) {
    // 从底部开始向上查找第一个空位置
    for (let targetRow = this.grid.rows - 1; targetRow > sourceRow; targetRow--) {
      if (!this.grid.getBlock(targetRow, col)) {
        return targetRow;
      }
    }
    
    // 如果没有找到空位置，返回原位置（不移动）
    return sourceRow;
  }

  /**
   * 处理多行消除后的方块下移
   * @param {Array<number>} emptyRows - 空行数组
   * @returns {boolean} 是否有方块下移
   * @private
   */
  _handleMultipleRowClear(emptyRows) {
    if (emptyRows.length === 0) return false;
    
    console.log('处理多行消除，空行:', emptyRows);
    
    // 排序空行，从上到下
    emptyRows.sort((a, b) => a - b);
    
    let hasFallen = false;
    
    // 🎯 重要修复：从下往上遍历行，避免连锁错误移动
    // 这样可以确保下方行先移动，避免上方行与下方行混合的问题
    for (let row = this.grid.rows - 1; row >= 0; row--) {
      // 计算当前行下方有多少个空行
      const emptyRowsBelowCount = emptyRows.filter(emptyRow => emptyRow > row).length;
      
      // 如果下方有空行，且当前行不是空行，则需要下移
      if (emptyRowsBelowCount > 0 && !emptyRows.includes(row)) {
        // 检查当前行是否有方块需要移动
        let hasBlocksInRow = false;
        for (let col = 0; col < this.grid.cols; col++) {
          if (this.grid.getBlock(row, col)) {
            hasBlocksInRow = true;
            break;
          }
        }
        
        if (hasBlocksInRow) {
          // 移动整行方块
          const targetRow = row + emptyRowsBelowCount;
          console.log(`从下往上处理：移动第${row}行到第${targetRow}行，下移${emptyRowsBelowCount}行`);
          
          // 🎯 重要修复：确保目标行为空，如果不为空则说明算法有误
          let targetRowEmpty = true;
          for (let col = 0; col < this.grid.cols; col++) {
            if (this.grid.getBlock(targetRow, col)) {
              targetRowEmpty = false;
              console.error(`目标行${targetRow}列${col}不为空，存在方块！这不应该发生`);
              break;
            }
          }
          
          if (!targetRowEmpty) {
            console.error(`跳过移动第${row}行，因为目标行${targetRow}不为空`);
            continue;
          }
          
          for (let col = 0; col < this.grid.cols; col++) {
            const block = this.grid.getBlock(row, col);
            if (block) {
              // 移除原位置的方块
              this.grid.removeBlock(row, col);
              
              // 放置到新位置
              this.grid.placeBlock(block, targetRow, col);
              
              // 创建下落动画
              this.grid.addFallingAnimation(block, row, col, targetRow, col);
              
              hasFallen = true;
            }
          }
        }
      }
    }
    
    return hasFallen;
  }

  /**
   * 处理多行消除后的方块下移（备用方案：一次性计算最终位置）
   * 这个方法通过预先计算每行的最终位置来避免连锁移动错误
   * @param {Array<number>} emptyRows - 空行数组
   * @returns {boolean} 是否有方块下移
   * @private
   */
  _handleMultipleRowClearV2(emptyRows) {
    if (emptyRows.length === 0) return false;
    
    console.log('使用V2算法处理多行消除，空行:', emptyRows);
    
    // 排序空行，从上到下
    emptyRows.sort((a, b) => a - b);
    
    // 创建行映射表：原行号 -> 最终行号
    const rowMapping = new Map();
    
    // 计算每一行的最终位置
    for (let row = 0; row < this.grid.rows; row++) {
      if (emptyRows.includes(row)) {
        // 空行不需要映射
        continue;
      }
      
      // 计算当前行下方有多少个空行
      const emptyRowsBelowCount = emptyRows.filter(emptyRow => emptyRow > row).length;
      const targetRow = row + emptyRowsBelowCount;
      
      rowMapping.set(row, targetRow);
    }
    
    console.log('行映射表:', Array.from(rowMapping.entries()));
    
    // 临时存储需要移动的方块
    const blocksToMove = [];
    
    // 收集所有需要移动的方块
    for (const [sourceRow, targetRow] of rowMapping) {
      if (sourceRow !== targetRow) {
        // 检查源行是否有方块
        for (let col = 0; col < this.grid.cols; col++) {
          const block = this.grid.getBlock(sourceRow, col);
          if (block) {
            blocksToMove.push({
              block,
              sourceRow,
              sourceCol: col,
              targetRow,
              targetCol: col
            });
          }
        }
      }
    }
    
    console.log(`需要移动${blocksToMove.length}个方块`);
    
    if (blocksToMove.length === 0) {
      return false;
    }
    
    // 清空所有需要移动的方块的原位置
    for (const moveInfo of blocksToMove) {
      this.grid.removeBlock(moveInfo.sourceRow, moveInfo.sourceCol);
    }
    
    // 将所有方块放置到最终位置
    for (const moveInfo of blocksToMove) {
      console.log(`V2移动：第${moveInfo.sourceRow}行列${moveInfo.sourceCol} -> 第${moveInfo.targetRow}行列${moveInfo.targetCol}`);
      
      this.grid.placeBlock(moveInfo.block, moveInfo.targetRow, moveInfo.targetCol);
      
      // 创建下落动画
      this.grid.addFallingAnimation(
        moveInfo.block, 
        moveInfo.sourceRow, 
        moveInfo.sourceCol, 
        moveInfo.targetRow, 
        moveInfo.targetCol
      );
    }
    
    return true;
  }

  /**
   * 收集需要检查下落的方块
   * @private
   * @param {boolean} isSpecialEffect - 是否为特殊效果（地雷、火球等）导致的消除
   * @returns {Set<Block>} 需要检查的方块集合
   */
  _collectBlocksToCheck(isSpecialEffect = false) {
    const blocksToCheck = new Set();
    
    // 🎯 重要修复：无论是否为特殊效果，都要收集原活动方块的剩余子方块
    // 因为即使是特殊效果消除，原活动方块的剩余子方块仍然需要下落检测
    console.log(`🔍 收集需要重力检查的方块 (isSpecialEffect: ${isSpecialEffect})`);
    
    // 收集来自lastTetrominoPositions的方块
    this._collectBlocksFromLastTetromino(blocksToCheck);
    
    if (isSpecialEffect) {
      console.log('✅ 特殊效果消除，但仍检查原活动方块的剩余子方块');
    } else {
      console.log('✅ 普通消除，检查原活动方块的剩余子方块');
    }
    
    console.log(`📦 最终收集到 ${blocksToCheck.size} 个方块需要重力检查`);
    
    return blocksToCheck;
  }

  /**
   * 从最近锁定的方块组合中收集方块
   * @private
   * @param {Set<Block>} blocksToCheck - 用于存储收集的方块的集合
   */
  _collectBlocksFromLastTetromino(blocksToCheck) {
    if (!this.lastTetrominoPositions) {
      console.log('  ⚠️ 没有lastTetrominoPositions数据');
      return;
    }
    
    console.log(`  🔍 检查 ${this.lastTetrominoPositions.length} 个原活动方块位置:`);
    
    for (const { row, col, block } of this.lastTetrominoPositions) {
      if (block) {
        // 检查方块是否仍然存在于网格中（未被消除）
        const currentBlock = this.grid.getBlock(row, col);
        
        if (currentBlock === block) {
          // 方块仍然存在，需要检查重力
          blocksToCheck.add(block);
          console.log(`    ✅ [${row}, ${col}]: 方块仍存在，加入重力检测`);
        } else if (currentBlock) {
          // 位置上有其他方块
          console.log(`    🔄 [${row}, ${col}]: 位置上有其他方块`);
        } else {
          // 方块已被消除
          console.log(`    ❌ [${row}, ${col}]: 方块已被消除`);
        }
      } else {
        console.log(`    ⚠️ [${row}, ${col}]: 无效的方块引用`);
      }
    }
  }
  
  /**
   * 检查和处理匹配
   * @private
   */
  _checkMatches() {
    // 先检查是否有满行需要消除
    if (this._checkAndClearFullRows()) {
      // 如果有满行被消除，就不需要再检查方块匹配了
      return;
    }
    
    // 使用匹配检测器检查匹配
    this.hasMatches = this.matchChecker.checkMatches();
    
    if (this.hasMatches) {
      // 开始消除动画
      this.effectsToApply = this.matchChecker.removeMatches();

      // 增加分数和连击
      const matchCount = this.matchChecker.getMatchCount();
      this.combo++;

      // 添加连击到连击系统
      this.comboSystem.addCombo(matchCount, this.effectsToApply);

      // 计算分数（使用连击系统的倍数）
      const comboMultiplier = this.comboSystem.getComboMultiplier();
      const baseScore = matchCount * 10;
      const finalScore = Math.floor(baseScore * comboMultiplier);

      const oldScore = this.score;
      this.score += finalScore;

      // 🎯 重要修复：立即同步到全局分数
      GameGlobal.databus.score = this.score;

      // 详细的分数追踪日志
      console.log(`📊 连击分数: ${baseScore} × ${comboMultiplier.toFixed(1)}x = ${finalScore} (${oldScore} → ${this.score})`);
      console.log(`🔍 分数同步检查: controller=${this.score}, global=${GameGlobal.databus.score}, 一致=${this.score === GameGlobal.databus.score}`);

      // 检测布局模式并应用奖励
      const layoutPatterns = this.comboSystem.detectLayoutPatterns(this.grid);
      if (layoutPatterns.length > 0) {
        this.comboSystem.applyLayoutRewards(layoutPatterns);
      }

      // 切换到动画状态
      this.state = GAME_STATE.ANIMATING;
      this.animationTimer = 0;

      // 发出匹配事件
      this.emit('blocks:match', {
        matchCount,
        combo: this.combo,
        comboMultiplier: comboMultiplier,
        layoutPatterns: layoutPatterns
      });
    } else {
      // 无匹配，重置连击
      this.combo = 0;

      // 继续游戏，生成新的方块
      this._generateRandomTetromino();
      this.state = GAME_STATE.PLAYING;
    }
  }
  
  /**
   * 应用特殊效果
   * @private
   */
  _applyEffects() {
    // 添加调试日志
    console.log('准备应用特效，当前效果列表:', this.effectsToApply);
    
    if (this.effectsToApply.length === 0) {
      return;
    }
    
    // 去重特效列表，防止同一个效果被处理多次
    const uniqueEffects = [];
    const effectPositions = new Set();
    
    for (const effect of this.effectsToApply) {
      const posKey = `${effect.type}_${effect.row}_${effect.col}`;
      if (!effectPositions.has(posKey)) {
        effectPositions.add(posKey);
        uniqueEffects.push(effect);
        console.log('添加唯一特效:', posKey);
      } else {
        console.log('跳过重复特效:', posKey);
      }
    }
    
    console.log('去重后特效数量:', uniqueEffects.length);
    
    // 专门收集地雷爆炸产生的被消除方块
    let mineRemovedPositions = [];
    
    // 应用每个效果
    for (const effect of uniqueEffects) {
      console.log('处理特效:', effect.type, effect.row, effect.col);
      
      switch (effect.type) {
        case BLOCK_EFFECTS.MINE:
          // 处理地雷效果
          console.log('触发地雷效果:', effect.row, effect.col);
          // 现在_handleMineEffect返回的是包含affectedColumns和removedPositions的对象
          const mineResult = this._handleMineEffect(effect);
          // 将地雷爆炸范围内的方块位置单独保存
          if (mineResult && mineResult.removedPositions) {
            mineRemovedPositions = [...mineRemovedPositions, ...mineResult.removedPositions];
          }
          break;
          
        case BLOCK_EFFECTS.FROZEN:
          // 处理冰冻效果
          this._handleFrozenEffect(effect);
          break;
      }
    }
    
    // 清空效果列表
    this.effectsToApply = [];
    
    // 如果有地雷爆炸，单独处理受影响的方块动画
    if (mineRemovedPositions.length > 0) {
      console.log('处理地雷爆炸影响的方块:', mineRemovedPositions.length);
      
      // 为每个被爆炸移除的方块播放消除动画
      // 这些方块已经从网格中移除，但需要播放动画效果
      for (const { row, col, block } of mineRemovedPositions) {
        if (block) {
          // 确保爆炸动画会正确播放
          block.isDestroying = true;
          block.destroyAnimationFrame = 0;
          block.destroyProgress = 0;
        }
      }
      
      // 保存被消除方块的位置，用于下落逻辑
      this.lastRemovedPositions = mineRemovedPositions;
    }
    
    // 处理消除后的下落效果，传递特殊效果标志
    this._handleBlocksDrop(true);  // 传递true表示这是特殊效果触发的下落
  }
  
  /**
   * 处理方块消除后的下落效果
   * 这个方法处理任何原因导致的方块消除后的下落逻辑
   * @private
   * @param {boolean} isSpecialEffect - 是否为特殊效果（地雷、火球等）导致的消除
   */
  _handleBlocksDrop(isSpecialEffect = false) {
    console.log('处理方块下落', isSpecialEffect ? '(特殊效果)' : '(普通消除)');
    
    // 收集所有被消除的方块位置
    const removedPositions = this._collectRemovedPositions();
    
    // 收集所有需要检查的列
    const columnsToCheck = this._collectAffectedColumns(removedPositions);

    // 收集需要下落的方块，传递特殊效果标志
    const blocksToCheck = this._collectBlocksToCheck(isSpecialEffect);
    
    // 在应用重力前，确保所有被标记为消除的方块确实从网格中被移除
    // 但只移除地雷爆炸和三消匹配标记的方块，不影响其他方块
    if (removedPositions && removedPositions.length > 0) {
      for (const { row, col } of removedPositions) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          // 只移除已被标记为销毁的方块或在matchedBlocks中的方块
          if (block.isDestroying || this.matchChecker.matchedBlocks.has(block)) {
            console.log(`确保移除位置 [${row}, ${col}] 的方块`);
            this.grid.removeBlock(row, col);
          } else {
            console.log(`保留位置 [${row}, ${col}] 的方块，它不在销毁列表中`);
          }
        }
      }
    }
    
    // 🎯 重要修复：应用重力前的详细状态检查
    console.log(`🌍 开始应用重力:`);
    console.log(`  受影响列数: ${columnsToCheck.size} [${Array.from(columnsToCheck).join(', ')}]`);
    console.log(`  需检查方块数: ${blocksToCheck.size}`);
    console.log(`  被移除位置数: ${removedPositions.length}`);
    
    // 应用重力，使方块下落
    const hasFallen = this.grid.applyGravity(columnsToCheck, blocksToCheck, removedPositions);
    
    console.log(`🌍 重力应用结果: ${hasFallen ? '有方块下落' : '无方块下落'}`);
    
    // 如果有方块下落，设置标记，等待下落动画完成后检查新的匹配
    if (hasFallen) {
      console.log('✅ 有方块下落，等待动画完成后再检查新的匹配');
      this._pendingAfterAnimationCheck = true;
    } else {
      // 没有方块下落，直接检查新的匹配
      console.log('⚠️ 没有方块下落，直接检查新的匹配');
      this._checkForNewMatches();
    }
    
    // 清理临时数据
    this._cleanupAfterDrop(hasFallen);
  }
  
  /**
   * 收集所有被消除的方块位置
   * @private
   * @returns {Array<{row: number, col: number}>} 被消除的方块位置
   */
  _collectRemovedPositions() {
    // 优先使用已保存的位置信息
    if (this.lastRemovedPositions && this.lastRemovedPositions.length > 0) {
      return this.lastRemovedPositions;
    }
    
    // 否则从matchedBlocks收集位置信息
    const positions = [];
    for (const block of this.matchChecker.matchedBlocks) {
      if (block && typeof block.row === 'number' && typeof block.col === 'number') {
        positions.push({ row: block.row, col: block.col });
      }
    }
    
    return positions;
  }
  
  /**
   * 收集所有受影响的列
   * @private
   * @param {Array<{row: number, col: number}>} removedPositions - 被消除的方块位置
   * @returns {Set<number>} 受影响的列集合
   */
  _collectAffectedColumns(removedPositions) {
    const columnsToCheck = new Set();
    
    // 从被消除的位置收集列
    for (const position of removedPositions) {
      // 检查是否是标准的{row, col}结构或是包含更多信息的对象
      if (position && typeof position.col === 'number' && isFinite(position.col)) {
        columnsToCheck.add(position.col);
      }
    }
    
    // 从被消除的方块收集列 - 但不包括地雷爆炸的方块，因为它们已经通过removedPositions处理
    for (const block of this.matchChecker.matchedBlocks) {
      if (block && typeof block.col === 'number' && isFinite(block.col)) {
        columnsToCheck.add(block.col);
      }
    }
    
    // 从锁定的方块位置收集列
    if (this.lastTetrominoPositions) {
      for (const position of this.lastTetrominoPositions) {
        if (position && typeof position.col === 'number' && isFinite(position.col)) {
          columnsToCheck.add(position.col);
        }
      }
    }
    
    // 确保至少返回一些列，如果没有收集到任何列
    if (columnsToCheck.size === 0 && removedPositions.length > 0) {
      console.log('没有收集到任何列，但有被移除的方块，检查数据结构:', removedPositions);
      // 尝试不同的数据结构格式
      for (const position of removedPositions) {
        if (position) {
          console.log('检查位置:', position);
          // 原始数据可能是{row, col, block}或其他格式
          if (position.block && typeof position.block.col === 'number') {
            columnsToCheck.add(position.block.col);
          }
        }
      }
    }
    
    return columnsToCheck;
  }
  
  /**
   * 清理下落后的临时数据
   * @private
   * @param {boolean} hasFallen - 是否有方块下落
   */
  _cleanupAfterDrop(hasFallen) {
    // 如果有方块下落，不要立即清理，等待动画完成
    if (hasFallen) {
      console.log('有方块下落，不清理临时数据，等待动画完成');
      return;
    }
    
    // 只有在没有方块下落或动画完成后才清理
    if (this.state !== GAME_STATE.ANIMATING) {
      console.log('清理临时数据');
      this.lastTetrominoPositions = null;
      
      // 清理地雷爆炸相关的临时数据
      // 但保留方块对象，防止引用丢失
      if (this.lastRemovedPositions) {
        // 处理特殊情况：lastRemovedPositions中的block对象可能仍然处于消除状态
        for (const position of this.lastRemovedPositions) {
          if (position.block && position.block.isDestroying) {
            // 标记动画已完成，但不移除block对象
            position.block.isDestroying = false;
            position.block.destroyAnimationFrame = 0;
            position.block.destroyProgress = 0;
          }
        }
      }
    }
  }
  
  /**
   * 应用特殊效果后的检查
   * @private
   */
  _checkAfterEffects() {
    // 🔥 防护：如果匹配列表刚被清空，等待一帧再检查
    if (this.matchChecker.matchedBlocks.size === 0) {
      console.log('⚠️ 匹配列表为空，可能刚被清理，跳过本次检查防止无限循环');
      return false;
    }
    
    // 检查是否有新的匹配
    const hasNewMatches = this.matchChecker.checkMatches();

    if (hasNewMatches) {
      // 🔥 额外检查：确保这些不是刚刚被移除的方块
      console.log(`🔍 检测到${this.matchChecker.matchedBlocks.size}个新匹配方块`);
      
      // 有新的匹配，继续消除流程
      console.log('检测到新的匹配，触发连锁消除');
      this.effectsToApply = this.matchChecker.removeMatches();

      // 增加分数，连锁消除额外加分
      const matchCount = this.matchChecker.getMatchCount();
      this.combo++;

      // 🎯 重要修复：自动连续消除也要添加到连击系统
      this.comboSystem.addCombo(matchCount, this.effectsToApply);

      // 计算分数（使用连击系统的倍数）
      const comboMultiplier = this.comboSystem.getComboMultiplier();
      const baseScore = matchCount * 20; // 连锁消除基础分数更高
      const finalScore = Math.floor(baseScore * comboMultiplier);

      const oldScore = this.score;
      this.score += finalScore;

      // 🎯 重要修复：立即同步到全局分数
      GameGlobal.databus.score = this.score;

      // 连锁消除日志
      console.log(`🔗 自动连锁消除! 连击: ${this.comboSystem.combo}, 分数: ${baseScore} × ${comboMultiplier.toFixed(1)}x = ${finalScore} (${oldScore} → ${this.score})`);

      // 重置动画计时器，进入新的消除动画
      this.animationTimer = 0;
      this.effectsTimer = 0;

      // 保持在动画状态
      this.state = GAME_STATE.ANIMATING;

      // 发出匹配事件
      this.emit('blocks:match', { matchCount, combo: this.combo });

      return true;
    }

    return false;
  }
  
  /**
   * 检查是否有新的匹配或满行（包含悬空方块检测）
   * @private
   */
  _checkForNewMatches() {
    console.log('检查是否有新的消除或匹配');
    
    // 首先检查游戏是否已经结束，如果是则不进行任何操作
    if (this.state === GAME_STATE.GAME_OVER) {
      console.log('游戏已结束，停止生成新方块');
      return;
    }
    
    // 🎯 新增：在检查匹配前，先进行悬空方块检测
    const hasFloatingBlocks = this._detectAndHandleFloatingBlocks();
    
    if (hasFloatingBlocks) {
      console.log('发现悬空方块，等待下落完成后再检查匹配');
      this._pendingAfterAnimationCheck = true;
      return;
    }
    
    // 先确保所有方块的状态都是正确的
    this._ensureBlockStates();
    
    // 清除matchedBlocks列表，以便重新检查
    this.matchChecker.matchedBlocks.clear();
    
    // 然后检查是否有新的满行或匹配
    if (this._checkAndClearFullRows()) {
      // 如果有满行，保持在动画状态
      console.log('检测到新的满行，继续动画状态');
    } else if (this._checkAfterEffects()) {
      // 如果有新的匹配，保持在动画状态
      console.log('检测到新的匹配，继续动画状态');
    } else {
      // 如果没有新的匹配，回到游戏状态
      console.log('没有新的匹配或满行，回到游戏状态');
      this.state = GAME_STATE.PLAYING;
      this.combo = 0; // 重置连击
      this.lastTetrominoPositions = null; // 清除最后锁定的方块位置
      this.lastRemovedPositions = []; // 清空地雷爆炸的位置信息
      this._generateRandomTetromino();
    }
  }
  
  /**
   * 确保所有方块的状态正确
   * @private
   */
  _ensureBlockStates() {
    // 检查网格中的所有方块
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          // 如果方块不在匹配列表中但处于销毁状态，重置它
          if (!this.matchChecker.matchedBlocks.has(block) && block.isDestroying) {
            console.log(`修复异常方块状态: (${row}, ${col})`);
            block.isDestroying = false;
            block.destroyAnimationFrame = 0;
            block.destroyProgress = 0;
          }
        }
      }
    }
  }
  
  /**
   * 设置连击系统事件监听
   * @private
   */
  _setupComboSystemEvents() {
    // 连击阶段提升事件
    this.comboSystem.on('combo_stage_up', (data) => {
      console.log(`连击阶段提升: ${data.stage.name} (${data.multiplier}x)`);
      this.comboNotification.showComboNotification(data);
      this.emit('combo:stage_up', data);
    });

    // 连击添加事件
    this.comboSystem.on('combo_added', (data) => {
      console.log(`连击添加: ${data.combo}连击 (${data.multiplier.toFixed(2)}x)`);
      this.comboNotification.showComboNotification(data);
      this.emit('combo:added', data);
    });

    // 能量等级提升事件
    this.comboSystem.on('energy_level_up', (data) => {
      console.log(`能量等级提升: ${data.level.name} (${data.multiplier}x)`);
      this.comboNotification.showEnergyNotification(data);
      this.emit('energy:level_up', data);
    });

    // 能量爆发事件
    this.comboSystem.on('energy_burst', (data) => {
      console.log(`能量爆发! ${data.level.name} - ${data.finalMultiplier.toFixed(2)}x`);
      this.comboNotification.showBurstNotification(data);
      this._handleEnergyBurst(data);
      this.emit('energy:burst', data);
    });

    // 布局模式检测事件
    this.comboSystem.on('layout_pattern_detected', (data) => {
      console.log(`检测到布局模式: ${data.pattern.name}`);
      this.emit('layout:pattern_detected', data);
    });

    // 耐心奖励事件
    this.comboSystem.on('patience_reward', (data) => {
      console.log(`获得耐心奖励: ${data.message}`);
      this.emit('patience:reward', data);
    });

    // 🎯 重要修复：监听能量达到最大值事件，自动触发能量爆发
    this.comboSystem.on('energy_max_reached', (data) => {
      console.log(`能量达到最大值，自动触发能量爆发! 能量: ${data.energy}`);
      // 自动触发能量爆发
      const burstResult = this.comboSystem.triggerEnergyBurst(false); // false表示自动触发
      if (burstResult.success) {
        console.log('自动能量爆发成功');
      } else {
        console.log('自动能量爆发失败:', burstResult.reason);
      }
    });

    // 移除了所有调试按钮功能
  }

  /**
   * 处理能量爆发
   * @param {Object} burstData - 爆发数据
   * @private
   */
  _handleEnergyBurst(burstData) {
    // 应用分数倍数
    const bonusScore = Math.floor(this.score * (burstData.finalMultiplier - 1));
    this.score += bonusScore;

    // 触发爆发动画
    this.comboDisplay.triggerBurstAnimation();

    // 创建屏幕特效
    this._createBurstEffects(burstData);

    console.log(`能量爆发奖励分数: +${bonusScore}`);
  }

  /**
   * 创建爆发特效
   * @param {Object} burstData - 爆发数据
   * @private
   */
  _createBurstEffects(burstData) {
    // 根据爆发等级创建不同的特效
    for (const effect of burstData.effects) {
      switch (effect) {
        case 'small_burst':
          // 小型爆发特效
          break;
        case 'medium_burst':
          // 中型爆发特效
          break;
        case 'large_burst':
          // 大型爆发特效
          break;
        case 'super_burst':
          // 超级爆发特效
          break;
        case 'legendary_burst':
          // 传奇爆发特效
          break;
        case 'combo_explosion':
          // 连击爆炸特效
          break;
        case 'screen_shake':
          // 屏幕震动特效
          break;
      }
    }
  }

  /**
   * 渲染游戏
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    // 渲染游戏网格
    this.grid.render(ctx);

    // 渲染当前方块 - 添加调试日志
    if (this.currentTetromino && this.state === GAME_STATE.PLAYING) {
      console.log(`🎨 渲染活动方块: 形状=${this.currentTetromino.shape}, 位置=(${this.currentTetromino.position.row}, ${this.currentTetromino.position.col}), 效果=${this.currentTetromino.effect || '无'}`);
      this.currentTetromino.render(ctx, this.grid.blockSize);
    }

    // 渲染连击显示UI
    this.comboDisplay.render(ctx);

    // 🎯 移除：将调试按钮渲染移到主渲染循环的最后
    // this.renderDebugButton(ctx);
    // this.renderDebugInfo(ctx);
  }

  /**
   * 更新游戏状态
   */
  update() {
    // 🔍 添加update方法开始调试
    if (this.matchChecker.matchedBlocks.size > 0) {
      console.log(`🎮 UPDATE开始 - 状态: ${this.state}, matchedBlocks: ${this.matchChecker.matchedBlocks.size}, 动画数: ${this.grid.animations.length}`);
    }
    
    // 🔥 修复暂停状态逻辑：允许消除动画在暂停时继续完成
    if (this.state === GAME_STATE.PAUSED) {
      // 检查是否有未完成的消除动画需要处理
      if (this.matchChecker.matchedBlocks.size > 0) {
        console.log(`🔥 暂停状态下发现未完成的消除动画，继续处理 ${this.matchChecker.matchedBlocks.size} 个方块`);
        
        // 强制移除所有标记为消除的方块
        let removedCount = 0;
        this.matchChecker.matchedBlocks.forEach(block => {
          if (block && typeof block.row === 'number' && typeof block.col === 'number') {
            // 跳过冰冻方块
            if (block.effect === BLOCK_EFFECTS.FROZEN && block.isFrozen) {
              console.log(`❄️ 暂停状态下跳过冰冻方块 [${block.row}, ${block.col}]`);
              return;
            }
            
            // 强制移除方块
            const currentBlock = this.grid.getBlock(block.row, block.col);
            if (currentBlock === block) {
              this.grid.removeBlock(block.row, block.col);
              removedCount++;
              console.log(`✅ 暂停状态下强制移除方块 [${block.row}, ${block.col}]`);
            }
          }
        });
        
        if (removedCount > 0) {
          console.log(`🔥 暂停状态下完成移除 ${removedCount} 个方块`);
          
          // 清空匹配列表
          this.matchChecker.matchedBlocks.clear();
          
          // 添加调试网格状态
          console.log(`🔍 ===== 暂停状态下强制移除完成后的网格状态 =====`);
          if (typeof debugGridState === 'function') {
            debugGridState();
          }
          console.log(`🔍 ===== 暂停状态下强制移除完成后的网格状态结束 =====`);
        }
      }
      
      // 在暂停状态下不执行其他逻辑更新，但保持方块可见
      return;
    }

    // 游戏结束或关卡完成时只更新动画，不生成新方块
    if (this.state === GAME_STATE.GAME_OVER || this.state === GAME_STATE.LEVEL_CLEAR) {
      // 仍然允许现有动画完成
      if (this.grid.animations.length > 0) {
        this.grid.updateAnimations();
      }
      return;
    }

    // 更新垃圾生成器（只在游戏进行中）
    if (this.state === GAME_STATE.PLAYING || this.state === GAME_STATE.ANIMATING) {
      const garbageEvent = this.garbageGenerator.update();
      if (garbageEvent) {
        this._handleGarbageEvent(garbageEvent);
      }
    }

    // 更新连击系统
    this.comboSystem.update();

    // 更新连击显示UI
    this.comboDisplay.updateStatus(this.comboSystem.getStatus());
    this.comboDisplay.update();

    // 更新连击通知
    this.comboNotification.update();

    // 🎯 新增：定期智能悬空检查（每300帧检查一次，约5秒）
    if (!this.frameCounter) this.frameCounter = 0;
    if (this.frameCounter % 300 === 0) {
      this._smartFloatingCheck();
    }
    this.frameCounter++;

    // 移除了预览测试状态更新
    
    // 游戏准备状态
    if (this.state === GAME_STATE.READY) {
      return;
    }
    
    // 更新所有下落动画
    if (this.grid.animations.length > 0) {
      // 🔍 添加下落动画调试信息
      console.log(`🎭 检测到下落动画: ${this.grid.animations.length}个, 当前状态: ${this.state}, matchedBlocks: ${this.matchChecker.matchedBlocks.size}`);
      
      // 标记有动画在进行中
      this.isAnimating = true;
      
      const animationsComplete = !this.grid.updateAnimations();
      
      // 如果动画全部完成，且有挂起的消除检查，执行检查
      if (animationsComplete && this._pendingAfterAnimationCheck) {
        console.log('下落动画完成，执行挂起的检查');
        this._pendingAfterAnimationCheck = false;
        this.isAnimating = false;
        
        // 确保所有方块的状态正确
        this._ensureBlockStates();
        
        // 检查是否有新的匹配或满行
        this._checkForNewMatches();
      } else if (animationsComplete) {
        // 所有动画完成，但没有挂起的检查
        console.log('所有动画完成，没有挂起的检查');
        this.isAnimating = false;
      }
      
      // 🔥 修复：如果当前是消除动画状态，不要被下落动画打断
      if (this.state === GAME_STATE.ANIMATING) {
        console.log(`🔥 消除动画状态不被下落动画打断，继续执行消除逻辑`);
        // 继续执行，不要返回
      } else {
        // 动画仍在进行中，等待下一帧
        return;
      }
    } else {
      // 没有动画在进行中
      this.isAnimating = false;
    }
    
    // 游戏进行中，处理方块下落
    if (this.state === GAME_STATE.PLAYING || 
        (this.state === GAME_STATE.ANIMATING && this.currentTetromino && this.matchChecker.matchedBlocks.size === 0)) {
      
      // 🔍 调试：检查是否进入了这个分支
      if (this.matchChecker.matchedBlocks.size > 0) {
        console.log(`🎯 进入游戏进行分支: 状态=${this.state}, 有当前方块=${!!this.currentTetromino}`);
      }
      
      if (!this.currentTetromino) {
        // 检查是否刚从暂停恢复（通过检测刚设置的恢复标志）
        if (this.justResumed) {
          this.justResumed = false;
          console.log('跳过恢复后的方块生成');
          return;
        }
        
        this._generateRandomTetromino();
        return;
      }
      
      // 新的下落速度机制：大幅降低自动提升，保持相对稳定
      let fallSpeed;

      if (this.isSoftDropping) {
        // 快速下落：保持原有的高速度，不受新的速度限制影响
        let baseSoftDropSpeed = Math.max(1, Math.floor(BASE_SPEED / SOFT_DROP_SPEED));
        fallSpeed = baseSoftDropSpeed;
      } else {
        // 自动下落：使用新的稳定速度机制
        const baseFallSpeed = Math.max(15, BASE_SPEED - Math.floor(this.options.level / 10) * 2); // 每10级才减少2帧，最快15帧

        // 应用速度因子（speedFactor越大，下落越快）
        const speedFactor = this.options.speedFactor || 1.0;
        fallSpeed = Math.max(10, Math.floor(baseFallSpeed / speedFactor)); // 自动下落最快10帧
      }
      
      // 检查当前方块是否可以下落
      const canMoveDown = this.currentTetromino.canMoveDown(this.grid);
      
      // 如果不能下落，增加锁定计时器（每帧都增加）
      if (!canMoveDown) {
        // 🔍 重构调试：当方块无法下落时，打印详细信息
        const currentPos = this.currentTetromino.position;
        const blockPositions = this.currentTetromino.getBlockPositions();
        console.log(`🚨 方块无法下落！位置: (${currentPos.row}, ${currentPos.col}), 锁定计时: ${this.lockTimer}/${LOCK_DELAY}`);
        console.log('方块占据的网格位置:', blockPositions.map(p => `(${p.row}, ${p.col})`).join(', '));
        
        // 检查方块下方是否有阻挡
        let hasObstacle = false;
        for (const pos of blockPositions) {
          const belowRow = pos.row + 1;
          if (belowRow >= this.grid.rows) {
            console.log(`位置(${pos.row}, ${pos.col})已到达底部边界`);
            hasObstacle = true;
          } else {
            const blockBelow = this.grid.getBlock(belowRow, pos.col);
            if (blockBelow) {
              console.log(`位置(${pos.row}, ${pos.col})下方(${belowRow}, ${pos.col})有方块阻挡:`, blockBelow.color);
              hasObstacle = true;
            }
          }
        }
        
        // 🎯 特别检查：如果方块在第9行或以上就锁定，打印完整网格状态
        if (currentPos.row <= 9) {
          let totalBlocks = 0;
          for (let row = 0; row < this.grid.rows; row++) {
            let rowState = `第${row.toString().padStart(2)}行: `;
            for (let col = 0; col < this.grid.cols; col++) {
              const block = this.grid.getBlock(row, col);
              if (block) {
                totalBlocks++;
                rowState += (block.color ? block.color[0].toUpperCase() : 'X');
              } else {
                rowState += '·';
              }
            }
            const marker = row === currentPos.row ? ' ← 当前方块' : '';
            console.log(rowState + marker);
          }
          console.log(`📊 网格统计：${totalBlocks}个方块 / ${this.grid.rows * this.grid.cols}格 (${(totalBlocks/(this.grid.rows * this.grid.cols)*100).toFixed(1)}%)`);
        } else if (!hasObstacle) {
          console.log('⚠️ 异常：方块下方无阻挡但canMoveDown返回false！');
        }
        
        this.lockTimer++;
        
        // 锁定延迟结束，锁定方块
        if (this.lockTimer >= LOCK_DELAY) {
          this._lockTetromino();
          return;
        }
      }
      
      // 方块自动下落逻辑（仍然受fallTimer控制）
      this.fallTimer++;
      if (this.fallTimer >= fallSpeed) {
        this.fallTimer = 0;

        // 尝试下落
        if (canMoveDown) {
          this.currentTetromino.moveDown();
          this.lockTimer = 0; // 重置锁定计时器
        }
      }
    }
    // 检查匹配状态
    else if (this.state === GAME_STATE.CHECKING) {
      // 🔍 调试：检查匹配状态分支
      if (this.matchChecker.matchedBlocks.size > 0) {
        console.log(`🔍 进入检查匹配分支`);
      }
      this._checkMatches();
    }
    // 动画状态，处理消除动画
    else if (this.state === GAME_STATE.ANIMATING) {
      // 🔍 添加动画状态调试
      console.log(`🎬 动画状态处理: timer=${this.animationTimer}, state=${this.state}, matchedBlocks=${this.matchChecker.matchedBlocks.size}`);
      
      // 增加动画计时器
      this.animationTimer++;
      
      // 动画开始（第10帧）
      if (this.animationTimer === 10) {
        console.log('动画阶段: 开始消除动画');
        
        // 开始所有匹配方块的消除动画
        for (let row = 0; row < this.grid.rows; row++) {
          for (let col = 0; col < this.grid.cols; col++) {
            const block = this.grid.getBlock(row, col);
            if (block && this.matchChecker.matchedBlocks.has(block)) {
              block.startDestroyAnimation();
            }
          }
        }
        
        // 检查lastRemovedPositions是否包含被地雷影响的方块
        if (this.lastRemovedPositions && this.lastRemovedPositions.length > 0) {
          console.log('为地雷爆炸范围内的方块启动消除动画:', this.lastRemovedPositions.length);
          // 这些方块可能已经不在网格中，但仍需要显示动画
          for (const { row, col, block } of this.lastRemovedPositions) {
            if (block && !block.isDestroying) {
              block.startDestroyAnimation();
              console.log(`启动地雷爆炸影响的方块动画 [${row}, ${col}]`);
            }
          }
        }
      }
      // 检查消除动画是否完成（此时20-30帧）
      else if (this.animationTimer >= 10 && this.animationTimer <= this.destroyDuration + 10) {
        let allAnimationsComplete = true;
        const blocksToRemove = [];
        const columnsToCheck = new Set();
        const blocksToCheck = new Set();
        
        // 更新所有正在消除的方块的动画
        for (let row = 0; row < this.grid.rows; row++) {
          for (let col = 0; col < this.grid.cols; col++) {
            const block = this.grid.getBlock(row, col);
            if (block && this.matchChecker.matchedBlocks.has(block)) {
              // 🔍 调试：检查满行方块的动画状态
              console.log(`📊 检查满行方块[${row},${col}]: isDestroying=${block.isDestroying}, progress=${block.destroyProgress}/${block.destroyDuration}, frame=${block.destroyAnimationFrame}`);
              
              if (block.updateDestroyAnimation()) {
                blocksToRemove.push({ row, col, block });
                columnsToCheck.add(col);
                
                // 记录这一列中所有上方的方块，用于后续下落检查
                for (let r = 0; r < row; r++) {
                  const upperBlock = this.grid.getBlock(r, col);
                  if (upperBlock) {
                    blocksToCheck.add(upperBlock);
                  }
                }
              } else {
                allAnimationsComplete = false;
                console.log(`📊 满行方块[${row},${col}]动画未完成，继续等待`);
              }
            } else if (block && block.isDestroying) {
              // 关键修复：处理不在匹配列表但正在消除的方块（地雷效果触发的）
              console.log(`检查独立消除方块 [${row}, ${col}]`);
              if (block.updateDestroyAnimation()) {
                console.log(`独立消除方块动画完成，移除 [${row}, ${col}]`);
                blocksToRemove.push({ row, col, block });
                columnsToCheck.add(col);
                
                // 记录这一列中所有上方的方块
                for (let r = 0; r < row; r++) {
                  const upperBlock = this.grid.getBlock(r, col);
                  if (upperBlock) {
                    blocksToCheck.add(upperBlock);
                  }
                }
              } else {
                // 如果动画还未完成，确保下一帧继续检查
                allAnimationsComplete = false;
              }
            }
          }
        }
        
        // 处理lastRemovedPositions中可能的地雷爆炸影响的方块
        if (this.lastRemovedPositions && this.lastRemovedPositions.length > 0) {
          for (const position of this.lastRemovedPositions) {
            // 检查position中是否有block属性，这是地雷效果特有的
            if (position.block) {
              if (position.block.updateDestroyAnimation()) {
                console.log(`地雷影响的方块动画完成 [${position.row}, ${position.col}]`);
                // 这些方块已经从网格中移除，只需更新动画状态
                columnsToCheck.add(position.col);
              } else {
                allAnimationsComplete = false;
              }
            }
          }
        }
        
        // 如果所有动画都完成了
        if (allAnimationsComplete || this.animationTimer === this.destroyDuration + 10) {
          console.log('所有消除动画完成');
          
          console.log(`🔍 开始方块移除: matchedBlocks=${this.matchChecker.matchedBlocks.size}个`);
          
          // 🔥 强制移除策略：直接移除所有matchedBlocks中的方块
          let removedCount = 0;
          this.matchChecker.matchedBlocks.forEach(block => {
            if (block) {
              // 跳过冰冻方块
              if (block.effect === BLOCK_EFFECTS.FROZEN && block.isFrozen) {
                console.log(`❄️ 跳过冰冻方块 [${block.row}, ${block.col}]`);
                return;
              }
              
              // 🔥 修复：优先使用方块自身的位置信息，如果无效则通过网格搜索
              let targetRow = block.row;
              let targetCol = block.col;
              
              // 如果方块位置信息无效，在网格中搜索该方块
              if (targetRow < 0 || targetCol < 0 || targetRow >= this.grid.rows || targetCol >= this.grid.cols) {
                console.log(`⚠️ 方块位置信息无效 [${targetRow}, ${targetCol}]，搜索网格...`);
                let found = false;
                for (let row = 0; row < this.grid.rows && !found; row++) {
                  for (let col = 0; col < this.grid.cols && !found; col++) {
                    if (this.grid.getBlock(row, col) === block) {
                      targetRow = row;
                      targetCol = col;
                      // 修复方块的位置信息
                      block.row = row;
                      block.col = col;
                      console.log(`✅ 找到方块实际位置 [${row}, ${col}]`);
                      found = true;
                    }
                  }
                }
                
                if (!found) {
                  console.log(`❌ 未在网格中找到方块，跳过`);
                  return;
                }
              }
              
              // 🔥 修复：强制移除方块，不依赖引用比较
              const currentBlock = this.grid.getBlock(targetRow, targetCol);
              if (currentBlock) {
                this.grid.removeBlock(targetRow, targetCol);
                removedCount++;
                console.log(`✅ 强制移除方块 [${targetRow}, ${targetCol}]`);
              } else {
                console.log(`⚠️ 位置为空，方块已被移除 [${targetRow}, ${targetCol}]`);
              }
            }
          });
          
          console.log(`🔍 强制移除完成，共移除 ${removedCount} 个方块`);
          
          // 🔍 添加移除完成后的网格状态调试
          console.log(`🔍 ===== 强制移除完成后的网格状态 =====`);
          if (typeof debugGridState === 'function') {
            debugGridState();
          }
          console.log(`🔍 ===== 强制移除完成后的网格状态结束 =====`);
          
          // 🔥 修复：完全清理匹配状态，防止无限循环
          console.log(`🧹 清理匹配状态: 当前有${this.matchChecker.matchedBlocks.size}个匹配方块`);
          this.matchChecker.matchedBlocks.clear();
          this.hasMatches = false;
          this.effectsToApply = [];
          
          // 应用重力，让方块下落
          console.log('应用重力，让方块下落');
          this._handleRowClear();
          
          // 重置动画计时器
          this.animationTimer = 0;
          
          // 检查是否有新的匹配或满行
          if (this.grid.animations.length > 0) {
            console.log('等待下落动画完成后再检查新的消除');
            this._pendingAfterAnimationCheck = true;
          } else {
            this._checkForNewMatches();
          }
        }
      }
    }
  }
  
  /**
   * 渲染游戏
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    // 渲染网格和固定的方块
    this.grid.render(ctx);
    
    // 渲染当前下落中的方块
    if (this.currentTetromino && !this.currentTetromino.isLocked) {
      // 🎯 恢复：渲染当前运动方块的底部投影虚影（重要的游戏功能）
      this.currentTetromino.renderGhost(ctx, this.grid);

      // 渲染当前方块
      this.currentTetromino.render(ctx, this.grid);
    }
    
    // 渲染下一个方块的预览
    if (this.nextTetromino) {
      this._renderNextTetromino(ctx);
    }
    
    // 渲染分数
    this._renderScore(ctx);
    
    // 渲染游戏状态
    if (this.state === GAME_STATE.PAUSED) {
      this._renderPauseScreen(ctx);
    } else if (this.state === GAME_STATE.GAME_OVER) {
      this._renderGameOverScreen(ctx);
    }

    // 渲染连击通知（在最上层，确保不被遮挡）
    if (this.comboNotification) {
      this.comboNotification.render(ctx);
    }
  }
  
  /**
   * 渲染下一个方块预览
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderNextTetromino(ctx) {
    const previewX = this.grid.offsetX + this.grid.cols * this.grid.blockSize + 20;
    const previewY = this.grid.offsetY;
    const previewSize = this.grid.blockSize * 0.8;
    
    // 绘制预览框
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(
      previewX, 
      previewY, 
      previewSize * 4, 
      previewSize * 4
    );
    
    // 绘制标题
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('下一个', previewX + previewSize * 2, previewY - 10);
    
    // 获取方块位置
    const blockPositions = this.nextTetromino.getBlockPositions();
    
    // 计算预览居中偏移
    let minRow = Infinity;
    let maxRow = -Infinity;
    let minCol = Infinity;
    let maxCol = -Infinity;
    
    blockPositions.forEach(({ row, col }) => {
      minRow = Math.min(minRow, row);
      maxRow = Math.max(maxRow, row);
      minCol = Math.min(minCol, col);
      maxCol = Math.max(maxCol, col);
    });
    
    const width = maxCol - minCol + 1;
    const height = maxRow - minRow + 1;
    const offsetX = previewX + (4 - width) * previewSize / 2;
    const offsetY = previewY + (4 - height) * previewSize / 2;
    
    // 绘制方块
    blockPositions.forEach(({ row, col, block }) => {
      const x = offsetX + (col - minCol) * previewSize;
      const y = offsetY + (row - minRow) * previewSize;
      
      block.render(ctx, x, y, previewSize);
    });
  }
  
  /**
   * 渲染分数
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderScore(ctx) {
    const scoreX = this.grid.offsetX + this.grid.cols * this.grid.blockSize + 20;
    const scoreY = this.grid.offsetY + this.grid.blockSize * 5;
    
    // 绘制分数背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(
      scoreX, 
      scoreY, 
      this.grid.blockSize * 4, 
      this.grid.blockSize * 2
    );
    
    // 绘制分数标题
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '20px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('分数', scoreX + 10, scoreY + 30);
    
    // 绘制分数值
    ctx.font = '24px Arial';
    ctx.fillText(this.score.toString(), scoreX + 10, scoreY + 70);
    
    // 绘制连击
    if (this.combo > 1) {
      ctx.fillStyle = '#FFFF00';
      ctx.font = '18px Arial';
      ctx.fillText(`连击: ${this.combo}x`, scoreX + 10, scoreY + 100);
    }
  }
  
  /**
   * 渲染暂停画面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderPauseScreen(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 暂停文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '36px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('游戏暂停', SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 20);
    
    // 继续提示
    ctx.font = '20px Arial';
    ctx.fillText('点击屏幕继续', SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 20);
  }
  
  /**
   * 渲染游戏结束画面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderGameOverScreen(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);

    // 游戏结束文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '36px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('游戏结束', SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 50);

    // 分数
    ctx.font = '24px Arial';
    ctx.fillText(`最终分数: ${this.score}`, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2);

    // 重新开始提示
    ctx.font = '20px Arial';
    ctx.fillText('点击屏幕重新开始', SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 50);
  }

  /**
   * 处理垃圾生成事件
   * @param {Object} event - 垃圾生成事件
   * @private
   */
  _handleGarbageEvent(event) {
    switch (event.type) {
      case 'warning':
        console.log(`垃圾生成预警：${event.timeLeft}帧后生成`);
        this.emit('garbagewarning', { timeLeft: event.timeLeft });
        break;

      case 'generated':
        console.log(`生成了${event.rowCount}行垃圾方块，共${event.blockCount}个方块`);
        
        // 如果需要调整活动方块位置
        if (event.needsActiveTetrominoAdjustment && this.currentTetromino) {
          this._adjustActiveTetrominoAfterGarbage(event.rowCount);
        }
        
        this.emit('garbagegenerated', {
          rowCount: event.rowCount,
          blockCount: event.blockCount,
          blocks: event.blocks
        });

        // 🔧 修复：移除立即的游戏结束检测
        // 垃圾行生成后，方块被推到顶部是正常现象，不应立即判断为游戏结束
        // 真正的游戏结束应该在无法生成垃圾行时（'failed'事件）处理
        // this._checkGameOver();
        
        console.log('垃圾行生成完成，继续游戏');
        break;

      case 'failed':
        console.log('垃圾生成失败：', event.reason);
        if (event.reason === 'no_space') {
          // 空间不足，游戏结束
          console.log('垃圾生成失败：空间不足，游戏结束');
          this.gameOver();
        }
        break;
    }
  }

  /**
   * 更新关卡配置
   * @param {number} level - 新关卡
   * @param {Object} config - 关卡配置
   */
  updateLevel(level, config = {}) {
    this.options.level = level;

    // 更新垃圾生成器的关卡
    this.garbageGenerator.updateLevel(level);

    // 更新其他配置
    if (config.colorCount) {
      this.options.colorCount = config.colorCount;
    }
    if (config.speedFactor) {
      this.options.speedFactor = config.speedFactor;
    }
    if (config.allowedEffects) {
      this.options.allowedEffects = config.allowedEffects;
    }

    console.log(`游戏控制器更新到关卡${level}`, this.options);
  }

  /**
   * 调整活动方块位置以应对垃圾行生成
   * @param {number} rowCount - 生成的垃圾行数
   * @private
   */
  _adjustActiveTetrominoAfterGarbage(rowCount) {
    if (!this.currentTetromino) return;
    
    console.log(`调整活动方块位置，垃圾行数: ${rowCount}`);
    
    // 保存原始位置
    const originalPosition = { ...this.currentTetromino.position };
    
    // 尝试将活动方块向上移动相应的行数
    this.currentTetromino.position.row -= rowCount;
    
    // 检查新位置是否有效
    if (!this.currentTetromino.isValidPosition(this.grid)) {
      console.log('向上移动后位置无效，尝试其他调整策略');
      
      // 恢复原始位置
      this.currentTetromino.position = originalPosition;
      
      // 策略1: 尝试向左或向右移动一格，然后再向上
      const horizontalOffsets = [0, -1, 1, -2, 2]; // 优先尝试不移动，然后左右移动
      let adjustmentSuccess = false;
      
      for (const offset of horizontalOffsets) {
        this.currentTetromino.position.col = originalPosition.col + offset;
        this.currentTetromino.position.row = originalPosition.row - rowCount;
        
        if (this.currentTetromino.isValidPosition(this.grid)) {
          console.log(`通过水平偏移 ${offset} 成功调整活动方块位置`);
          adjustmentSuccess = true;
          break;
        }
      }
      
      if (!adjustmentSuccess) {
        console.log('常规调整失败，保持原位置继续游戏');
        
        // 策略2: 如果无法调整，回到原始位置继续游戏
        this.currentTetromino.position = originalPosition;
        
        // 检查原始位置是否仍然有效
        if (this.currentTetromino.isValidPosition(this.grid)) {
          // 原始位置有效，正常继续游戏
          console.log('原始位置仍有效，继续正常游戏');
          // 不设置立即锁定，让玩家有正常的操作时间
        } else {
          // 原始位置也无效，这是真正的游戏结束条件
          // 但这种情况很少见，因为垃圾行生成前位置应该是有效的
          console.log('原始位置也无效，这可能是异常情况');
          console.log('位置信息:', this.currentTetromino.position, '原始位置:', originalPosition);
          
          // 尝试将方块移到最顶部的中央位置作为最后尝试
          this.currentTetromino.position = {
            row: 0,
            col: Math.floor(this.grid.cols / 2) - 1
          };
          
          if (this.currentTetromino.isValidPosition(this.grid)) {
            console.log('成功将方块移到顶部中央位置');
          } else {
            // 真正无法放置时才结束游戏
            console.log('所有位置调整尝试都失败，游戏结束');
            this.gameOver();
            return;
          }
        }
      }
    } else {
      console.log(`成功将活动方块向上移动 ${rowCount} 行`);
    }
    
    // 重置锁定计时器，给玩家一些反应时间
    this.lockTimer = 0;
    
    // 发出活动方块位置调整事件
    this.emit('tetromino:adjusted', {
      originalPosition,
      newPosition: { ...this.currentTetromino.position },
      reason: 'garbage_generation'
    });
  }

  /**
   * 检查游戏是否结束（顶部是否有方块）
   * @private
   */
  _checkGameOver() {
    // 🔧 改进的游戏结束检测逻辑
    // 只检查最顶行（row = 0），避免垃圾行推移后的误判
    for (let col = 0; col < this.grid.cols; col++) {
      if (this.grid.getBlock(0, col)) {
        console.log(`检测到最顶行有方块 [0, ${col}]，游戏结束`);
        this.gameOver();
        return true;
      }
    }
    
    // 额外检查：如果有活动方块且无法放置在任何位置
    if (this.currentTetromino && !this.currentTetromino.isValidPosition(this.grid)) {
      // 尝试将方块移到不同位置看是否能放置
      const originalPosition = { ...this.currentTetromino.position };
      let canPlace = false;
      
      // 尝试几个不同的列位置
      for (let col = 0; col < this.grid.cols - 1; col++) {
        this.currentTetromino.position = { row: 0, col };
        if (this.currentTetromino.isValidPosition(this.grid)) {
          canPlace = true;
          break;
        }
      }
      
      // 恢复原始位置
      this.currentTetromino.position = originalPosition;
      
      if (!canPlace) {
        console.log('活动方块无法在任何位置放置，游戏结束');
        this.gameOver();
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 处理地雷效果
   * @param {Object} effect - 效果对象
   * @private
   */
  _handleMineEffect(effect) {
    const { row, col } = effect;
    
    console.log('处理地雷效果，位置:', row, col);
    
    // 创建爆炸动画
    this.grid.createExplosionAnimation(row, col, 1);
    
    // 获取3x3范围内的方块位置
    const removedPositions = [];
    const range = 1; // 爆炸范围
    
    // 检查周围的方块（3x3区域）
    for (let r = Math.max(0, row - range); r <= Math.min(this.grid.rows - 1, row + range); r++) {
      for (let c = Math.max(0, col - range); c <= Math.min(this.grid.cols - 1, col + range); c++) {
        // 跳过中心方块（地雷位置，已经被消除了）
        if (r === row && c === col) continue;
        
        // 获取当前位置的方块
        const targetBlock = this.grid.getBlock(r, c);
        console.log('检查周围的方块:', r, c, targetBlock);
        
        if (targetBlock) {
          console.log('方块状态:', r, c, '冰冻=', targetBlock.isFrozen, '消除中=', targetBlock.isDestroying);
          
          // 处理冰冻方块 - 先解除冰冻状态
          if (targetBlock.isFrozen) {
            console.log('解除冰冻方块:', r, c);
            targetBlock.setEffect(BLOCK_EFFECTS.NONE);
            // 解除冰冻后添加到匹配列表，下次三消时会被消除
            continue;
          }
          
          // 确保方块没有正在被消除
          if (!targetBlock.isDestroying) {
            // 强制开始方块消除动画
            targetBlock.isDestroying = true;
            targetBlock.destroyAnimationFrame = 0;
            targetBlock.destroyProgress = 0;
            
            // 添加到被消除位置列表，包含方块对象
            removedPositions.push({ row: r, col: c, block: targetBlock });
            
            // 不再将方块添加到matchChecker.matchedBlocks
            // 这是关键修改，防止爆炸影响其他方块
            // this.matchChecker.matchedBlocks.add(targetBlock);
            
            // 确保从网格中移除该方块，防止重新出现
            // 在地雷效果中直接移除方块，防止它在网格中残留
            this.grid.removeBlock(r, c);
            
            console.log('标记方块进行消除并从网格移除:', r, c);
          } else {
            console.log('方块已经在消除中:', r, c);
          }
        } else {
          console.log('位置上没有方块:', r, c);
        }
      }
    }
    
    // 播放爆炸音效
    if (GameGlobal.musicManager) {
      GameGlobal.musicManager.playEffect('explosion');
    }
    
    // 🎯 重要修复：地雷爆炸得分也要应用连击倍数并同步
    const baseMineScore = this._calculateMineScore(removedPositions.length);
    let finalMineScore = 0; // 🎯 修复：初始化变量避免未定义错误

    if (baseMineScore > 0) {
      const comboMultiplier = this.comboSystem.getComboMultiplier();
      finalMineScore = Math.floor(baseMineScore * comboMultiplier);

      const oldScore = this.score;
      this.score += finalMineScore;
      GameGlobal.databus.score = this.score;

      console.log(`💣 地雷爆炸分数: ${baseMineScore} × ${comboMultiplier.toFixed(1)}x = ${finalMineScore} (${oldScore} → ${this.score})`);
    }

    // 发出效果事件
    this.emit('effect:mine', {
      row,
      col,
      affectedBlocks: removedPositions.length,
      score: finalMineScore
    });
    
    // 将被影响的方块列转换为Set以供其他方法使用
    const affectedColumns = new Set();
    for (const { col } of removedPositions) {
      affectedColumns.add(col);
    }
    
    // 确保地雷所在列也被检查
    affectedColumns.add(col);
    
    // 保存被消除方块的位置，用于下落逻辑
    this.lastRemovedPositions = [...this.lastRemovedPositions || [], ...removedPositions];
    
    console.log('地雷消除位置总数:', removedPositions.length, removedPositions);
    
    return { affectedColumns, removedPositions };
  }

  /**
   * 计算地雷爆炸得分
   * @param {number} blocksCount - 爆炸消除的方块数量
   * @returns {number} 得分
   * @private
   */
  _calculateMineScore(blocksCount) {
    if (blocksCount === 0) return 0;

    // 地雷爆炸基础分数：每个方块30分
    const baseScore = blocksCount * 30;

    // 爆炸范围加成：消除越多方块，单个方块分数越高
    const rangeBonus = 1 + Math.min(blocksCount / 8, 1.5); // 最多2.5倍加成

    // 连击加成
    const comboBonus = 1 + (this.combo * 0.1);

    const finalScore = Math.floor(baseScore * rangeBonus * comboBonus);

    console.log(`地雷得分计算: 方块数: ${blocksCount}, 基础分: ${baseScore}, 最终分: ${finalScore}`);

    return finalScore;
  }
  
  /**
   * 处理冰冻效果
   * @param {Object} effect - 效果对象
   * @private
   */
  _handleFrozenEffect(effect) {
    const { row, col } = effect;
    const radius = 1; // 冰冻半径
    const affectedBlocks = new Set();
    
    // 检查周围的方块（3x3区域）
    for (let r = row - radius; r <= row + radius; r++) {
      for (let c = col - radius; c <= col + radius; c++) {
        // 跳过中心方块（已经在消除列表中）
        if (r === row && c === col) continue;
        
        // 检查边界
        if (r >= 0 && r < this.grid.rows && c >= 0 && c < this.grid.cols) {
          const block = this.grid.getBlock(r, c);
          if (block && !block.isDestroying && !block.isFrozen) {
            // 将方块变为冰冻状态
            block.setEffect(BLOCK_EFFECTS.FROZEN);
            affectedBlocks.add(block);
          }
        }
      }
    }
    
    // 播放冰冻音效
    if (GameGlobal.musicManager) {
      GameGlobal.musicManager.playEffect('freeze');
    }
    
    // 发出效果事件
    this.emit('effect:frozen', { row, col, affectedBlocks });
    
    return affectedBlocks;
  }
  
  /**
   * 修复lastTetrominoPositions中的位置信息
   * 确保它包含完整的网格位置而不是相对位置
   * @private
   */
  _fixLockTetrominoPositions() {
    if (!this.lastTetrominoPositions || this.lastTetrominoPositions.length === 0) {
      return;
    }
    
    // 更新每个位置，确保它是网格的绝对位置
    for (let i = 0; i < this.lastTetrominoPositions.length; i++) {
      const position = this.lastTetrominoPositions[i];
      
      // 确保行列有值，且是有限数字
      if (!position || 
          !Number.isFinite(position.row) || 
          !Number.isFinite(position.col)) {
        console.error('_fixLockTetrominoPositions: 发现无效位置', position);
        continue;
      }
      
      // 确认方块在网格中
      const block = this.grid.getBlock(position.row, position.col);
      if (block) {
        // 用网格中方块的实际位置更新
        position.row = block.row;
        position.col = block.col;
      }
    }
  }

  // 移除了所有调试按钮和预览测试功能

  /**
   * 测试方法：模拟用户报告的特定bug场景
   * 行0-8完全为空，行9-13有零散方块，行14是满行并被消除
   * @private
   */
  _testBugScenario() {
    console.log('🧪 开始测试bug场景');
    
    // 模拟场景：行0-8为空，行9-13有方块，行14被消除
    const emptyRows = [0, 1, 2, 3, 4, 5, 6, 7, 8, 14];
    
    console.log('模拟的空行:', emptyRows);
    
    // 打印预期结果
    console.log('预期结果:');
    console.log('- 行9的方块应该下移到行10');
    console.log('- 行10的方块应该下移到行11');
    console.log('- 行11的方块应该下移到行12');
    console.log('- 行12的方块应该下移到行13');
    console.log('- 行13的方块应该下移到行14');
    
    // 测试V1算法（从下往上）
    console.log('\n🔬 测试V1算法（从下往上遍历）:');
    const v1Result = this._testMultipleRowClearV1(emptyRows);
    
    // 测试V2算法（一次性计算）
    console.log('\n🔬 测试V2算法（一次性计算最终位置）:');
    const v2Result = this._testMultipleRowClearV2(emptyRows);
    
    console.log('\n🧪 测试完成');
    return { v1Result, v2Result };
  }

  /**
   * 测试V1算法的逻辑（不实际移动方块）
   * @param {Array<number>} emptyRows - 空行数组
   * @returns {Array} 移动计划
   * @private
   */
  _testMultipleRowClearV1(emptyRows) {
    const movePlan = [];
    
    // 排序空行，从上到下
    emptyRows.sort((a, b) => a - b);
    
    // 从下往上遍历行
    for (let row = this.grid.rows - 1; row >= 0; row--) {
      // 计算当前行下方有多少个空行
      const emptyRowsBelowCount = emptyRows.filter(emptyRow => emptyRow > row).length;
      
      // 如果下方有空行，且当前行不是空行，则需要下移
      if (emptyRowsBelowCount > 0 && !emptyRows.includes(row)) {
        const targetRow = row + emptyRowsBelowCount;
        movePlan.push({
          algorithm: 'V1',
          sourceRow: row,
          targetRow: targetRow,
          moveDistance: emptyRowsBelowCount
        });
        console.log(`V1计划：第${row}行 -> 第${targetRow}行（下移${emptyRowsBelowCount}行）`);
      }
    }
    
    return movePlan;
  }

  /**
   * 测试V2算法的逻辑（不实际移动方块）
   * @param {Array<number>} emptyRows - 空行数组
   * @returns {Array} 移动计划
   * @private
   */
  _testMultipleRowClearV2(emptyRows) {
    const movePlan = [];
    
    // 排序空行，从上到下
    emptyRows.sort((a, b) => a - b);
    
    // 计算每一行的最终位置
    for (let row = 0; row < this.grid.rows; row++) {
      if (emptyRows.includes(row)) {
        // 空行不需要映射
        continue;
      }
      
      // 计算当前行下方有多少个空行
      const emptyRowsBelowCount = emptyRows.filter(emptyRow => emptyRow > row).length;
      const targetRow = row + emptyRowsBelowCount;
      
      if (row !== targetRow) {
        movePlan.push({
          algorithm: 'V2',
          sourceRow: row,
          targetRow: targetRow,
          moveDistance: emptyRowsBelowCount
        });
        console.log(`V2计划：第${row}行 -> 第${targetRow}行（下移${emptyRowsBelowCount}行）`);
      }
    }
    
    return movePlan;
  }

  /**
   * 全局悬空方块检测和处理（兜底机制）
   * 定期检查整个网格中的悬空方块并让它们下落
   * @returns {boolean} 是否有悬空方块被处理
   * @private
   */
  _detectAndHandleFloatingBlocks() {
    console.log('🔍 开始全局悬空方块检测');
    
    const floatingBlocks = [];
    
    // 从下往上扫描整个网格，寻找悬空方块
    for (let row = this.grid.rows - 2; row >= 0; row--) { // 从倒数第二行开始
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        
        if (block && this._isBlockFloating(row, col)) {
          // 🎯 修复：确保方块信息的行列都是数字类型
          floatingBlocks.push({
            block,
            row: Number(row),    // 确保是数字
            col: Number(col)     // 确保是数字
          });
          console.log(`发现悬空方块 [${row}, ${col}]`);
        }
      }
    }
    
    if (floatingBlocks.length === 0) {
      console.log('✅ 未发现悬空方块');
      return false;
    }
    
    console.log(`🎯 发现${floatingBlocks.length}个悬空方块，开始一次性计算最终位置`);
    
    // 🎯 新算法：一次性计算所有方块的最终位置
    const movePlan = this._calculateOptimalFloatingBlockPositions(floatingBlocks);
    
    if (movePlan.length === 0) {
      console.log('❌ 未找到有效的移动方案');
      return false;
    }
    
    // 🎯 一次性执行所有移动并启动动画
    return this._executeOptimalMovePlan(movePlan);
  }

  /**
   * 计算悬空方块的最优移动方案
   * 一次性计算所有方块的最终位置，避免冲突
   * @param {Array} floatingBlocks - 悬空方块列表
   * @returns {Array} 移动计划
   * @private
   */
  _calculateOptimalFloatingBlockPositions(floatingBlocks) {
    console.log('🎯 开始计算最优移动方案...');
    
    // 创建网格状态快照，用于计算
    const gridSnapshot = this._createGridSnapshot();
    
    // 按列分组悬空方块
    const blocksByColumn = {};
    for (const blockInfo of floatingBlocks) {
      const { col } = blockInfo;
      if (!blocksByColumn[col]) {
        blocksByColumn[col] = [];
      }
      blocksByColumn[col].push(blockInfo);
    }
    
    // 对每列的方块按从下到上排序（重要：保证下落顺序）
    for (const col in blocksByColumn) {
      blocksByColumn[col].sort((a, b) => b.row - a.row);
    }
    
    const movePlan = [];
    
    // 逐列计算每个方块的最终位置
    for (const col in blocksByColumn) {
      const colIndex = parseInt(col, 10); // 🎯 修复：确保列索引是数字类型
      const columnBlocks = blocksByColumn[col];
      console.log(`📊 处理第${colIndex}列的${columnBlocks.length}个悬空方块`);
      
      for (const blockInfo of columnBlocks) {
        const { block, row } = blockInfo;
        
        // 在快照中移除当前方块（模拟它开始下落）
        gridSnapshot[row][colIndex] = null;
        
        // 计算最终位置
        const targetRow = this._findLowestPositionInSnapshot(gridSnapshot, row, colIndex);
        
        if (targetRow > row) {
          // 记录移动计划
          movePlan.push({
            block,
            fromRow: row,
            fromCol: colIndex, // 🎯 修复：使用数字类型的列索引
            toRow: targetRow,
            toCol: colIndex    // 🎯 修复：使用数字类型的列索引
          });
          
          // 在快照中放置方块到新位置（影响后续计算）
          gridSnapshot[targetRow][colIndex] = block;
          
          console.log(`📋 计划移动：[${row}, ${colIndex}] -> [${targetRow}, ${colIndex}]`);
        } else {
          // 方块无需移动，放回快照
          gridSnapshot[row][colIndex] = block;
          console.log(`📋 方块[${row}, ${colIndex}]无需移动`);
        }
      }
    }
    
    console.log(`🎯 移动方案计算完成，共${movePlan.length}个方块需要移动`);
    return movePlan;
  }

  /**
   * 创建网格状态快照
   * @returns {Array} 网格快照
   * @private
   */
  _createGridSnapshot() {
    const snapshot = [];
    for (let row = 0; row < this.grid.rows; row++) {
      snapshot[row] = [];
      for (let col = 0; col < this.grid.cols; col++) {
        snapshot[row][col] = this.grid.getBlock(row, col);
      }
    }
    return snapshot;
  }

  /**
   * 在网格快照中找到最低可放置位置
   * @param {Array} gridSnapshot - 网格快照
   * @param {number} currentRow - 当前行
   * @param {number} col - 列
   * @returns {number} 最低可放置的行索引
   * @private
   */
  _findLowestPositionInSnapshot(gridSnapshot, currentRow, col) {
    // 从底部向上查找第一个空位
    for (let targetRow = this.grid.rows - 1; targetRow > currentRow; targetRow--) {
      if (gridSnapshot[targetRow][col] === null) {
        return targetRow;
      }
    }
    
    // 如果没有找到空位，返回当前位置
    return currentRow;
  }

  /**
   * 执行最优移动方案
   * 同时移动所有方块并启动动画，实现自然的统一下落效果
   * @param {Array} movePlan - 移动计划
   * @returns {boolean} 是否成功执行
   * @private
   */
  _executeOptimalMovePlan(movePlan) {
    console.log(`🚀 开始执行移动方案，共${movePlan.length}个方块同时下落`);
    
    let successCount = 0;
    const animations = [];
    
    // 🎯 关键：先移动所有方块到新位置，再统一创建动画
    // 第一阶段：移动所有方块到新位置
    for (const move of movePlan) {
      const { block, fromRow, fromCol, toRow, toCol } = move;
      
      try {
        // 🎯 修复：确保所有参数都是数字类型
        const numFromRow = Number(fromRow);
        const numFromCol = Number(fromCol);
        const numToRow = Number(toRow);
        const numToCol = Number(toCol);
        
        // 验证转换后的数字是否有效
        if (!Number.isFinite(numFromRow) || !Number.isFinite(numFromCol) || 
            !Number.isFinite(numToRow) || !Number.isFinite(numToCol)) {
          console.error(`❌ 无效的数字参数：fromRow=${fromRow}, fromCol=${fromCol}, toRow=${toRow}, toCol=${toCol}`);
          continue;
        }
        
        // 验证移动的有效性
        if (this.grid.getBlock(numFromRow, numFromCol) !== block) {
          console.warn(`⚠️ 方块位置不匹配 [${numFromRow}, ${numFromCol}]`);
          continue;
        }
        
        if (this.grid.getBlock(numToRow, numToCol) !== null) {
          console.warn(`⚠️ 目标位置 [${numToRow}, ${numToCol}] 不为空`);
          continue;
        }
        
        // 移除原位置
        this.grid.removeBlock(numFromRow, numFromCol);
        
        // 放置到新位置
        this.grid.placeBlock(block, numToRow, numToCol);
        
        // 记录动画信息（确保使用数字类型）
        animations.push({
          block,
          fromRow: numFromRow,
          fromCol: numFromCol,
          toRow: numToRow,
          toCol: numToCol
        });
        
        successCount++;
        
      } catch (error) {
        console.error(`❌ 移动方块失败 [${fromRow}, ${fromCol}] -> [${toRow}, ${toCol}]:`, error);
      }
    }
    
    // 第二阶段：统一创建所有下落动画
    console.log(`🎬 创建${animations.length}个同步下落动画`);
    
    for (const anim of animations) {
      // 🎯 修复：再次确保参数类型正确
      const { block, fromRow, fromCol, toRow, toCol } = anim;
      
      // 添加详细的调试日志
      console.log(`🎭 准备创建动画：block=${!!block}, fromRow=${fromRow}(${typeof fromRow}), fromCol=${fromCol}(${typeof fromCol}), toRow=${toRow}(${typeof toRow}), toCol=${toCol}(${typeof toCol})`);
      
      this.grid.addFallingAnimation(
        block,
        fromRow,
        fromCol,
        toRow,
        toCol
      );
      
      console.log(`🎭 同步动画：[${fromRow}, ${fromCol}] -> [${toRow}, ${toCol}]`);
    }
    
    console.log(`✅ 移动方案执行完成，成功移动${successCount}个方块，创建${animations.length}个同步动画`);
    
    return successCount > 0;
  }

  /**
   * 检查指定位置的方块是否悬空（使用三点支撑检查）
   * 只有当左下、正下、右下三个位置都没有支撑时，才认为是不合理的悬空
   * @param {number} row - 行索引
   * @param {number} col - 列索引
   * @returns {boolean} 是否悬空
   * @private
   */
  _isBlockFloating(row, col) {
    const block = this.grid.getBlock(row, col);
    if (!block) return false;
    
    // 如果已经在底行，不算悬空
    if (row === this.grid.rows - 1) return false;
    
    // 检查三个支撑点：左下、正下、右下
    const supportPositions = [
      { row: row + 1, col: col - 1 }, // 左下
      { row: row + 1, col: col },     // 正下
      { row: row + 1, col: col + 1 }  // 右下
    ];
    
    let hasSupport = false;
    
    for (const pos of supportPositions) {
      // 如果位置超出墙外，算作没有支撑
      if (pos.col < 0 || pos.col >= this.grid.cols) {
        continue; // 墙外位置，跳过
      }
      
      // 如果超出底部，算作有支撑（地面支撑）
      if (pos.row >= this.grid.rows) {
        hasSupport = true;
        break;
      }
      
      // 检查该位置是否有方块支撑
      const supportBlock = this.grid.getBlock(pos.row, pos.col);
      if (supportBlock) {
        hasSupport = true;
        break;
      }
    }
    
    // 如果三个位置都没有支撑，且下方还有空位可以下落，则认为是悬空
    if (!hasSupport) {
      // 进一步检查：确认下方确实有空位可以下落
      for (let checkRow = row + 1; checkRow < this.grid.rows; checkRow++) {
        if (!this.grid.getBlock(checkRow, col)) {
          console.log(`发现严重悬空方块 [${row}, ${col}]：左下、正下、右下都无支撑`);
          return true; // 确认是严重悬空
        }
      }
    }
    
    return false;
  }

  /**
   * 为指定方块找到最低可放置位置
   * @param {number} row - 当前行
   * @param {number} col - 当前列
   * @returns {number} 最低可放置的行索引
   * @private
   */
  _findLowestPositionForBlock(row, col) {
    // 从底部向上查找第一个空位
    for (let targetRow = this.grid.rows - 1; targetRow > row; targetRow--) {
      if (!this.grid.getBlock(targetRow, col)) {
        return targetRow;
      }
    }
    
    // 如果没有找到空位，返回当前位置
    return row;
  }

  /**
   * 智能悬空检查：在特定时机触发悬空方块检测
   * 现在使用更严格的三点支撑判断，只处理真正不合理的悬空（左下、正下、右下都无支撑）
   * @private
   */
  _smartFloatingCheck() {
    // 只在游戏进行中且没有动画时检查
    if (this.state !== GAME_STATE.PLAYING || this.isAnimating) {
      return;
    }
    
    // 只在没有当前方块时检查（避免干扰正常游戏）
    if (this.currentTetromino) {
      return;
    }
    
    const hasFloatingBlocks = this._detectAndHandleFloatingBlocks();
    
    if (hasFloatingBlocks) {
      console.log('🔧 智能检查发现严重悬空方块（三点无支撑），开始处理');
      this._pendingAfterAnimationCheck = true;
    }
  }

  /**
   * 移除所有事件监听器
   * TinyEmitter 没有 removeAllListeners 方法，需要手动实现
   */
  removeAllListeners() {
    // 清空 TinyEmitter 的事件对象
    if (this.e) {
      this.e = {};
    }
  }

  /**
   * 销毁控制器并清理资源
   * 移除所有事件监听器，防止内存泄漏
   */
  destroy() {
    console.log('🧹 销毁游戏控制器，清理事件监听器');

    // 移除键盘事件监听器
    if (this._keyDownHandler) {
      wx.offKeyDown(this._keyDownHandler);
      this._keyDownHandler = null;
    }

    if (this._keyUpHandler) {
      wx.offKeyUp(this._keyUpHandler);
      this._keyUpHandler = null;
    }

    // 清理连击系统
    if (this.comboSystem) {
      this.comboSystem.removeAllListeners();
    }

    // 清理垃圾生成器
    if (this.garbageGenerator) {
      this.garbageGenerator.removeAllListeners();
    }

    // 清理自身的所有事件监听器
    this.removeAllListeners();

    console.log('✅ 游戏控制器清理完成');
  }

}